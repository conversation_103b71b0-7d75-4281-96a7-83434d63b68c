# 🏢 Żyrardów.poleca.to - Portal Lokalny

Nowoczesny portal lokalny dla miasta Żyrardów z panelem administracyjnym opartym na Node.js, MySQL i Express.js.

## ✨ Funkcjonalności

### 🌐 Strona główna
- **Katalog firm** - przeglądanie firm z filtrowaniem po kategoriach
- **System TOP 3** - wyróżnienie najlepszych firm w kategoriach
- **Oferty specjalne** - promocje i rabaty od lokalnych firm
- **Kupony rabatowe** - system kuponów z kodami promocyjnymi
- **Wyszukiwarka** - zaawansowane wyszukiwanie firm i ofert
- **Responsywny design** - dostosowany do urządzeń mobilnych

### 🔧 Panel administracyjny
- **Zarządzanie firmami** - dodawanie, edycja, usuwanie firm
- **Zarządzanie ofertami** - tworzenie i moderacja ofert specjalnych
- **System kuponów** - generowanie i zarządzanie kuponami rabatowymi
- **Kategorie** - hierarchiczne zarządzanie kategoriami firm
- **Upload plików** - automatyczna optymalizacja obrazów
- **System TOP** - przydzielanie pozycji TOP 1-3
- **Bezpieczne logowanie** - JWT + ochrona przed atakami
- **Statystyki** - monitoring wyświetleń i kliknięć

## 🛠️ Technologie

### Backend
- **Node.js** - środowisko uruchomieniowe
- **Express.js** - framework webowy
- **MySQL** - baza danych
- **Sequelize** - ORM dla MySQL
- **JWT** - autoryzacja i autentykacja
- **Multer + Sharp** - upload i optymalizacja obrazów
- **Helmet** - bezpieczeństwo HTTP
- **bcryptjs** - hashowanie haseł

### Frontend
- **Vanilla JavaScript** - bez frameworków
- **Bootstrap 5** - responsywny design
- **Font Awesome** - ikony
- **CSS3** - nowoczesne style

### Bezpieczeństwo
- **Rate limiting** - ochrona przed spam
- **CORS** - kontrola dostępu
- **Input sanitization** - walidacja danych
- **SQL injection protection** - Sequelize ORM
- **XSS protection** - Helmet middleware

## 🚀 Szybki start (Development)

### Wymagania
- Node.js 16+ 
- MySQL 8.0+
- npm lub yarn

### 1. Klonowanie repozytorium
```bash
git clone https://github.com/twoje-repo/portal-zyrardow-poleca-to.git
cd portal-zyrardow-poleca-to
```

### 2. Instalacja zależności
```bash
npm install
```

### 3. Konfiguracja bazy danych
```bash
# Uruchom MySQL i utwórz bazę danych
mysql -u root -p
CREATE DATABASE zyrardow_poleca_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;
```

### 4. Konfiguracja zmiennych środowiskowych
```bash
cp .env.example .env
# Edytuj plik .env z własnymi danymi
```

### 5. Inicjalizacja bazy danych
```bash
npm run setup-db
```

### 6. Uruchomienie aplikacji
```bash
# Development
npm run dev

# Production
npm start
```

### 7. Dostęp do aplikacji
- **Strona główna**: http://localhost:3000
- **Panel admin**: http://localhost:3000/admin
- **API**: http://localhost:3000/api

### Domyślne dane logowania
- **Email**: <EMAIL>
- **Hasło**: AdminZyrardow2024!

## 📁 Struktura projektu

```
portal-zyrardow-poleca-to/
├── server/                     # Backend Node.js
│   ├── config/                # Konfiguracja bazy danych
│   ├── models/                # Modele Sequelize
│   ├── controllers/           # Kontrolery API
│   ├── routes/                # Definicje tras API
│   ├── middleware/            # Middleware (auth, security, upload)
│   ├── scripts/               # Skrypty pomocnicze
│   ├── uploads/               # Przesłane pliki
│   └── app.js                 # Główny plik serwera
├── admin/                     # Panel administracyjny
│   ├── css/                   # Style CSS
│   ├── js/                    # JavaScript
│   ├── images/                # Obrazy
│   └── *.html                 # Strony HTML
├── public/                    # Strona główna
│   ├── css/                   # Style CSS
│   ├── js/                    # JavaScript
│   ├── images/                # Obrazy
│   └── index.html             # Strona główna
├── package.json               # Zależności npm
├── .env                       # Zmienne środowiskowe
├── README.md                  # Ten plik
└── DEPLOYMENT.md              # Instrukcje wdrożenia
```

## 🔌 API Endpoints

### Publiczne (bez autoryzacji)
```
GET    /api/public/companies     # Lista aktywnych firm
GET    /api/public/companies/top # Firmy TOP 3
GET    /api/public/offers        # Aktywne oferty
GET    /api/public/categories    # Kategorie
GET    /api/public/search        # Wyszukiwanie
```

### Autoryzacja
```
POST   /api/auth/login          # Logowanie
POST   /api/auth/logout         # Wylogowanie
GET    /api/auth/check          # Sprawdzenie autoryzacji
POST   /api/auth/change-password # Zmiana hasła
```

### Zarządzanie firmami (wymagana autoryzacja)
```
GET    /api/companies           # Lista firm
GET    /api/companies/:id       # Szczegóły firmy
POST   /api/companies           # Dodanie firmy
PUT    /api/companies/:id       # Edycja firmy
DELETE /api/companies/:id       # Usunięcie firmy
PATCH  /api/companies/:id/top-position # Ustawienie pozycji TOP
```

### Zarządzanie ofertami
```
GET    /api/offers              # Lista ofert
GET    /api/offers/:id          # Szczegóły oferty
POST   /api/offers              # Dodanie oferty
PUT    /api/offers/:id          # Edycja oferty
DELETE /api/offers/:id          # Usunięcie oferty
```

### Zarządzanie kuponów
```
GET    /api/coupons             # Lista kuponów
GET    /api/coupons/:id         # Szczegóły kuponu
POST   /api/coupons             # Dodanie kuponu
PUT    /api/coupons/:id         # Edycja kuponu
DELETE /api/coupons/:id         # Usunięcie kuponu
```

### Zarządzanie kategoriami
```
GET    /api/categories          # Lista kategorii
GET    /api/categories/:id      # Szczegóły kategorii
POST   /api/categories          # Dodanie kategorii
PUT    /api/categories/:id      # Edycja kategorii
DELETE /api/categories/:id      # Usunięcie kategorii
```

## 🗄️ Baza danych

### Główne tabele
- **admins** - administratorzy systemu
- **categories** - kategorie i podkategorie
- **companies** - firmy w katalogu
- **offers** - oferty specjalne
- **coupons** - kupony rabatowe

### Relacje
- Company belongsTo Category (kategoria główna)
- Company belongsTo Category (podkategoria)
- Offer belongsTo Company
- Coupon belongsTo Company
- Category hasMany Category (podkategorie)

## 🔒 Bezpieczeństwo

### Implementowane zabezpieczenia
- **JWT Authentication** - bezpieczne tokeny dostępu
- **Password Hashing** - bcrypt z salt rounds
- **Rate Limiting** - ochrona przed spam i DDoS
- **Input Sanitization** - walidacja wszystkich danych wejściowych
- **CORS Protection** - kontrola dostępu cross-origin
- **Helmet.js** - bezpieczne nagłówki HTTP
- **SQL Injection Protection** - Sequelize ORM
- **File Upload Security** - walidacja typów i rozmiarów plików
- **Account Lockout** - blokada po nieudanych próbach logowania

## 📊 Monitoring i logi

### Logi aplikacji
- **Morgan** - logi HTTP requests
- **Custom logging** - logi bezpieczeństwa i błędów
- **PM2 logs** - logi procesów (produkcja)

### Metryki
- **Wyświetlenia firm** - tracking popularności
- **Kliki w oferty** - analiza konwersji
- **Użycie kuponów** - statystyki rabatów

## 🚀 Wdrożenie na produkcję

Szczegółowe instrukcje wdrożenia na VPS Aruba znajdziesz w pliku [DEPLOYMENT.md](DEPLOYMENT.md).

### Kluczowe kroki:
1. Konfiguracja serwera Ubuntu
2. Instalacja Node.js, MySQL, Nginx
3. Konfiguracja SSL (Let's Encrypt)
4. Uruchomienie z PM2
5. Konfiguracja backupów
6. Monitoring i bezpieczeństwo

## 🤝 Rozwój

### Uruchomienie w trybie development
```bash
npm run dev  # Nodemon z auto-restart
```

### Dostępne skrypty
```bash
npm start           # Uruchomienie produkcyjne
npm run dev         # Uruchomienie development
npm run setup-db    # Inicjalizacja bazy danych
npm run seed        # Dodanie przykładowych danych
```

### Struktura kodu
- **MVC Pattern** - separacja logiki biznesowej
- **Middleware Pattern** - modularne przetwarzanie żądań
- **Repository Pattern** - abstrakcja dostępu do danych
- **Error Handling** - centralna obsługa błędów

## 📝 Licencja

MIT License - szczegóły w pliku LICENSE

## 📞 Kontakt

- **Email**: <EMAIL>
- **Telefon**: 570 888 999
- **Strona**: https://zyrardow.poleca.to

---

**Żyrardów.poleca.to** - Twój lokalny przewodnik po najlepszych firmach w Żyrardowie! 🏢✨
