/**
 * Obsługa kategorii w sidebarze - Żyrardów Poleca
 * JavaScript dla obsługi interakcji z kategoriami w sidebarze
 */

document.addEventListener('DOMContentLoaded', function() {
    // Obsługa rozwijania/zwijania kategorii
    initCategorySidebar();

    // Obsługa kliknięć w podkategorie
    initSubcategoryLinks();

    // Obsługa wyszukiwania w katalogu
    initCatalogSearch();

    // Obsługa lightboxa dla wizytówek firm
    initCompanyLightbox();

    // Dodanie lightboxa do strony
    addCompanyLightboxToPage();
});

/**
 * Inicjalizacja sidebara z kategoriami
 */
function initCategorySidebar() {
    const categoryItems = document.querySelectorAll('.category-item');

    categoryItems.forEach(item => {
        const header = item.querySelector('.category-header');

        if (header) {
            header.addEventListener('click', () => {
                // Przełącz aktywną kategorię
                item.classList.toggle('active');
            });
        }
    });

    // Domyślnie wszystkie kategorie są zwinięte
    categoryItems.forEach(item => {
        item.classList.remove('active');
    });
}

/**
 * Inicjalizacja linków podkategorii
 */
function initSubcategoryLinks() {
    const subcategoryLinks = document.querySelectorAll('.subcategories-list a');

    subcategoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Usuń aktywną klasę ze wszystkich linków
            subcategoryLinks.forEach(l => l.classList.remove('active'));

            // Dodaj aktywną klasę do klikniętego linku
            this.classList.add('active');

            const category = this.getAttribute('data-category');
            const subcategory = this.getAttribute('data-subcategory');

            // Pokaż firmy z wybranej kategorii
            showCompaniesForCategory(category, subcategory);
        });
    });
}

/**
 * Inicjalizacja wyszukiwania w katalogu
 */
function initCatalogSearch() {
    const catalogSearchForm = document.getElementById('catalogSearchForm');
    const catalogSearchInput = document.getElementById('catalogSearchInput');

    if (catalogSearchForm && catalogSearchInput) {
        catalogSearchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const searchQuery = catalogSearchInput.value.trim();

            if (searchQuery.length > 2) {
                // Wyszukaj firmy
                searchCompanies(searchQuery);
            }
        });
    }
}

/**
 * Wyświetlanie firm dla wybranej kategorii i podkategorii
 */
function showCompaniesForCategory(category, subcategory) {
    const categoryContent = document.getElementById('categoryContent');
    if (!categoryContent) return;

    // Ukryj ekran powitalny
    const catalogWelcome = document.getElementById('catalogWelcome');
    if (catalogWelcome) {
        catalogWelcome.style.display = 'none';
    }

    // Tutaj w przyszłości można dodać kod do pobierania firm z API
    // Na razie używamy przykładowych danych

    // Formatowanie nazwy kategorii i podkategorii
    const formattedCategory = formatCategoryName(category);
    const formattedSubcategory = formatSubcategoryName(subcategory);

    // Wyświetl firmy
    categoryContent.innerHTML = `
        <div class="top-companies-section">
            <h3>TOP 3 firmy w kategorii: ${formattedSubcategory}</h3>
            <div class="top-companies-list">
                <div class="top-company-card">
                    <div class="company-badge">TOP 1</div>
                    <div class="company-logo">
                        <img src="images/business-logo1.jpg" alt="Logo Przykładowej Firmy">
                    </div>
                    <div class="company-info">
                        <h4>Przykładowa Firma 1</h4>
                        <div class="company-tags">
                            <span class="company-tag">${formattedCategory}</span>
                            <span class="company-tag">${formattedSubcategory}</span>
                        </div>
                        <p class="company-description">Opis przykładowej firmy w wybranej kategorii. Oferujemy profesjonalne usługi w Żyrardowie i okolicach.</p>
                        <div class="company-contact">
                            <p><i class="fas fa-map-marker-alt"></i> ul. Przykładowa 1, Żyrardów</p>
                            <p><i class="fas fa-phone"></i> +48 ***********</p>
                            <p><i class="fas fa-globe"></i> <a href="https://www.przykladowa-firma1.pl" target="_blank">www.przykladowa-firma1.pl</a></p>
                            <p><i class="fas fa-envelope"></i> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                        <a href="#" class="btn btn-sm btn-outline company-details-btn" data-company="firma1">Zobacz więcej</a>
                    </div>
                </div>

                <div class="top-company-card">
                    <div class="company-badge">TOP 2</div>
                    <div class="company-logo">
                        <img src="images/business-logo2.jpg" alt="Logo Przykładowej Firmy">
                    </div>
                    <div class="company-info">
                        <h4>Przykładowa Firma 2</h4>
                        <div class="company-tags">
                            <span class="company-tag">${formattedCategory}</span>
                            <span class="company-tag">${formattedSubcategory}</span>
                        </div>
                        <p class="company-description">Opis przykładowej firmy w wybranej kategorii. Działamy na rynku od 10 lat, zapewniając najwyższą jakość usług.</p>
                        <div class="company-contact">
                            <p><i class="fas fa-map-marker-alt"></i> ul. Przykładowa 2, Żyrardów</p>
                            <p><i class="fas fa-phone"></i> +**************</p>
                            <p><i class="fas fa-globe"></i> <a href="https://www.przykladowa-firma2.pl" target="_blank">www.przykladowa-firma2.pl</a></p>
                            <p><i class="fas fa-envelope"></i> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                        <a href="#" class="btn btn-sm btn-outline company-details-btn" data-company="firma2">Zobacz więcej</a>
                    </div>
                </div>

                <div class="top-company-card">
                    <div class="company-badge">TOP 3</div>
                    <div class="company-logo">
                        <img src="images/business-logo3.jpg" alt="Logo Przykładowej Firmy">
                    </div>
                    <div class="company-info">
                        <h4>Przykładowa Firma 3</h4>
                        <div class="company-tags">
                            <span class="company-tag">${formattedCategory}</span>
                            <span class="company-tag">${formattedSubcategory}</span>
                        </div>
                        <p class="company-description">Opis przykładowej firmy w wybranej kategorii. Naszym priorytetem jest zadowolenie klienta i profesjonalizm.</p>
                        <div class="company-contact">
                            <p><i class="fas fa-map-marker-alt"></i> ul. Przykładowa 3, Żyrardów</p>
                            <p><i class="fas fa-phone"></i> +**************</p>
                            <p><i class="fas fa-globe"></i> <a href="https://www.przykladowa-firma3.pl" target="_blank">www.przykladowa-firma3.pl</a></p>
                            <p><i class="fas fa-envelope"></i> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                        <a href="#" class="btn btn-sm btn-outline company-details-btn" data-company="firma3">Zobacz więcej</a>
                    </div>
                </div>
            </div>

            <div class="add-company-promo">
                <div class="promo-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="promo-content">
                    <h4>Dodaj swoją firmę do katalogu</h4>
                    <p>Zwiększ widoczność swojej firmy w internecie i pozyskaj nowych klientów.</p>
                    <a href="dodaj-firme.html" class="btn btn-primary">Dodaj firmę</a>
                </div>
            </div>
        </div>
    `;
}

/**
 * Wyszukiwanie firm
 */
function searchCompanies(query) {
    const categoryContent = document.getElementById('categoryContent');
    if (!categoryContent) return;

    // Ukryj ekran powitalny
    const catalogWelcome = document.getElementById('catalogWelcome');
    if (catalogWelcome) {
        catalogWelcome.style.display = 'none';
    }

    // Znajdź pasujące podkategorie
    const matchingSubcategories = findMatchingSubcategories(query);

    // Wyświetl wyniki wyszukiwania
    categoryContent.innerHTML = `
        <div class="top-companies-section">
            <h3>Podkategorie pasujące do zapytania: "${query}"</h3>
            <div class="search-subcategories">
                ${matchingSubcategories.map(subcategory => `
                    <div class="search-subcategory-card">
                        <div class="subcategory-icon">
                            <i class="${subcategory.icon}"></i>
                        </div>
                        <h4>${subcategory.name}</h4>
                        <p>${subcategory.description}</p>
                        <a href="#" class="btn btn-sm btn-primary" data-category="${subcategory.category}" data-subcategory="${subcategory.id}">Pokaż firmy</a>
                    </div>
                `).join('')}
            </div>

            <div class="search-help">
                <h4>Nie znalazłeś tego, czego szukasz?</h4>
                <p>Spróbuj wybrać kategorię z listy po lewej stronie lub skorzystaj z bardziej ogólnego hasła.</p>
            </div>
        </div>
    `;

    // Dodaj obsługę kliknięć w przyciski "Pokaż firmy"
    const showCompanyButtons = categoryContent.querySelectorAll('.search-subcategory-card .btn');
    showCompanyButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const category = this.getAttribute('data-category');
            const subcategory = this.getAttribute('data-subcategory');
            showCompaniesForCategory(category, subcategory);

            // Aktywuj odpowiednią kategorię w sidebarze
            activateCategoryInSidebar(category, subcategory);
        });
    });
}

/**
 * Znajdź pasujące podkategorie
 */
function findMatchingSubcategories(query) {
    // Normalizuj zapytanie
    const normalizedQuery = query.toLowerCase().trim();

    // Lista wszystkich podkategorii
    const allSubcategories = [
        // Jedzenie i Gastronomia
        { id: 'restaurants', category: 'food', name: 'Restauracje', icon: 'fas fa-utensils', description: 'Restauracje oferujące różnorodne dania i kuchnie z całego świata.' },
        { id: 'pizzerias', category: 'food', name: 'Pizzerie', icon: 'fas fa-pizza-slice', description: 'Pizzerie serwujące tradycyjną włoską pizzę i inne dania.' },
        { id: 'fastfood', category: 'food', name: 'Fast Food', icon: 'fas fa-hamburger', description: 'Lokale typu fast food oferujące szybkie posiłki.' },
        { id: 'cafes', category: 'food', name: 'Kawiarnie i herbaciarnie', icon: 'fas fa-coffee', description: 'Kawiarnie i herbaciarnie z bogatą ofertą napojów i deserów.' },
        { id: 'bakeries', category: 'food', name: 'Cukiernie i piekarnie', icon: 'fas fa-bread-slice', description: 'Cukiernie i piekarnie oferujące świeże wypieki i słodkości.' },

        // Zdrowie i Uroda
        { id: 'clinics', category: 'health', name: 'Przychodnie i gabinety', icon: 'fas fa-clinic-medical', description: 'Przychodnie i gabinety lekarskie oferujące usługi medyczne.' },
        { id: 'pharmacies', category: 'health', name: 'Apteki', icon: 'fas fa-prescription-bottle-alt', description: 'Apteki oferujące leki i produkty farmaceutyczne.' },
        { id: 'cosmetics', category: 'health', name: 'Salony kosmetyczne', icon: 'fas fa-spa', description: 'Salony kosmetyczne oferujące zabiegi pielęgnacyjne i upiększające.' },
        { id: 'hairdressers', category: 'health', name: 'Salony fryzjerskie', icon: 'fas fa-cut', description: 'Salony fryzjerskie oferujące usługi strzyżenia i stylizacji włosów.' },

        // Dom i Ogród
        { id: 'furniture', category: 'home', name: 'Sklepy meblowe', icon: 'fas fa-couch', description: 'Sklepy meblowe oferujące meble do domu i ogrodu.' },
        { id: 'construction', category: 'home', name: 'Sklepy budowlane', icon: 'fas fa-hammer', description: 'Sklepy budowlane oferujące materiały i narzędzia budowlane.' },
        { id: 'gardening', category: 'home', name: 'Ogrodnicy', icon: 'fas fa-leaf', description: 'Firmy ogrodnicze oferujące usługi projektowania i pielęgnacji ogrodów.' },

        // Motoryzacja
        { id: 'mechanics', category: 'automotive', name: 'Warsztaty samochodowe', icon: 'fas fa-wrench', description: 'Warsztaty samochodowe oferujące naprawy i serwis pojazdów.' },
        { id: 'dealers', category: 'automotive', name: 'Salony samochodowe', icon: 'fas fa-car', description: 'Salony samochodowe oferujące sprzedaż nowych i używanych pojazdów.' },
        { id: 'carwash', category: 'automotive', name: 'Myjnie', icon: 'fas fa-soap', description: 'Myjnie samochodowe oferujące usługi mycia i czyszczenia pojazdów.' }
    ];

    // Filtruj podkategorie pasujące do zapytania
    const matchingSubcategories = allSubcategories.filter(subcategory => {
        return subcategory.name.toLowerCase().includes(normalizedQuery) ||
               subcategory.description.toLowerCase().includes(normalizedQuery);
    });

    // Zwróć maksymalnie 3 pasujące podkategorie
    return matchingSubcategories.slice(0, 3);
}

/**
 * Aktywuj kategorię w sidebarze
 */
function activateCategoryInSidebar(category, subcategory) {
    // Znajdź element kategorii
    const categoryItem = document.querySelector(`.category-item[data-category="${category}"]`);
    if (!categoryItem) return;

    // Aktywuj kategorię
    const allCategoryItems = document.querySelectorAll('.category-item');
    allCategoryItems.forEach(item => item.classList.remove('active'));
    categoryItem.classList.add('active');

    // Znajdź i aktywuj podkategorię
    const subcategoryLink = categoryItem.querySelector(`a[data-subcategory="${subcategory}"]`);
    if (subcategoryLink) {
        const allSubcategoryLinks = document.querySelectorAll('.subcategories-list a');
        allSubcategoryLinks.forEach(link => link.classList.remove('active'));
        subcategoryLink.classList.add('active');
    }
}

/**
 * Formatowanie nazwy kategorii
 */
function formatCategoryName(category) {
    const categoryMap = {
        'food': 'Jedzenie i Gastronomia',
        'health': 'Zdrowie i Uroda',
        'home': 'Dom i Ogród',
        'automotive': 'Motoryzacja',
        'education': 'Edukacja i Nauka',
        'business': 'Usługi Biznesowe',
        'shopping': 'Zakupy i Handel',
        'entertainment': 'Rozrywka i Czas Wolny',
        'tourism': 'Turystyka i Noclegi',
        'transport': 'Transport i Logistyka'
    };

    return categoryMap[category] || category;
}

/**
 * Formatowanie nazwy podkategorii
 */
function formatSubcategoryName(subcategory) {
    const subcategoryMap = {
        // Jedzenie i Gastronomia
        'restaurants': 'Restauracje',
        'pizzerias': 'Pizzerie',
        'fastfood': 'Fast Food',
        'cafes': 'Kawiarnie i herbaciarnie',
        'bakeries': 'Cukiernie i piekarnie',
        'catering': 'Catering',
        'groceries': 'Sklepy spożywcze',

        // Zdrowie i Uroda
        'clinics': 'Przychodnie i gabinety',
        'pharmacies': 'Apteki',
        'cosmetics': 'Salony kosmetyczne',
        'hairdressers': 'Salony fryzjerskie',
        'spa': 'SPA i masaż',
        'fitness': 'Siłownie i fitness',

        // Dom i Ogród
        'furniture': 'Sklepy meblowe',
        'construction': 'Sklepy budowlane',
        'architects': 'Architekci i projektanci',
        'builders': 'Firmy budowlane',
        'gardening': 'Ogrodnicy',

        // Motoryzacja
        'mechanics': 'Warsztaty samochodowe',
        'dealers': 'Salony samochodowe',
        'carwash': 'Myjnie',
        'parts': 'Części i akcesoria',
        'tires': 'Wulkanizacja'
    };

    return subcategoryMap[subcategory] || subcategory;
}

/**
 * Inicjalizacja lightboxa dla wizytówek firm
 */
function initCompanyLightbox() {
    // Nasłuchiwanie na kliknięcia w przyciski "Zobacz więcej"
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('company-details-btn') ||
            e.target.closest('.company-details-btn')) {

            e.preventDefault();

            // Pobierz przycisk (może być sam przycisk lub element wewnątrz przycisku)
            const button = e.target.classList.contains('company-details-btn') ?
                          e.target : e.target.closest('.company-details-btn');

            // Pobierz identyfikator firmy
            const companyId = button.getAttribute('data-company');

            // Pokaż lightbox z danymi firmy
            showCompanyLightbox(companyId);
        }
    });

    // Zamykanie lightboxa po kliknięciu w tło lub przycisk zamknięcia
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('company-lightbox-overlay') ||
            e.target.classList.contains('company-lightbox-close')) {

            e.preventDefault();
            hideCompanyLightbox();
        }
    });

    // Zamykanie lightboxa po naciśnięciu klawisza Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideCompanyLightbox();
        }
    });
}

/**
 * Dodanie lightboxa do strony
 */
function addCompanyLightboxToPage() {
    // Sprawdź, czy lightbox już istnieje
    if (!document.getElementById('companyLightbox')) {
        // Utwórz element lightboxa
        const lightboxHTML = `
            <div id="companyLightbox" class="company-lightbox-overlay">
                <div class="company-lightbox-content">
                    <button class="company-lightbox-close" aria-label="Zamknij">&times;</button>
                    <div class="company-lightbox-body">
                        <!-- Tutaj będą wyświetlane dane firmy -->
                    </div>
                </div>
            </div>
        `;

        // Dodaj lightbox do body
        document.body.insertAdjacentHTML('beforeend', lightboxHTML);
    }
}

/**
 * Pokazanie lightboxa z danymi firmy
 */
function showCompanyLightbox(companyId) {
    // Pobierz dane firmy (w rzeczywistej aplikacji dane byłyby pobierane z API)
    const companyData = getCompanyData(companyId);

    // Pobierz element lightboxa
    const lightbox = document.getElementById('companyLightbox');
    const lightboxBody = lightbox.querySelector('.company-lightbox-body');

    // Wypełnij lightbox danymi firmy
    lightboxBody.innerHTML = `
        <div class="company-lightbox-header">
            <div class="company-lightbox-logo">
                <img src="${companyData.logo}" alt="${companyData.name}">
            </div>
            <div class="company-lightbox-title">
                <h2>${companyData.name}</h2>
                <div class="company-lightbox-tags">
                    ${companyData.tags.map(tag => `<span class="company-tag">${tag}</span>`).join('')}
                </div>
            </div>
        </div>
        <div class="company-lightbox-description">
            <p>${companyData.description}</p>
        </div>
        <div class="company-lightbox-details">
            <div class="company-lightbox-contact">
                <h3>Dane kontaktowe</h3>
                <p><i class="fas fa-map-marker-alt"></i> ${companyData.address}</p>
                <p><i class="fas fa-phone"></i> ${companyData.phone}</p>
                <p><i class="fas fa-globe"></i> <a href="${companyData.website}" target="_blank">${companyData.websiteDisplay}</a></p>
                <p><i class="fas fa-envelope"></i> <a href="mailto:${companyData.email}">${companyData.email}</a></p>
            </div>
            <div class="company-lightbox-social">
                <h3>Media społecznościowe</h3>
                <div class="company-social-links">
                    ${companyData.social.facebook ? `<a href="${companyData.social.facebook}" target="_blank" class="social-link"><i class="fab fa-facebook-f"></i></a>` : ''}
                    ${companyData.social.instagram ? `<a href="${companyData.social.instagram}" target="_blank" class="social-link"><i class="fab fa-instagram"></i></a>` : ''}
                    ${companyData.social.youtube ? `<a href="${companyData.social.youtube}" target="_blank" class="social-link"><i class="fab fa-youtube"></i></a>` : ''}
                </div>
            </div>
        </div>
        <div class="company-lightbox-gallery">
            <h3>Galeria</h3>
            <div class="company-gallery-grid">
                ${companyData.gallery.map(image => `
                    <div class="gallery-item">
                        <img src="${image}" alt="${companyData.name}">
                    </div>
                `).join('')}
            </div>
        </div>
        <div class="company-lightbox-hours">
            <h3>Godziny otwarcia</h3>
            <ul class="company-hours-list">
                ${Object.entries(companyData.hours).map(([day, hours]) => `
                    <li><span class="day">${day}:</span> <span class="hours">${hours}</span></li>
                `).join('')}
            </ul>
        </div>
    `;

    // Pokaż lightbox
    lightbox.classList.add('active');
    document.body.classList.add('lightbox-open');
}

/**
 * Ukrycie lightboxa
 */
function hideCompanyLightbox() {
    const lightbox = document.getElementById('companyLightbox');
    if (lightbox) {
        lightbox.classList.remove('active');
        document.body.classList.remove('lightbox-open');
    }
}

/**
 * Pobieranie danych firmy (przykładowe dane)
 */
function getCompanyData(companyId) {
    // W rzeczywistej aplikacji dane byłyby pobierane z API
    const companiesData = {
        'firma1': {
            name: 'Przykładowa Firma 1',
            logo: 'images/business-logo1.jpg',
            tags: ['Gastronomia', 'Restauracja'],
            description: 'Przykładowa Firma 1 to restauracja oferująca tradycyjne polskie dania w nowoczesnym wydaniu. Nasze menu opiera się na świeżych, lokalnych składnikach. Organizujemy również imprezy okolicznościowe, catering oraz eventy firmowe. Zapraszamy do odwiedzenia naszego lokalu w centrum Żyrardowa.',
            address: 'ul. Przykładowa 1, Żyrardów',
            phone: '+48 ***********',
            website: 'https://www.przykladowa-firma1.pl',
            websiteDisplay: 'www.przykladowa-firma1.pl',
            email: '<EMAIL>',
            social: {
                facebook: 'https://www.facebook.com/przykladowa-firma1',
                instagram: 'https://www.instagram.com/przykladowa-firma1',
                youtube: 'https://www.youtube.com/przykladowa-firma1'
            },
            gallery: [
                'images/business-logo1.jpg',
                'images/business-logo1.jpg',
                'images/business-logo1.jpg',
                'images/business-logo1.jpg'
            ],
            hours: {
                'Poniedziałek': '10:00 - 22:00',
                'Wtorek': '10:00 - 22:00',
                'Środa': '10:00 - 22:00',
                'Czwartek': '10:00 - 22:00',
                'Piątek': '10:00 - 23:00',
                'Sobota': '12:00 - 23:00',
                'Niedziela': '12:00 - 20:00'
            }
        },
        'firma2': {
            name: 'Przykładowa Firma 2',
            logo: 'images/business-logo2.jpg',
            tags: ['Zdrowie', 'Medycyna'],
            description: 'Przykładowa Firma 2 to nowoczesne centrum medyczne oferujące kompleksową opiekę zdrowotną. Nasz zespół składa się z doświadczonych lekarzy specjalistów, którzy zapewniają profesjonalną opiekę medyczną. Oferujemy szeroki zakres usług medycznych, w tym konsultacje specjalistyczne, badania diagnostyczne oraz zabiegi.',
            address: 'ul. Przykładowa 2, Żyrardów',
            phone: '+**************',
            website: 'https://www.przykladowa-firma2.pl',
            websiteDisplay: 'www.przykladowa-firma2.pl',
            email: '<EMAIL>',
            social: {
                facebook: 'https://www.facebook.com/przykladowa-firma2',
                instagram: 'https://www.instagram.com/przykladowa-firma2',
                youtube: null
            },
            gallery: [
                'images/business-logo2.jpg',
                'images/business-logo2.jpg',
                'images/business-logo2.jpg',
                'images/business-logo2.jpg'
            ],
            hours: {
                'Poniedziałek': '8:00 - 20:00',
                'Wtorek': '8:00 - 20:00',
                'Środa': '8:00 - 20:00',
                'Czwartek': '8:00 - 20:00',
                'Piątek': '8:00 - 20:00',
                'Sobota': '9:00 - 14:00',
                'Niedziela': 'Zamknięte'
            }
        },
        'firma3': {
            name: 'Przykładowa Firma 3',
            logo: 'images/business-logo3.jpg',
            tags: ['Uroda', 'Fryzjer'],
            description: 'Przykładowa Firma 3 to profesjonalny salon fryzjerski oferujący pełen zakres usług dla kobiet, mężczyzn i dzieci. Nasi styliści regularnie uczestniczą w szkoleniach, aby być na bieżąco z najnowszymi trendami. Oferujemy strzyżenie, koloryzację, stylizację, zabiegi regeneracyjne oraz profesjonalne doradztwo.',
            address: 'ul. Przykładowa 3, Żyrardów',
            phone: '+**************',
            website: 'https://www.przykladowa-firma3.pl',
            websiteDisplay: 'www.przykladowa-firma3.pl',
            email: '<EMAIL>',
            social: {
                facebook: 'https://www.facebook.com/przykladowa-firma3',
                instagram: 'https://www.instagram.com/przykladowa-firma3',
                youtube: 'https://www.youtube.com/przykladowa-firma3'
            },
            gallery: [
                'images/business-logo3.jpg',
                'images/business-logo3.jpg',
                'images/business-logo3.jpg',
                'images/business-logo3.jpg'
            ],
            hours: {
                'Poniedziałek': '9:00 - 19:00',
                'Wtorek': '9:00 - 19:00',
                'Środa': '9:00 - 19:00',
                'Czwartek': '9:00 - 19:00',
                'Piątek': '9:00 - 19:00',
                'Sobota': '9:00 - 16:00',
                'Niedziela': 'Zamknięte'
            }
        }
    };

    return companiesData[companyId] || null;
}
