/**
 * Mo<PERSON>ł wiadomości lokalnych dla strony głównej
 * Żyrardów Poleca
 */

document.addEventListener('DOMContentLoaded', function() {
    initNewsSection();
});

/**
 * Inicjalizacja sekcji wiadomości
 */
function initNewsSection() {
    // Sprawdź ustawienia widoczności sekcji
    const newsSettings = getNewsSettings();

    if (newsSettings.visible) {
        showNewsSection();
        loadAndDisplayNews();
    } else {
        hideNewsSection();
    }
}

/**
 * Pobieranie ustawień wiadomości z localStorage
 */
function getNewsSettings() {
    const settings = localStorage.getItem('zyrardow_news_settings');
    if (settings) {
        try {
            return JSON.parse(settings);
        } catch (error) {
            console.error('Błąd podczas parsowania ustawień wiadomości:', error);
        }
    }

    // Domyślne ustawienia
    return {
        visible: false, // Domyślnie ukryte
        maxItems: 3,
        categories: ['local', 'events', 'business', 'culture', 'sport']
    };
}

/**
 * Pokazywanie sekcji wiadomości
 */
function showNewsSection() {
    const newsSection = document.getElementById('newsSection');
    if (newsSection) {
        newsSection.style.display = 'block';
    }
}

/**
 * Ukrywanie sekcji wiadomości
 */
function hideNewsSection() {
    const newsSection = document.getElementById('newsSection');
    if (newsSection) {
        newsSection.style.display = 'none';
    }
}

/**
 * Ładowanie i wyświetlanie wiadomości
 */
function loadAndDisplayNews() {
    // Pobierz wiadomości z localStorage lub API
    const newsData = getNewsData();
    const settings = getNewsSettings();

    // Filtruj i sortuj wiadomości
    const filteredNews = filterNews(newsData, settings);
    const limitedNews = filteredNews.slice(0, settings.maxItems);

    // Wyświetl wiadomości
    renderNews(limitedNews);
}

/**
 * Pobieranie danych wiadomości
 */
function getNewsData() {
    // Najpierw sprawdź localStorage
    const savedNews = localStorage.getItem('zyrardow_news');
    if (savedNews) {
        try {
            const newsData = JSON.parse(savedNews);
            return newsData.filter(news => news.status === 'published');
        } catch (error) {
            console.error('Błąd podczas ładowania wiadomości z localStorage:', error);
        }
    }

    // Jeśli nie ma danych w localStorage, zwróć przykładowe dane
    return getExampleNews();
}

/**
 * Przykładowe wiadomości (dla demonstracji)
 */
function getExampleNews() {
    return [
        {
            id: 1,
            title: "Nowe inwestycje w centrum Żyrardowa",
            category: "local",
            excerpt: "Miasto planuje modernizację centrum miasta z nowymi chodnikami i oświetleniem LED.",
            content: "Szczegółowy opis inwestycji...",
            image: "images/news/inwestycje.jpg",
            imageAlt: "Centrum Żyrardowa",
            status: "published",
            publishDate: "2024-01-15T10:00:00",
            slug: "nowe-inwestycje-centrum-zyrardowa",
            metaDescription: "Miasto Żyrardów planuje nowe inwestycje w centrum miasta",
            tags: "żyrardów, inwestycje, centrum, modernizacja"
        },
        {
            id: 2,
            title: "Festiwal Kultury Żyrardowskiej 2024",
            category: "culture",
            excerpt: "Już w czerwcu odbędzie się coroczny Festiwal Kultury z bogatym programem artystycznym.",
            content: "Program festiwalu...",
            image: null,
            imageAlt: "",
            status: "published",
            publishDate: "2024-01-10T18:00:00",
            slug: "festiwal-kultury-zyrardowskiej-2024",
            metaDescription: "Festiwal Kultury Żyrardowskiej 2024 - program i informacje",
            tags: "żyrardów, kultura, festiwal, wydarzenia"
        },
        {
            id: 3,
            title: "Nowa restauracja w centrum miasta",
            category: "business",
            excerpt: "W centrum Żyrardowa otwarto nową restaurację serwującą kuchnię regionalną.",
            content: "Szczegóły o restauracji...",
            image: "images/news/restauracja.jpg",
            imageAlt: "Nowa restauracja w Żyrardowie",
            status: "published",
            publishDate: "2024-01-05T12:00:00",
            slug: "nowa-restauracja-centrum-miasta",
            metaDescription: "Nowa restauracja w centrum Żyrardowa - kuchnia regionalna",
            tags: "żyrardów, restauracja, gastronomia, biznes"
        }
    ];
}

/**
 * Filtrowanie wiadomości
 */
function filterNews(newsData, settings) {
    return newsData
        .filter(news => settings.categories.includes(news.category))
        .sort((a, b) => new Date(b.publishDate) - new Date(a.publishDate));
}

/**
 * Renderowanie wiadomości
 */
function renderNews(newsData) {
    const newsGrid = document.getElementById('newsGrid');
    if (!newsGrid) return;

    if (newsData.length === 0) {
        newsGrid.innerHTML = `
            <div class="news-empty">
                <p>Brak wiadomości do wyświetlenia.</p>
            </div>
        `;
        return;
    }

    const newsHTML = newsData.map(news => createNewsItemHTML(news)).join('');
    newsGrid.innerHTML = newsHTML;

    // Dodaj obsługę kliknięć
    addNewsClickHandlers();
}

/**
 * Tworzenie HTML dla pojedynczej wiadomości
 */
function createNewsItemHTML(news) {
    const categoryName = getCategoryName(news.category);
    const categoryClass = `news-category-${news.category}`;
    const formattedDate = formatDate(news.publishDate);

    return `
        <article class="news-item" data-id="${news.id}" data-slug="${news.slug}">
            <div class="news-item-image ${news.image ? '' : 'no-image'}">
                ${news.image ?
                    `<img src="${news.image}" alt="${news.imageAlt || news.title}" loading="lazy">` :
                    '<i class="fas fa-newspaper"></i>'
                }
                <span class="news-category-badge ${categoryClass}">${categoryName}</span>
            </div>
            <div class="news-item-content">
                <h3 class="news-item-title">${news.title}</h3>
                <p class="news-item-excerpt">${news.excerpt}</p>
                <div class="news-item-meta">
                    <span class="news-item-date">
                        <i class="fas fa-calendar-alt"></i>
                        ${formattedDate}
                    </span>
                    <a href="#" class="news-item-read-more" data-id="${news.id}">
                        Czytaj więcej <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </article>
    `;
}

/**
 * Dodawanie obsługi kliknięć na wiadomości
 */
function addNewsClickHandlers() {
    const newsItems = document.querySelectorAll('.news-item');
    const readMoreLinks = document.querySelectorAll('.news-item-read-more');

    // Obsługa kliknięcia na całą wiadomość
    newsItems.forEach(item => {
        item.addEventListener('click', function(e) {
            if (e.target.closest('.news-item-read-more')) return;

            const newsId = this.dataset.id;
            const newsSlug = this.dataset.slug;
            openNewsDetail(newsId, newsSlug);
        });
    });

    // Obsługa kliknięcia na "Czytaj więcej"
    readMoreLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const newsId = this.dataset.id;
            const newsSlug = this.closest('.news-item').dataset.slug;
            openNewsDetail(newsId, newsSlug);
        });
    });
}

/**
 * Otwieranie szczegółów wiadomości
 */
function openNewsDetail(newsId, newsSlug) {
    // Przekierowanie do strony wiadomości z parametrem ID
    const url = `wiadomosci.html?id=${newsId}`;
    window.location.href = url;
}

/**
 * Wyświetlanie wiadomości w lightboxie
 */
function showNewsLightbox(newsId) {
    const newsData = getNewsData();
    const news = newsData.find(n => n.id == newsId);

    if (!news) {
        console.error('Nie znaleziono wiadomości o ID:', newsId);
        return;
    }

    // Utwórz lightbox jeśli nie istnieje
    let lightbox = document.getElementById('newsLightbox');
    if (!lightbox) {
        lightbox = createNewsLightbox();
        document.body.appendChild(lightbox);
    }

    // Wypełnij lightbox danymi
    fillNewsLightbox(lightbox, news);

    // Pokaż lightbox
    lightbox.classList.add('active');
    document.body.style.overflow = 'hidden';
}

/**
 * Tworzenie lightboxa dla wiadomości
 */
function createNewsLightbox() {
    const lightbox = document.createElement('div');
    lightbox.id = 'newsLightbox';
    lightbox.className = 'lightbox';
    lightbox.innerHTML = `
        <div class="lightbox-content">
            <button class="lightbox-close">&times;</button>
            <div class="lightbox-body" id="newsLightboxBody">
                <!-- Treść wiadomości -->
            </div>
        </div>
    `;

    // Dodaj obsługę zamykania
    lightbox.addEventListener('click', function(e) {
        if (e.target === lightbox || e.target.classList.contains('lightbox-close')) {
            closeLightbox();
        }
    });

    return lightbox;
}

/**
 * Wypełnianie lightboxa danymi wiadomości
 */
function fillNewsLightbox(lightbox, news) {
    const body = lightbox.querySelector('#newsLightboxBody');
    const categoryName = getCategoryName(news.category);
    const categoryClass = `news-category-${news.category}`;
    const formattedDate = formatDate(news.publishDate);

    body.innerHTML = `
        <div class="news-details">
            ${news.image ? `
                <div class="news-details-image">
                    <img src="${news.image}" alt="${news.imageAlt || news.title}">
                </div>
            ` : ''}
            <div class="news-details-header">
                <span class="news-category-badge ${categoryClass}">${categoryName}</span>
                <h2>${news.title}</h2>
                <div class="news-details-meta">
                    <span class="news-details-date">
                        <i class="fas fa-calendar-alt"></i>
                        ${formattedDate}
                    </span>
                </div>
            </div>
            <div class="news-details-content">
                ${news.content.replace(/\n/g, '</p><p>')}
            </div>
            ${news.tags ? `
                <div class="news-details-tags">
                    <strong>Tagi:</strong> ${news.tags}
                </div>
            ` : ''}
        </div>
    `;
}

/**
 * Zamykanie lightboxa
 */
function closeLightbox() {
    const lightbox = document.getElementById('newsLightbox');
    if (lightbox) {
        lightbox.classList.remove('active');
        document.body.style.overflow = '';
    }
}

/**
 * Pobieranie nazwy kategorii
 */
function getCategoryName(category) {
    const categories = {
        'local': 'Lokalne',
        'events': 'Wydarzenia',
        'business': 'Biznes',
        'culture': 'Kultura',
        'sport': 'Sport'
    };
    return categories[category] || category;
}

/**
 * Formatowanie daty
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('pl-PL', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Eksport funkcji dla dashboardu
window.NewsHomepage = {
    showNewsSection,
    hideNewsSection,
    loadAndDisplayNews,
    getNewsSettings
};
