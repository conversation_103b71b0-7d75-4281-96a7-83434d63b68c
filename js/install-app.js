/**
 * Obsługa instalacji aplikacji - Żyrardów Poleca
 * JavaScript dla obsługi instalacji aplikacji jako skrótu na ekranie startowym
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja przycisku instalacji
    initInstallButton();
    
    // Obsługa zdarzenia beforeinstallprompt
    initInstallPrompt();
});

// Zmienna do przechowywania zdarzenia beforeinstallprompt
let deferredPrompt;

/**
 * Inicjalizacja przycisku instalacji
 */
function initInstallButton() {
    const installButton = document.getElementById('installAppButton');
    if (!installButton) return;
    
    // Domyślnie ukryj przycisk
    installButton.style.display = 'none';
    
    installButton.addEventListener('click', function() {
        // Jeśli mamy zapisane zdarzenie beforeinstallprompt
        if (deferredPrompt) {
            // <PERSON><PERSON>ż prompt instalacji
            deferredPrompt.prompt();
            
            // Czekaj na decyzję użytkownika
            deferredPrompt.userChoice.then(function(choiceResult) {
                if (choiceResult.outcome === 'accepted') {
                    console.log('Użytkownik zaakceptował instalację');
                    // Ukryj przycisk po zaakceptowaniu
                    installButton.style.display = 'none';
                } else {
                    console.log('Użytkownik odrzucił instalację');
                }
                
                // Wyczyść zapisane zdarzenie
                deferredPrompt = null;
            });
        } else {
            // Jeśli nie mamy zapisanego zdarzenia, pokaż instrukcję manualnej instalacji
            showManualInstallInstructions();
        }
    });
}

/**
 * Inicjalizacja obsługi zdarzenia beforeinstallprompt
 */
function initInstallPrompt() {
    // Nasłuchuj zdarzenia beforeinstallprompt
    window.addEventListener('beforeinstallprompt', function(e) {
        // Zapobiegnij automatycznemu pokazaniu promptu
        e.preventDefault();
        
        // Zapisz zdarzenie
        deferredPrompt = e;
        
        // Pokaż przycisk instalacji
        const installButton = document.getElementById('installAppButton');
        if (installButton) {
            installButton.style.display = 'block';
        }
    });
    
    // Nasłuchuj zdarzenia appinstalled
    window.addEventListener('appinstalled', function(e) {
        console.log('Aplikacja została zainstalowana');
        
        // Ukryj przycisk instalacji
        const installButton = document.getElementById('installAppButton');
        if (installButton) {
            installButton.style.display = 'none';
        }
    });
}

/**
 * Pokazanie instrukcji manualnej instalacji
 */
function showManualInstallInstructions() {
    // Utwórz lightbox z instrukcjami
    const lightboxHTML = `
        <div id="installInstructionsLightbox" class="lightbox">
            <div class="lightbox-content">
                <button class="lightbox-close" aria-label="Zamknij">&times;</button>
                <div class="lightbox-header">
                    <h3>Jak zainstalować aplikację</h3>
                </div>
                <div class="lightbox-body">
                    <div class="install-instructions">
                        <div class="install-platform android">
                            <h4>Android</h4>
                            <ol>
                                <li>Otwórz stronę w przeglądarce Chrome</li>
                                <li>Dotknij ikony menu (trzy kropki) w prawym górnym rogu</li>
                                <li>Wybierz "Dodaj do ekranu głównego"</li>
                                <li>Potwierdź, dotykając "Dodaj"</li>
                            </ol>
                            <div class="install-image">
                                <img src="images/install-android.jpg" alt="Instrukcja instalacji na Androidzie">
                            </div>
                        </div>
                        <div class="install-platform ios">
                            <h4>iPhone (iOS)</h4>
                            <ol>
                                <li>Otwórz stronę w przeglądarce Safari</li>
                                <li>Dotknij ikony udostępniania (strzałka w górę) na dole ekranu</li>
                                <li>Przewiń w dół i wybierz "Dodaj do ekranu początkowego"</li>
                                <li>Potwierdź, dotykając "Dodaj" w prawym górnym rogu</li>
                            </ol>
                            <div class="install-image">
                                <img src="images/install-ios.jpg" alt="Instrukcja instalacji na iOS">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Dodaj lightbox do body
    document.body.insertAdjacentHTML('beforeend', lightboxHTML);
    
    // Pokaż lightbox
    const lightbox = document.getElementById('installInstructionsLightbox');
    lightbox.classList.add('active');
    
    // Obsługa zamykania lightboxa
    const closeButton = lightbox.querySelector('.lightbox-close');
    closeButton.addEventListener('click', function() {
        lightbox.classList.remove('active');
        setTimeout(() => {
            lightbox.remove();
        }, 300);
    });
    
    // Zamykanie lightboxa po kliknięciu w tło
    lightbox.addEventListener('click', function(e) {
        if (e.target === lightbox) {
            lightbox.classList.remove('active');
            setTimeout(() => {
                lightbox.remove();
            }, 300);
        }
    });
}
