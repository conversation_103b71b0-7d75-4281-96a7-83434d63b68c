/**
 * Obsługa slidera kuponów - Żyrardów Poleca
 * JavaScript dla obsługi slidera kuponów rabatowych
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja slidera kuponów
    initCouponsSlider();

    // Obsługa przycisków "Pokaż kod"
    initCouponCodeButtons();

    // Inicjalizacja lightbox dla szczegółów kuponów
    initCouponLightbox();
});

/**
 * Inicjalizacja slidera kuponów
 */
function initCouponsSlider() {
    // Sprawdź czy to strona główna z .coupons-grid
    const couponsGrid = document.querySelector('.coupons-grid');
    const featuredCouponsSection = document.querySelector('.featured-coupons');

    if (couponsGrid && featuredCouponsSection) {
        console.log('Strona główna: slider kuponów wyłączony, używamy .coupons-grid');
        return;
    }

    // Pobierz elementy slidera
    const sliders = document.querySelectorAll('.coupons-slider');

    sliders.forEach(slider => {
        const sliderId = slider.id;
        const prevButton = document.getElementById(sliderId.replace('Slider', 'Prev')) || document.querySelector(`[data-slider="${sliderId}"].prev`);
        const nextButton = document.getElementById(sliderId.replace('Slider', 'Next')) || document.querySelector(`[data-slider="${sliderId}"].next`);
        const dotsContainer = document.getElementById(sliderId.replace('Slider', 'Dots')) || document.querySelector(`[data-slider="${sliderId}"].dots`);

        if (!prevButton || !nextButton) return;

        // Pobierz karty kuponów
        const cards = slider.querySelectorAll('.coupon-card');
        if (cards.length === 0) return;

        // Ustaw szerokość karty
        const cardWidth = cards[0].offsetWidth + parseInt(window.getComputedStyle(cards[0]).marginRight);

        // Dodaj kropki nawigacyjne
        if (dotsContainer) {
            createDots(dotsContainer, cards.length, sliderId);
        }

        // Obsługa przycisku "Poprzedni"
        prevButton.addEventListener('click', () => {
            slider.scrollBy({
                left: -cardWidth * 4,
                behavior: 'smooth'
            });

            // Aktualizuj aktywną kropkę
            updateActiveDot(slider, dotsContainer, cardWidth);
        });

        // Obsługa przycisku "Następny"
        nextButton.addEventListener('click', () => {
            slider.scrollBy({
                left: cardWidth * 4,
                behavior: 'smooth'
            });

            // Aktualizuj aktywną kropkę
            updateActiveDot(slider, dotsContainer, cardWidth);
        });

        // Obsługa przewijania
        slider.addEventListener('scroll', () => {
            // Aktualizuj aktywną kropkę
            updateActiveDot(slider, dotsContainer, cardWidth);
        });
    });
}

/**
 * Tworzenie kropek nawigacyjnych
 */
function createDots(dotsContainer, count, sliderId) {
    if (!dotsContainer) return;

    // Wyczyść kontener kropek
    dotsContainer.innerHTML = '';

    // Dodaj kropki
    for (let i = 0; i < Math.ceil(count / 4); i++) {
        const dot = document.createElement('button');
        dot.classList.add('slider-dot');
        dot.setAttribute('aria-label', `Przejdź do slajdu ${i + 1}`);
        dot.setAttribute('data-index', i);
        dot.setAttribute('data-slider', sliderId);

        // Obsługa kliknięcia w kropkę
        dot.addEventListener('click', () => {
            const slider = document.getElementById(sliderId);
            if (!slider) return;

            const cards = slider.querySelectorAll('.coupon-card');
            if (cards.length === 0) return;

            const cardWidth = cards[0].offsetWidth + parseInt(window.getComputedStyle(cards[0]).marginRight);

            // Przewiń do wybranej karty
            slider.scrollTo({
                left: i * cardWidth * 4,
                behavior: 'smooth'
            });

            // Aktualizuj aktywną kropkę
            updateActiveDot(slider, dotsContainer, cardWidth);
        });

        // Dodaj kropkę do kontenera
        dotsContainer.appendChild(dot);
    }

    // Ustaw pierwszą kropkę jako aktywną
    if (dotsContainer.firstChild) {
        dotsContainer.firstChild.classList.add('active');
    }
}

/**
 * Aktualizacja aktywnej kropki
 */
function updateActiveDot(slider, dotsContainer, cardWidth) {
    if (!dotsContainer) return;

    // Pobierz aktualną pozycję przewijania
    const scrollPosition = slider.scrollLeft;

    // Oblicz indeks aktywnej kropki
    const activeIndex = Math.round(scrollPosition / (cardWidth * 4));

    // Pobierz wszystkie kropki
    const dots = dotsContainer.querySelectorAll('.slider-dot');

    // Usuń klasę aktywną ze wszystkich kropek
    dots.forEach(dot => dot.classList.remove('active'));

    // Dodaj klasę aktywną do aktywnej kropki
    if (dots[activeIndex]) {
        dots[activeIndex].classList.add('active');
    }
}

/**
 * Inicjalizacja przycisków "Pokaż kod"
 */
function initCouponCodeButtons() {
    // Pobierz wszystkie przyciski "Pokaż kod"
    const codeButtons = document.querySelectorAll('.coupon-code-btn');

    codeButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Pobierz kod kuponu
            const code = this.getAttribute('data-code');

            // Pobierz dane kuponu
            const couponCard = this.closest('.coupon-card');
            const couponTitle = couponCard.querySelector('h3').textContent;
            const couponDescription = couponCard.querySelector('.coupon-title').textContent;
            const couponValidity = couponCard.querySelector('.coupon-validity span').textContent;
            const couponDiscount = couponCard.querySelector('.coupon-discount').textContent;
            const couponLogo = couponCard.querySelector('.coupon-logo img').src;

            // Otwórz lightbox z danymi kuponu
            openCouponLightbox(couponTitle, couponDescription, code, couponValidity, couponDiscount, couponLogo);
        });
    });
}

/**
 * Inicjalizacja lightbox dla szczegółów kuponów
 */
function initCouponLightbox() {
    const lightbox = document.getElementById('couponLightbox');
    const lightboxClose = document.querySelector('.lightbox-close');

    if (lightbox && lightboxClose) {
        // Obsługa zamknięcia lightbox
        lightboxClose.addEventListener('click', function() {
            lightbox.classList.remove('active');
            document.body.style.overflow = '';
        });

        // Zamknięcie lightbox po kliknięciu poza zawartością
        lightbox.addEventListener('click', function(e) {
            if (e.target === lightbox) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        });

        // Obsługa klawisza Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && lightbox.classList.contains('active')) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }
}

/**
 * Otwiera lightbox z danymi kuponu
 */
function openCouponLightbox(title, description, code, validity, discount, logoSrc) {
    const lightbox = document.getElementById('couponLightbox');
    const lightboxBody = document.getElementById('couponDetails');

    if (lightbox && lightboxBody) {
        // Wygenerowanie zawartości lightbox
        lightboxBody.innerHTML = `
            <div class="coupon-details-header">
                <div class="coupon-details-logo">
                    <img src="${logoSrc}" alt="${title}">
                </div>
                <div class="coupon-details-title">
                    <h2>${title}</h2>
                    <p>Kupon rabatowy ${discount}</p>
                </div>
            </div>
            <div class="coupon-details-content">
                <p>${description}</p>
            </div>
            <div class="coupon-details-code">
                <div class="coupon-details-code-label">Kod rabatowy:</div>
                <div class="coupon-details-code-value">${code}</div>
                <button class="coupon-copy-btn" data-code="${code}">Kopiuj kod</button>
            </div>
            <div class="coupon-details-info">
                <div class="coupon-details-info-item">
                    <div class="coupon-details-info-label">Zniżka:</div>
                    <div>${discount}</div>
                </div>
                <div class="coupon-details-info-item">
                    <div class="coupon-details-info-label">Ważność:</div>
                    <div>${validity}</div>
                </div>
                <div class="coupon-details-info-item">
                    <div class="coupon-details-info-label">Warunki:</div>
                    <div>Kupon ważny w lokalu. Nie łączy się z innymi promocjami.</div>
                </div>
            </div>
        `;

        // Dodaj obsługę przycisku kopiowania kodu
        const copyButton = lightboxBody.querySelector('.coupon-copy-btn');
        if (copyButton) {
            copyButton.addEventListener('click', function() {
                const codeToCopy = this.getAttribute('data-code');

                // Kopiuj kod do schowka
                navigator.clipboard.writeText(codeToCopy).then(() => {
                    // Zmień tekst przycisku
                    this.textContent = 'Skopiowano!';
                    this.disabled = true;

                    // Przywróć oryginalny tekst przycisku po 2 sekundach
                    setTimeout(() => {
                        this.textContent = 'Kopiuj kod';
                        this.disabled = false;
                    }, 2000);
                }).catch(err => {
                    console.error('Nie udało się skopiować kodu: ', err);
                });
            });
        }

        // Otwarcie lightbox
        lightbox.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}
