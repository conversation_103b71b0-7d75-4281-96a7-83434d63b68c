/**
 * Wyszukiwarka - Żyrardów Poleca
 * JavaScript dla wyszukiwarki firm
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja wyszukiwarki
    initSearch();

    // Inicjalizacja filtrów wyników
    initResultsFilters();

    // Inicjalizacja wyboru kategorii
    initCategorySelection();
});

/**
 * Inicjalizacja wyszukiwarki
 */
function initSearch() {
    const searchForm = document.getElementById('mainSearchForm');
    const searchInput = document.getElementById('mainSearchInput');
    const catalogSearchForm = document.getElementById('catalogSearchForm');
    const catalogSearchInput = document.getElementById('catalogSearchInput');

    // Główny formularz wyszukiwania
    if (searchForm && searchInput) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const searchQuery = searchInput.value.trim();

            if (searchQuery.length > 0) {
                // Wykonanie wyszukiwania
                performSearch(searchQuery);
            }
        });

        // Automatyczne wyszukiwanie podczas pisania
        searchInput.addEventListener('input', function() {
            const searchQuery = this.value.trim();

            if (searchQuery.length >= 3) {
                // Opóźnienie wyszukiwania o 300ms
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    performSearch(searchQuery);
                }, 300);
            }
        });
    }

    // Formularz wyszukiwania w katalogu
    if (catalogSearchForm && catalogSearchInput) {
        catalogSearchForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const searchQuery = catalogSearchInput.value.trim();

            if (searchQuery.length > 0) {
                // Wykonanie wyszukiwania
                performSearch(searchQuery);
            }
        });

        // Automatyczne wyszukiwanie podczas pisania
        catalogSearchInput.addEventListener('input', function() {
            const searchQuery = this.value.trim();

            if (searchQuery.length >= 3) {
                // Opóźnienie wyszukiwania o 300ms
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    performSearch(searchQuery);
                }, 300);
            }
        });
    }
}

/**
 * Inicjalizacja filtrów wyników
 */
function initResultsFilters() {
    const sortSelect = document.getElementById('sortResults');

    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            // Sortowanie wyników
            sortSearchResults(this.value);
        });
    }
}

/**
 * Inicjalizacja wyboru kategorii
 */
function initCategorySelection() {
    const categoryLinks = document.querySelectorAll('.subcategories-list a');

    if (categoryLinks.length) {
        categoryLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                const category = this.getAttribute('data-category');
                const subcategory = this.getAttribute('data-subcategory');

                // Wyszukiwanie firm w wybranej kategorii
                searchByCategory(category, subcategory);
            });
        });
    }
}

/**
 * Wykonanie wyszukiwania
 * @param {string} query - Zapytanie wyszukiwania
 */
function performSearch(query) {
    // Wyświetlenie sekcji wyników
    const searchResultsSection = document.getElementById('searchResults');
    const searchQuerySpan = document.getElementById('searchQuery');

    if (searchResultsSection && searchQuerySpan) {
        searchResultsSection.style.display = 'block';
        searchQuerySpan.textContent = query;

        // Przewinięcie do wyników
        searchResultsSection.scrollIntoView({ behavior: 'smooth' });

        // Wyszukiwanie firm na podstawie zapytania
        const results = searchCompanies(query);

        // Wyświetlenie wyników
        displaySearchResults(results);
    }
}

/**
 * Wyszukiwanie firm w wybranej kategorii
 * @param {string} category - Kategoria
 * @param {string} subcategory - Podkategoria
 */
function searchByCategory(category, subcategory) {
    // Wyświetlenie sekcji wyników
    const searchResultsSection = document.getElementById('searchResults');
    const searchResultsSubtitle = document.getElementById('searchResultsSubtitle');

    if (searchResultsSection && searchResultsSubtitle) {
        searchResultsSection.style.display = 'block';

        // Ustawienie tytułu wyników
        const categoryName = getCategoryName(category);
        const subcategoryName = getSubcategoryName(subcategory);
        searchResultsSubtitle.textContent = `Firmy w kategorii: ${categoryName} > ${subcategoryName}`;

        // Przewinięcie do wyników
        searchResultsSection.scrollIntoView({ behavior: 'smooth' });

        // Wyszukiwanie firm w wybranej kategorii
        const results = searchCompaniesByCategory(category, subcategory);

        // Wyświetlenie wyników
        displaySearchResults(results);
    }
}

/**
 * Wyszukiwanie firm na podstawie zapytania
 * @param {string} query - Zapytanie wyszukiwania
 * @returns {Array} - Wyniki wyszukiwania
 */
function searchCompanies(query) {
    // Symulacja wyszukiwania firm na podstawie zapytania
    // W rzeczywistej implementacji byłoby to zapytanie do API lub bazy danych

    // Przykładowe dane firm
    const companies = getCompaniesData();

    // Konwersja zapytania na małe litery
    const queryLower = query.toLowerCase();

    // Mapowanie zapytań do kategorii z uwzględnieniem odmian
    const keywordsToCategoriesMap = {
        // Gastronomia
        'pizza': { category: 'gastronomy', subcategory: 'pizzerias' },
        'pizzy': { category: 'gastronomy', subcategory: 'pizzerias' },
        'pizze': { category: 'gastronomy', subcategory: 'pizzerias' },
        'pizzeria': { category: 'gastronomy', subcategory: 'pizzerias' },
        'pizzerii': { category: 'gastronomy', subcategory: 'pizzerias' },
        'pizzerie': { category: 'gastronomy', subcategory: 'pizzerias' },
        'restauracja': { category: 'gastronomy', subcategory: 'restaurants' },
        'restauracji': { category: 'gastronomy', subcategory: 'restaurants' },
        'restauracje': { category: 'gastronomy', subcategory: 'restaurants' },
        'restau': { category: 'gastronomy', subcategory: 'restaurants' },
        'resturacja': { category: 'gastronomy', subcategory: 'restaurants' },
        'resturacje': { category: 'gastronomy', subcategory: 'restaurants' },
        'kawiarnia': { category: 'gastronomy', subcategory: 'cafes' },
        'kawiarni': { category: 'gastronomy', subcategory: 'cafes' },
        'kawiarnie': { category: 'gastronomy', subcategory: 'cafes' },
        'kawa': { category: 'gastronomy', subcategory: 'cafes' },
        'cukiernia': { category: 'gastronomy', subcategory: 'bakeries' },
        'cukierni': { category: 'gastronomy', subcategory: 'bakeries' },
        'cukiernie': { category: 'gastronomy', subcategory: 'bakeries' },
        'piekarnia': { category: 'gastronomy', subcategory: 'bakeries' },
        'piekarni': { category: 'gastronomy', subcategory: 'bakeries' },
        'piekarnie': { category: 'gastronomy', subcategory: 'bakeries' },
        'fast food': { category: 'gastronomy', subcategory: 'fastfood' },
        'fastfood': { category: 'gastronomy', subcategory: 'fastfood' },
        'kebab': { category: 'gastronomy', subcategory: 'fastfood' },
        'burger': { category: 'gastronomy', subcategory: 'fastfood' },

        // Uroda i zdrowie
        'fryzjer': { category: 'beauty', subcategory: 'hairdressers' },
        'fryzjera': { category: 'beauty', subcategory: 'hairdressers' },
        'fryzjerzy': { category: 'beauty', subcategory: 'hairdressers' },
        'salon fryzjerski': { category: 'beauty', subcategory: 'hairdressers' },
        'kosmetyczka': { category: 'beauty', subcategory: 'cosmetics' },
        'kosmetyczki': { category: 'beauty', subcategory: 'cosmetics' },
        'salon kosmetyczny': { category: 'beauty', subcategory: 'cosmetics' },
        'salon urody': { category: 'beauty', subcategory: 'cosmetics' },
        'spa': { category: 'beauty', subcategory: 'spa' },
        'masaż': { category: 'beauty', subcategory: 'spa' },
        'masażu': { category: 'beauty', subcategory: 'spa' },
        'masażysta': { category: 'beauty', subcategory: 'spa' },
        'siłownia': { category: 'beauty', subcategory: 'fitness' },
        'siłowni': { category: 'beauty', subcategory: 'fitness' },
        'siłownie': { category: 'beauty', subcategory: 'fitness' },
        'fitness': { category: 'beauty', subcategory: 'fitness' },
        'trener': { category: 'beauty', subcategory: 'fitness' },
        'lekarz': { category: 'beauty', subcategory: 'medical' },
        'lekarza': { category: 'beauty', subcategory: 'medical' },
        'przychodnia': { category: 'beauty', subcategory: 'medical' },
        'przychodni': { category: 'beauty', subcategory: 'medical' },
        'gabinet': { category: 'beauty', subcategory: 'medical' },
        'dentysta': { category: 'beauty', subcategory: 'medical' },
        'stomatolog': { category: 'beauty', subcategory: 'medical' },

        // Motoryzacja
        'mechanik': { category: 'automotive', subcategory: 'mechanics' },
        'mechanika': { category: 'automotive', subcategory: 'mechanics' },
        'warsztat': { category: 'automotive', subcategory: 'mechanics' },
        'warsztat samochodowy': { category: 'automotive', subcategory: 'mechanics' },
        'auto': { category: 'automotive', subcategory: 'mechanics' },
        'samochód': { category: 'automotive', subcategory: 'mechanics' },
        'samochodu': { category: 'automotive', subcategory: 'mechanics' },
        'naprawa': { category: 'automotive', subcategory: 'mechanics' },
        'naprawa samochodu': { category: 'automotive', subcategory: 'mechanics' },
        'myjnia': { category: 'automotive', subcategory: 'carwash' },
        'myjni': { category: 'automotive', subcategory: 'carwash' },
        'myjnie': { category: 'automotive', subcategory: 'carwash' },
        'części': { category: 'automotive', subcategory: 'parts' },
        'części samochodowe': { category: 'automotive', subcategory: 'parts' },
        'akcesoria': { category: 'automotive', subcategory: 'parts' },
        'salon samochodowy': { category: 'automotive', subcategory: 'dealers' },
        'komis': { category: 'automotive', subcategory: 'dealers' },
        'szkoła jazdy': { category: 'automotive', subcategory: 'driving' },
        'nauka jazdy': { category: 'automotive', subcategory: 'driving' },

        // Usługi
        'prawnik': { category: 'services', subcategory: 'legal' },
        'prawnika': { category: 'services', subcategory: 'legal' },
        'adwokat': { category: 'services', subcategory: 'legal' },
        'adwokata': { category: 'services', subcategory: 'legal' },
        'radca prawny': { category: 'services', subcategory: 'legal' },
        'notariusz': { category: 'services', subcategory: 'legal' },
        'księgowy': { category: 'services', subcategory: 'legal' },
        'księgowa': { category: 'services', subcategory: 'legal' },
        'biuro rachunkowe': { category: 'services', subcategory: 'legal' },
        'sprzątanie': { category: 'services', subcategory: 'cleaning' },
        'sprzątania': { category: 'services', subcategory: 'cleaning' },
        'firma sprzątająca': { category: 'services', subcategory: 'cleaning' },
        'naprawa': { category: 'services', subcategory: 'repairs' },
        'naprawy': { category: 'services', subcategory: 'repairs' },
        'remont': { category: 'services', subcategory: 'repairs' },
        'remonty': { category: 'services', subcategory: 'repairs' },
        'hydraulik': { category: 'services', subcategory: 'repairs' },
        'elektryk': { category: 'services', subcategory: 'repairs' },
        'informatyk': { category: 'services', subcategory: 'it' },
        'komputery': { category: 'services', subcategory: 'it' },
        'naprawa komputera': { category: 'services', subcategory: 'it' },
        'serwis komputerowy': { category: 'services', subcategory: 'it' },

        // Edukacja
        'szkoła': { category: 'education', subcategory: 'schools' },
        'szkoły': { category: 'education', subcategory: 'schools' },
        'szkole': { category: 'education', subcategory: 'schools' },
        'szkolny': { category: 'education', subcategory: 'schools' },
        'przedszkole': { category: 'education', subcategory: 'schools' },
        'przedszkola': { category: 'education', subcategory: 'schools' },
        'przedszkolny': { category: 'education', subcategory: 'schools' },
        'żłobek': { category: 'education', subcategory: 'schools' },
        'żłobka': { category: 'education', subcategory: 'schools' },
        'szkoła językowa': { category: 'education', subcategory: 'languages' },
        'języki': { category: 'education', subcategory: 'languages' },
        'angielski': { category: 'education', subcategory: 'languages' },
        'niemiecki': { category: 'education', subcategory: 'languages' },
        'korepetycje': { category: 'education', subcategory: 'tutoring' },
        'korepetytor': { category: 'education', subcategory: 'tutoring' },
        'nauka': { category: 'education', subcategory: 'tutoring' },
        'kurs': { category: 'education', subcategory: 'courses' },
        'kursy': { category: 'education', subcategory: 'courses' },
        'szkolenie': { category: 'education', subcategory: 'courses' },
        'szkolenia': { category: 'education', subcategory: 'courses' },
        'szkoła muzyczna': { category: 'education', subcategory: 'arts' },
        'szkoła tańca': { category: 'education', subcategory: 'arts' },
        'zajęcia artystyczne': { category: 'education', subcategory: 'arts' },

        // Noclegi i turystyka
        'hotel': { category: 'tourism', subcategory: 'hotels' },
        'hotele': { category: 'tourism', subcategory: 'hotels' },
        'hotelu': { category: 'tourism', subcategory: 'hotels' },
        'pensjonat': { category: 'tourism', subcategory: 'hotels' },
        'nocleg': { category: 'tourism', subcategory: 'hotels' },
        'noclegi': { category: 'tourism', subcategory: 'hotels' },
        'apartament': { category: 'tourism', subcategory: 'apartments' },
        'apartamenty': { category: 'tourism', subcategory: 'apartments' },
        'atrakcja': { category: 'tourism', subcategory: 'attractions' },
        'atrakcje': { category: 'tourism', subcategory: 'attractions' },
        'zwiedzanie': { category: 'tourism', subcategory: 'attractions' },
        'biuro podróży': { category: 'tourism', subcategory: 'travel' },
        'wycieczka': { category: 'tourism', subcategory: 'travel' },
        'wycieczki': { category: 'tourism', subcategory: 'travel' },
        'przewodnik': { category: 'tourism', subcategory: 'guides' },
        'przewodnika': { category: 'tourism', subcategory: 'guides' }
    };

    // Sprawdzenie, czy zapytanie pasuje do jakiejś kategorii
    let matchedCategory = null;
    let matchedSubcategory = null;

    // Rozbijamy zapytanie na słowa
    const queryWords = queryLower.split(/\s+/);

    // Sprawdzamy każde słowo w zapytaniu
    for (const word of queryWords) {
        // Sprawdzamy, czy słowo pasuje do jakiegoś klucza w mapie
        for (const keyword in keywordsToCategoriesMap) {
            // Dokładne dopasowanie
            if (word === keyword) {
                matchedCategory = keywordsToCategoriesMap[keyword].category;
                matchedSubcategory = keywordsToCategoriesMap[keyword].subcategory;
                break;
            }
            // Sprawdzamy czy słowo zaczyna się od słowa kluczowego (min 4 znaki)
            if (word.length >= 4 && word.startsWith(keyword) && keyword.length >= 4) {
                matchedCategory = keywordsToCategoriesMap[keyword].category;
                matchedSubcategory = keywordsToCategoriesMap[keyword].subcategory;
                break;
            }
            // Sprawdzamy czy słowo kluczowe zaczyna się od wpisanego słowa (min 4 znaki)
            if (keyword.length >= 4 && keyword.startsWith(word) && word.length >= 4) {
                matchedCategory = keywordsToCategoriesMap[keyword].category;
                matchedSubcategory = keywordsToCategoriesMap[keyword].subcategory;
                break;
            }
        }

        // Jeśli znaleźliśmy dopasowanie, przerywamy pętlę
        if (matchedCategory && matchedSubcategory) {
            break;
        }
    }

    // Jeśli nie znaleźliśmy dopasowania, sprawdzamy całe zapytanie
    if (!matchedCategory || !matchedSubcategory) {
        for (const keyword in keywordsToCategoriesMap) {
            if (queryLower.includes(keyword)) {
                matchedCategory = keywordsToCategoriesMap[keyword].category;
                matchedSubcategory = keywordsToCategoriesMap[keyword].subcategory;
                break;
            }
        }
    }

    // Filtrowanie firm na podstawie zapytania
    let results = [];

    if (matchedCategory && matchedSubcategory) {
        // Jeśli zapytanie pasuje do kategorii, filtrujemy firmy z tej kategorii
        results = companies.filter(company =>
            company.category === matchedCategory &&
            company.subcategory === matchedSubcategory
        );
    } else {
        // W przeciwnym razie szukamy firm, których nazwa, opis lub tagi zawierają zapytanie
        results = companies.filter(company => {
            // Sprawdzamy, czy nazwa firmy zawiera zapytanie
            const nameMatch = company.name.toLowerCase().includes(queryLower);

            // Sprawdzamy, czy opis firmy zawiera zapytanie
            const descriptionMatch = company.description.toLowerCase().includes(queryLower);

            // Sprawdzamy, czy któryś z tagów firmy zawiera zapytanie
            const tagMatch = company.tags.some(tag => {
                const tagLower = tag.toLowerCase();
                // Sprawdzamy dokładne dopasowanie
                if (tagLower === queryLower) return true;
                // Sprawdzamy, czy tag zawiera zapytanie
                if (tagLower.includes(queryLower)) return true;
                // Sprawdzamy, czy zapytanie zawiera tag
                if (queryLower.includes(tagLower)) return true;
                // Sprawdzamy każde słowo w zapytaniu
                for (const word of queryWords) {
                    if (tagLower === word || tagLower.includes(word) || word.includes(tagLower)) {
                        return true;
                    }
                }
                return false;
            });

            return nameMatch || descriptionMatch || tagMatch;
        });
    }

    return results;
}

/**
 * Wyszukiwanie firm w wybranej kategorii
 * @param {string} category - Kategoria
 * @param {string} subcategory - Podkategoria
 * @returns {Array} - Wyniki wyszukiwania
 */
function searchCompaniesByCategory(category, subcategory) {
    // Symulacja wyszukiwania firm w wybranej kategorii
    // W rzeczywistej implementacji byłoby to zapytanie do API lub bazy danych

    // Przykładowe dane firm
    const companies = getCompaniesData();

    // Filtrowanie firm na podstawie kategorii i podkategorii
    const results = companies.filter(company =>
        company.category === category &&
        company.subcategory === subcategory
    );

    return results;
}

/**
 * Wyświetlenie wyników wyszukiwania
 * @param {Array} results - Wyniki wyszukiwania
 */
function displaySearchResults(results) {
    const searchResultsGrid = document.getElementById('searchResultsGrid');
    const noResults = document.getElementById('noResults');

    if (searchResultsGrid && noResults) {
        // Wyczyszczenie poprzednich wyników
        searchResultsGrid.innerHTML = '';

        if (results.length > 0) {
            // Wyświetlenie wyników
            results.forEach(company => {
                const companyCard = createCompanyCard(company);
                searchResultsGrid.appendChild(companyCard);
            });

            // Ukrycie komunikatu o braku wyników
            noResults.style.display = 'none';

            // Pokazanie wyników
            searchResultsGrid.style.display = 'grid';
        } else {
            // Ukrycie wyników
            searchResultsGrid.style.display = 'none';

            // Pokazanie komunikatu o braku wyników
            noResults.style.display = 'block';
        }
    }
}

/**
 * Sortowanie wyników wyszukiwania
 * @param {string} sortBy - Kryterium sortowania
 */
function sortSearchResults(sortBy) {
    const searchResultsGrid = document.getElementById('searchResultsGrid');

    if (searchResultsGrid) {
        // Pobranie wszystkich kart firm
        const companyCards = Array.from(searchResultsGrid.querySelectorAll('.company-card'));

        // Sortowanie kart firm
        companyCards.sort((a, b) => {
            if (sortBy === 'name') {
                // Sortowanie według nazwy
                const nameA = a.querySelector('h3').textContent.toLowerCase();
                const nameB = b.querySelector('h3').textContent.toLowerCase();
                return nameA.localeCompare(nameB);
            } else if (sortBy === 'rating') {
                // Sortowanie według oceny (symulacja)
                const ratingA = parseInt(a.getAttribute('data-rating') || '0');
                const ratingB = parseInt(b.getAttribute('data-rating') || '0');
                return ratingB - ratingA;
            }

            return 0;
        });

        // Wyczyszczenie kontenera
        searchResultsGrid.innerHTML = '';

        // Dodanie posortowanych kart firm
        companyCards.forEach(card => {
            searchResultsGrid.appendChild(card);
        });
    }
}

/**
 * Utworzenie karty firmy
 * @param {Object} company - Dane firmy
 * @returns {HTMLElement} - Element karty firmy
 */
function createCompanyCard(company) {
    const companyCard = document.createElement('div');
    companyCard.className = 'company-card';
    companyCard.setAttribute('data-rating', company.rating || '0');

    companyCard.innerHTML = `
        <div class="company-header">
            <img src="${company.coverImage}" alt="${company.name}" class="company-cover">
            <div class="company-logo">
                <img src="${company.logoImage}" alt="Logo ${company.name}">
            </div>
        </div>
        <div class="company-content">
            <div class="company-title">
                <h3>${company.name}</h3>
            </div>
            <div class="company-tags">
                ${company.tags.map(tag => `<span class="company-tag">${tag}</span>`).join('')}
            </div>
            <div class="company-description">
                ${company.description}
            </div>
            <div class="company-contact">
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>${company.address}</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <a href="tel:${company.phone}">${company.phone}</a>
                </div>
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <a href="mailto:${company.email}">${company.email}</a>
                </div>
            </div>
            <div class="company-footer">
                <div class="company-social">
                    ${company.facebook ? `<a href="${company.facebook}" class="social-link" target="_blank" rel="noopener noreferrer"><i class="fab fa-facebook-f"></i></a>` : ''}
                    ${company.instagram ? `<a href="${company.instagram}" class="social-link" target="_blank" rel="noopener noreferrer"><i class="fab fa-instagram"></i></a>` : ''}
                </div>
                ${company.website ? `<a href="${company.website}" class="company-website" target="_blank" rel="dofollow">Odwiedź stronę <i class="fas fa-external-link-alt"></i></a>` : ''}
            </div>
        </div>
    `;

    return companyCard;
}

/**
 * Pobranie nazwy kategorii
 * @param {string} category - Identyfikator kategorii
 * @returns {string} - Nazwa kategorii
 */
function getCategoryName(category) {
    const categories = {
        'gastronomy': 'Gastronomia',
        'beauty': 'Uroda i zdrowie',
        'services': 'Usługi',
        'shopping': 'Zakupy',
        'automotive': 'Motoryzacja',
        'realestate': 'Nieruchomości',
        'education': 'Edukacja',
        'tourism': 'Noclegi i turystyka'
    };

    return categories[category] || category;
}

/**
 * Pobranie nazwy podkategorii
 * @param {string} subcategory - Identyfikator podkategorii
 * @returns {string} - Nazwa podkategorii
 */
function getSubcategoryName(subcategory) {
    const subcategories = {
        // Gastronomia
        'restaurants': 'Restauracje',
        'pizzerias': 'Pizzerie',
        'cafes': 'Kawiarnie',
        'fastfood': 'Fast food',
        'bakeries': 'Piekarnie i cukiernie',

        // Uroda i zdrowie
        'hairdressers': 'Fryzjerzy',
        'cosmetics': 'Salony kosmetyczne',
        'spa': 'SPA i masaż',
        'fitness': 'Fitness i siłownie',
        'medical': 'Przychodnie i gabinety',

        // Usługi
        'legal': 'Prawne i finansowe',
        'cleaning': 'Sprzątanie',
        'repairs': 'Naprawy i remonty',
        'education': 'Edukacja i kursy',
        'it': 'IT i komputery',

        // Zakupy
        'clothing': 'Odzież i obuwie',
        'electronics': 'Elektronika',
        'home': 'Dom i ogród',
        'groceries': 'Spożywcze',
        'gifts': 'Prezenty i upominki',

        // Motoryzacja
        'mechanics': 'Mechanicy',
        'carwash': 'Myjnie',
        'parts': 'Części i akcesoria',
        'dealers': 'Salony i komisy',
        'driving': 'Szkoły jazdy',

        // Nieruchomości
        'agencies': 'Biura nieruchomości',
        'developers': 'Deweloperzy',
        'architects': 'Architekci i projektanci',
        'construction': 'Firmy budowlane',
        'interior': 'Wyposażenie wnętrz',

        // Edukacja
        'schools': 'Szkoły i przedszkola',
        'languages': 'Szkoły językowe',
        'tutoring': 'Korepetycje',
        'courses': 'Kursy i szkolenia',
        'arts': 'Szkoły artystyczne',

        // Noclegi i turystyka
        'hotels': 'Hotele i pensjonaty',
        'apartments': 'Apartamenty',
        'attractions': 'Atrakcje turystyczne',
        'travel': 'Biura podróży',
        'guides': 'Przewodnicy'
    };

    return subcategories[subcategory] || subcategory;
}

/**
 * Pobranie przykładowych danych firm
 * @returns {Array} - Przykładowe dane firm
 */
function getCompaniesData() {
    // Przykładowe dane firm
    // W rzeczywistej implementacji byłyby to dane z API lub bazy danych
    return [
        {
            id: 1,
            name: 'Restauracja Stary Młyn',
            category: 'gastronomy',
            subcategory: 'restaurants',
            tags: ['Gastronomia', 'Restauracja', 'Kuchnia polska', 'Jedzenie', 'Obiad', 'Kolacja', 'Imprezy okolicznościowe', 'Catering'],
            description: 'Restauracja w zabytkowym budynku dawnego młyna. Tradycyjna polska kuchnia z nowoczesnymi akcentami. Organizacja imprez okolicznościowych.',
            address: 'ul. Młyńska 8, 96-300 Żyrardów',
            phone: '+**************',
            email: '<EMAIL>',
            website: 'https://starymlyn.pl',
            facebook: 'https://facebook.com/zyrardow.poleca.to',
            instagram: 'https://instagram.com/zyrardow.poleca.to',
            logoImage: 'images/business-logo1.jpg',
            coverImage: 'images/companies/company1.jpg',
            rating: 5
        },
        {
            id: 2,
            name: 'Pizzeria Napoli',
            category: 'gastronomy',
            subcategory: 'pizzerias',
            tags: ['Gastronomia', 'Pizzeria', 'Kuchnia włoska', 'Pizza', 'Makarony', 'Jedzenie', 'Dostawa', 'Fast food'],
            description: 'Autentyczna włoska pizzeria z tradycyjnym piecem opalanym drewnem. Szeroki wybór pizz, makaronów i innych dań kuchni włoskiej.',
            address: 'ul. 1 Maja 20, 96-300 Żyrardów',
            phone: '+**************',
            email: '<EMAIL>',
            website: 'https://pizzeria-napoli.pl',
            facebook: 'https://facebook.com/zyrardow.poleca.to',
            instagram: 'https://instagram.com/zyrardow.poleca.to',
            logoImage: 'images/business-logo2.jpg',
            coverImage: 'images/companies/company2.jpg',
            rating: 4
        },
        {
            id: 3,
            name: 'Salon Fryzjerski Bella',
            category: 'beauty',
            subcategory: 'hairdressers',
            tags: ['Uroda', 'Fryzjer', 'Salon', 'Strzyżenie', 'Koloryzacja', 'Stylizacja', 'Pielęgnacja włosów', 'Fryzury'],
            description: 'Profesjonalny salon fryzjerski oferujący pełen zakres usług dla kobiet, mężczyzn i dzieci. Strzyżenie, koloryzacja, stylizacja i pielęgnacja włosów.',
            address: 'ul. Okrzei 15, 96-300 Żyrardów',
            phone: '+**************',
            email: '<EMAIL>',
            website: 'https://bella-fryzjer.pl',
            facebook: 'https://facebook.com/zyrardow.poleca.to',
            instagram: 'https://instagram.com/zyrardow.poleca.to',
            logoImage: 'images/business-logo3.jpg',
            coverImage: 'images/companies/company3.jpg',
            rating: 4
        },
        {
            id: 4,
            name: 'Szkoła Podstawowa nr 1 im. Filipa de Girarda',
            category: 'education',
            subcategory: 'schools',
            tags: ['Edukacja', 'Szkoła', 'Szkoła podstawowa', 'Nauka', 'Dzieci', 'Młodzież', 'Zajęcia dodatkowe', 'Edukacja podstawowa'],
            description: 'Publiczna szkoła podstawowa oferująca edukację dla dzieci w klasach 1-8. Bogata oferta zajęć dodatkowych, nowoczesne wyposażenie i wykwalifikowana kadra pedagogiczna.',
            address: 'ul. Szkolna 10, 96-300 Żyrardów',
            phone: '+**************',
            email: '<EMAIL>',
            website: 'https://sp1zyrardow.pl',
            facebook: 'https://facebook.com/zyrardow.poleca.to',
            instagram: 'https://instagram.com/zyrardow.poleca.to',
            logoImage: 'images/business-logo1.jpg',
            coverImage: 'images/companies/company1.jpg',
            rating: 5
        },
        {
            id: 5,
            name: 'Przedszkole Miejskie nr 2',
            category: 'education',
            subcategory: 'schools',
            tags: ['Edukacja', 'Przedszkole', 'Dzieci', 'Wychowanie przedszkolne', 'Opieka nad dziećmi', 'Edukacja wczesnoszkolna'],
            description: 'Publiczne przedszkole oferujące opiekę i edukację dla dzieci w wieku 3-6 lat. Przyjazna atmosfera, wykwalifikowana kadra i bogaty program zajęć.',
            address: 'ul. Przedszkolna 5, 96-300 Żyrardów',
            phone: '+**************',
            email: '<EMAIL>',
            website: 'https://pm2zyrardow.pl',
            facebook: 'https://facebook.com/zyrardow.poleca.to',
            instagram: 'https://instagram.com/zyrardow.poleca.to',
            logoImage: 'images/business-logo2.jpg',
            coverImage: 'images/companies/company2.jpg',
            rating: 5
        },
        {
            id: 6,
            name: 'Szkoła Językowa Lingua',
            category: 'education',
            subcategory: 'languages',
            tags: ['Edukacja', 'Języki obce', 'Kursy językowe', 'Angielski', 'Niemiecki', 'Francuski', 'Hiszpański', 'Szkoła językowa', 'Nauka języków'],
            description: 'Szkoła językowa oferująca kursy języków obcych dla dzieci, młodzieży i dorosłych. Angielski, niemiecki, francuski, hiszpański i inne. Małe grupy i doświadczeni lektorzy.',
            address: 'ul. 1 Maja 30, 96-300 Żyrardów',
            phone: '+**************',
            email: '<EMAIL>',
            website: 'https://lingua-zyrardow.pl',
            facebook: 'https://facebook.com/zyrardow.poleca.to',
            instagram: 'https://instagram.com/zyrardow.poleca.to',
            logoImage: 'images/business-logo3.jpg',
            coverImage: 'images/companies/company3.jpg',
            rating: 4
        },
        {
            id: 7,
            name: 'Centrum Medyczne Zdrowie',
            category: 'beauty',
            subcategory: 'medical',
            tags: ['Zdrowie', 'Medycyna', 'Przychodnia', 'Lekarze', 'Specjaliści', 'Badania', 'Konsultacje', 'Gabinet lekarski'],
            description: 'Nowoczesne centrum medyczne oferujące kompleksową opiekę zdrowotną. Konsultacje specjalistyczne, badania diagnostyczne i zabiegi medyczne.',
            address: 'ul. 1 Maja 45, 96-300 Żyrardów',
            phone: '+**************',
            email: '<EMAIL>',
            website: 'https://cm-zdrowie.pl',
            facebook: 'https://facebook.com/zyrardow.poleca.to',
            instagram: 'https://instagram.com/zyrardow.poleca.to',
            logoImage: 'images/business-logo1.jpg',
            coverImage: 'images/companies/company1.jpg',
            rating: 5
        },
        {
            id: 8,
            name: 'Auto Serwis Mechanik',
            category: 'automotive',
            subcategory: 'mechanics',
            tags: ['Motoryzacja', 'Warsztat', 'Mechanik', 'Naprawa samochodów', 'Serwis', 'Auto', 'Samochód', 'Przegląd'],
            description: 'Profesjonalny warsztat samochodowy oferujący kompleksowe usługi naprawy i serwisu pojazdów wszystkich marek. Diagnostyka, naprawy mechaniczne, elektryczne i blacharskie.',
            address: 'ul. Warsztatowa 12, 96-300 Żyrardów',
            phone: '+**************',
            email: '<EMAIL>',
            website: 'https://auto-mechanik.pl',
            facebook: 'https://facebook.com/zyrardow.poleca.to',
            instagram: 'https://instagram.com/zyrardow.poleca.to',
            logoImage: 'images/business-logo2.jpg',
            coverImage: 'images/companies/company2.jpg',
            rating: 4
        }
    ];
}
