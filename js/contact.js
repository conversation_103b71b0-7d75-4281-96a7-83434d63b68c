/**
 * Kontakt - Żyrardów Poleca
 * JavaScript dla strony kontaktowej
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja formularza kontaktowego
    initContactForm();
    
    // Inicjalizacja FAQ
    initFaq();
});

/**
 * Inicjalizacja formularza kontaktowego
 */
function initContactForm() {
    const contactForm = document.getElementById('contactForm');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Walidacja formularza
            if (validateForm(contactForm)) {
                // Symulacja wysłania formularza
                showFormSuccess();
            }
        });
    }
}

/**
 * Walidacja formularza
 * @param {HTMLFormElement} form - Formularz do walidacji
 * @returns {boolean} - Czy formularz jest poprawny
 */
function validateForm(form) {
    let isValid = true;
    
    // Pobranie pól formularza
    const nameInput = form.querySelector('#name');
    const emailInput = form.querySelector('#email');
    const subjectInput = form.querySelector('#subject');
    const messageInput = form.querySelector('#message');
    const privacyInput = form.querySelector('#privacy');
    
    // Usunięcie poprzednich komunikatów o błędach
    const errorMessages = form.querySelectorAll('.error-message');
    errorMessages.forEach(message => message.remove());
    
    // Walidacja imienia i nazwiska
    if (!nameInput.value.trim()) {
        showError(nameInput, 'Proszę podać imię i nazwisko');
        isValid = false;
    }
    
    // Walidacja adresu email
    if (!emailInput.value.trim()) {
        showError(emailInput, 'Proszę podać adres email');
        isValid = false;
    } else if (!isValidEmail(emailInput.value)) {
        showError(emailInput, 'Proszę podać poprawny adres email');
        isValid = false;
    }
    
    // Walidacja tematu
    if (!subjectInput.value) {
        showError(subjectInput, 'Proszę wybrać temat');
        isValid = false;
    }
    
    // Walidacja wiadomości
    if (!messageInput.value.trim()) {
        showError(messageInput, 'Proszę wpisać treść wiadomości');
        isValid = false;
    }
    
    // Walidacja zgody na przetwarzanie danych
    if (!privacyInput.checked) {
        showError(privacyInput, 'Proszę wyrazić zgodę na przetwarzanie danych osobowych');
        isValid = false;
    }
    
    return isValid;
}

/**
 * Wyświetlenie komunikatu o błędzie
 * @param {HTMLElement} input - Pole formularza
 * @param {string} message - Komunikat o błędzie
 */
function showError(input, message) {
    const formGroup = input.closest('.form-group');
    const errorMessage = document.createElement('div');
    errorMessage.className = 'error-message';
    errorMessage.textContent = message;
    errorMessage.style.color = 'red';
    errorMessage.style.fontSize = '0.85rem';
    errorMessage.style.marginTop = '5px';
    formGroup.appendChild(errorMessage);
    
    input.style.borderColor = 'red';
    
    input.addEventListener('input', function() {
        const errorMessage = formGroup.querySelector('.error-message');
        if (errorMessage) {
            errorMessage.remove();
        }
        input.style.borderColor = '';
    });
}

/**
 * Sprawdzenie poprawności adresu email
 * @param {string} email - Adres email do sprawdzenia
 * @returns {boolean} - Czy adres email jest poprawny
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Wyświetlenie komunikatu o pomyślnym wysłaniu formularza
 */
function showFormSuccess() {
    const contactForm = document.getElementById('contactForm');
    const formContainer = contactForm.parentElement;
    
    // Ukrycie formularza
    contactForm.style.display = 'none';
    
    // Utworzenie komunikatu o sukcesie
    const successMessage = document.createElement('div');
    successMessage.className = 'success-message';
    successMessage.innerHTML = `
        <div style="text-align: center; padding: 40px 20px;">
            <div style="font-size: 4rem; color: var(--primary-color); margin-bottom: 20px;">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3 style="margin-bottom: 15px; font-size: 1.5rem;">Dziękujemy za wiadomość!</h3>
            <p style="margin-bottom: 30px; color: var(--text-medium);">Twoja wiadomość została wysłana. Odpowiemy najszybciej jak to możliwe.</p>
            <button id="newMessageBtn" class="btn btn-primary">Wyślij nową wiadomość</button>
        </div>
    `;
    
    formContainer.appendChild(successMessage);
    
    // Obsługa przycisku "Wyślij nową wiadomość"
    const newMessageBtn = document.getElementById('newMessageBtn');
    newMessageBtn.addEventListener('click', function() {
        // Usunięcie komunikatu o sukcesie
        successMessage.remove();
        
        // Wyczyszczenie formularza
        contactForm.reset();
        
        // Pokazanie formularza
        contactForm.style.display = '';
    });
}

/**
 * Inicjalizacja FAQ
 */
function initFaq() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        
        question.addEventListener('click', function() {
            // Zamknięcie innych elementów FAQ
            faqItems.forEach(otherItem => {
                if (otherItem !== item && otherItem.classList.contains('active')) {
                    otherItem.classList.remove('active');
                }
            });
            
            // Przełączenie aktywnego elementu
            item.classList.toggle('active');
        });
    });
}
