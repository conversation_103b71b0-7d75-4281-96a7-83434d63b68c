/**
 * Kupony - Żyrardów Poleca
 * JavaScript dla kuponów
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja filtrów kuponów
    initCouponsFilters();
    
    // Inicjalizacja wyszukiwania
    initCouponsSearch();
    
    // Inicjalizacja odkrywania kodów rabatowych
    initCouponCodes();
    
    // Inicjalizacja lightbox dla szczegółów kuponów
    initCouponDetails();
});

/**
 * Inicjalizacja filtrów kuponów
 */
function initCouponsFilters() {
    const categoryFilter = document.getElementById('categoryFilter');
    const sortFilter = document.getElementById('sortFilter');
    
    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            filterCoupons();
        });
    }
    
    if (sortFilter) {
        sortFilter.addEventListener('change', function() {
            sortCoupons();
        });
    }
}

/**
 * Inicjalizacja wyszukiwania
 */
function initCouponsSearch() {
    const searchInput = document.getElementById('couponsSearch');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterCoupons();
        });
    }
}

/**
 * Inicjalizacja odkrywania kodów rabatowych
 */
function initCouponCodes() {
    const couponCodeContainers = document.querySelectorAll('.coupon-code-hidden');
    
    couponCodeContainers.forEach(container => {
        container.addEventListener('click', function() {
            this.classList.add('active');
            
            // Kopiowanie kodu do schowka
            const codeText = this.querySelector('.coupon-code span').textContent;
            navigator.clipboard.writeText(codeText).then(() => {
                // Opcjonalnie: wyświetlenie komunikatu o skopiowaniu
                const notification = document.createElement('div');
                notification.className = 'copy-notification';
                notification.textContent = 'Kod skopiowany do schowka!';
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.classList.add('active');
                }, 10);
                
                setTimeout(() => {
                    notification.classList.remove('active');
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 2000);
            });
        });
    });
}

/**
 * Filtrowanie kuponów na podstawie kategorii i wyszukiwania
 */
function filterCoupons() {
    const searchInput = document.getElementById('couponsSearch');
    const categoryFilter = document.getElementById('categoryFilter');
    const couponCards = document.querySelectorAll('.coupon-card');
    
    if (searchInput && categoryFilter && couponCards.length) {
        const searchTerm = searchInput.value.toLowerCase();
        const categoryValue = categoryFilter.value;
        
        couponCards.forEach(card => {
            // Pobranie danych kuponu
            const couponTitle = card.querySelector('h3').textContent.toLowerCase();
            const couponDescription = card.querySelector('p').textContent.toLowerCase();
            const couponCode = card.querySelector('.coupon-code span').textContent.toLowerCase();
            
            // Pobranie kategorii kuponu (z atrybutu data lub na podstawie nazwy firmy)
            let couponCategory = '';
            if (couponTitle.includes('restauracja')) couponCategory = 'gastronomy';
            else if (couponTitle.includes('salon')) couponCategory = 'beauty';
            else if (couponTitle.includes('centrum fitness')) couponCategory = 'sport';
            else if (couponTitle.includes('sklep')) couponCategory = 'shopping';
            else if (couponTitle.includes('auto')) couponCategory = 'automotive';
            else if (couponTitle.includes('szkoła')) couponCategory = 'education';
            
            // Sprawdzenie czy kupon pasuje do wyszukiwania
            const matchesSearch = searchTerm === '' || 
                                 couponTitle.includes(searchTerm) || 
                                 couponDescription.includes(searchTerm) ||
                                 couponCode.includes(searchTerm);
            
            // Sprawdzenie czy kupon pasuje do wybranej kategorii
            const matchesCategory = categoryValue === '' || couponCategory.includes(categoryValue);
            
            // Pokazanie lub ukrycie kuponu
            if (matchesSearch && matchesCategory) {
                card.style.display = '';
            } else {
                card.style.display = 'none';
            }
        });
        
        // Sprawdzenie czy są widoczne kupony
        const visibleCoupons = document.querySelectorAll('.coupon-card[style="display: ;"]');
        const noResultsMessage = document.getElementById('noResultsMessage');
        
        if (visibleCoupons.length === 0) {
            // Jeśli nie ma widocznych kuponów, pokaż komunikat
            if (!noResultsMessage) {
                const couponsContainer = document.getElementById('couponsContainer');
                const message = document.createElement('div');
                message.id = 'noResultsMessage';
                message.className = 'no-results-message';
                message.innerHTML = `
                    <i class="fas fa-search"></i>
                    <h3>Brak wyników</h3>
                    <p>Nie znaleziono kuponów spełniających kryteria wyszukiwania.</p>
                `;
                couponsContainer.parentNode.insertBefore(message, couponsContainer.nextSibling);
            } else {
                noResultsMessage.style.display = '';
            }
        } else if (noResultsMessage) {
            // Jeśli są widoczne kupony, ukryj komunikat
            noResultsMessage.style.display = 'none';
        }
    }
}

/**
 * Sortowanie kuponów
 */
function sortCoupons() {
    const sortFilter = document.getElementById('sortFilter');
    const couponsContainer = document.getElementById('couponsContainer');
    
    if (sortFilter && couponsContainer) {
        const sortValue = sortFilter.value;
        const couponCards = Array.from(document.querySelectorAll('.coupon-card'));
        
        // Sortowanie kuponów
        couponCards.sort((a, b) => {
            if (sortValue === 'discount') {
                // Sortowanie według zniżki
                const discountA = getDiscountValue(a);
                const discountB = getDiscountValue(b);
                return discountB - discountA; // Od największej do najmniejszej
            } else if (sortValue === 'expiry') {
                // Sortowanie według daty ważności
                const expiryA = getExpiryDate(a);
                const expiryB = getExpiryDate(b);
                return expiryA - expiryB; // Od najbliższej do najdalszej
            } else if (sortValue === 'popularity') {
                // Sortowanie według popularności (w tym przypadku losowo)
                return Math.random() - 0.5;
            }
            
            return 0;
        });
        
        // Usunięcie wszystkich kuponów z kontenera
        while (couponsContainer.firstChild) {
            couponsContainer.removeChild(couponsContainer.firstChild);
        }
        
        // Dodanie posortowanych kuponów do kontenera
        couponCards.forEach(card => {
            couponsContainer.appendChild(card);
        });
    }
}

/**
 * Pobranie wartości zniżki z kuponu
 * @param {Element} couponCard - Element karty kuponu
 * @returns {number} - Wartość zniżki
 */
function getDiscountValue(couponCard) {
    const discountText = couponCard.querySelector('.coupon-discount span').textContent;
    const percentMatch = discountText.match(/(\d+)%/);
    
    if (percentMatch) {
        return parseInt(percentMatch[1]);
    }
    
    const valueMatch = discountText.match(/(\d+)/);
    if (valueMatch) {
        return parseInt(valueMatch[1]);
    }
    
    return 0;
}

/**
 * Pobranie daty ważności z kuponu
 * @param {Element} couponCard - Element karty kuponu
 * @returns {Date} - Data ważności
 */
function getExpiryDate(couponCard) {
    const validityElement = couponCard.querySelector('.coupon-validity');
    
    if (validityElement) {
        const validityText = validityElement.textContent;
        const dateMatch = validityText.match(/(\d{1,2})\.(\d{1,2})\.(\d{4})/);
        
        if (dateMatch) {
            const day = parseInt(dateMatch[1]);
            const month = parseInt(dateMatch[2]) - 1; // Miesiące w JS są od 0 do 11
            const year = parseInt(dateMatch[3]);
            
            return new Date(year, month, day);
        }
    }
    
    return new Date(9999, 11, 31); // Bardzo odległa data
}

/**
 * Inicjalizacja lightbox dla szczegółów kuponów
 */
function initCouponDetails() {
    const couponButtons = document.querySelectorAll('.js-coupon-details');
    const lightbox = document.getElementById('couponLightbox');
    const lightboxClose = document.querySelector('.lightbox-close');
    const lightboxBody = document.getElementById('couponDetails');
    
    if (couponButtons.length && lightbox && lightboxClose && lightboxBody) {
        // Obsługa kliknięcia w przycisk "Zobacz szczegóły"
        couponButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Pobranie ID kuponu
                const couponId = this.getAttribute('data-coupon');
                
                // Pobranie danych kuponu
                const couponCard = this.closest('.coupon-card');
                const couponTitle = couponCard.querySelector('h3').textContent;
                const couponDescription = couponCard.querySelector('p').textContent;
                const couponCode = couponCard.querySelector('.coupon-code span').textContent;
                const couponValidity = couponCard.querySelector('.coupon-validity').textContent.replace('Ważne do:', '').trim();
                const couponDiscount = couponCard.querySelector('.coupon-discount span').textContent;
                
                // Wygenerowanie zawartości lightbox
                lightboxBody.innerHTML = `
                    <div class="coupon-details-header">
                        <div class="coupon-details-logo">
                            <img src="images/business-logo${couponId.slice(-1)}.jpg" alt="${couponTitle}">
                        </div>
                        <div class="coupon-details-title">
                            <h2>${couponTitle}</h2>
                            <p>Kupon rabatowy ${couponDiscount}</p>
                        </div>
                    </div>
                    <div class="coupon-details-content">
                        <p>${couponDescription}</p>
                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
                    </div>
                    <div class="coupon-details-code">
                        <div class="coupon-details-code-label">Kod rabatowy:</div>
                        <div class="coupon-details-code-value">${couponCode}</div>
                    </div>
                    <div class="coupon-details-info">
                        <div class="coupon-details-info-item">
                            <div class="coupon-details-info-label">Zniżka:</div>
                            <div>${couponDiscount}</div>
                        </div>
                        <div class="coupon-details-info-item">
                            <div class="coupon-details-info-label">Ważność:</div>
                            <div>${couponValidity}</div>
                        </div>
                        <div class="coupon-details-info-item">
                            <div class="coupon-details-info-label">Warunki:</div>
                            <div>Kupon ważny w lokalu. Nie łączy się z innymi promocjami.</div>
                        </div>
                    </div>
                    <div class="coupon-details-actions">
                        <a href="tel:+48123456789" class="btn btn-primary">Zadzwoń i zarezerwuj</a>
                        <a href="https://maps.google.com" target="_blank" class="btn btn-secondary">Zobacz na mapie</a>
                    </div>
                `;
                
                // Otwarcie lightbox
                lightbox.classList.add('active');
                document.body.style.overflow = 'hidden';
            });
        });
        
        // Obsługa zamknięcia lightbox
        lightboxClose.addEventListener('click', function() {
            lightbox.classList.remove('active');
            document.body.style.overflow = '';
        });
        
        // Zamknięcie lightbox po kliknięciu poza zawartością
        lightbox.addEventListener('click', function(e) {
            if (e.target === lightbox) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
        
        // Obsługa klawisza Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && lightbox.classList.contains('active')) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }
}
