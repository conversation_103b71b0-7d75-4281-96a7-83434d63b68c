/**
 * Widget pogody dla Żyrardów Poleca
 * Bezpieczne pobieranie danych pogodowych przez backend API
 */

class WeatherWidget {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.updateInterval = 30 * 60 * 1000; // 30 minut
        this.retryDelay = 5 * 60 * 1000; // 5 minut przy błędzie
        this.maxRetries = 3;
        this.currentRetries = 0;

        // Sprawdź czy mamy klucz API w localStorage
        this.config = this.getWeatherConfig();
        this.apiUrl = this.config.apiKey ?
            `https://api.weatherapi.com/v1/forecast.json?key=${this.config.apiKey}&q=${this.config.lat},${this.config.lon}&days=3&aqi=no&alerts=no` :
            './api/weather.json'; // Fallback do statycznych danych

        this.init();
    }

    getWeatherConfig() {
        try {
            const config = localStorage.getItem('weather_config');
            if (config) {
                return JSON.parse(config);
            }
        } catch (error) {
            console.log('Błąd odczytu konfiguracji pogody:', error);
        }

        // Domyślna konfiguracja
        return {
            enabled: true,
            city: 'Żyrardów',
            lat: 52.0500,
            lon: 20.4500,
            position: 'bottom',
            apiKey: ''
        };
    }

    init() {
        if (!this.container) {
            console.error('Weather widget container not found');
            return;
        }

        this.createWidget();
        this.loadWeatherData();
        this.startAutoUpdate();
    }

    createWidget() {
        this.container.innerHTML = `
            <div class="weather-widget">
                <div class="container">
                    <div class="weather-content">
                        <div class="weather-header">
                            <h3 class="weather-title">
                                <i class="fas fa-cloud-sun"></i>
                                Prognoza pogody
                            </h3>
                            <div class="weather-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span id="weather-city-name">Żyrardów</span>
                            </div>
                        </div>
                        <div id="weather-forecast-container">
                            ${this.createLoadingState()}
                        </div>
                        <div class="weather-updated" id="weather-updated"></div>
                    </div>
                </div>
            </div>
        `;
    }

    createLoadingState() {
        return `
            <div class="weather-loading">
                <i class="fas fa-spinner"></i>
                <p>Ładowanie prognozy pogody...</p>
            </div>
        `;
    }

    createErrorState(message) {
        return `
            <div class="weather-error">
                <i class="fas fa-exclamation-triangle"></i>
                <p>Błąd ładowania prognozy pogody</p>
                <small>${message}</small>
                <button onclick="window.weatherWidget.refresh()" class="btn btn-sm btn-outline" style="margin-top: 10px;">
                    <i class="fas fa-refresh"></i> Spróbuj ponownie
                </button>
            </div>
        `;
    }

    async loadWeatherData() {
        console.log('Ładowanie danych pogodowych z:', this.apiUrl);
        try {
            const response = await fetch(this.apiUrl, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                }
            });

            console.log('Odpowiedź z API:', response.status, response.statusText);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Dane z API:', data);

            // Sprawdź czy to dane z WeatherAPI.com czy z naszego JSON
            if (this.config.apiKey && data.location) {
                // Dane z WeatherAPI.com - przekształć na nasz format
                const processedData = this.processWeatherAPIData(data);
                this.renderWeatherData(processedData);
            } else if (data.success) {
                // Dane z naszego JSON API
                this.renderWeatherData(data);
            } else {
                throw new Error(data.error || 'Błędne dane z API');
            }

            this.currentRetries = 0; // Reset retry counter on success

        } catch (error) {
            console.error('Weather API Error:', error);
            this.handleError(error.message);
        }
    }

    processWeatherAPIData(data) {
        const dayNames = ['Niedziela', 'Poniedziałek', 'Wtorek', 'Środa', 'Czwartek', 'Piątek', 'Sobota'];
        const forecast = [];

        // Aktualna pogoda (dziś)
        const current = data.current;
        const today = data.forecast.forecastday[0];

        forecast.push({
            date: today.date,
            day_name_pl: 'Dziś',
            temp: Math.round(current.temp_c),
            temp_min: Math.round(today.day.mintemp_c),
            temp_max: Math.round(today.day.maxtemp_c),
            description: current.condition.text,
            icon: current.condition.icon.startsWith('//') ? current.condition.icon : '//' + current.condition.icon,
            humidity: current.humidity,
            wind_speed: current.wind_kph,
            wind_dir: current.wind_dir,
            pressure: current.pressure_mb,
            visibility: current.vis_km,
            uv: current.uv,
            condition: current.condition.text
        });

        // Prognoza na kolejne dni
        for (let i = 1; i < data.forecast.forecastday.length && i < 3; i++) {
            const day = data.forecast.forecastday[i];
            const date = new Date(day.date);
            const dayName = i === 1 ? 'Jutro' : dayNames[date.getDay()];

            forecast.push({
                date: day.date,
                day_name_pl: dayName,
                temp: Math.round(day.day.avgtemp_c),
                temp_min: Math.round(day.day.mintemp_c),
                temp_max: Math.round(day.day.maxtemp_c),
                description: day.day.condition.text,
                icon: day.day.condition.icon.startsWith('//') ? day.day.condition.icon : '//' + day.day.condition.icon,
                humidity: day.day.avghumidity,
                wind_speed: day.day.maxwind_kph,
                wind_dir: '',
                pressure: 0,
                visibility: day.day.avgvis_km,
                uv: day.day.uv,
                condition: day.day.condition.text
            });
        }

        return {
            success: true,
            city: data.location.name,
            country: data.location.country,
            forecast: forecast,
            updated: new Date().toLocaleString('pl-PL'),
            source: 'WeatherAPI.com'
        };
    }

    renderWeatherData(data) {
        const cityNameElement = document.getElementById('weather-city-name');
        const forecastContainer = document.getElementById('weather-forecast-container');
        const updatedElement = document.getElementById('weather-updated');

        if (cityNameElement) {
            cityNameElement.textContent = data.city || 'Żyrardów';
        }

        if (forecastContainer && data.forecast) {
            forecastContainer.innerHTML = this.createForecastHTML(data.forecast);
        }

        if (updatedElement) {
            // Napraw formatowanie daty
            let updateTime;
            try {
                if (data.updated) {
                    updateTime = new Date(data.updated).toLocaleString('pl-PL');
                } else {
                    updateTime = new Date().toLocaleString('pl-PL');
                }
            } catch (error) {
                updateTime = new Date().toLocaleString('pl-PL');
            }

            const apiStatus = this.config.apiKey ?
                '<span style="color: #27ae60;"><i class="fas fa-check-circle"></i> WeatherAPI.com</span>' :
                '<span style="color: #f39c12;"><i class="fas fa-exclamation-triangle"></i> Dane testowe</span>';
            updatedElement.innerHTML = `<i class="fas fa-clock"></i> ${updateTime} • ${apiStatus}`;
        }
    }

    createForecastHTML(forecast) {
        if (!forecast || forecast.length === 0) {
            return this.createErrorState('Brak danych prognozy');
        }

        const forecastHTML = forecast.slice(0, 3).map((day, index) => {
            const isToday = index === 0;
            const dayName = isToday ? 'Dziś' : day.day_name_pl;

            return `
                <div class="weather-day ${isToday ? 'today' : ''}">
                    <div class="day-name">${dayName}</div>
                    <div class="weather-icon">
                        <img src="${day.icon.startsWith('//') ? 'https:' + day.icon : day.icon}" alt="${day.description}" loading="lazy">
                    </div>
                    <div class="weather-temp">${day.temp}°C</div>
                    <div class="weather-temp-range">${day.temp_min}° / ${day.temp_max}°</div>
                    <div class="weather-description">${day.description}</div>
                    <div class="weather-details">
                        <div class="weather-detail">
                            <i class="fas fa-tint"></i>
                            <span>${day.humidity}%</span>
                        </div>
                        <div class="weather-detail">
                            <i class="fas fa-wind"></i>
                            <span>${Math.round(day.wind_speed)} km/h</span>
                        </div>
                        <div class="weather-detail">
                            <i class="fas fa-eye"></i>
                            <span>${day.visibility || day.clouds || 0}${day.visibility ? ' km' : '%'}</span>
                        </div>
                        ${day.wind_dir ? `<div class="weather-detail">
                            <i class="fas fa-compass"></i>
                            <span>${day.wind_dir}</span>
                        </div>` : ''}
                        ${day.pressure ? `<div class="weather-detail">
                            <i class="fas fa-thermometer-half"></i>
                            <span>${day.pressure} hPa</span>
                        </div>` : ''}
                        ${day.uv ? `<div class="weather-detail">
                            <i class="fas fa-sun"></i>
                            <span>UV ${day.uv}</span>
                        </div>` : ''}
                    </div>
                </div>
            `;
        }).join('');

        return `<div class="weather-forecast">${forecastHTML}</div>`;
    }

    handleError(message) {
        const forecastContainer = document.getElementById('weather-forecast-container');
        if (forecastContainer) {
            forecastContainer.innerHTML = this.createErrorState(message);
        }

        // Retry logic
        if (this.currentRetries < this.maxRetries) {
            this.currentRetries++;
            console.log(`Retrying weather API call (${this.currentRetries}/${this.maxRetries}) in ${this.retryDelay/1000} seconds`);
            setTimeout(() => this.loadWeatherData(), this.retryDelay);
        }
    }

    startAutoUpdate() {
        setInterval(() => {
            this.loadWeatherData();
        }, this.updateInterval);
    }

    // Public method to manually refresh weather data
    refresh() {
        this.currentRetries = 0;
        this.loadWeatherData();
    }

    // Public method to destroy the widget
    destroy() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
        }
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM załadowany, szukam kontenera widgetu pogody...');

    // Check if weather widget container exists
    const weatherContainer = document.getElementById('weather-widget-container');
    if (weatherContainer) {
        console.log('Kontener widgetu pogody znaleziony, inicjalizuję...');
        // Initialize weather widget
        window.weatherWidget = new WeatherWidget('weather-widget-container');

        // Add refresh button functionality if exists
        const refreshBtn = document.getElementById('weather-refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                window.weatherWidget.refresh();
            });
        }
    } else {
        console.log('Kontener widgetu pogody NIE został znaleziony!');
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WeatherWidget;
}
