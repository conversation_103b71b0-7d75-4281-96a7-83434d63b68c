/**
 * Dodawanie firmy - Żyrardów Poleca
 * JavaScript dla strony dodawania firmy
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja formularza dodawania firmy
    initAddCompanyForm();
    
    // Inicjalizacja uploadu plików
    initFileUploads();
});

/**
 * Inicjalizacja formularza dodawania firmy
 */
function initAddCompanyForm() {
    const addCompanyForm = document.getElementById('addCompanyForm');
    
    if (addCompanyForm) {
        addCompanyForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Walidacja formularza
            if (validateForm(addCompanyForm)) {
                // Symulacja wysłania formularza
                showFormSuccess();
            }
        });
    }
}

/**
 * Walidacja formularza
 * @param {HTMLFormElement} form - Formularz do walidacji
 * @returns {boolean} - <PERSON><PERSON> formularz jest poprawny
 */
function validateForm(form) {
    let isValid = true;
    
    // Pobranie pól formularza
    const companyNameInput = form.querySelector('#companyName');
    const companyCategoryInput = form.querySelector('#companyCategory');
    const companyDescriptionInput = form.querySelector('#companyDescription');
    const companyAddressInput = form.querySelector('#companyAddress');
    const companyPhoneInput = form.querySelector('#companyPhone');
    const companyEmailInput = form.querySelector('#companyEmail');
    const companyLogoInput = form.querySelector('#companyLogo');
    const companyCoverInput = form.querySelector('#companyCover');
    const ownerNameInput = form.querySelector('#ownerName');
    const ownerEmailInput = form.querySelector('#ownerEmail');
    const ownerPhoneInput = form.querySelector('#ownerPhone');
    const privacyInput = form.querySelector('#privacy');
    const termsInput = form.querySelector('#terms');
    
    // Usunięcie poprzednich komunikatów o błędach
    const errorMessages = form.querySelectorAll('.error-message');
    errorMessages.forEach(message => message.remove());
    
    // Walidacja nazwy firmy
    if (!companyNameInput.value.trim()) {
        showError(companyNameInput, 'Proszę podać nazwę firmy');
        isValid = false;
    }
    
    // Walidacja kategorii
    if (!companyCategoryInput.value) {
        showError(companyCategoryInput, 'Proszę wybrać kategorię');
        isValid = false;
    }
    
    // Walidacja opisu
    if (!companyDescriptionInput.value.trim()) {
        showError(companyDescriptionInput, 'Proszę podać opis firmy');
        isValid = false;
    }
    
    // Walidacja adresu
    if (!companyAddressInput.value.trim()) {
        showError(companyAddressInput, 'Proszę podać adres firmy');
        isValid = false;
    }
    
    // Walidacja telefonu
    if (!companyPhoneInput.value.trim()) {
        showError(companyPhoneInput, 'Proszę podać numer telefonu');
        isValid = false;
    }
    
    // Walidacja adresu email
    if (!companyEmailInput.value.trim()) {
        showError(companyEmailInput, 'Proszę podać adres email');
        isValid = false;
    } else if (!isValidEmail(companyEmailInput.value)) {
        showError(companyEmailInput, 'Proszę podać poprawny adres email');
        isValid = false;
    }
    
    // Walidacja logo
    if (companyLogoInput.files.length === 0) {
        showError(companyLogoInput.parentElement, 'Proszę wybrać logo firmy');
        isValid = false;
    }
    
    // Walidacja zdjęcia głównego
    if (companyCoverInput.files.length === 0) {
        showError(companyCoverInput.parentElement, 'Proszę wybrać zdjęcie główne');
        isValid = false;
    }
    
    // Walidacja imienia i nazwiska właściciela
    if (!ownerNameInput.value.trim()) {
        showError(ownerNameInput, 'Proszę podać imię i nazwisko');
        isValid = false;
    }
    
    // Walidacja adresu email właściciela
    if (!ownerEmailInput.value.trim()) {
        showError(ownerEmailInput, 'Proszę podać adres email');
        isValid = false;
    } else if (!isValidEmail(ownerEmailInput.value)) {
        showError(ownerEmailInput, 'Proszę podać poprawny adres email');
        isValid = false;
    }
    
    // Walidacja telefonu właściciela
    if (!ownerPhoneInput.value.trim()) {
        showError(ownerPhoneInput, 'Proszę podać numer telefonu');
        isValid = false;
    }
    
    // Walidacja zgody na przetwarzanie danych
    if (!privacyInput.checked) {
        showError(privacyInput, 'Proszę wyrazić zgodę na przetwarzanie danych osobowych');
        isValid = false;
    }
    
    // Walidacja akceptacji regulaminu
    if (!termsInput.checked) {
        showError(termsInput, 'Proszę zaakceptować regulamin');
        isValid = false;
    }
    
    return isValid;
}

/**
 * Wyświetlenie komunikatu o błędzie
 * @param {HTMLElement} input - Pole formularza
 * @param {string} message - Komunikat o błędzie
 */
function showError(input, message) {
    const formGroup = input.closest('.form-group');
    const errorMessage = document.createElement('div');
    errorMessage.className = 'error-message';
    errorMessage.textContent = message;
    errorMessage.style.color = 'red';
    errorMessage.style.fontSize = '0.85rem';
    errorMessage.style.marginTop = '5px';
    formGroup.appendChild(errorMessage);
    
    if (input.tagName === 'INPUT' || input.tagName === 'SELECT' || input.tagName === 'TEXTAREA') {
        input.style.borderColor = 'red';
        
        input.addEventListener('input', function() {
            const errorMessage = formGroup.querySelector('.error-message');
            if (errorMessage) {
                errorMessage.remove();
            }
            input.style.borderColor = '';
        });
    } else if (input.classList.contains('file-upload')) {
        input.style.borderColor = 'red';
        
        input.querySelector('input[type="file"]').addEventListener('change', function() {
            const errorMessage = formGroup.querySelector('.error-message');
            if (errorMessage) {
                errorMessage.remove();
            }
            input.style.borderColor = '';
        });
    }
}

/**
 * Sprawdzenie poprawności adresu email
 * @param {string} email - Adres email do sprawdzenia
 * @returns {boolean} - Czy adres email jest poprawny
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Wyświetlenie komunikatu o pomyślnym wysłaniu formularza
 */
function showFormSuccess() {
    const addCompanyForm = document.getElementById('addCompanyForm');
    const formContainer = addCompanyForm.parentElement;
    
    // Ukrycie formularza
    addCompanyForm.style.display = 'none';
    
    // Utworzenie komunikatu o sukcesie
    const successMessage = document.createElement('div');
    successMessage.className = 'success-message';
    successMessage.innerHTML = `
        <div style="text-align: center; padding: 40px 20px;">
            <div style="font-size: 4rem; color: var(--primary-color); margin-bottom: 20px;">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3 style="margin-bottom: 15px; font-size: 1.5rem;">Dziękujemy za dodanie firmy!</h3>
            <p style="margin-bottom: 30px; color: var(--text-medium);">Twoja firma została dodana do naszego katalogu. Po weryfikacji przez administratora, zostanie opublikowana na stronie.</p>
            <div style="display: flex; gap: 15px; justify-content: center;">
                <a href="katalog-firm.html" class="btn btn-primary">Przejdź do katalogu firm</a>
                <button id="newCompanyBtn" class="btn btn-secondary">Dodaj kolejną firmę</button>
            </div>
        </div>
    `;
    
    formContainer.appendChild(successMessage);
    
    // Obsługa przycisku "Dodaj kolejną firmę"
    const newCompanyBtn = document.getElementById('newCompanyBtn');
    newCompanyBtn.addEventListener('click', function() {
        // Usunięcie komunikatu o sukcesie
        successMessage.remove();
        
        // Wyczyszczenie formularza
        addCompanyForm.reset();
        
        // Wyczyszczenie podglądu plików
        const filePreviewContainers = document.querySelectorAll('.file-preview');
        filePreviewContainers.forEach(container => {
            container.remove();
        });
        
        // Pokazanie formularza
        addCompanyForm.style.display = '';
    });
}

/**
 * Inicjalizacja uploadu plików
 */
function initFileUploads() {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const fileUpload = this.closest('.file-upload');
            const formGroup = fileUpload.closest('.form-group');
            
            // Usunięcie poprzedniego podglądu
            const previousPreview = formGroup.querySelector('.file-preview');
            if (previousPreview) {
                previousPreview.remove();
            }
            
            // Sprawdzenie czy wybrano plik
            if (this.files.length > 0) {
                // Utworzenie kontenera podglądu
                const previewContainer = document.createElement('div');
                previewContainer.className = 'file-preview';
                previewContainer.style.marginTop = '15px';
                previewContainer.style.display = 'flex';
                previewContainer.style.flexWrap = 'wrap';
                previewContainer.style.gap = '10px';
                
                // Dodanie podglądu dla każdego pliku
                for (let i = 0; i < this.files.length; i++) {
                    const file = this.files[i];
                    
                    // Sprawdzenie czy plik jest obrazem
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        
                        reader.onload = function(e) {
                            const previewItem = document.createElement('div');
                            previewItem.style.position = 'relative';
                            previewItem.style.width = '100px';
                            previewItem.style.height = '100px';
                            previewItem.style.borderRadius = '8px';
                            previewItem.style.overflow = 'hidden';
                            
                            const img = document.createElement('img');
                            img.src = e.target.result;
                            img.style.width = '100%';
                            img.style.height = '100%';
                            img.style.objectFit = 'cover';
                            
                            previewItem.appendChild(img);
                            previewContainer.appendChild(previewItem);
                        };
                        
                        reader.readAsDataURL(file);
                    }
                }
                
                formGroup.appendChild(previewContainer);
            }
        });
    });
}
