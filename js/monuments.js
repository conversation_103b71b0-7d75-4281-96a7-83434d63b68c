/**
 * Zabytki - Żyrardów Poleca
 * JavaScript dla strony zabytków
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja zakładek kategorii
    initCategoryTabs();
    
    // Inicjalizacja lightbox dla szczegółów zabytków
    initMonumentDetails();
});

/**
 * Inicjalizacja zakładek kategorii
 */
function initCategoryTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    if (tabButtons.length && tabPanes.length) {
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Usunięcie klasy active ze wszystkich przycisków
                tabButtons.forEach(btn => btn.classList.remove('active'));
                
                // Dodanie klasy active do klikniętego przycisku
                this.classList.add('active');
                
                // Pobranie ID zakładki
                const tabId = this.getAttribute('data-tab');
                
                // Ukrycie wszystkich zakładek
                tabPanes.forEach(pane => pane.classList.remove('active'));
                
                // Pokazanie wybranej zakładki
                document.getElementById(tabId).classList.add('active');
            });
        });
    }
}

/**
 * Inicjalizacja lightbox dla szczegółów zabytków
 */
function initMonumentDetails() {
    const monumentButtons = document.querySelectorAll('.js-monument-details');
    const lightbox = document.getElementById('monumentLightbox');
    const lightboxClose = document.querySelector('.lightbox-close');
    const lightboxBody = document.getElementById('monumentDetails');
    
    if (monumentButtons.length && lightbox && lightboxClose && lightboxBody) {
        // Obsługa kliknięcia w przycisk "Zobacz więcej"
        monumentButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Pobranie ID zabytku
                const monumentId = this.getAttribute('data-monument');
                
                // Pobranie danych zabytku
                const monumentCard = this.closest('.monument-card');
                const monumentTitle = monumentCard.querySelector('h3').textContent;
                const monumentDate = monumentCard.querySelector('.monument-date').textContent;
                const monumentDescription = monumentCard.querySelector('p:not(.monument-date)').textContent;
                const monumentImage = monumentCard.querySelector('.monument-image img').src;
                
                // Wygenerowanie zawartości lightbox
                lightboxBody.innerHTML = generateMonumentDetails(monumentId, monumentTitle, monumentDate, monumentDescription, monumentImage);
                
                // Otwarcie lightbox
                lightbox.classList.add('active');
                document.body.style.overflow = 'hidden';
            });
        });
        
        // Obsługa zamknięcia lightbox
        lightboxClose.addEventListener('click', function() {
            lightbox.classList.remove('active');
            document.body.style.overflow = '';
        });
        
        // Zamknięcie lightbox po kliknięciu poza zawartością
        lightbox.addEventListener('click', function(e) {
            if (e.target === lightbox) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
        
        // Obsługa klawisza Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && lightbox.classList.contains('active')) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }
}

/**
 * Generowanie zawartości szczegółów zabytku
 * @param {string} id - ID zabytku
 * @param {string} title - Tytuł zabytku
 * @param {string} date - Data budowy zabytku
 * @param {string} description - Opis zabytku
 * @param {string} image - URL obrazu zabytku
 * @returns {string} - HTML zawartości szczegółów zabytku
 */
function generateMonumentDetails(id, title, date, description, image) {
    // Dodatkowe informacje o zabytkach (w rzeczywistej implementacji byłyby pobierane z bazy danych)
    const monumentsData = {
        factory1: {
            address: 'ul. 1 Maja 45, 96-300 Żyrardów',
            architect: 'Karol Dittrich',
            style: 'Architektura przemysłowa',
            status: 'Wpisany do rejestru zabytków',
            renovation: '2010-2015',
            currentUse: 'Centrum handlowo-usługowe',
            additionalInfo: 'Centrala to główny budynek dawnej fabryki lnu, który był sercem przemysłowego Żyrardowa. W swoim czasie była to jedna z największych i najnowocześniejszych fabryk lnu w Europie. Charakterystyczna czerwona cegła i przemysłowa architektura stanowią wizytówkę miasta. Po upadku fabryki w latach 90. XX wieku, budynek został zrewitalizowany i obecnie pełni funkcje handlowo-usługowe, zachowując jednocześnie swój historyczny charakter.'
        },
        factory2: {
            address: 'ul. Lniarska 15, 96-300 Żyrardów',
            architect: 'Nieznany',
            style: 'Architektura przemysłowa',
            status: 'Wpisany do rejestru zabytków',
            renovation: '2012-2014',
            currentUse: 'Centrum handlowe',
            additionalInfo: 'Bielnik to budynek, w którym odbywał się proces bielenia i wykańczania tkanin lnianych. Był to jeden z kluczowych etapów produkcji wysokiej jakości wyrobów lnianych, z których słynął Żyrardów. Budynek charakteryzuje się typową dla osady fabrycznej architekturą z czerwonej cegły. Po rewitalizacji zachowano oryginalny układ przestrzenny i elementy wyposażenia, które przypominają o przemysłowej przeszłości obiektu.'
        },
        factory3: {
            address: 'ul. Dittricha 10, 96-300 Żyrardów',
            architect: 'Karol Dittrich',
            style: 'Eklektyzm',
            status: 'Wpisany do rejestru zabytków',
            renovation: '2008',
            currentUse: 'Muzeum Lniarstwa',
            additionalInfo: 'Kantor to reprezentacyjny budynek, w którym mieściła się dyrekcja fabryki. Wyróżnia się bogatszą architekturą i detalami w porównaniu do innych budynków fabrycznych. Był to symbol władzy i prestiżu właścicieli fabryki. Obecnie mieści się w nim Muzeum Lniarstwa im. Filipa de Girarda, które prezentuje historię przemysłu lniarskiego w Żyrardowie i postać wynalazcy, od którego nazwiska pochodzi nazwa miasta.'
        },
        religious1: {
            address: 'Plac Jana Pawła II, 96-300 Żyrardów',
            architect: 'Józef Pius Dziekoński',
            style: 'Neogotyk',
            status: 'Wpisany do rejestru zabytków',
            renovation: '2005-2007',
            currentUse: 'Kościół parafialny',
            additionalInfo: 'Kościół pw. Matki Bożej Pocieszenia to neogotycka świątynia wybudowana dla pracowników fabryki wyznania katolickiego. Charakterystyczna czerwona cegła i dwie strzeliste wieże dominują w panoramie miasta. Wewnątrz zachowało się oryginalne wyposażenie, w tym ołtarze, ambona, chrzcielnica i organy. Kościół był ważnym elementem życia społecznego i religijnego osady fabrycznej, a obecnie jest jednym z najważniejszych zabytków Żyrardowa.'
        },
        religious2: {
            address: 'ul. Żeromskiego 56, 96-300 Żyrardów',
            architect: 'Nieznany',
            style: 'Neogotyk',
            status: 'Wpisany do rejestru zabytków',
            renovation: '2010',
            currentUse: 'Kościół parafialny',
            additionalInfo: 'Kościół Ewangelicko-Augsburski został wybudowany dla pracowników fabryki wyznania ewangelickiego, głównie pochodzenia niemieckiego. Budynek w stylu neogotyckim z charakterystyczną wieżą stanowił ważny element wielokulturowego charakteru osady fabrycznej. Wewnątrz zachowały się oryginalne empory i organy. Obecnie kościół nadal służy niewielkiej społeczności ewangelickiej w Żyrardowie.'
        },
        religious3: {
            address: 'ul. Okrzei 26, 96-300 Żyrardów',
            architect: 'Nieznany',
            style: 'Bizantyjsko-rosyjski',
            status: 'Wpisany do rejestru zabytków',
            renovation: '2015',
            currentUse: 'Obiekt kulturalny',
            additionalInfo: 'Dawna cerkiew prawosławna została wybudowana dla pracowników fabryki wyznania prawosławnego, głównie pochodzenia rosyjskiego. Budynek reprezentował styl bizantyjsko-rosyjski z charakterystycznymi kopułami. Po I wojnie światowej, gdy większość Rosjan opuściła Żyrardów, cerkiew straciła swoją pierwotną funkcję. Obecnie budynek, po rewitalizacji, pełni funkcje kulturalne, zachowując jednocześnie swój historyczny charakter.'
        }
    };
    
    // Pobranie danych dla konkretnego zabytku
    const monumentData = monumentsData[id] || {};
    
    // Generowanie HTML
    return `
        <div class="monument-details-image">
            <img src="${image}" alt="${title}">
        </div>
        <div class="monument-details-title">
            <h2>${title}</h2>
            <p>${date}</p>
        </div>
        <div class="monument-details-content">
            <p>${description}</p>
            <p>${monumentData.additionalInfo || 'Brak dodatkowych informacji.'}</p>
        </div>
        <div class="monument-details-info">
            <div class="monument-details-info-item">
                <div class="monument-details-info-label">Adres:</div>
                <div>${monumentData.address || 'Brak danych'}</div>
            </div>
            <div class="monument-details-info-item">
                <div class="monument-details-info-label">Architekt:</div>
                <div>${monumentData.architect || 'Nieznany'}</div>
            </div>
            <div class="monument-details-info-item">
                <div class="monument-details-info-label">Styl:</div>
                <div>${monumentData.style || 'Brak danych'}</div>
            </div>
            <div class="monument-details-info-item">
                <div class="monument-details-info-label">Status:</div>
                <div>${monumentData.status || 'Brak danych'}</div>
            </div>
            <div class="monument-details-info-item">
                <div class="monument-details-info-label">Ostatnia renowacja:</div>
                <div>${monumentData.renovation || 'Brak danych'}</div>
            </div>
            <div class="monument-details-info-item">
                <div class="monument-details-info-label">Obecne przeznaczenie:</div>
                <div>${monumentData.currentUse || 'Brak danych'}</div>
            </div>
        </div>
        <div class="monument-details-gallery">
            <div class="monument-details-gallery-item">
                <img src="${image}" alt="${title} - widok 1">
            </div>
            <div class="monument-details-gallery-item">
                <img src="${image.replace('.jpg', '2.jpg')}" alt="${title} - widok 2">
            </div>
            <div class="monument-details-gallery-item">
                <img src="${image.replace('.jpg', '3.jpg')}" alt="${title} - widok 3">
            </div>
        </div>
        <div class="monument-details-actions">
            <a href="https://maps.google.com/?q=${encodeURIComponent(monumentData.address || title + ', Żyrardów')}" target="_blank" class="btn btn-primary">
                <i class="fas fa-map-marker-alt"></i> Zobacz na mapie
            </a>
            <a href="turystyka.html" class="btn btn-secondary">
                <i class="fas fa-route"></i> Trasy turystyczne
            </a>
        </div>
    `;
}
