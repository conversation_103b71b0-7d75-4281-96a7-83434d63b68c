/**
 * Zarządzanie widocznością sekcji na stronie głównej
 */

class SectionVisibilityManager {
    constructor() {
        this.sectionSelectors = {
            'hero': '.hero',
            'city-info': '.city-info-section',
            'categories': '.categories-section',
            'coupons': '.featured-coupons',
            'girard': '.filip-girard-year',
            'heritage': '.heritage-monument',
            'about': '.about-section',
            'seo': '.seo-content',
            'offers': '.featured-offers',
            'news': '.news-section',
            'places': '.places-section',
            'stats': '.stats-section',
            'weather': '.weather-section'
        };
        
        this.init();
    }

    init() {
        // Sprawdź czy jesteśmy na stronie głównej
        if (this.isHomePage()) {
            this.applySavedVisibility();
        }
    }

    isHomePage() {
        // Sprawdź czy to strona główna (index.html lub root)
        const path = window.location.pathname;
        return path === '/' || 
               path === '/index.html' || 
               path.endsWith('/') || 
               path.endsWith('/index.html') ||
               path.includes('zyrardow/') && !path.includes('admin/');
    }

    applySavedVisibility() {
        try {
            const savedVisibility = localStorage.getItem('section_visibility');
            let visibility;
            
            if (!savedVisibility) {
                console.log('Brak zapisanych ustawień widoczności sekcji, używam domyślnych');
                visibility = this.getDefaultVisibility();
                // Zapisz domyślne ustawienia
                localStorage.setItem('section_visibility', JSON.stringify(visibility));
            } else {
                visibility = JSON.parse(savedVisibility);
                console.log('Aplikowanie widoczności sekcji:', visibility);
            }

            this.updateSectionVisibility(visibility);
        } catch (error) {
            console.error('Błąd aplikowania widoczności sekcji:', error);
        }
    }

    updateSectionVisibility(visibility) {
        Object.entries(visibility).forEach(([sectionId, isVisible]) => {
            const selector = this.sectionSelectors[sectionId];
            if (selector) {
                const element = document.querySelector(selector);
                if (element) {
                    if (isVisible) {
                        element.style.display = 'block';
                        element.style.visibility = 'visible';
                        console.log(`Pokazano sekcję ${sectionId}`);
                    } else {
                        element.style.display = 'none';
                        element.style.visibility = 'hidden';
                        console.log(`Ukryto sekcję ${sectionId}`);
                    }
                }
            }
        });

        console.log('Widoczność sekcji została zastosowana');
    }

    // Metoda do zapisywania widoczności (wywoływana z dashboardu)
    saveVisibility(visibility) {
        try {
            localStorage.setItem('section_visibility', JSON.stringify(visibility));
            console.log('Widoczność sekcji została zapisana:', visibility);
            return true;
        } catch (error) {
            console.error('Błąd zapisywania widoczności sekcji:', error);
            return false;
        }
    }

    // Metoda do pobierania aktualnej widoczności
    getCurrentVisibility() {
        try {
            const saved = localStorage.getItem('section_visibility');
            return saved ? JSON.parse(saved) : this.getDefaultVisibility();
        } catch (error) {
            console.error('Błąd pobierania widoczności sekcji:', error);
            return this.getDefaultVisibility();
        }
    }

    // Domyślna widoczność sekcji
    getDefaultVisibility() {
        return {
            'hero': true,
            'city-info': true,
            'categories': true,
            'coupons': true,
            'girard': true,
            'heritage': true,
            'about': true,
            'seo': true,
            'offers': true,
            'news': false, // Domyślnie ukryta
            'places': true,
            'stats': true,
            'weather': true
        };
    }

    // Metoda do debugowania - pokazuje wszystkie sekcje
    debugSections() {
        console.log('=== DEBUG WIDOCZNOŚCI SEKCJI ===');
        Object.entries(this.sectionSelectors).forEach(([id, selector]) => {
            const element = document.querySelector(selector);
            if (element) {
                const isVisible = element.style.display !== 'none' && element.style.visibility !== 'hidden';
                console.log(`${id}: ${selector} -> ${isVisible ? 'WIDOCZNA' : 'UKRYTA'}`);
            } else {
                console.log(`${id}: ${selector} -> BRAK ELEMENTU`);
            }
        });
    }
}

// Inicjalizacja po załadowaniu DOM
document.addEventListener('DOMContentLoaded', function() {
    // Małe opóźnienie, żeby wszystkie elementy się załadowały
    setTimeout(() => {
        window.sectionVisibilityManager = new SectionVisibilityManager();
        
        // Debug w konsoli (można usunąć w produkcji)
        if (window.location.search.includes('debug=visibility')) {
            window.sectionVisibilityManager.debugSections();
        }
    }, 200);
});
