/**
 * Obsługa mapy interaktywnej - Żyrardów Poleca
 * JavaScript dla obsługi mapy interaktywnej z firmami
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja przycisku mapy
    initMapButton();
});

/**
 * Inicjalizacja przycisku mapy
 */
function initMapButton() {
    const mapButton = document.getElementById('mapButton');
    const mapButtonTop = document.getElementById('mapButtonTop');

    // Obsługa przycisku mapy na dole strony (jeśli istnieje)
    if (mapButton) {
        mapButton.addEventListener('click', function(e) {
            e.preventDefault();
            toggleMap();
        });
    }

    // Obsługa przycisku mapy obok wyszukiwarki
    if (mapButtonTop) {
        mapButtonTop.addEventListener('click', function(e) {
            e.preventDefault();
            toggleMap();
        });
    }
}

/**
 * Przełączanie widoczności mapy
 */
function toggleMap() {
    const mapContainer = document.getElementById('mapContainer');
    if (!mapContainer) {
        createMapContainer();
        return;
    }

    if (mapContainer.classList.contains('active')) {
        mapContainer.classList.remove('active');
        setTimeout(() => {
            mapContainer.style.display = 'none';
        }, 300);
    } else {
        mapContainer.style.display = 'block';
        setTimeout(() => {
            mapContainer.classList.add('active');
            initMap();
        }, 10);
    }
}

/**
 * Tworzenie kontenera mapy
 */
function createMapContainer() {
    const mapHTML = `
        <div id="mapContainer" class="map-container">
            <div class="map-header">
                <h3>Mapa firm w Żyrardowie</h3>
                <button id="closeMapButton" class="close-map-button" aria-label="Zamknij mapę">&times;</button>
            </div>
            <div class="map-filters">
                <div class="map-filter">
                    <label for="mapCategoryFilter">Kategoria:</label>
                    <select id="mapCategoryFilter">
                        <option value="all">Wszystkie kategorie</option>
                        <option value="food">Jedzenie i Gastronomia</option>
                        <option value="health">Zdrowie i Uroda</option>
                        <option value="home">Dom i Ogród</option>
                        <option value="automotive">Motoryzacja</option>
                        <option value="education">Edukacja i Nauka</option>
                        <option value="business">Usługi Biznesowe</option>
                        <option value="shopping">Zakupy i Handel</option>
                        <option value="entertainment">Rozrywka i Czas Wolny</option>
                    </select>
                </div>
                <div class="map-search">
                    <input type="text" id="mapSearchInput" placeholder="Szukaj firmy...">
                    <button id="mapSearchButton"><i class="fas fa-search"></i></button>
                </div>
            </div>
            <div id="map" class="map"></div>
            <div class="map-info">
                <p>Kliknij na marker, aby zobaczyć szczegóły firmy.</p>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', mapHTML);

    const mapContainer = document.getElementById('mapContainer');
    const closeMapButton = document.getElementById('closeMapButton');

    closeMapButton.addEventListener('click', function() {
        mapContainer.classList.remove('active');
        setTimeout(() => {
            mapContainer.style.display = 'none';
        }, 300);
    });

    // Obsługa filtrów mapy
    const mapCategoryFilter = document.getElementById('mapCategoryFilter');
    const mapSearchInput = document.getElementById('mapSearchInput');
    const mapSearchButton = document.getElementById('mapSearchButton');

    mapCategoryFilter.addEventListener('change', function() {
        filterMapMarkers();
    });

    mapSearchButton.addEventListener('click', function() {
        filterMapMarkers();
    });

    mapSearchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            filterMapMarkers();
        }
    });

    // Wyświetl mapę
    mapContainer.style.display = 'block';
    setTimeout(() => {
        mapContainer.classList.add('active');
        initMap();
    }, 10);
}

/**
 * Inicjalizacja mapy
 */
function initMap() {
    // Sprawdź, czy mapa już istnieje
    if (window.mapInstance) return;

    // Sprawdź, czy API Google Maps jest załadowane
    if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
        loadGoogleMapsAPI();
        return;
    }

    // Współrzędne centrum Żyrardowa
    const zyrardowCenter = { lat: 52.0500, lng: 20.4500 };

    // Utwórz mapę
    window.mapInstance = new google.maps.Map(document.getElementById('map'), {
        center: zyrardowCenter,
        zoom: 14,
        styles: [
            {
                "featureType": "poi",
                "elementType": "labels",
                "stylers": [
                    { "visibility": "off" }
                ]
            }
        ]
    });

    // Pobierz dane firm
    fetchCompaniesData();
}

/**
 * Ładowanie API Google Maps
 */
function loadGoogleMapsAPI() {
    const script = document.createElement('script');
    script.src = 'https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap';
    script.async = true;
    script.defer = true;
    document.head.appendChild(script);

    // Definiuj funkcję callback w globalnym zakresie
    window.initMap = initMap;
}

/**
 * Pobieranie danych firm
 */
function fetchCompaniesData() {
    // W rzeczywistej aplikacji dane byłyby pobierane z API
    // Na razie używamy przykładowych danych
    const companiesData = [
        {
            id: 'firma1',
            name: 'Restauracja Stary Młyn',
            category: 'food',
            subcategory: 'restaurants',
            address: 'ul. Młyńska 8, Żyrardów',
            lat: 52.0520,
            lng: 20.4480,
            logo: 'images/business-logo1.jpg'
        },
        {
            id: 'firma2',
            name: 'Centrum Medyczne Zdrowie',
            category: 'health',
            subcategory: 'clinics',
            address: 'ul. 1 Maja 45, Żyrardów',
            lat: 52.0490,
            lng: 20.4520,
            logo: 'images/business-logo2.jpg'
        },
        {
            id: 'firma3',
            name: 'Salon Fryzjerski Bella',
            category: 'health',
            subcategory: 'hairdressers',
            address: 'ul. Okrzei 15, Żyrardów',
            lat: 52.0510,
            lng: 20.4550,
            logo: 'images/business-logo3.jpg'
        }
    ];

    // Dodaj markery na mapie
    addMarkersToMap(companiesData);
}

/**
 * Dodawanie markerów na mapie
 */
function addMarkersToMap(companies) {
    // Tablica markerów
    window.mapMarkers = [];

    // Dodaj markery dla każdej firmy
    companies.forEach(company => {
        const marker = new google.maps.Marker({
            position: { lat: company.lat, lng: company.lng },
            map: window.mapInstance,
            title: company.name,
            icon: {
                url: 'images/map-marker.png',
                scaledSize: new google.maps.Size(30, 30)
            }
        });

        // Dodaj dane firmy do markera
        marker.company = company;

        // Dodaj obsługę kliknięcia w marker
        marker.addListener('click', function() {
            showCompanyInfoWindow(this);
        });

        // Dodaj marker do tablicy
        window.mapMarkers.push(marker);
    });
}

/**
 * Wyświetlanie okna informacyjnego firmy
 */
function showCompanyInfoWindow(marker) {
    // Zamknij poprzednie okno informacyjne
    if (window.infoWindow) {
        window.infoWindow.close();
    }

    // Utwórz nowe okno informacyjne
    window.infoWindow = new google.maps.InfoWindow({
        content: `
            <div class="map-info-window">
                <div class="info-window-header">
                    <img src="${marker.company.logo}" alt="${marker.company.name}">
                    <h4>${marker.company.name}</h4>
                </div>
                <div class="info-window-content">
                    <p><i class="fas fa-map-marker-alt"></i> ${marker.company.address}</p>
                    <a href="#" class="btn btn-sm btn-primary company-details-btn" data-company="${marker.company.id}">Zobacz więcej</a>
                </div>
            </div>
        `
    });

    // Otwórz okno informacyjne
    window.infoWindow.open(window.mapInstance, marker);

    // Dodaj obsługę kliknięcia w przycisk "Zobacz więcej"
    google.maps.event.addListenerOnce(window.infoWindow, 'domready', function() {
        const detailsButton = document.querySelector('.map-info-window .company-details-btn');
        if (detailsButton) {
            detailsButton.addEventListener('click', function(e) {
                e.preventDefault();
                const companyId = this.getAttribute('data-company');
                showCompanyLightbox(companyId);
            });
        }
    });
}

/**
 * Filtrowanie markerów na mapie
 */
function filterMapMarkers() {
    if (!window.mapMarkers) return;

    const categoryFilter = document.getElementById('mapCategoryFilter').value;
    const searchQuery = document.getElementById('mapSearchInput').value.toLowerCase();

    window.mapMarkers.forEach(marker => {
        let visible = true;

        // Filtruj według kategorii
        if (categoryFilter !== 'all' && marker.company.category !== categoryFilter) {
            visible = false;
        }

        // Filtruj według wyszukiwania
        if (searchQuery && !marker.company.name.toLowerCase().includes(searchQuery)) {
            visible = false;
        }

        // Ustaw widoczność markera
        marker.setVisible(visible);
    });
}
