/**
 * Skrypt obsługujący animację licznika statystyk
 */
document.addEventListener('DOMContentLoaded', function() {
    initStatsCounter();
});

/**
 * Inicjalizacja licznika statystyk
 */
function initStatsCounter() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    if (statNumbers.length === 0) return;
    
    // Funkcja sprawdzająca, czy element jest widoczny w oknie przeglądarki
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        // Zmodyfikowana funkcja, aby element był uznany za widoczny, gdy jest częściowo w viewport
        return (
            rect.top < (window.innerHeight || document.documentElement.clientHeight) &&
            rect.bottom > 0 &&
            rect.left < (window.innerWidth || document.documentElement.clientWidth) &&
            rect.right > 0
        );
    }
    
    // Funkcja animująca licznik
    function animateCounter(el) {
        // Upewnij się, że element ma atrybut data-count
        if (!el.hasAttribute('data-count')) {
            el.setAttribute('data-count', el.textContent || '0');
        }
        
        const target = parseInt(el.getAttribute('data-count'));
        if (isNaN(target)) return; // Zabezpieczenie przed NaN
        
        const duration = 2000; // Czas trwania animacji w milisekundach
        const step = target / (duration / 16); // 60 FPS
        let current = 0;
        
        el.classList.add('animate');
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                clearInterval(timer);
                el.textContent = target;
            } else {
                el.textContent = Math.floor(current);
            }
        }, 16);
    }
    
    // Obsługa zdarzenia przewijania strony
    let animated = false;
    
    function checkScroll() {
        if (animated) return;
        
        // Sprawdź zarówno .city-stats jak i .stats-section (dla kompatybilności)
        const statsSection = document.querySelector('.city-stats') || document.querySelector('.stats-section');
        if (!statsSection) return;
        
        if (isElementInViewport(statsSection)) {
            console.log('Animacja liczników statystyk rozpoczęta');
            statNumbers.forEach(statNumber => {
                animateCounter(statNumber);
            });
            animated = true;
            window.removeEventListener('scroll', checkScroll);
        }
    }
    
    // Dodaj obsługę zdarzenia przewijania
    window.addEventListener('scroll', checkScroll);
    
    // Sprawdź przy załadowaniu strony
    checkScroll();
    
    // Dodatkowe sprawdzenie po krótkim opóźnieniu (dla pewności)
    setTimeout(checkScroll, 500);
}
