/**
 * Żyrardów - <PERSON> Mi<PERSON>
 * Główny plik JavaScript
 * Wersja: 2.0 - Bezpieczna
 */

// Globalne funkcje bezpieczeństwa
const Security = {
    /**
     * Sanityzacja HTML - usuwa niebezpieczne tagi
     * @param {string} str - String do sanityzacji
     * @returns {string} - Bezpieczny string
     */
    sanitizeHTML: function(str) {
        if (typeof str !== 'string') return '';
        const temp = document.createElement('div');
        temp.textContent = str;
        return temp.innerHTML;
    },

    /**
     * Walidacja URL
     * @param {string} url - URL do walidacji
     * @returns {boolean} - C<PERSON> URL jest bezpieczny
     */
    isValidURL: function(url) {
        try {
            const urlObj = new URL(url);
            return ['http:', 'https:'].includes(urlObj.protocol);
        } catch {
            return false;
        }
    },

    /**
     * Walidacja selektora CSS
     * @param {string} selector - Selektor do walidacji
     * @returns {boolean} - <PERSON>zy selektor jest bezpieczny
     */
    isValidSelector: function(selector) {
        try {
            document.querySelector(selector);
            return true;
        } catch {
            return false;
        }
    }
};

document.addEventListener('DOMContentLoaded', function() {
    // Resetowanie potencjalnie uszkodzonych ustawień sekcji
    resetCorruptedSectionSettings();
    
    // Inicjalizacja slidera na stronie głównej
    initHeroSlider();

    // Obsługa wyszukiwarki
    initSearchToggle();

    // Obsługa menu mobilnego
    initMobileMenu();

    // Inicjalizacja animacji przy przewijaniu
    initScrollAnimations();

    // Inicjalizacja płynnego przewijania
    initSmoothScroll();

    // Inicjalizacja przycisku powrotu do góry
    initBackToTop();

    // Inicjalizacja powiadomienia o ciasteczkach
    initCookieNotice();

    // Lazy loading dla obrazów
    initLazyLoading();

    // Inicjalizacja odkrywania kodów rabatowych
    initCouponCodes();

    // Inicjalizacja lightbox dla ofert i kuponów
    initLightbox();

    // Inicjalizacja filtrowania kategorii firm
    initCategoryFilter();

    // Inicjalizacja dynamicznych kategorii
    initDynamicCategories();

    // Nasłuchuj zmian kategorii z admin
    window.addEventListener('categoriesUpdated', function(event) {
        console.log('Otrzymano aktualizację kategorii:', event.detail);
        loadDynamicCategories();
    });
});

/**
 * Resetowanie potencjalnie uszkodzonych ustawień sekcji
 */
function resetCorruptedSectionSettings() {
    try {
        // Sprawdź czy localStorage jest dostępny
        if (typeof localStorage === 'undefined') return;
        
        // Domyślna kolejność sekcji
        const defaultOrder = [
            'hero',
            'city-info',
            'categories',
            'coupons',
            'girard',
            'heritage',
            'about',
            'seo',
            'offers',
            'news',
            'places',
            'stats',
            'weather'
        ];
        
        // Domyślna widoczność sekcji
        const defaultVisibility = {
            'hero': true,
            'city-info': true,
            'categories': true,
            'coupons': true,
            'girard': true,
            'heritage': true,
            'about': true,
            'seo': true,
            'offers': true,
            'news': false, // Domyślnie ukryta
            'places': true,
            'stats': true,
            'weather': true
        };
        
        // Resetuj ustawienia, jeśli URL zawiera parametr reset=sections
        if (window.location.search.includes('reset=sections')) {
            localStorage.removeItem('section_order');
            localStorage.removeItem('section_visibility');
            localStorage.removeItem('temp_section_order');
            console.log('Zresetowano ustawienia sekcji');
            return;
        }
        
        // Sprawdź czy ustawienia są poprawne, jeśli nie - zresetuj
        try {
            const savedOrder = localStorage.getItem('section_order');
            const savedVisibility = localStorage.getItem('section_visibility');
            
            if (savedOrder) {
                JSON.parse(savedOrder); // Sprawdź czy można sparsować
            }
            
            if (savedVisibility) {
                JSON.parse(savedVisibility); // Sprawdź czy można sparsować
            }
        } catch (error) {
            // Jeśli wystąpił błąd parsowania, zresetuj ustawienia
            console.error('Wykryto uszkodzone ustawienia sekcji, resetowanie...', error);
            localStorage.setItem('section_order', JSON.stringify(defaultOrder));
            localStorage.setItem('section_visibility', JSON.stringify(defaultVisibility));
            localStorage.removeItem('temp_section_order');
        }
    } catch (error) {
        console.error('Błąd podczas resetowania ustawień sekcji:', error);
    }
}

/**
 * Inicjalizacja slidera na stronie głównej
 */
function initHeroSlider() {
    const slider = document.getElementById('heroSlider');
    if (!slider) return;

    const slides = slider.querySelectorAll('.hero-slide');
    const dots = document.querySelectorAll('.hero-dot');
    const prevBtn = document.getElementById('heroSliderPrev');
    const nextBtn = document.getElementById('heroSliderNext');

    let currentSlide = 0;
    let slideInterval;

    // Funkcja do pokazywania slajdu
    function showSlide(index) {
        // Ukryj wszystkie slajdy
        slides.forEach(slide => {
            slide.style.opacity = '0';
            slide.style.zIndex = '1';
        });

        // Pokaż wybrany slajd
        slides[index].style.opacity = '1';
        slides[index].style.zIndex = '2';

        // Aktualizuj aktywną kropkę
        dots.forEach((dot, i) => {
            dot.classList.toggle('active', i === index);
        });

        currentSlide = index;
    }

    // Funkcja do przejścia do następnego slajdu
    function nextSlide() {
        let index = currentSlide + 1;
        if (index >= slides.length) index = 0;
        showSlide(index);
    }

    // Funkcja do przejścia do poprzedniego slajdu
    function prevSlide() {
        let index = currentSlide - 1;
        if (index < 0) index = slides.length - 1;
        showSlide(index);
    }

    // Inicjalizacja pierwszego slajdu
    showSlide(0);

    // Obsługa przycisków
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            clearInterval(slideInterval);
            prevSlide();
            startSlideInterval();
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            clearInterval(slideInterval);
            nextSlide();
            startSlideInterval();
        });
    }

    // Obsługa kropek
    dots.forEach((dot, i) => {
        dot.addEventListener('click', () => {
            clearInterval(slideInterval);
            showSlide(i);
            startSlideInterval();
        });
    });

    // Funkcja do uruchamiania automatycznego przewijania
    function startSlideInterval() {
        slideInterval = setInterval(nextSlide, 6000);
    }

    // Uruchom automatyczne przewijanie
    startSlideInterval();

    // Zatrzymaj automatyczne przewijanie, gdy kursor jest nad sliderem
    slider.addEventListener('mouseenter', () => {
        clearInterval(slideInterval);
    });

    // Wznów automatyczne przewijanie, gdy kursor opuści slider
    slider.addEventListener('mouseleave', () => {
        startSlideInterval();
    });
}

/**
 * Inicjalizacja wyszukiwarki
 */
function initSearchToggle() {
    const searchToggle = document.querySelector('.search-toggle');
    const searchContainer = document.querySelector('.search-container');

    if (!searchToggle || !searchContainer) return;

    searchToggle.addEventListener('click', () => {
        searchContainer.style.display = searchContainer.style.display === 'block' ? 'none' : 'block';
        if (searchContainer.style.display === 'block') {
            searchContainer.querySelector('input').focus();
        }
    });

    // Obsługa formularza wyszukiwania
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const query = searchForm.querySelector('input').value.trim();
            if (query) {
                // Tutaj można dodać logikę wyszukiwania
                alert('Wyszukiwanie: ' + query);
            }
        });
    }
}

/**
 * Inicjalizacja menu mobilnego
 */
function initMobileMenu() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navLinks = document.querySelector('.nav-links');

    if (!mobileMenuBtn || !navLinks) return;

    mobileMenuBtn.addEventListener('click', () => {
        navLinks.classList.toggle('active');
        mobileMenuBtn.classList.toggle('active');
        document.body.classList.toggle('menu-open');
    });

    // Zamykanie menu po kliknięciu w link
    document.querySelectorAll('.nav-links a').forEach(link => {
        link.addEventListener('click', () => {
            navLinks.classList.remove('active');
            mobileMenuBtn.classList.remove('active');
            document.body.classList.remove('menu-open');
        });
    });

    // Zamykanie menu po kliknięciu poza menu
    document.addEventListener('click', (e) => {
        if (navLinks.classList.contains('active') &&
            !navLinks.contains(e.target) &&
            !mobileMenuBtn.contains(e.target)) {
            navLinks.classList.remove('active');
            mobileMenuBtn.classList.remove('active');
            document.body.classList.remove('menu-open');
        }
    });
}

/**
 * Inicjalizacja animacji przy przewijaniu
 */
function initScrollAnimations() {
    const observerOptions = {
        root: null,
        rootMargin: '0px',
        threshold: 0.1
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Obserwuj wszystkie sekcje
    document.querySelectorAll('section').forEach(section => {
        observer.observe(section);
    });

    // Dodanie klasy do nagłówka przy przewijaniu
    const header = document.querySelector('.site-header');
    if (header) {
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });
    }
}

/**
 * Inicjalizacja płynnego przewijania
 */
function initSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Efekt smooth scroll dla całej strony
    document.documentElement.style.scrollBehavior = 'smooth';
}

/**
 * Inicjalizacja przycisku powrotu do góry
 */
function initBackToTop() {
    const backToTopBtn = document.querySelector('.back-to-top');
    if (!backToTopBtn) return;

    window.addEventListener('scroll', () => {
        if (window.scrollY > 300) {
            backToTopBtn.classList.add('active');
        } else {
            backToTopBtn.classList.remove('active');
        }
    });

    backToTopBtn.addEventListener('click', (e) => {
        e.preventDefault();
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

/**
 * Inicjalizacja powiadomienia o ciasteczkach
 */
function initCookieNotice() {
    const cookieNotice = document.getElementById('cookieNotice');
    const acceptCookiesBtn = document.getElementById('acceptCookies');

    if (!cookieNotice || !acceptCookiesBtn) return;

    // Sprawdź, czy użytkownik już zaakceptował ciasteczka
    if (!localStorage.getItem('cookiesAccepted')) {
        cookieNotice.classList.add('active');
    }

    acceptCookiesBtn.addEventListener('click', () => {
        localStorage.setItem('cookiesAccepted', 'true');
        cookieNotice.classList.remove('active');
    });
}

/**
 * Inicjalizacja lazy loading dla obrazów
 */
function initLazyLoading() {
    if ('loading' in HTMLImageElement.prototype) {
        const images = document.querySelectorAll('img[loading="lazy"]');
        images.forEach(img => {
            if (img.dataset.src) {
                img.src = img.dataset.src;
            }
        });
    } else {
        // Fallback dla przeglądarek, które nie wspierają lazy loading
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                    }
                    observer.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[loading="lazy"]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

/**
 * Efekty hover dla elementów interaktywnych
 */
function initHoverEffects() {
    // Efekt hover dla kart
    const cards = document.querySelectorAll('.highlight-card, .attraction-card, .municipality, .business-feature, .tourism-highlight');
    cards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-5px)';
            card.style.boxShadow = 'var(--shadow-lg)';
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0)';
            card.style.boxShadow = 'var(--shadow-md)';
        });
    });

    // Efekt hover dla przycisków
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-3px)';
            button.style.boxShadow = 'var(--shadow-md)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.transform = 'translateY(0)';
            button.style.boxShadow = 'none';
        });
    });
}

// Wywołanie funkcji efektów hover po załadowaniu strony
document.addEventListener('DOMContentLoaded', initHoverEffects);

/**
 * Inicjalizacja odkrywania kodów rabatowych
 */
function initCouponCodes() {
    // Obsługa starszych kodów rabatowych (kompatybilność wsteczna)
    const couponCodeContainers = document.querySelectorAll('.coupon-code-hidden');
    couponCodeContainers.forEach(container => {
        container.addEventListener('click', function() {
            this.classList.add('active');
        });
    });

    // Obsługa nowych przycisków "Pokaż kod" dla kuponów
    const couponCodeButtons = document.querySelectorAll('.coupon-code-btn');
    couponCodeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const codeContainer = this.parentElement;
            const codeElement = codeContainer.querySelector('.coupon-code');

            if (codeElement) {
                // Ukryj przycisk
                this.style.display = 'none';

                // Pokaż kod kuponu
                codeElement.style.display = 'block';

                // Zaznacz kod kuponu do skopiowania
                const range = document.createRange();
                range.selectNode(codeElement);
                window.getSelection().removeAllRanges();
                window.getSelection().addRange(range);

                // Skopiuj kod do schowka
                try {
                    document.execCommand('copy');

                    // Dodaj informację o skopiowaniu
                    const copyInfo = document.createElement('div');
                    copyInfo.className = 'copy-info';
                    copyInfo.textContent = 'Kod skopiowany do schowka!';
                    copyInfo.style.color = 'var(--primary-color)';
                    copyInfo.style.fontSize = '0.8rem';
                    copyInfo.style.marginTop = '5px';
                    copyInfo.style.textAlign = 'center';
                    codeContainer.appendChild(copyInfo);

                    // Usuń informację po 2 sekundach
                    setTimeout(() => {
                        copyInfo.remove();
                    }, 2000);
                } catch (err) {
                    console.error('Nie udało się skopiować kodu: ', err);
                }

                window.getSelection().removeAllRanges();
            }
        });
    });
}

/**
 * Inicjalizacja lightbox dla ofert i kuponów
 */
function initLightbox() {
    // Dane ofert
    const offerData = {
        offer1: {
            title: 'Kolacja dla dwojga',
            place: 'Restauracja Pod Akacjami',
            logo: 'images/business-logo1.jpg',
            description: 'Romantyczna kolacja dla dwojga z butelką wina w cenie. Oferta ważna od poniedziałku do czwartku.',
            details: '<h4>Szczegóły oferty:</h4><p>Zapraszamy na romantyczną kolację dla dwojga w wyjątkowej atmosferze naszej restauracji. W cenie otrzymujecie:</p><ul><li>Przystawkę do wyboru z karty</li><li>Danie główne dla dwóch osób</li><li>Deser do wyboru</li><li>Butelkę wina (białe lub czerwone)</li></ul><p>Oferta dostępna od poniedziałku do czwartku w godzinach 17:00-21:00.</p>',
            oldPrice: '200 zł',
            newPrice: '140 zł',
            validity: 'Ważne do: 30.06.2024'
        },
        offer2: {
            title: 'Pakiet relaksacyjny',
            place: 'Salon Piękności Bella',
            logo: 'images/business-logo2.jpg',
            description: 'Masaż relaksacyjny całego ciała oraz zabieg na twarz. Idealny prezent lub sposób na odprężenie po ciężkim tygodniu.',
            details: '<h4>Szczegóły oferty:</h4><p>Pakiet relaksacyjny obejmuje:</p><ul><li>Masaż relaksacyjny całego ciała (60 minut)</li><li>Zabieg na twarz dobrany do typu skóry</li><li>Aromaterapię</li><li>Herbatę ziołową</li></ul><p>Zabieg trwa łącznie około 2 godzin. Konieczna wcześniejsza rezerwacja terminu.</p>',
            oldPrice: '300 zł',
            newPrice: '220 zł',
            validity: 'Ważne do: 15.07.2024'
        },
        offer3: {
            title: 'Karnet open',
            place: 'Centrum Fitness Active',
            logo: 'images/business-logo3.jpg',
            description: 'Miesięczny karnet open na siłownię i zajęcia grupowe. Dla nowych klientów pierwsza konsultacja z trenerem gratis!',
            details: '<h4>Szczegóły oferty:</h4><p>Miesięczny karnet open obejmuje:</p><ul><li>Nielimitowany dostęp do siłowni 7 dni w tygodniu</li><li>Udział we wszystkich zajęciach grupowych</li><li>Dostęp do strefy cardio</li><li>Dla nowych klientów: konsultacja z trenerem personalnym (60 minut)</li></ul><p>Karnet ważny przez 30 dni od daty aktywacji.</p>',
            oldPrice: '150 zł',
            newPrice: '99 zł',
            validity: 'Ważne do: 31.07.2024'
        }
    };

    // Dane kuponów
    const couponData = {
        coupon1: {
            title: 'Restauracja Pod Akacjami',
            logo: 'images/business-logo1.jpg',
            description: '15% zniżki na całe menu w godzinach 12:00-16:00 od poniedziałku do piątku.',
            details: '<h4>Szczegóły kuponu:</h4><p>Kupon uprawnia do 15% zniżki na całe menu restauracji Pod Akacjami w godzinach 12:00-16:00 od poniedziałku do piątku.</p><p>Warunki wykorzystania kuponu:</p><ul><li>Kupon należy okazać przed złożeniem zamówienia</li><li>Zniżka nie łączy się z innymi promocjami</li><li>Kupon nie obejmuje napojów alkoholowych</li><li>Jeden kupon można wykorzystać tylko raz</li></ul>',
            code: 'AKACJA15',
            discount: '15%',
            validity: 'Ważne do: 31.08.2024'
        },
        coupon2: {
            title: 'Salon Piękności Bella',
            logo: 'images/business-logo2.jpg',
            description: '20% zniżki na pierwszy zabieg dla nowych klientów. Nie łączy się z innymi promocjami.',
            details: '<h4>Szczegóły kuponu:</h4><p>Kupon uprawnia nowych klientów do 20% zniżki na pierwszy dowolny zabieg w Salonie Piękności Bella.</p><p>Warunki wykorzystania kuponu:</p><ul><li>Kupon przeznaczony tylko dla nowych klientów</li><li>Zniżka dotyczy dowolnego zabiegu z oferty salonu</li><li>Konieczna wcześniejsza rezerwacja terminu</li><li>Przy rezerwacji należy podać kod kuponu</li><li>Zniżka nie łączy się z innymi promocjami</li></ul>',
            code: 'BELLA20',
            discount: '20%',
            validity: 'Ważne do: 30.09.2024'
        },
        coupon3: {
            title: 'Centrum Fitness Active',
            logo: 'images/business-logo3.jpg',
            description: '25% zniżki na karnet trzymiesięczny. Dodatkowo gratis konsultacja z trenerem personalnym.',
            details: '<h4>Szczegóły kuponu:</h4><p>Kupon uprawnia do 25% zniżki na karnet trzymiesięczny w Centrum Fitness Active oraz darmowej konsultacji z trenerem personalnym (60 minut).</p><p>Warunki wykorzystania kuponu:</p><ul><li>Kupon należy okazać przy zakupie karnetu</li><li>Zniżka dotyczy tylko karnetu trzymiesięcznego</li><li>Konsultację z trenerem należy umówić w ciągu 14 dni od zakupu karnetu</li><li>Zniżka nie łączy się z innymi promocjami</li></ul>',
            code: 'ACTIVE25',
            discount: '25%',
            validity: 'Ważne do: 15.08.2024'
        }
    };

    // Obsługa kliknięcia w przycisk "Zobacz szczegóły" dla ofert
    const offerButtons = document.querySelectorAll('.js-offer-details');
    const offerLightbox = document.getElementById('offerLightbox');
    const offerLightboxBody = offerLightbox.querySelector('.lightbox-body');

    offerButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const offerId = this.getAttribute('data-offer');
            const offer = offerData[offerId];

            if (offer) {
                offerLightboxBody.innerHTML = `
                    <div class="offer-details">
                        <div class="offer-details-header">
                            <div class="offer-details-logo">
                                <img src="${offer.logo}" alt="${offer.title}">
                            </div>
                            <div class="offer-details-title">
                                <h3>${offer.title}</h3>
                                <p>${offer.place}</p>
                            </div>
                        </div>
                        <div class="offer-details-content">
                            ${offer.details}
                            <div class="offer-details-price">
                                <span class="old-price">${offer.oldPrice}</span>
                                <span class="new-price">${offer.newPrice}</span>
                            </div>
                            <div class="offer-details-validity">
                                <i class="far fa-clock"></i> ${offer.validity}
                            </div>
                        </div>
                    </div>
                `;

                offerLightbox.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        });
    });

    // Obsługa kliknięcia w przycisk "Zobacz szczegóły" dla kuponów
    const couponButtons = document.querySelectorAll('.js-coupon-details');
    const couponLightbox = document.getElementById('couponLightbox');
    const couponLightboxBody = couponLightbox.querySelector('.lightbox-body');

    couponButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const couponId = this.getAttribute('data-coupon');
            const coupon = couponData[couponId];

            if (coupon) {
                couponLightboxBody.innerHTML = `
                    <div class="coupon-details">
                        <div class="coupon-details-header">
                            <div class="coupon-details-logo">
                                <img src="${coupon.logo}" alt="${coupon.title}">
                            </div>
                            <div class="coupon-details-title">
                                <h3>${coupon.title}</h3>
                                <p>Rabat: ${coupon.discount}</p>
                            </div>
                        </div>
                        <div class="coupon-details-content">
                            ${coupon.details}
                            <div class="coupon-details-code">
                                <span>${coupon.code}</span>
                            </div>
                            <div class="coupon-details-validity">
                                <i class="far fa-clock"></i> ${coupon.validity}
                            </div>
                        </div>
                    </div>
                `;

                couponLightbox.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        });
    });

    // Obsługa zamykania lightbox
    const lightboxes = document.querySelectorAll('.lightbox');

    lightboxes.forEach(lightbox => {
        // Zamykanie przez przycisk X
        const closeButtons = lightbox.querySelectorAll('.lightbox-close, .lightbox-close-btn');
        closeButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            });
        });

        // Zamykanie przez kliknięcie w tło lightboxa (ale nie w content)
        lightbox.addEventListener('click', (e) => {
            if (e.target === lightbox) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        });

        // Zapobieganie zamykaniu przy kliknięciu w content
        const lightboxContent = lightbox.querySelector('.lightbox-content');
        if (lightboxContent) {
            lightboxContent.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }
    });

    // Zamykanie przez klawisz ESC - globalne
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            const activeLightbox = document.querySelector('.lightbox.active');
            if (activeLightbox) {
                activeLightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        }
    });
}

/**
 * Inicjalizacja filtrowania kategorii firm
 */
function initCategoryFilter() {
    const categoryButtons = document.querySelectorAll('.category-btn');
    const subcategoriesContainers = document.querySelectorAll('.subcategories');
    const subcategoryButtons = document.querySelectorAll('.subcategory-btn');

    // Obsługa kliknięcia w przycisk kategorii
    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Usunięcie klasy active ze wszystkich przycisków kategorii
            categoryButtons.forEach(btn => btn.classList.remove('active'));

            // Dodanie klasy active do klikniętego przycisku
            this.classList.add('active');

            // Pobranie kategorii z atrybutu data-category
            const category = this.getAttribute('data-category');

            // Ukrycie wszystkich kontenerów podkategorii
            subcategoriesContainers.forEach(container => {
                container.classList.remove('active');
            });

            // Jeśli wybrano kategorię inną niż "wszystkie", pokaż odpowiedni kontener podkategorii
            if (category !== 'all') {
                const subcategoriesContainer = document.querySelector(`.subcategories[data-parent="${category}"]`);
                if (subcategoriesContainer) {
                    subcategoriesContainer.classList.add('active');
                }
            }

            // Filtrowanie firm na podstawie wybranej kategorii
            filterCompanies(category, null);
        });
    });

    // Obsługa kliknięcia w przycisk podkategorii
    subcategoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Pobranie aktywnej kategorii
            const activeCategory = document.querySelector('.category-btn.active').getAttribute('data-category');

            // Usunięcie klasy active ze wszystkich przycisków podkategorii w aktywnym kontenerze
            const activeSubcategoriesContainer = document.querySelector(`.subcategories[data-parent="${activeCategory}"]`);
            if (activeSubcategoriesContainer) {
                const buttons = activeSubcategoriesContainer.querySelectorAll('.subcategory-btn');
                buttons.forEach(btn => btn.classList.remove('active'));
            }

            // Dodanie klasy active do klikniętego przycisku
            this.classList.add('active');

            // Pobranie podkategorii z atrybutu data-subcategory
            const subcategory = this.getAttribute('data-subcategory');

            // Filtrowanie firm na podstawie wybranej kategorii i podkategorii
            filterCompanies(activeCategory, subcategory);
        });
    });

    /**
     * Filtrowanie firm na podstawie kategorii i podkategorii
     * @param {string} category - Kategoria
     * @param {string|null} subcategory - Podkategoria (null jeśli nie wybrano)
     */
    function filterCompanies(category, subcategory) {
        // W rzeczywistej implementacji tutaj byłoby filtrowanie firm
        // Na potrzeby demonstracji wyświetlamy komunikat w konsoli
        console.log(`Filtrowanie firm: kategoria=${category}, podkategoria=${subcategory || 'wszystkie'}`);

        // Tutaj można dodać kod do filtrowania firm na podstawie kategorii i podkategorii
        // Na przykład poprzez ukrywanie/pokazywanie odpowiednich elementów DOM
        // lub pobieranie danych z API
    }
}

/**
 * Inicjalizacja dynamicznych kategorii
 */
function initDynamicCategories() {
    console.log('Inicjalizacja dynamicznych kategorii...');
    loadDynamicCategories();
}

/**
 * Ładowanie dynamicznych kategorii z localStorage
 */
function loadDynamicCategories() {
    try {
        const frontendCategories = localStorage.getItem('zyrardow_frontend_categories');

        if (frontendCategories) {
            const categories = JSON.parse(frontendCategories);
            console.log('Załadowano kategorie z localStorage:', categories);
            renderCategories(categories);
        } else {
            console.log('Brak kategorii w localStorage, używam domyślnych');
            // Jeśli nie ma kategorii w localStorage, pozostaw domyślne z HTML
        }
    } catch (error) {
        console.error('Błąd podczas ładowania kategorii:', error);
    }
}

/**
 * Renderowanie kategorii na stronie głównej
 */
function renderCategories(categories) {
    const categoriesList = document.querySelector('.categories-list');
    if (!categoriesList) {
        console.warn('Nie znaleziono kontenera .categories-list');
        return;
    }

    // Wyczyść istniejące kategorie
    categoriesList.innerHTML = '';

    // Renderuj każdą kategorię
    categories.forEach((category, index) => {
        const categoryHTML = `
            <li class="category-item" data-category="${category.dataCategory}">
                <div class="category-header">
                    <i class="fas ${category.icon}"></i>
                    <span>${category.name}</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <ul class="subcategories-list">
                    ${category.subcategories.map(sub => `
                        <li><a href="#" data-category="${category.dataCategory}" data-subcategory="${sub.dataSubcategory}">${sub.name}</a></li>
                    `).join('')}
                </ul>
            </li>
        `;

        categoriesList.insertAdjacentHTML('beforeend', categoryHTML);
    });

    // Ponownie zainicjalizuj obsługę kategorii
    reinitializeCategoryHandlers();

    console.log('Kategorie zostały zaktualizowane na stronie głównej');
}

/**
 * Ponowna inicjalizacja obsługi kategorii
 */
function reinitializeCategoryHandlers() {
    // Obsługa rozwijania/zwijania kategorii
    const categoryHeaders = document.querySelectorAll('.category-header');
    categoryHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const categoryItem = this.parentElement;
            const subcategoriesList = categoryItem.querySelector('.subcategories-list');
            const chevron = this.querySelector('.fa-chevron-down');

            // Toggle aktywnej kategorii
            categoryItem.classList.toggle('active');

            // Animacja chevron
            if (categoryItem.classList.contains('active')) {
                chevron.style.transform = 'rotate(180deg)';
                subcategoriesList.style.maxHeight = subcategoriesList.scrollHeight + 'px';
            } else {
                chevron.style.transform = 'rotate(0deg)';
                subcategoriesList.style.maxHeight = '0';
            }
        });
    });

    // Obsługa kliknięcia w podkategorie
    const subcategoryLinks = document.querySelectorAll('.subcategories-list a');
    subcategoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const category = this.getAttribute('data-category');
            const subcategory = this.getAttribute('data-subcategory');

            // Usuń aktywną klasę ze wszystkich linków
            subcategoryLinks.forEach(l => l.classList.remove('active'));

            // Dodaj aktywną klasę do klikniętego linku
            this.classList.add('active');

            // Filtruj firmy (tutaj można dodać logikę filtrowania)
            console.log(`Wybrano kategorię: ${category}, podkategorię: ${subcategory}`);

            // Aktualizuj widok firm (placeholder)
            updateCompaniesView(category, subcategory);
        });
    });
}

/**
 * Aktualizacja widoku firm na podstawie wybranej kategorii
 */
function updateCompaniesView(category, subcategory) {
    const categoryContent = document.getElementById('categoryContent');
    if (!categoryContent) return;

    // Placeholder - tutaj można dodać logikę ładowania firm z danej kategorii
    const categoryName = getCategoryDisplayName(category, subcategory);

    categoryContent.innerHTML = `
        <div class="top-companies-section">
            <h3>TOP 3 polecane firmy - ${categoryName}</h3>
            <div class="companies-loading">
                <p>Ładowanie firm z kategorii: ${categoryName}...</p>
                <div class="loading-spinner"></div>
            </div>

            <div class="add-company-promo">
                <div class="promo-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="promo-content">
                    <h4>Dodaj swoją firmę do katalogu</h4>
                    <p>Zwiększ widoczność swojej firmy w kategorii "${categoryName}" i pozyskaj nowych klientów.</p>
                    <a href="dodaj-firme.html" class="btn btn-primary">Dodaj firmę</a>
                </div>
            </div>
        </div>
    `;

    // Symulacja ładowania firm (w rzeczywistej implementacji byłoby to API call)
    setTimeout(() => {
        loadCompaniesForCategory(category, subcategory);
    }, 1000);
}

/**
 * Pobieranie nazwy kategorii do wyświetlenia
 */
function getCategoryDisplayName(category, subcategory) {
    const categoryNames = {
        'jedzenieigastronomia': 'Jedzenie i Gastronomia',
        'zdrowieeuroda': 'Zdrowie i Uroda',
        'domogrod': 'Dom i Ogród',
        'motoryzacja': 'Motoryzacja',
        'edukacjanauka': 'Edukacja i Nauka',
        'uslugibiznesowe': 'Usługi Biznesowe',
        'zakupyhandel': 'Zakupy i Handel',
        'rozrywkaczaswolny': 'Rozrywka i Czas Wolny'
    };

    const subcategoryNames = {
        'restauracje': 'Restauracje',
        'pizzerie': 'Pizzerie',
        'fastfood': 'Fast Food',
        'kawiarnieiherbaciarnie': 'Kawiarnie i herbaciarnie',
        'cukiernieipiekarnie': 'Cukiernie i piekarnie',
        'przychodniegabinety': 'Przychodnie i gabinety',
        'apteki': 'Apteki',
        'salonykosmetyczne': 'Salony kosmetyczne'
    };

    let displayName = categoryNames[category] || category;
    if (subcategory && subcategoryNames[subcategory]) {
        displayName += ` - ${subcategoryNames[subcategory]}`;
    }

    return displayName;
}

/**
 * Ładowanie firm dla wybranej kategorii (placeholder)
 */
function loadCompaniesForCategory(category, subcategory) {
    const categoryContent = document.getElementById('categoryContent');
    if (!categoryContent) return;

    const categoryName = getCategoryDisplayName(category, subcategory);

    // Placeholder firm - w rzeczywistej implementacji byłoby to pobierane z API
    const placeholderCompanies = [
        {
            rank: 'TOP 1',
            name: `Najlepsza firma - ${categoryName}`,
            logo: 'images/business-logo1.jpg',
            tags: [categoryName.split(' - ')[0], subcategory || 'Usługi'],
            description: `Najwyżej oceniana firma w kategorii ${categoryName} w Żyrardowie.`,
            address: 'ul. Przykładowa 1, Żyrardów',
            phone: '+**************'
        },
        {
            rank: 'TOP 2',
            name: `Druga najlepsza - ${categoryName}`,
            logo: 'images/business-logo2.jpg',
            tags: [categoryName.split(' - ')[0], subcategory || 'Usługi'],
            description: `Druga w rankingu firma w kategorii ${categoryName} w Żyrardowie.`,
            address: 'ul. Przykładowa 2, Żyrardów',
            phone: '+**************'
        },
        {
            rank: 'TOP 3',
            name: `Trzecia najlepsza - ${categoryName}`,
            logo: 'images/business-logo3.jpg',
            tags: [categoryName.split(' - ')[0], subcategory || 'Usługi'],
            description: `Trzecia w rankingu firma w kategorii ${categoryName} w Żyrardowie.`,
            address: 'ul. Przykładowa 3, Żyrardów',
            phone: '+**************'
        }
    ];

    const companiesHTML = placeholderCompanies.map(company => `
        <div class="top-company-card">
            <div class="company-badge">${company.rank}</div>
            <div class="company-logo">
                <img src="${company.logo}" alt="Logo ${company.name}">
            </div>
            <div class="company-info">
                <h4>${company.name}</h4>
                <div class="company-tags">
                    ${company.tags.map(tag => `<span class="company-tag">${tag}</span>`).join('')}
                </div>
                <p class="company-description">${company.description}</p>
                <div class="company-contact">
                    <p><i class="fas fa-map-marker-alt"></i> ${company.address}</p>
                    <p><i class="fas fa-phone"></i> ${company.phone}</p>
                </div>
                <a href="#" class="btn btn-sm btn-outline">Zobacz więcej</a>
            </div>
        </div>
    `).join('');

    categoryContent.innerHTML = `
        <div class="top-companies-section">
            <h3>TOP 3 polecane firmy - ${categoryName}</h3>
            <div class="top-companies-list">
                ${companiesHTML}
            </div>

            <div class="add-company-promo">
                <div class="promo-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="promo-content">
                    <h4>Dodaj swoją firmę do katalogu</h4>
                    <p>Zwiększ widoczność swojej firmy w kategorii "${categoryName}" i pozyskaj nowych klientów.</p>
                    <a href="dodaj-firme.html" class="btn btn-primary">Dodaj firmę</a>
                </div>
            </div>
        </div>
    `;
}