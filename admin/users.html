<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zarządzanie użytkownikami - Panel Administracyjny - Żyrardów Poleca</title>
    <meta name="description" content="Panel administracyjny portalu Żyrardów Poleca - zarządzanie użytkownikami">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/users.css">
    <link rel="icon" href="../favicon.ico" type="image/x-icon">
</head>
<body class="users-page">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.png" alt="Żyrardów Poleca Logo" class="sidebar-logo">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="Admin Avatar">
                </div>
                <div class="user-info">
                    <h3>Administrator</h3>
                    <p><EMAIL></p>
                </div>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="companies.html">
                            <i class="fas fa-building"></i>
                            <span>Firmy</span>
                        </a>
                    </li>
                    <li>
                        <a href="offers.html">
                            <i class="fas fa-tag"></i>
                            <span>Oferty</span>
                        </a>
                    </li>
                    <li>
                        <a href="coupons.html">
                            <i class="fas fa-ticket-alt"></i>
                            <span>Kupony</span>
                        </a>
                    </li>
                    <li>
                        <a href="categories.html">
                            <i class="fas fa-list"></i>
                            <span>Kategorie</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Użytkownicy</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Ustawienia</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <button id="logoutBtn" class="btn-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Wyloguj</span>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-title">
                    <h1>Zarządzanie użytkownikami</h1>
                    <p>Dodawaj, edytuj i usuwaj użytkowników systemu</p>
                </div>
                <div class="header-actions">
                    <div class="search-box">
                        <input type="text" id="user-search" placeholder="Szukaj użytkownika...">
                        <button><i class="fas fa-search"></i></button>
                    </div>
                    <button class="btn btn-primary" id="add-user-btn">
                        <i class="fas fa-plus"></i> Dodaj użytkownika
                    </button>
                </div>
            </header>

            <div class="content-body">
                <!-- Filters -->
                <div class="filters-container">
                    <div class="filters">
                        <div class="filter-group">
                            <label for="role-filter">Rola:</label>
                            <select id="role-filter" class="form-control">
                                <option value="">Wszystkie role</option>
                                <option value="admin">Administrator</option>
                                <option value="editor">Redaktor</option>
                                <option value="business">Właściciel firmy</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="status-filter">Status:</label>
                            <select id="status-filter" class="form-control">
                                <option value="">Wszystkie statusy</option>
                                <option value="active">Aktywny</option>
                                <option value="inactive">Nieaktywny</option>
                                <option value="pending">Oczekujący</option>
                            </select>
                        </div>
                    </div>
                    <button id="reset-filters" class="btn btn-outline">
                        <i class="fas fa-redo"></i> Resetuj filtry
                    </button>
                </div>

                <!-- Users Table -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="select-all">
                                </th>
                                <th>Imię i nazwisko</th>
                                <th>Email</th>
                                <th>Rola</th>
                                <th>Status</th>
                                <th>Data rejestracji</th>
                                <th>Akcje</th>
                            </tr>
                        </thead>
                        <tbody id="users-table-body">
                            <tr>
                                <td><input type="checkbox" class="user-select"></td>
                                <td>Jan Kowalski</td>
                                <td><EMAIL></td>
                                <td><span class="role-badge admin">Administrator</span></td>
                                <td><span class="status-badge active">Aktywny</span></td>
                                <td>01.01.2023</td>
                                <td class="actions">
                                    <button class="btn-icon" title="Edytuj"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon" title="Resetuj hasło"><i class="fas fa-key"></i></button>
                                    <button class="btn-icon delete" title="Usuń"><i class="fas fa-trash-alt"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" class="user-select"></td>
                                <td>Anna Nowak</td>
                                <td><EMAIL></td>
                                <td><span class="role-badge editor">Redaktor</span></td>
                                <td><span class="status-badge active">Aktywny</span></td>
                                <td>15.02.2023</td>
                                <td class="actions">
                                    <button class="btn-icon" title="Edytuj"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon" title="Resetuj hasło"><i class="fas fa-key"></i></button>
                                    <button class="btn-icon delete" title="Usuń"><i class="fas fa-trash-alt"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" class="user-select"></td>
                                <td>Piotr Wiśniewski</td>
                                <td><EMAIL></td>
                                <td><span class="role-badge business">Właściciel firmy</span></td>
                                <td><span class="status-badge inactive">Nieaktywny</span></td>
                                <td>10.03.2023</td>
                                <td class="actions">
                                    <button class="btn-icon" title="Edytuj"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon" title="Resetuj hasło"><i class="fas fa-key"></i></button>
                                    <button class="btn-icon delete" title="Usuń"><i class="fas fa-trash-alt"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" class="user-select"></td>
                                <td>Magdalena Kowalczyk</td>
                                <td><EMAIL></td>
                                <td><span class="role-badge business">Właściciel firmy</span></td>
                                <td><span class="status-badge pending">Oczekujący</span></td>
                                <td>05.04.2023</td>
                                <td class="actions">
                                    <button class="btn-icon" title="Edytuj"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon" title="Resetuj hasło"><i class="fas fa-key"></i></button>
                                    <button class="btn-icon delete" title="Usuń"><i class="fas fa-trash-alt"></i></button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Bulk Actions -->
                <div class="bulk-actions">
                    <div class="bulk-actions-select">
                        <span>Z zaznaczonymi:</span>
                        <select id="bulk-action" class="form-control">
                            <option value="">Wybierz akcję</option>
                            <option value="activate">Aktywuj</option>
                            <option value="deactivate">Dezaktywuj</option>
                            <option value="delete">Usuń</option>
                        </select>
                        <button id="apply-bulk-action" class="btn btn-outline">Zastosuj</button>
                    </div>
                    <div class="pagination">
                        <button class="btn-page" disabled><i class="fas fa-chevron-left"></i></button>
                        <button class="btn-page active">1</button>
                        <button class="btn-page">2</button>
                        <button class="btn-page"><i class="fas fa-chevron-right"></i></button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Add/Edit User Modal -->
    <div class="modal" id="userModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Dodaj użytkownika</h2>
                <button class="modal-close"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <form id="user-form">
                    <input type="hidden" id="user-id">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="user-first-name">Imię</label>
                            <input type="text" id="user-first-name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="user-last-name">Nazwisko</label>
                            <input type="text" id="user-last-name" class="form-control" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="user-email">Email</label>
                        <input type="email" id="user-email" class="form-control" required>
                    </div>
                    <div class="form-group password-group">
                        <label for="user-password">Hasło</label>
                        <div class="password-input">
                            <input type="password" id="user-password" class="form-control" required>
                            <button type="button" class="toggle-password"><i class="fas fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="user-role">Rola</label>
                        <select id="user-role" class="form-control" required>
                            <option value="">Wybierz rolę</option>
                            <option value="admin">Administrator</option>
                            <option value="editor">Redaktor</option>
                            <option value="business">Właściciel firmy</option>
                        </select>
                    </div>
                    <div class="form-group business-fields" style="display: none;">
                        <label for="user-company">Firma</label>
                        <select id="user-company" class="form-control">
                            <option value="">Wybierz firmę</option>
                            <option value="1">Restauracja Pod Akacjami</option>
                            <option value="2">Salon Fryzjerski Bella</option>
                            <option value="3">Sklep Sportowy Active</option>
                            <option value="4">Kancelaria Prawna Paragraf</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Status</label>
                        <div class="radio-group">
                            <div class="radio-option">
                                <input type="radio" id="status-active" name="user-status" value="active" checked>
                                <label for="status-active">Aktywny</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="status-inactive" name="user-status" value="inactive">
                                <label for="status-inactive">Nieaktywny</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="status-pending" name="user-status" value="pending">
                                <label for="status-pending">Oczekujący</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline modal-cancel">Anuluj</button>
                        <button type="submit" class="btn btn-primary">Zapisz</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Reset Password Modal -->
    <div class="modal" id="resetPasswordModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Resetuj hasło</h2>
                <button class="modal-close"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <form id="reset-password-form">
                    <input type="hidden" id="reset-user-id">
                    <p>Resetowanie hasła dla użytkownika: <strong id="reset-user-name"></strong></p>
                    <div class="form-group">
                        <label for="new-password">Nowe hasło</label>
                        <div class="password-input">
                            <input type="password" id="new-password" class="form-control" required>
                            <button type="button" class="toggle-password"><i class="fas fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="confirm-password">Potwierdź hasło</label>
                        <div class="password-input">
                            <input type="password" id="confirm-password" class="form-control" required>
                            <button type="button" class="toggle-password"><i class="fas fa-eye"></i></button>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="checkbox-option">
                            <input type="checkbox" id="send-email" checked>
                            <label for="send-email">Wyślij email z nowym hasłem</label>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline modal-cancel">Anuluj</button>
                        <button type="submit" class="btn btn-primary">Resetuj hasło</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/admin.js"></script>
    <script src="js/users.js"></script>
</body>
</html>
