[{"id": 1, "name": "Gastronomia", "slug": "gastronomia", "icon": "fas fa-utensils", "color": "#e74c3c", "parentId": null, "sortOrder": 1, "description": "Restauracje, kawiarnie, bary, catering"}, {"id": 2, "name": "Zdrowie i uroda", "slug": "zdrowie-uroda", "icon": "fas fa-heartbeat", "color": "#27ae60", "parentId": null, "sortOrder": 2, "description": "Prz<PERSON><PERSON><PERSON>, gabinety, salony kosmetyczne"}, {"id": 3, "name": "<PERSON><PERSON><PERSON>", "slug": "u<PERSON>a", "icon": "fas fa-cut", "color": "#9b59b6", "parentId": null, "sortOrder": 3, "description": "Salony fryzjerskie, kosmetyczne, SPA"}, {"id": 4, "name": "Sport i rekreacja", "slug": "sport-rekrea<PERSON>ja", "icon": "fas fa-dumbbell", "color": "#3498db", "parentId": null, "sortOrder": 4, "description": "Sił<PERSON>ie, kluby fitness, sklepy sportowe"}, {"id": 5, "name": "Motoryzacja", "slug": "motoryzacja", "icon": "fas fa-car", "color": "#f39c12", "parentId": null, "sortOrder": 5, "description": "Warsztaty, komisy, stacje paliw"}, {"id": 6, "name": "Usługi", "slug": "uslugi", "icon": "fas fa-tools", "color": "#34495e", "parentId": null, "sortOrder": 6, "description": "Różne usługi dla mieszkańców"}, {"id": 7, "name": "Zakupy", "slug": "zakupy", "icon": "fas fa-shopping-bag", "color": "#e67e22", "parentId": null, "sortOrder": 7, "description": "<PERSON><PERSON><PERSON>, centra handlowe, butiki"}, {"id": 8, "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON><PERSON>", "icon": "fas fa-graduation-cap", "color": "#2c3e50", "parentId": null, "sortOrder": 8, "description": "Szkoły, kursy, korepetycje"}]