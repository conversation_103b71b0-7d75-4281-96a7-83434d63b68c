<?php
/**
 * Konfiguracja bazy danych SQLite
 * Żyrardów Poleca - Panel Administracyjny
 */

// Ścieżka do bazy danych (poza katalogiem www)
$dbPath = __DIR__ . '/../../data/zyrardow_poleca.db';

// Utwórz katalog jeśli nie istnieje
$dbDir = dirname($dbPath);
if (!is_dir($dbDir)) {
    mkdir($dbDir, 0755, true);
}

try {
    // Połączenie z SQLite
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    // Utwórz tabele jeśli nie istnieją
    createTables($pdo);
    
} catch (PDOException $e) {
    die("Błąd połączenia z bazą danych: " . $e->getMessage());
}

/**
 * Tworzenie tabel w bazie danych
 */
function createTables($pdo) {
    // Tabela kategorii
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            slug TEXT UNIQUE NOT NULL,
            icon TEXT,
            color TEXT,
            parentId INTEGER,
            sortOrder INTEGER DEFAULT 0,
            description TEXT,
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");
    
    // Tabela firm
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS companies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            slug TEXT UNIQUE NOT NULL,
            description TEXT,
            categoryId INTEGER,
            subcategoryId INTEGER,
            address TEXT,
            postalCode TEXT,
            city TEXT DEFAULT 'Żyrardów',
            phone TEXT,
            email TEXT,
            website TEXT,
            logo TEXT,
            status TEXT DEFAULT 'pending',
            topPosition INTEGER,
            publishedAt DATETIME,
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (categoryId) REFERENCES categories(id)
        )
    ");
    
    // Tabela kuponów
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS coupons (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            code TEXT UNIQUE NOT NULL,
            discount_type TEXT NOT NULL,
            discount_value REAL NOT NULL,
            validUntil DATE,
            company_id INTEGER,
            company_name TEXT,
            company_logo TEXT,
            status TEXT DEFAULT 'active',
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (company_id) REFERENCES companies(id)
        )
    ");
    
    // Tabela ofert
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS offers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            price REAL,
            originalPrice REAL,
            validUntil DATE,
            company_id INTEGER,
            company_name TEXT,
            company_logo TEXT,
            status TEXT DEFAULT 'active',
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (company_id) REFERENCES companies(id)
        )
    ");
    
    // Wstaw przykładowe kategorie jeśli tabela jest pusta
    $count = $pdo->query("SELECT COUNT(*) FROM categories")->fetchColumn();
    if ($count == 0) {
        insertSampleCategories($pdo);
    }
}

/**
 * Wstaw przykładowe kategorie
 */
function insertSampleCategories($pdo) {
    $categories = [
        ['Gastronomia', 'gastronomia', 'fas fa-utensils', '#e74c3c', 1],
        ['Zdrowie i uroda', 'zdrowie-uroda', 'fas fa-heartbeat', '#27ae60', 2],
        ['Uroda', 'uroda', 'fas fa-cut', '#9b59b6', 3],
        ['Sport i rekreacja', 'sport-rekreacja', 'fas fa-dumbbell', '#3498db', 4],
        ['Motoryzacja', 'motoryzacja', 'fas fa-car', '#f39c12', 5],
        ['Usługi', 'uslugi', 'fas fa-tools', '#34495e', 6],
        ['Zakupy', 'zakupy', 'fas fa-shopping-bag', '#e67e22', 7],
        ['Edukacja', 'edukacja', 'fas fa-graduation-cap', '#2c3e50', 8]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO categories (name, slug, icon, color, sortOrder, description) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($categories as $cat) {
        $stmt->execute([
            $cat[0], $cat[1], $cat[2], $cat[3], $cat[4], 
            ucfirst($cat[1]) . ' - kategoria firm'
        ]);
    }
}

return $pdo;
?>
