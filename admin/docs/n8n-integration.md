# Integracja z N8n - Automatyczne generowanie wiadomości lokalnych

## Przegląd

Ten dokument opisuje, jak skonfigurować automatyczne generowanie i publikowanie wiadomości lokalnych dla strony Żyrardów Poleca przy użyciu N8n i AI.

## Architektura

```
AI (OpenAI/Claude) → N8n Workflow → Webhook API → Strona Żyrardów
```

## Konfiguracja N8n

### 1. Workflow Overview

Workflow składa się z następujących kroków:
1. **Trigger** - Cron job (np. codziennie o 9:00)
2. **AI Node** - Generowanie treści przez AI
3. **Data Processing** - Przetwarzanie odpowiedzi AI
4. **Webhook** - Wysłanie do API strony
5. **Notification** - Powiadomienie o sukcesie/błędzie

### 2. Konfiguracja AI Node

#### OpenAI Configuration:
```json
{
  "model": "gpt-4",
  "messages": [
    {
      "role": "system",
      "content": "Jesteś lokalnym dziennikarzem piszącym dla mieszkańców Żyrardowa. Używaj szablonu JSON do generowania wiadomości lokalnych."
    },
    {
      "role": "user", 
      "content": "Używając szablonu z pliku ai-news-template.json, wygeneruj wiadomość lokalną o [TEMAT]. Zwróć wynik w formacie JSON gotowym do importu."
    }
  ],
  "max_tokens": 1500,
  "temperature": 0.7
}
```

#### Claude Configuration:
```json
{
  "model": "claude-3-sonnet-20240229",
  "max_tokens": 1500,
  "messages": [
    {
      "role": "user",
      "content": "Wygeneruj wiadomość lokalną dla Żyrardowa używając załączonego szablonu JSON. Temat: [TEMAT]"
    }
  ]
}
```

### 3. Data Processing Node

```javascript
// Przetwarzanie odpowiedzi AI
const aiResponse = $input.first().json;
let newsData;

try {
  // Jeśli AI zwróciło JSON jako string
  if (typeof aiResponse.content === 'string') {
    newsData = JSON.parse(aiResponse.content);
  } else {
    newsData = aiResponse.content;
  }
  
  // Walidacja wymaganych pól
  const required = ['title', 'category', 'excerpt', 'content'];
  for (const field of required) {
    if (!newsData[field]) {
      throw new Error(`Brak wymaganego pola: ${field}`);
    }
  }
  
  // Dodaj metadane
  newsData.publishDate = new Date().toISOString();
  newsData.status = 'published';
  
  return [{ json: newsData }];
  
} catch (error) {
  throw new Error(`Błąd przetwarzania danych AI: ${error.message}`);
}
```

### 4. Webhook Node Configuration

```json
{
  "url": "https://zyrardow.poleca.to/admin/api/news-webhook.php",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-API-Key": "zyrardow_api_2024_secure_key"
  },
  "body": {
    "api_key": "zyrardow_api_2024_secure_key",
    "news": "={{ $json }}"
  }
}
```

## Przykładowe tematy dla AI

### Tematy lokalne:
- "Nowa inwestycja w infrastrukturę Żyrardowa"
- "Otwarcie nowego sklepu/restauracji w centrum"
- "Remont ulicy [nazwa] w Żyrardowie"
- "Nowe nasadzenia w parkach miejskich"
- "Modernizacja oświetlenia LED"

### Wydarzenia:
- "Festiwal kultury w Żyrardowie"
- "Dni Żyrardowa 2024"
- "Koncert w parku miejskim"
- "Wystawa w muzeum"
- "Zawody sportowe"

### Biznes lokalny:
- "Sukces lokalnego przedsiębiorcy"
- "Nowe miejsca pracy w Żyrardowie"
- "Wsparcie dla małych firm"
- "Lokalny rynek i produkty regionalne"

## Workflow N8n - JSON Export

```json
{
  "name": "Żyrardów News Generator",
  "nodes": [
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "field": "cronExpression",
              "expression": "0 9 * * *"
            }
          ]
        }
      },
      "name": "Daily Trigger",
      "type": "n8n-nodes-base.cron",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "resource": "chat",
        "operation": "create",
        "model": "gpt-4",
        "messages": {
          "values": [
            {
              "role": "system",
              "content": "Jesteś lokalnym dziennikarzem dla Żyrardowa. Generuj wiadomości lokalne w formacie JSON."
            },
            {
              "role": "user",
              "content": "Wygeneruj wiadomość lokalną o nowej inwestycji w Żyrardowie używając szablonu JSON."
            }
          ]
        },
        "maxTokens": 1500,
        "temperature": 0.7
      },
      "name": "OpenAI",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [460, 300]
    },
    {
      "parameters": {
        "jsCode": "// Kod przetwarzania danych (jak wyżej)"
      },
      "name": "Process AI Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [680, 300]
    },
    {
      "parameters": {
        "url": "https://zyrardow.poleca.to/admin/api/news-webhook.php",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "X-API-Key",
              "value": "zyrardow_api_2024_secure_key"
            }
          ]
        },
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "api_key",
              "value": "zyrardow_api_2024_secure_key"
            },
            {
              "name": "news",
              "value": "={{ $json }}"
            }
          ]
        }
      },
      "name": "Send to Website",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [900, 300]
    }
  ],
  "connections": {
    "Daily Trigger": {
      "main": [
        [
          {
            "node": "OpenAI",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "OpenAI": {
      "main": [
        [
          {
            "node": "Process AI Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Process AI Response": {
      "main": [
        [
          {
            "node": "Send to Website",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  }
}
```

## Bezpieczeństwo

1. **API Key**: Zmień domyślny klucz API na bezpieczny
2. **IP Whitelist**: Dodaj IP serwera N8n do listy dozwolonych
3. **Rate Limiting**: Ogranicz liczbę żądań na godzinę
4. **Logging**: Monitoruj logi API pod kątem podejrzanej aktywności

## Testowanie

### Test lokalny:
```bash
curl -X POST https://zyrardow.poleca.to/admin/api/news-webhook.php \
  -H "Content-Type: application/json" \
  -H "X-API-Key: zyrardow_api_2024_secure_key" \
  -d '{
    "api_key": "zyrardow_api_2024_secure_key",
    "news": {
      "title": "Test wiadomości z N8n",
      "category": "local",
      "excerpt": "To jest testowa wiadomość",
      "content": "Pełna treść testowej wiadomości dla Żyrardowa."
    }
  }'
```

## Monitoring

1. **Logi API**: `/admin/logs/news-api.log`
2. **Status wiadomości**: Panel administracyjny → Wiadomości
3. **N8n Execution History**: Sprawdzaj historię wykonań workflow

## Rozwiązywanie problemów

### Częste błędy:
1. **401 Unauthorized**: Sprawdź API key
2. **400 Bad Request**: Waliduj format JSON
3. **500 Server Error**: Sprawdź logi serwera
4. **Timeout**: Zwiększ timeout w N8n

### Debug:
1. Włącz szczegółowe logowanie w API
2. Sprawdź execution logs w N8n
3. Testuj każdy krok workflow osobno
