<?php
/**
 * API dla zarządzania kuponami
 * Żyrardów Poleca - Panel Administracyjny
 * System SQLite
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Obsługa preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Połączenie z bazą danych
require_once __DIR__ . '/../config/database.php';

// Routing
$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';

switch ($method) {
    case 'GET':
        if ($path === 'active') {
            getActiveCoupons();
        } else {
            getAllCoupons();
        }
        break;
    
    case 'POST':
        if ($path === 'create') {
            createCoupon();
        }
        break;
    
    case 'PUT':
        if ($path === 'update') {
            updateCoupon();
        }
        break;
    
    case 'DELETE':
        if ($path === 'delete') {
            deleteCoupon();
        }
        break;
    
    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'Metoda nie jest obsługiwana'
        ]);
        break;
}

/**
 * Pobierz aktywne kupony
 */
function getActiveCoupons() {
    global $pdo;
    
    try {
        $limit = (int)($_GET['limit'] ?? 10);
        
        $stmt = $pdo->prepare("
            SELECT * FROM coupons 
            WHERE status = 'active' 
            ORDER BY createdAt DESC 
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        $coupons = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'data' => $coupons
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania kuponów',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Pobierz wszystkie kupony
 */
function getAllCoupons() {
    global $pdo;
    
    try {
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 20);
        $status = $_GET['status'] ?? '';

        // Buduj zapytanie
        $whereClause = '';
        $params = [];
        
        if ($status) {
            $whereClause = 'WHERE status = ?';
            $params[] = $status;
        }

        // Policz wszystkie kupony
        $countStmt = $pdo->prepare("SELECT COUNT(*) FROM coupons $whereClause");
        $countStmt->execute($params);
        $total = $countStmt->fetchColumn();

        // Pobierz kupony z paginacją
        $offset = ($page - 1) * $limit;
        $stmt = $pdo->prepare("
            SELECT * FROM coupons 
            $whereClause 
            ORDER BY createdAt DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute(array_merge($params, [$limit, $offset]));
        $coupons = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'data' => [
                'coupons' => $coupons,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania kuponów',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Utwórz nowy kupon
 */
function createCoupon() {
    global $pdo;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        $requiredFields = ['title', 'code', 'discount_type', 'discount_value'];
        foreach ($requiredFields as $field) {
            if (empty($input[$field])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => "Pole {$field} jest wymagane"
                ]);
                return;
            }
        }

        $stmt = $pdo->prepare("
            INSERT INTO coupons (title, description, code, discount_type, discount_value, validUntil, company_id, company_name, company_logo, status, createdAt, updatedAt) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        ");
        
        $stmt->execute([
            $input['title'],
            $input['description'] ?? '',
            strtoupper($input['code']),
            $input['discount_type'],
            (float)$input['discount_value'],
            $input['validUntil'] ?? null,
            isset($input['company_id']) ? (int)$input['company_id'] : null,
            $input['company_name'] ?? '',
            $input['company_logo'] ?? '',
            $input['status'] ?? 'active'
        ]);
        
        $newId = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'Kupon został utworzony pomyślnie',
            'data' => ['id' => $newId]
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd tworzenia kuponu',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Aktualizuj kupon
 */
function updateCoupon() {
    global $pdo;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $couponId = $input['id'] ?? null;

        if (!$couponId) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'ID kuponu jest wymagane'
            ]);
            return;
        }

        $stmt = $pdo->prepare("
            UPDATE coupons 
            SET title = ?, description = ?, code = ?, discount_type = ?, discount_value = ?, validUntil = ?, status = ?, updatedAt = datetime('now')
            WHERE id = ?
        ");
        
        $stmt->execute([
            $input['title'] ?? '',
            $input['description'] ?? '',
            isset($input['code']) ? strtoupper($input['code']) : '',
            $input['discount_type'] ?? 'percentage',
            isset($input['discount_value']) ? (float)$input['discount_value'] : 0,
            $input['validUntil'] ?? null,
            $input['status'] ?? 'active',
            $couponId
        ]);

        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Kupon został zaktualizowany pomyślnie'
            ]);
        } else {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Kupon nie został znaleziony'
            ]);
        }

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd aktualizacji kuponu',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Usuń kupon
 */
function deleteCoupon() {
    global $pdo;
    
    try {
        $couponId = $_GET['id'] ?? null;

        if (!$couponId) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'ID kuponu jest wymagane'
            ]);
            return;
        }

        $stmt = $pdo->prepare("DELETE FROM coupons WHERE id = ?");
        $stmt->execute([$couponId]);

        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Kupon został usunięty pomyślnie'
            ]);
        } else {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Kupon nie został znaleziony'
            ]);
        }

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd usuwania kuponu',
            'error' => $e->getMessage()
        ]);
    }
}
?>
