<?php
/**
 * API do zapisywania zmian w menu nagłówka i stopki
 * Bezpieczny endpoint z autoryzacją i walidacją
 */

// Rozpocznij sesję dla autoryzacji
session_start();

// Bezpieczne nagłówki HTTP
header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// CORS - tylko dla admin panelu
$allowed_origins = [
    'https://zyrardow.poleca.to',
    'http://localhost:8000',
    'http://127.0.0.1:8000'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowed_origins)) {
    header("Access-Control-Allow-Origin: $origin");
}
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Bezpieczeństwo PHP
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Funkcja do logowania błędów bezpieczeństwa
function logSecurityEvent($event, $details = '') {
    $log_entry = date('Y-m-d H:i:s') . " - $event - IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown') . " - $details\n";
    file_put_contents(__DIR__ . '/security.log', $log_entry, FILE_APPEND | LOCK_EX);
}

// Sprawdź autoryzację (symulacja - w rzeczywistości sprawdź sesję/token)
function isAuthorized() {
    // W rzeczywistej implementacji sprawdź sesję administratora
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

// Sprawdź metodę HTTP
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    logSecurityEvent('INVALID_METHOD', $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Nieprawidłowa metoda żądania.']);
    exit;
}

// Sprawdź autoryzację
if (!isAuthorized()) {
    logSecurityEvent('UNAUTHORIZED_ACCESS', 'Menu save attempt');
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Brak autoryzacji.']);
    exit;
}

// Ustawienia
$templatesDir = realpath('../../templates/') . '/';
$headerFile = $templatesDir . 'header.html';
$footerFile = $templatesDir . 'footer.html';

// Sprawdź czy katalog templates istnieje
if (!is_dir($templatesDir)) {
    logSecurityEvent('DIRECTORY_NOT_FOUND', $templatesDir);
    echo json_encode(['success' => false, 'message' => 'Katalog templates nie istnieje.']);
    exit;
}

// Inicjalizacja odpowiedzi
$response = [
    'success' => false,
    'message' => ''
];

// Sprawdź, czy akcja jest poprawna
if (!isset($_POST['action']) || $_POST['action'] !== 'save_menus') {
    $response['message'] = 'Nieprawidłowa akcja.';
    echo json_encode($response);
    exit;
}

// Sprawdź, czy dane menu zostały przesłane
if (!isset($_POST['header_html']) || !isset($_POST['footer_html'])) {
    $response['message'] = 'Brak wymaganych danych.';
    echo json_encode($response);
    exit;
}

// Funkcja do walidacji HTML
function validateHtml($html) {
    // Sprawdź długość
    if (strlen($html) > 100000) { // Max 100KB
        return false;
    }

    // Sprawdź czy zawiera niebezpieczne tagi
    $dangerous_tags = ['<script', '<iframe', '<object', '<embed', '<form', '<input'];
    foreach ($dangerous_tags as $tag) {
        if (stripos($html, $tag) !== false) {
            return false;
        }
    }

    // Sprawdź czy zawiera javascript
    if (preg_match('/on\w+\s*=/i', $html) || stripos($html, 'javascript:') !== false) {
        return false;
    }

    return true;
}

// Pobierz i waliduj dane menu
$headerHtml = $_POST['header_html'] ?? '';
$footerHtml = $_POST['footer_html'] ?? '';

// Walidacja danych
if (!validateHtml($headerHtml)) {
    logSecurityEvent('INVALID_HTML', 'Header HTML validation failed');
    $response['message'] = 'Nieprawidłowy kod HTML nagłówka.';
    echo json_encode($response);
    exit;
}

if (!validateHtml($footerHtml)) {
    logSecurityEvent('INVALID_HTML', 'Footer HTML validation failed');
    $response['message'] = 'Nieprawidłowy kod HTML stopki.';
    echo json_encode($response);
    exit;
}

// Utwórz kopię zapasową przed zapisem
$backup_dir = $templatesDir . 'backups/';
if (!is_dir($backup_dir)) {
    mkdir($backup_dir, 0755, true);
}

$timestamp = date('Y-m-d_H-i-s');
if (file_exists($headerFile)) {
    copy($headerFile, $backup_dir . "header_backup_$timestamp.html");
}
if (file_exists($footerFile)) {
    copy($footerFile, $backup_dir . "footer_backup_$timestamp.html");
}

// Zapisz zmiany w pliku nagłówka
if (!file_put_contents($headerFile, $headerHtml, LOCK_EX)) {
    logSecurityEvent('FILE_WRITE_ERROR', 'Header file write failed');
    $response['message'] = 'Nie udało się zapisać zmian w pliku nagłówka.';
    echo json_encode($response);
    exit;
}

// Zapisz zmiany w pliku stopki
if (!file_put_contents($footerFile, $footerHtml, LOCK_EX)) {
    logSecurityEvent('FILE_WRITE_ERROR', 'Footer file write failed');
    $response['message'] = 'Nie udało się zapisać zmian w pliku stopki.';
    echo json_encode($response);
    exit;
}

// Wszystko poszło dobrze
$response['success'] = true;
$response['message'] = 'Menu zostało zapisane pomyślnie.';
echo json_encode($response);
