<?php
/**
 * API dla zarządzania kategoriami
 * Żyrardów Poleca - Panel Administracyjny
 * System SQLite
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Obsługa preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Połączenie z bazą danych
require_once __DIR__ . '/../config/database.php';

// Routing
$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';

switch ($method) {
    case 'GET':
        if ($path === 'tree') {
            getCategoriesTree();
        } elseif ($path === 'get' && isset($_GET['id'])) {
            getCategoryById($_GET['id']);
        } else {
            getAllCategories();
        }
        break;
    
    case 'POST':
        if ($path === 'create') {
            createCategory();
        }
        break;
    
    case 'PUT':
        if ($path === 'update') {
            updateCategory();
        }
        break;
    
    case 'DELETE':
        if ($path === 'delete') {
            deleteCategory();
        }
        break;
    
    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'Metoda nie jest obsługiwana'
        ]);
        break;
}

/**
 * Pobierz wszystkie kategorie
 */
function getAllCategories() {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM categories 
            ORDER BY parentId ASC, sortOrder ASC, name ASC
        ");
        $stmt->execute();
        $categories = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'data' => $categories
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania kategorii',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Pobierz drzewo kategorii (główne z podkategoriami)
 */
function getCategoriesTree() {
    global $pdo;
    
    try {
        // Pobierz kategorie główne
        $stmt = $pdo->prepare("
            SELECT * FROM categories 
            WHERE parentId IS NULL 
            ORDER BY sortOrder ASC, name ASC
        ");
        $stmt->execute();
        $mainCategories = $stmt->fetchAll();
        
        // Dla każdej kategorii głównej pobierz podkategorie
        foreach ($mainCategories as &$category) {
            $stmt = $pdo->prepare("
                SELECT * FROM categories 
                WHERE parentId = ? 
                ORDER BY sortOrder ASC, name ASC
            ");
            $stmt->execute([$category['id']]);
            $category['subcategories'] = $stmt->fetchAll();
        }
        
        echo json_encode([
            'success' => true,
            'data' => $mainCategories
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania drzewa kategorii',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Pobierz kategorię po ID
 */
function getCategoryById($categoryId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM categories WHERE id = ?");
        $stmt->execute([$categoryId]);
        $category = $stmt->fetch();
        
        if ($category) {
            echo json_encode([
                'success' => true,
                'data' => $category
            ]);
        } else {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Kategoria nie została znaleziona'
            ]);
        }
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania kategorii',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Utwórz nową kategorię
 */
function createCategory() {
    global $pdo;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        $requiredFields = ['name'];
        foreach ($requiredFields as $field) {
            if (empty($input[$field])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => "Pole {$field} jest wymagane"
                ]);
                return;
            }
        }

        // Generuj slug
        $slug = generateSlug($input['name']);
        
        // Sprawdź czy slug już istnieje
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM categories WHERE slug = ?");
        $stmt->execute([$slug]);
        if ($stmt->fetchColumn() > 0) {
            $slug .= '-' . time();
        }

        $stmt = $pdo->prepare("
            INSERT INTO categories (name, slug, icon, color, parentId, sortOrder, description, createdAt, updatedAt) 
            VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        ");
        
        $stmt->execute([
            $input['name'],
            $slug,
            $input['icon'] ?? 'fas fa-folder',
            $input['color'] ?? '#6c757d',
            $input['parentId'] ?? null,
            $input['sortOrder'] ?? 0,
            $input['description'] ?? ''
        ]);
        
        $newId = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'Kategoria została utworzona pomyślnie',
            'data' => ['id' => $newId]
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd tworzenia kategorii',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Aktualizuj kategorię
 */
function updateCategory() {
    global $pdo;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $categoryId = $input['id'] ?? null;

        if (!$categoryId) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'ID kategorii jest wymagane'
            ]);
            return;
        }

        $stmt = $pdo->prepare("
            UPDATE categories 
            SET name = ?, icon = ?, color = ?, parentId = ?, sortOrder = ?, description = ?, updatedAt = datetime('now')
            WHERE id = ?
        ");
        
        $stmt->execute([
            $input['name'] ?? '',
            $input['icon'] ?? 'fas fa-folder',
            $input['color'] ?? '#6c757d',
            $input['parentId'] ?? null,
            $input['sortOrder'] ?? 0,
            $input['description'] ?? '',
            $categoryId
        ]);

        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Kategoria została zaktualizowana pomyślnie'
            ]);
        } else {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Kategoria nie została znaleziona'
            ]);
        }

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd aktualizacji kategorii',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Usuń kategorię
 */
function deleteCategory() {
    global $pdo;
    
    try {
        $categoryId = $_GET['id'] ?? null;

        if (!$categoryId) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'ID kategorii jest wymagane'
            ]);
            return;
        }

        // Sprawdź czy kategoria ma podkategorie
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM categories WHERE parentId = ?");
        $stmt->execute([$categoryId]);
        $subcategoriesCount = $stmt->fetchColumn();
        
        if ($subcategoriesCount > 0) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Nie można usunąć kategorii która ma podkategorie'
            ]);
            return;
        }

        // Sprawdź czy kategoria ma przypisane firmy
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM companies WHERE categoryId = ?");
        $stmt->execute([$categoryId]);
        $companiesCount = $stmt->fetchColumn();
        
        if ($companiesCount > 0) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Nie można usunąć kategorii która ma przypisane firmy'
            ]);
            return;
        }

        $stmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");
        $stmt->execute([$categoryId]);

        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Kategoria została usunięta pomyślnie'
            ]);
        } else {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Kategoria nie została znaleziona'
            ]);
        }

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd usuwania kategorii',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Generuj slug z nazwy
 */
function generateSlug($name) {
    $slug = strtolower($name);
    
    // Zamiana polskich znaków
    $polish = ['ą', 'ć', 'ę', 'ł', 'ń', 'ó', 'ś', 'ź', 'ż'];
    $latin = ['a', 'c', 'e', 'l', 'n', 'o', 's', 'z', 'z'];
    $slug = str_replace($polish, $latin, $slug);
    
    $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
    $slug = preg_replace('/\s+/', '-', $slug);
    $slug = preg_replace('/\-+/', '-', $slug);
    $slug = trim($slug, '-');
    
    return $slug;
}
?>
