<?php
/**
 * API dla zarządzania ofertami specjalnymi
 * Żyrardów Poleca - Panel Administracyjny
 * System SQLite
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Obsługa preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Połączenie z bazą danych
require_once __DIR__ . '/../config/database.php';

// Routing
$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';

switch ($method) {
    case 'GET':
        if ($path === 'active') {
            getActiveOffers();
        } else {
            getAllOffers();
        }
        break;

    case 'POST':
        if ($path === 'create') {
            createOffer();
        }
        break;

    case 'PUT':
        if ($path === 'update') {
            updateOffer();
        }
        break;

    case 'DELETE':
        if ($path === 'delete') {
            deleteOffer();
        }
        break;

    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'Metoda nie jest obsługiwana'
        ]);
        break;
}

/**
 * Pobierz aktywne oferty specjalne
 */
function getActiveOffers() {
    global $pdo;

    try {
        $limit = (int)($_GET['limit'] ?? 10);

        $stmt = $pdo->prepare("
            SELECT * FROM offers
            WHERE status = 'active'
            ORDER BY createdAt DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        $offers = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'data' => $offers
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania ofert',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Pobierz wszystkie oferty
 */
function getAllOffers() {
    global $pdo;

    try {
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 20);
        $status = $_GET['status'] ?? '';

        // Buduj zapytanie
        $whereClause = '';
        $params = [];

        if ($status) {
            $whereClause = 'WHERE status = ?';
            $params[] = $status;
        }

        // Policz wszystkie oferty
        $countStmt = $pdo->prepare("SELECT COUNT(*) FROM offers $whereClause");
        $countStmt->execute($params);
        $total = $countStmt->fetchColumn();

        // Pobierz oferty z paginacją
        $offset = ($page - 1) * $limit;
        $stmt = $pdo->prepare("
            SELECT * FROM offers
            $whereClause
            ORDER BY createdAt DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute(array_merge($params, [$limit, $offset]));
        $offers = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'data' => [
                'offers' => $offers,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania ofert',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Utwórz nową ofertę
 */
function createOffer() {
    global $pdo;

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        $requiredFields = ['title', 'price'];
        foreach ($requiredFields as $field) {
            if (empty($input[$field])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => "Pole {$field} jest wymagane"
                ]);
                return;
            }
        }

        $stmt = $pdo->prepare("
            INSERT INTO offers (title, description, price, originalPrice, validUntil, company_id, company_name, company_logo, status, createdAt, updatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        ");

        $stmt->execute([
            $input['title'],
            $input['description'] ?? '',
            (float)$input['price'],
            isset($input['originalPrice']) ? (float)$input['originalPrice'] : null,
            $input['validUntil'] ?? null,
            isset($input['company_id']) ? (int)$input['company_id'] : null,
            $input['company_name'] ?? '',
            $input['company_logo'] ?? '',
            $input['status'] ?? 'active'
        ]);

        $newId = $pdo->lastInsertId();

        echo json_encode([
            'success' => true,
            'message' => 'Oferta została utworzona pomyślnie',
            'data' => ['id' => $newId]
        ]);

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd tworzenia oferty',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Aktualizuj ofertę
 */
function updateOffer() {
    global $pdo;

    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $offerId = $input['id'] ?? null;

        if (!$offerId) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'ID oferty jest wymagane'
            ]);
            return;
        }

        $stmt = $pdo->prepare("
            UPDATE offers
            SET title = ?, description = ?, price = ?, originalPrice = ?, validUntil = ?, status = ?, updatedAt = datetime('now')
            WHERE id = ?
        ");

        $stmt->execute([
            $input['title'] ?? '',
            $input['description'] ?? '',
            isset($input['price']) ? (float)$input['price'] : 0,
            isset($input['originalPrice']) ? (float)$input['originalPrice'] : null,
            $input['validUntil'] ?? null,
            $input['status'] ?? 'active',
            $offerId
        ]);

        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Oferta została zaktualizowana pomyślnie'
            ]);
        } else {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Oferta nie została znaleziona'
            ]);
        }

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd aktualizacji oferty',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Usuń ofertę
 */
function deleteOffer() {
    global $pdo;

    try {
        $offerId = $_GET['id'] ?? null;

        if (!$offerId) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'ID oferty jest wymagane'
            ]);
            return;
        }

        $stmt = $pdo->prepare("DELETE FROM offers WHERE id = ?");
        $stmt->execute([$offerId]);

        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Oferta została usunięta pomyślnie'
            ]);
        } else {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Oferta nie została znaleziona'
            ]);
        }

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd usuwania oferty',
            'error' => $e->getMessage()
        ]);
    }
}
?>
