<?php
/**
 * API Webhook dla N8n - Import wiadomości
 * Żyrardów Poleca - Panel Administracyjny
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Obsługa preflight request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Konfiguracja
$config = [
    'api_key' => 'zyrardow_api_2024_secure_key', // Zmień na bezpieczny klucz
    'allowed_ips' => ['127.0.0.1', '::1'], // Dodaj IP N8n
    'max_content_length' => 10000, // Max długość treści
    'data_file' => '../data/news.json' // Plik z danymi wiadomości
];

/**
 * Logowanie błędów
 */
function logError($message) {
    $logFile = '../logs/news-api.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message" . PHP_EOL;
    
    // Utwórz katalog logs jeśli nie istnieje
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * Walidacja API key
 */
function validateApiKey($providedKey, $validKey) {
    return hash_equals($validKey, $providedKey);
}

/**
 * Walidacja IP
 */
function validateIP($allowedIps) {
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    
    // W środowisku deweloperskim pozwól na wszystkie IP
    if (in_array($clientIP, ['127.0.0.1', '::1']) || 
        isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return true;
    }
    
    return in_array($clientIP, $allowedIps);
}

/**
 * Walidacja danych wiadomości
 */
function validateNewsData($data) {
    $required = ['title', 'category', 'excerpt', 'content'];
    $errors = [];
    
    foreach ($required as $field) {
        if (empty($data[$field])) {
            $errors[] = "Pole '$field' jest wymagane";
        }
    }
    
    // Walidacja kategorii
    $validCategories = ['local', 'events', 'business', 'culture', 'sport'];
    if (!empty($data['category']) && !in_array($data['category'], $validCategories)) {
        $errors[] = "Nieprawidłowa kategoria. Dozwolone: " . implode(', ', $validCategories);
    }
    
    // Walidacja długości
    if (!empty($data['title']) && strlen($data['title']) > 200) {
        $errors[] = "Tytuł jest za długi (max 200 znaków)";
    }
    
    if (!empty($data['content']) && strlen($data['content']) > 10000) {
        $errors[] = "Treść jest za długa (max 10000 znaków)";
    }
    
    // Walidacja slug
    if (!empty($data['slug']) && !preg_match('/^[a-z0-9\-]+$/', $data['slug'])) {
        $errors[] = "Slug może zawierać tylko małe litery, cyfry i myślniki";
    }
    
    return $errors;
}

/**
 * Generowanie slug z tytułu
 */
function generateSlug($title) {
    $slug = strtolower($title);
    
    // Zamiana polskich znaków
    $polish = ['ą', 'ć', 'ę', 'ł', 'ń', 'ó', 'ś', 'ź', 'ż'];
    $latin = ['a', 'c', 'e', 'l', 'n', 'o', 's', 'z', 'z'];
    $slug = str_replace($polish, $latin, $slug);
    
    // Usunięcie niepożądanych znaków
    $slug = preg_replace('/[^a-z0-9\s\-]/', '', $slug);
    $slug = preg_replace('/\s+/', '-', $slug);
    $slug = preg_replace('/\-+/', '-', $slug);
    $slug = trim($slug, '-');
    
    return $slug;
}

/**
 * Ładowanie istniejących wiadomości
 */
function loadNews($dataFile) {
    if (!file_exists($dataFile)) {
        return [];
    }
    
    $content = file_get_contents($dataFile);
    $data = json_decode($content, true);
    
    return is_array($data) ? $data : [];
}

/**
 * Zapisywanie wiadomości
 */
function saveNews($dataFile, $newsData) {
    // Utwórz katalog jeśli nie istnieje
    $dataDir = dirname($dataFile);
    if (!is_dir($dataDir)) {
        mkdir($dataDir, 0755, true);
    }
    
    $json = json_encode($newsData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    return file_put_contents($dataFile, $json, LOCK_EX) !== false;
}

/**
 * Główna logika API
 */
try {
    // Sprawdź metodę HTTP
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Metoda nie dozwolona']);
        exit();
    }
    
    // Walidacja IP (opcjonalna w środowisku deweloperskim)
    if (!validateIP($config['allowed_ips'])) {
        logError("Nieautoryzowany dostęp z IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
        http_response_code(403);
        echo json_encode(['error' => 'Dostęp zabroniony']);
        exit();
    }
    
    // Pobierz dane z żądania
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        http_response_code(400);
        echo json_encode(['error' => 'Nieprawidłowy format JSON']);
        exit();
    }
    
    // Walidacja API key
    $apiKey = $data['api_key'] ?? $_SERVER['HTTP_X_API_KEY'] ?? '';
    if (!validateApiKey($apiKey, $config['api_key'])) {
        logError("Nieprawidłowy API key");
        http_response_code(401);
        echo json_encode(['error' => 'Nieprawidłowy API key']);
        exit();
    }
    
    // Walidacja danych wiadomości
    $newsData = $data['news'] ?? $data;
    $validationErrors = validateNewsData($newsData);
    
    if (!empty($validationErrors)) {
        http_response_code(400);
        echo json_encode(['error' => 'Błędy walidacji', 'details' => $validationErrors]);
        exit();
    }
    
    // Przygotowanie danych wiadomości
    $existingNews = loadNews($config['data_file']);
    $newId = empty($existingNews) ? 1 : max(array_column($existingNews, 'id')) + 1;
    
    $newsItem = [
        'id' => $newId,
        'title' => $newsData['title'],
        'category' => $newsData['category'],
        'excerpt' => $newsData['excerpt'],
        'content' => $newsData['content'],
        'image' => $newsData['image'] ?? null,
        'imageAlt' => $newsData['imageAlt'] ?? '',
        'status' => $newsData['status'] ?? 'published',
        'publishDate' => $newsData['publishDate'] ?? date('c'),
        'slug' => $newsData['slug'] ?? generateSlug($newsData['title']),
        'metaDescription' => $newsData['metaDescription'] ?? '',
        'tags' => $newsData['tags'] ?? '',
        'createdAt' => date('c'),
        'source' => 'n8n-webhook'
    ];
    
    // Dodaj wiadomość do listy
    $existingNews[] = $newsItem;
    
    // Zapisz dane
    if (saveNews($config['data_file'], $existingNews)) {
        logError("Dodano nową wiadomość: " . $newsItem['title']);
        
        http_response_code(201);
        echo json_encode([
            'success' => true,
            'message' => 'Wiadomość została dodana',
            'data' => [
                'id' => $newsItem['id'],
                'title' => $newsItem['title'],
                'slug' => $newsItem['slug'],
                'url' => "https://zyrardow.poleca.to/wiadomosci/{$newsItem['slug']}"
            ]
        ]);
    } else {
        throw new Exception('Nie udało się zapisać wiadomości');
    }
    
} catch (Exception $e) {
    logError("Błąd API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'error' => 'Błąd serwera',
        'message' => $e->getMessage()
    ]);
}
?>
