<?php
/**
 * API dla autoryzacji - prosty system JSON
 * Żyrardów Poleca - Panel Administracyjny
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Obsługa preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Dane logowania
$validCredentials = [
    '<EMAIL>' => 'admin123',
    '<EMAIL>' => 'admin123',
    'admin' => 'admin123'
];

// Routing
$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';

switch ($method) {
    case 'POST':
        if ($path === 'login') {
            handleLogin();
        } elseif ($path === 'logout') {
            handleLogout();
        }
        break;

    case 'GET':
        if ($path === 'check') {
            handleAuthCheck();
        }
        break;

    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'Metoda nie jest obsługiwana'
        ]);
        break;
}

/**
 * Obsługa logowania
 */
function handleLogin() {
    global $validCredentials;

    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $email = $input['email'] ?? '';
        $password = $input['password'] ?? '';

        if (empty($email) || empty($password)) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Email i hasło są wymagane'
            ]);
            return;
        }

        // Sprawdź dane logowania
        if (isset($validCredentials[$email]) && $validCredentials[$email] === $password) {
            // Generuj prosty token
            $token = 'admin_token_' . time() . '_' . md5($email);

            echo json_encode([
                'success' => true,
                'message' => 'Logowanie pomyślne',
                'data' => [
                    'token' => $token,
                    'admin' => [
                        'email' => $email,
                        'name' => 'Administrator',
                        'loginTime' => date('c')
                    ]
                ]
            ]);
        } else {
            http_response_code(401);
            echo json_encode([
                'success' => false,
                'message' => 'Nieprawidłowe dane logowania'
            ]);
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd serwera',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Obsługa wylogowania
 */
function handleLogout() {
    echo json_encode([
        'success' => true,
        'message' => 'Wylogowanie pomyślne'
    ]);
}

/**
 * Sprawdzenie autoryzacji
 */
function handleAuthCheck() {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';

    if (empty($authHeader) || strpos($authHeader, 'Bearer ') !== 0) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'Brak tokenu autoryzacji'
        ]);
        return;
    }

    $token = substr($authHeader, 7); // Usuń "Bearer "

    // Prosty system - sprawdź czy token ma odpowiedni format
    if (strpos($token, 'admin_token_') === 0) {
        echo json_encode([
            'success' => true,
            'message' => 'Token ważny',
            'data' => [
                'admin' => [
                    'email' => '<EMAIL>',
                    'name' => 'Administrator'
                ]
            ]
        ]);
    } else {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'Nieprawidłowy token'
        ]);
    }
}
?>
