<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mapa TOP firm - Dashboard Żyrardów Poleca</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/map.css">
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin="" />
    <!-- Fallback Leaflet CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.css"
          integrity="sha512-h9FcoyWjHcOcmEVkxOfTLnmZFWIH0iZhZT1H2TbOq55xssQGEJHEaIm+PgoUaZbRvQTNTluNOEfb1ZRy6D3BOw=="
          crossorigin="anonymous" referrerpolicy="no-referrer" />
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="../images/logo.png" alt="Żyrardów Poleca">
                    <h2>Dashboard</h2>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="Admin Avatar">
                </div>
                <div class="user-info">
                    <h3>Administrator</h3>
                    <p><EMAIL></p>
                </div>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="companies.html">
                            <i class="fas fa-building"></i>
                            <span>Firmy</span>
                        </a>
                    </li>
                    <li>
                        <a href="offers.html">
                            <i class="fas fa-tag"></i>
                            <span>Oferty</span>
                        </a>
                    </li>
                    <li>
                        <a href="coupons.html">
                            <i class="fas fa-ticket-alt"></i>
                            <span>Kupony</span>
                        </a>
                    </li>
                    <li>
                        <a href="categories.html">
                            <i class="fas fa-list"></i>
                            <span>Kategorie</span>
                        </a>
                    </li>
                    <li>
                        <a href="news.html">
                            <i class="fas fa-newspaper"></i>
                            <span>Wiadomości</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Użytkownicy</span>
                        </a>
                    </li>
                    <li>
                        <a href="pages.html">
                            <i class="fas fa-file-alt"></i>
                            <span>Strony CMS</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="map.html">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Mapa TOP firm</span>
                        </a>
                    </li>
                    <li>
                        <a href="menus.html">
                            <i class="fas fa-bars"></i>
                            <span>Menu</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Ustawienia</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <button id="logoutBtn" class="btn-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Wyloguj</span>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-title">
                    <h1>Mapa TOP firm</h1>
                    <p>Zarządzaj lokalizacjami TOP firm na mapie Żyrardowa</p>
                </div>
                <div class="header-actions">
                    <div class="map-controls">
                        <select id="categoryFilter" class="form-control">
                            <option value="">Wszystkie kategorie</option>
                            <option value="food">Jedzenie i Gastronomia</option>
                            <option value="health">Zdrowie i Uroda</option>
                            <option value="home">Dom i Ogród</option>
                            <option value="automotive">Motoryzacja</option>
                        </select>
                        <select id="topFilter" class="form-control">
                            <option value="">Wszystkie pozycje TOP</option>
                            <option value="1">TOP 1</option>
                            <option value="2">TOP 2</option>
                            <option value="3">TOP 3</option>
                        </select>
                    </div>
                    <button class="btn btn-primary" id="addLocationBtn">
                        <i class="fas fa-map-pin"></i> Dodaj lokalizację
                    </button>
                </div>
            </header>

            <div class="content-body">
                <!-- Map Container -->
                <div class="map-container">
                    <div id="map" class="map"></div>

                    <!-- Map Legend -->
                    <div class="map-legend">
                        <h4>Legenda</h4>
                        <div class="legend-item">
                            <div class="legend-marker top-1"></div>
                            <span>TOP 1 (najdroższe)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-marker top-2"></div>
                            <span>TOP 2 (średnie)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-marker top-3"></div>
                            <span>TOP 3 (najtańsze)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-marker regular"></div>
                            <span>Inne firmy</span>
                        </div>
                    </div>
                </div>

                <!-- Companies List -->
                <div class="companies-sidebar">
                    <div class="sidebar-header">
                        <h3>TOP Firmy</h3>
                        <div class="companies-count">
                            <span id="companiesCount">0 firm</span>
                        </div>
                    </div>
                    <div class="companies-list" id="companiesList">
                        <!-- Lista firm będzie ładowana dynamicznie -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal dodawania lokalizacji -->
    <div class="modal" id="locationModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="locationModalTitle">Dodaj lokalizację firmy</h3>
                <button class="modal-close" id="locationModalClose">&times;</button>
            </div>
            <div class="modal-body">
                <form id="locationForm">
                    <input type="hidden" id="locationId">

                    <div class="form-group">
                        <label for="companySelect">Firma</label>
                        <select id="companySelect" required>
                            <option value="">Wybierz firmę</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="locationAddress">Adres</label>
                        <input type="text" id="locationAddress" placeholder="ul. Przykładowa 1, Żyrardów" required>
                        <button type="button" id="geocodeBtn" class="btn btn-outline btn-sm">
                            <i class="fas fa-search"></i> Znajdź na mapie
                        </button>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="locationLat">Szerokość geograficzna</label>
                            <input type="number" id="locationLat" step="0.000001" readonly>
                        </div>
                        <div class="form-group">
                            <label for="locationLng">Długość geograficzna</label>
                            <input type="number" id="locationLng" step="0.000001" readonly>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="locationDescription">Opis lokalizacji</label>
                        <textarea id="locationDescription" rows="3" placeholder="Dodatkowe informacje o lokalizacji..."></textarea>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="locationVisible" checked>
                            <span class="checkmark"></span>
                            Widoczna na mapie
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="locationCancelBtn">Anuluj</button>
                <button type="submit" form="locationForm" class="btn btn-primary" id="locationSaveBtn">Zapisz</button>
            </div>
        </div>
    </div>

    <!-- Powiadomienia -->
    <div class="notifications" id="notifications"></div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>
    <!-- Fallback Leaflet JS -->
    <script>
        // Sprawdź czy Leaflet załadował się poprawnie
        window.addEventListener('load', function() {
            if (typeof L === 'undefined') {
                console.warn('Primary Leaflet failed to load, trying fallback...');
                const fallbackScript = document.createElement('script');
                fallbackScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.js';
                fallbackScript.integrity = 'sha512-BwHfrr+c9kmtHDqcw6vdvnlJjKC9oLTQmdzMlmcQu1OOzh5o8xPdvpah/yXdLNYyNkzosoI7BvhSjNqKlqKL5g==';
                fallbackScript.crossOrigin = 'anonymous';
                fallbackScript.referrerPolicy = 'no-referrer';
                document.head.appendChild(fallbackScript);
            }
        });
    </script>
    <script src="js/admin.js"></script>
    <script src="js/map.js"></script>
</body>
</html>
