<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ustawienia - Panel Administracyjny - Żyrardów Poleca</title>
    <meta name="description" content="Panel administracyjny portalu Żyrardów Poleca - zarządzanie ustawieniami strony">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/settings.css">
    <link rel="icon" href="../favicon.ico" type="image/x-icon">

    <!-- Inline CSS dla drag&drop - nadpisuje konflikty -->
    <style>
        .section-order-container {
            background: #fff !important;
            border-radius: 8px !important;
            padding: 20px !important;
            border: 1px solid #dee2e6 !important;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
        }

        .section-description {
            color: #6c757d !important;
            margin-bottom: 20px !important;
            font-size: 0.95rem !important;
        }

        .sortable-sections {
            min-height: 400px !important;
            border: 2px dashed #dee2e6 !important;
            border-radius: 8px !important;
            padding: 15px !important;
            background: #f8f9fa !important;
        }

        .sortable-sections .section-item {
            display: flex !important;
            align-items: center !important;
            background: #fff !important;
            border: 1px solid #dee2e6 !important;
            border-radius: 8px !important;
            padding: 15px !important;
            margin-bottom: 10px !important;
            cursor: move !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05) !important;
            flex-direction: row !important;
            justify-content: flex-start !important;
        }

        .sortable-sections .section-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
            transform: translateY(-2px) !important;
            background-color: #fff !important;
        }

        .sortable-sections .section-item.sortable-ghost {
            opacity: 0.5 !important;
            background: #f39c12 !important;
            color: #fff !important;
        }

        .sortable-sections .section-item.sortable-chosen {
            background: #f39c12 !important;
            color: #fff !important;
        }

        .sortable-sections .section-handle {
            margin-right: 15px !important;
            color: #6c757d !important;
            font-size: 1.2rem !important;
            cursor: grab !important;
            margin-left: 0 !important;
            margin-top: 0 !important;
        }

        .sortable-sections .section-handle:active {
            cursor: grabbing !important;
        }

        .sortable-sections .section-info {
            flex: 1 !important;
            margin-left: 0 !important;
            margin-top: 0 !important;
        }

        .sortable-sections .section-info h4 {
            margin: 0 0 5px 0 !important;
            font-size: 1rem !important;
            font-weight: 600 !important;
            color: #343a40 !important;
        }

        .sortable-sections .section-info p {
            margin: 0 !important;
            font-size: 0.85rem !important;
            color: #6c757d !important;
        }

        .sortable-sections .section-status {
            margin-left: 15px !important;
            margin-top: 0 !important;
        }

        .status-badge {
            padding: 4px 12px !important;
            border-radius: 20px !important;
            font-size: 0.75rem !important;
            font-weight: 600 !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
            display: inline-block !important;
        }

        .status-badge.active {
            background: #e8f5e8 !important;
            color: #27ae60 !important;
        }

        .status-badge.hidden {
            background: #fef2e8 !important;
            color: #f39c12 !important;
        }

        .status-badge.fixed {
            background: #e8f2ff !important;
            color: #3498db !important;
        }

        .section-order-actions {
            display: flex !important;
            gap: 15px !important;
            margin-top: 20px !important;
            padding-top: 20px !important;
            border-top: 1px solid #dee2e6 !important;
        }

        /* Style przycisków */
        .section-order-actions .btn {
            padding: 10px 20px !important;
            border-radius: 6px !important;
            font-weight: 500 !important;
            font-size: 14px !important;
            border: none !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 8px !important;
            text-decoration: none !important;
        }

        .section-order-actions .btn:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        }

        .section-order-actions .btn:active {
            transform: translateY(0) !important;
        }

        .section-order-actions .btn.btn-primary {
            background: #f39c12 !important;
            color: white !important;
        }

        .section-order-actions .btn.btn-primary:hover {
            background: #e67e22 !important;
        }

        .section-order-actions .btn.btn-outline {
            background: white !important;
            color: #6c757d !important;
            border: 1px solid #dee2e6 !important;
        }

        .section-order-actions .btn.btn-outline:hover {
            background: #f8f9fa !important;
            color: #495057 !important;
        }

        .section-order-actions .btn.btn-success {
            background: #27ae60 !important;
            color: white !important;
        }

        .section-order-actions .btn.btn-success:hover {
            background: #229954 !important;
        }

        .section-order-actions .btn.btn-secondary {
            background: #6c757d !important;
            color: white !important;
        }

        .section-order-actions .btn.btn-secondary:hover {
            background: #5a6268 !important;
        }

        /* Responsywność */
        @media (max-width: 768px) {
            .section-order-actions {
                flex-direction: column !important;
            }

            .sortable-sections .section-item {
                padding: 12px !important;
            }

            .sortable-sections .section-handle {
                margin-right: 10px !important;
            }

            .section-order-actions .btn {
                width: 100% !important;
                justify-content: center !important;
            }
        }
    </style>
</head>
<body class="settings-page">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.png" alt="Żyrardów Poleca Logo" class="sidebar-logo">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="Admin Avatar">
                </div>
                <div class="user-info">
                    <h3>Administrator</h3>
                    <p><EMAIL></p>
                </div>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="companies.html">
                            <i class="fas fa-building"></i>
                            <span>Firmy</span>
                        </a>
                    </li>
                    <li>
                        <a href="offers.html">
                            <i class="fas fa-tag"></i>
                            <span>Oferty</span>
                        </a>
                    </li>
                    <li>
                        <a href="coupons.html">
                            <i class="fas fa-ticket-alt"></i>
                            <span>Kupony</span>
                        </a>
                    </li>
                    <li>
                        <a href="categories.html">
                            <i class="fas fa-list"></i>
                            <span>Kategorie</span>
                        </a>
                    </li>
                                        <li>
                        <a href="news.html">
                            <i class="fas fa-newspaper"></i>
                            <span>Wiadomości</span>
                        </a>
                    </li>
<li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Użytkownicy</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Ustawienia</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <button id="logoutBtn" class="btn-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Wyloguj</span>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-title">
                    <h1>Ustawienia</h1>
                    <p>Zarządzaj ustawieniami strony</p>
                </div>
                <div class="header-actions">
                    <div class="search-box">
                        <input type="text" placeholder="Szukaj...">
                        <button><i class="fas fa-search"></i></button>
                    </div>
                    <div class="notifications">
                        <button class="notification-btn">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge">3</span>
                        </button>
                    </div>
                </div>
            </header>

            <div class="content-body">
                <div class="settings-container">
                    <!-- Tabs Navigation -->
                    <div class="settings-tabs">
                        <button class="tab-btn active" data-tab="homepage">Strona główna</button>
                        <button class="tab-btn" data-tab="general">Ogólne</button>
                        <button class="tab-btn" data-tab="appearance">Wygląd</button>
                        <button class="tab-btn" data-tab="seo">SEO</button>
                        <button class="tab-btn" data-tab="social">Media społecznościowe</button>
                        <button class="tab-btn" data-tab="advanced">Zaawansowane</button>
                    </div>

                    <!-- Tab Content -->
                    <div class="settings-content">
                        <!-- Homepage Settings Tab -->
                        <div class="tab-content active" id="homepage">
                            <h2>Ustawienia strony głównej</h2>
                            <p class="settings-description">Zarządzaj sekcjami i zawartością strony głównej</p>

                            <div class="settings-section">
                                <h3>Widoczność sekcji</h3>
                                <div class="settings-options">
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Katalog firm</h4>
                                            <p>Pokaż/ukryj sekcję z katalogiem firm na stronie głównej</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-companies" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Kupony rabatowe</h4>
                                            <p>Pokaż/ukryj sekcję z kuponami rabatowymi na stronie głównej</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-coupons" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>O Żyrardowie</h4>
                                            <p>Pokaż/ukryj sekcję z informacjami o mieście na stronie głównej</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-about" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Odkryj Żyrardów</h4>
                                            <p>Pokaż/ukryj sekcję z atrakcjami miasta na stronie głównej</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-discover" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Polecane oferty</h4>
                                            <p>Pokaż/ukryj sekcję z polecanymi ofertami na stronie głównej</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-offers" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Najciekawsze miejsca</h4>
                                            <p>Pokaż/ukryj sekcję z najciekawszymi miejscami na stronie głównej</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-places" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Wiadomości lokalne</h4>
                                            <p>Pokaż/ukryj sekcję z najnowszymi wiadomościami lokalnymi</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="newsToggle">
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Żyrardów w liczbach</h4>
                                            <p>Pokaż/ukryj sekcję ze statystykami miasta na stronie głównej</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-stats" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Slider główny</h3>
                                <div class="settings-options">
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Edycja slidera</h4>
                                            <p>Zarządzaj slajdami na stronie głównej</p>
                                        </div>
                                        <div class="setting-control">
                                            <a href="slider-edit.html" class="btn btn-primary">Edytuj slider</a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Kolejność sekcji na stronie</h3>
                                <div class="settings-options">
                                    <div class="section-order-container">
                                        <p class="section-description">Przeciągnij sekcje, aby zmienić ich kolejność na stronie głównej:</p>
                                        <div class="sortable-sections" id="sortable-sections">
                                            <div class="section-item" data-section="hero">
                                                <div class="section-handle">
                                                    <i class="fas fa-grip-vertical"></i>
                                                </div>
                                                <div class="section-info">
                                                    <h4>Slider główny</h4>
                                                    <p>Sekcja z głównym sliderem</p>
                                                </div>
                                                <div class="section-status">
                                                    <span class="status-badge fixed">Stała pozycja</span>
                                                </div>
                                            </div>
                                            <div class="section-item" data-section="city-info">
                                                <div class="section-handle">
                                                    <i class="fas fa-grip-vertical"></i>
                                                </div>
                                                <div class="section-info">
                                                    <h4>Informacje o mieście</h4>
                                                    <p>3 boxy z informacjami o Żyrardowie</p>
                                                </div>
                                                <div class="section-status">
                                                    <span class="status-badge active">Aktywna</span>
                                                </div>
                                            </div>
                                            <div class="section-item" data-section="categories">
                                                <div class="section-handle">
                                                    <i class="fas fa-grip-vertical"></i>
                                                </div>
                                                <div class="section-info">
                                                    <h4>Kategorie firm</h4>
                                                    <p>Katalog firm z TOP 3 rekomendacjami</p>
                                                </div>
                                                <div class="section-status">
                                                    <span class="status-badge active">Aktywna</span>
                                                </div>
                                            </div>
                                            <div class="section-item" data-section="coupons">
                                                <div class="section-handle">
                                                    <i class="fas fa-grip-vertical"></i>
                                                </div>
                                                <div class="section-info">
                                                    <h4>Kupony rabatowe</h4>
                                                    <p>Slider z kuponami rabatowymi</p>
                                                </div>
                                                <div class="section-status">
                                                    <span class="status-badge active">Aktywna</span>
                                                </div>
                                            </div>
                                            <div class="section-item" data-section="girard">
                                                <div class="section-handle">
                                                    <i class="fas fa-grip-vertical"></i>
                                                </div>
                                                <div class="section-info">
                                                    <h4>Rok Filipa de Girarda</h4>
                                                    <p>Sekcja o patronie miasta</p>
                                                </div>
                                                <div class="section-status">
                                                    <span class="status-badge active">Aktywna</span>
                                                </div>
                                            </div>
                                            <div class="section-item" data-section="heritage">
                                                <div class="section-handle">
                                                    <i class="fas fa-grip-vertical"></i>
                                                </div>
                                                <div class="section-info">
                                                    <h4>Pomnik Historii</h4>
                                                    <p>Sekcja o zabytkowej osadzie fabrycznej</p>
                                                </div>
                                                <div class="section-status">
                                                    <span class="status-badge active">Aktywna</span>
                                                </div>
                                            </div>
                                            <div class="section-item" data-section="about">
                                                <div class="section-handle">
                                                    <i class="fas fa-grip-vertical"></i>
                                                </div>
                                                <div class="section-info">
                                                    <h4>O Żyrardowie</h4>
                                                    <p>Informacje o mieście</p>
                                                </div>
                                                <div class="section-status">
                                                    <span class="status-badge active">Aktywna</span>
                                                </div>
                                            </div>
                                            <div class="section-item" data-section="seo">
                                                <div class="section-handle">
                                                    <i class="fas fa-grip-vertical"></i>
                                                </div>
                                                <div class="section-info">
                                                    <h4>Odkryj Żyrardów</h4>
                                                    <p>Sekcje SEO z informacjami o mieście</p>
                                                </div>
                                                <div class="section-status">
                                                    <span class="status-badge active">Aktywna</span>
                                                </div>
                                            </div>
                                            <div class="section-item" data-section="offers">
                                                <div class="section-handle">
                                                    <i class="fas fa-grip-vertical"></i>
                                                </div>
                                                <div class="section-info">
                                                    <h4>Polecane oferty</h4>
                                                    <p>Sekcja z ofertami specjalnymi</p>
                                                </div>
                                                <div class="section-status">
                                                    <span class="status-badge active">Aktywna</span>
                                                </div>
                                            </div>
                                            <div class="section-item" data-section="news">
                                                <div class="section-handle">
                                                    <i class="fas fa-grip-vertical"></i>
                                                </div>
                                                <div class="section-info">
                                                    <h4>Wiadomości lokalne</h4>
                                                    <p>Najnowsze informacje z Żyrardowa</p>
                                                </div>
                                                <div class="section-status">
                                                    <span class="status-badge hidden">Ukryta</span>
                                                </div>
                                            </div>
                                            <div class="section-item" data-section="places">
                                                <div class="section-handle">
                                                    <i class="fas fa-grip-vertical"></i>
                                                </div>
                                                <div class="section-info">
                                                    <h4>Odkryj Żyrardów</h4>
                                                    <p>Najciekawsze miejsca w mieście</p>
                                                </div>
                                                <div class="section-status">
                                                    <span class="status-badge active">Aktywna</span>
                                                </div>
                                            </div>
                                            <div class="section-item" data-section="stats">
                                                <div class="section-handle">
                                                    <i class="fas fa-grip-vertical"></i>
                                                </div>
                                                <div class="section-info">
                                                    <h4>Żyrardów w liczbach</h4>
                                                    <p>Statystyki miasta</p>
                                                </div>
                                                <div class="section-status">
                                                    <span class="status-badge active">Aktywna</span>
                                                </div>
                                            </div>
                                            <div class="section-item" data-section="weather">
                                                <div class="section-handle">
                                                    <i class="fas fa-grip-vertical"></i>
                                                </div>
                                                <div class="section-info">
                                                    <h4>Widget pogody</h4>
                                                    <p>Prognoza pogody dla Żyrardowa</p>
                                                </div>
                                                <div class="section-status">
                                                    <span class="status-badge active">Aktywna</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="section-order-actions">
                                            <button type="button" class="btn btn-outline" id="reset-section-order">
                                                <i class="fas fa-undo"></i> Przywróć domyślną kolejność
                                            </button>
                                            <button type="button" class="btn btn-primary" id="save-section-order">
                                                <i class="fas fa-save"></i> Zapisz kolejność
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-actions">
                                <button type="button" class="btn btn-primary" id="saveHomepageSettings">Zapisz ustawienia</button>
                                <button type="button" class="btn btn-outline">Anuluj</button>
                            </div>
                        </div>

                        <!-- General Settings Tab -->
                        <div class="tab-content" id="general">
                            <h2>Ustawienia ogólne</h2>
                            <p class="settings-description">Podstawowe ustawienia strony</p>

                            <div class="settings-section">
                                <h3>Informacje o stronie</h3>
                                <div class="settings-options">
                                    <div class="form-group">
                                        <label for="site-name">Nazwa strony</label>
                                        <input type="text" id="site-name" class="form-control" value="Żyrardów Poleca">
                                    </div>
                                    <div class="form-group">
                                        <label for="site-description">Opis strony</label>
                                        <textarea id="site-description" class="form-control">Portal informacyjny o Żyrardowie - znajdź najlepsze firmy, oferty i kupony rabatowe w mieście.</textarea>
                                    </div>
                                    <div class="form-group">
                                        <label for="site-logo">Logo strony</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload"></i>
                                                <span>Wybierz plik</span>
                                            </label>
                                            <input type="file" id="site-logo" accept="image/*">
                                        </div>
                                        <div class="current-logo" style="margin-top: 10px;">
                                            <p>Aktualne logo:</p>
                                            <img src="../images/logo-zyrardow-poleca.png" alt="Aktualne logo" style="max-height: 60px; margin-top: 5px;">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="site-favicon">Favicon</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload"></i>
                                                <span>Wybierz plik</span>
                                            </label>
                                            <input type="file" id="site-favicon" accept="image/x-icon,image/png">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Kontakt</h3>
                                <div class="settings-options">
                                    <div class="form-group">
                                        <label for="contact-email">Email kontaktowy</label>
                                        <input type="email" id="contact-email" class="form-control" value="<EMAIL>">
                                    </div>
                                    <div class="form-group">
                                        <label for="contact-phone">Telefon kontaktowy</label>
                                        <input type="text" id="contact-phone" class="form-control" value="+48 123 456 789">
                                    </div>
                                    <div class="form-group">
                                        <label for="contact-address">Adres</label>
                                        <textarea id="contact-address" class="form-control">ul. Przykładowa 1, 96-300 Żyrardów</textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Ustawienia regionalne</h3>
                                <div class="settings-options">
                                    <div class="form-group">
                                        <label for="site-language">Język strony</label>
                                        <select id="site-language" class="form-control">
                                            <option value="pl" selected>Polski</option>
                                            <option value="en">English</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="site-timezone">Strefa czasowa</label>
                                        <select id="site-timezone" class="form-control">
                                            <option value="Europe/Warsaw" selected>Europe/Warsaw (GMT+1/GMT+2)</option>
                                            <option value="UTC">UTC</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-actions">
                                <button type="button" class="btn btn-primary" id="saveGeneralSettings">Zapisz ustawienia</button>
                                <button type="button" class="btn btn-outline">Anuluj</button>
                            </div>
                        </div>

                        <!-- Appearance Settings Tab -->
                        <div class="tab-content" id="appearance">
                            <h2>Wygląd</h2>
                            <p class="settings-description">Dostosuj wygląd strony</p>

                            <div class="settings-section">
                                <h3>Kolory</h3>
                                <div class="settings-options">
                                    <div class="form-group">
                                        <label for="primary-color">Kolor główny</label>
                                        <div class="color-picker-container">
                                            <input type="color" id="primary-color" class="form-control" value="#ff6600">
                                            <div class="color-preview" style="background-color: #ff6600;"></div>
                                            <input type="text" class="form-control" value="#ff6600" style="max-width: 120px;">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="secondary-color">Kolor drugorzędny</label>
                                        <div class="color-picker-container">
                                            <input type="color" id="secondary-color" class="form-control" value="#333333">
                                            <div class="color-preview" style="background-color: #333333;"></div>
                                            <input type="text" class="form-control" value="#333333" style="max-width: 120px;">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Czcionki</h3>
                                <div class="settings-options">
                                    <div class="form-group">
                                        <label for="font-family">Rodzina czcionek</label>
                                        <select id="font-family" class="form-control">
                                            <option value="'Segoe UI', Tahoma, Geneva, Verdana, sans-serif" selected>Segoe UI</option>
                                            <option value="'Roboto', sans-serif">Roboto</option>
                                            <option value="'Open Sans', sans-serif">Open Sans</option>
                                            <option value="'Montserrat', sans-serif">Montserrat</option>
                                            <option value="'Lato', sans-serif">Lato</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="font-size">Rozmiar czcionki podstawowej</label>
                                        <select id="font-size" class="form-control">
                                            <option value="14px">14px</option>
                                            <option value="16px" selected>16px</option>
                                            <option value="18px">18px</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Układ strony</h3>
                                <div class="settings-options">
                                    <div class="form-group">
                                        <label>Szerokość kontenera</label>
                                        <div class="toggle-group">
                                            <div class="toggle-option">
                                                <input type="radio" id="container-width-1200" name="container-width" value="1200px">
                                                <label for="container-width-1200">1200px</label>
                                            </div>
                                            <div class="toggle-option">
                                                <input type="radio" id="container-width-1400" name="container-width" value="1400px" checked>
                                                <label for="container-width-1400">1400px</label>
                                            </div>
                                            <div class="toggle-option">
                                                <input type="radio" id="container-width-1600" name="container-width" value="1600px">
                                                <label for="container-width-1600">1600px</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>Styl menu</label>
                                        <div class="toggle-group">
                                            <div class="toggle-option">
                                                <input type="radio" id="menu-style-standard" name="menu-style" value="standard" checked>
                                                <label for="menu-style-standard">Standardowe</label>
                                            </div>
                                            <div class="toggle-option">
                                                <input type="radio" id="menu-style-sticky" name="menu-style" value="sticky">
                                                <label for="menu-style-sticky">Przyklejone</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-actions">
                                <button type="button" class="btn btn-primary" id="saveAppearanceSettings">Zapisz ustawienia</button>
                                <button type="button" class="btn btn-outline">Anuluj</button>
                            </div>
                        </div>

                        <!-- SEO Settings Tab -->
                        <div class="tab-content" id="seo">
                            <h2>SEO</h2>
                            <p class="settings-description">Ustawienia optymalizacji dla wyszukiwarek</p>

                            <div class="settings-section">
                                <h3>Metadane</h3>
                                <div class="settings-options">
                                    <div class="form-group">
                                        <label for="meta-title">Tytuł strony (meta title)</label>
                                        <input type="text" id="meta-title" class="form-control" value="Żyrardów Poleca - Najlepsze firmy, oferty i kupony rabatowe w Żyrardowie">
                                        <small class="form-text text-muted">Zalecana długość: 50-60 znaków</small>
                                    </div>
                                    <div class="form-group">
                                        <label for="meta-description">Opis strony (meta description)</label>
                                        <textarea id="meta-description" class="form-control">Żyrardów Poleca to portal informacyjny o Żyrardowie. Znajdź najlepsze firmy, oferty i kupony rabatowe w mieście. Odkryj atrakcje turystyczne i wydarzenia.</textarea>
                                        <small class="form-text text-muted">Zalecana długość: 150-160 znaków</small>
                                    </div>
                                    <div class="form-group">
                                        <label for="meta-keywords">Słowa kluczowe (meta keywords)</label>
                                        <input type="text" id="meta-keywords" class="form-control" value="Żyrardów, firmy Żyrardów, kupony rabatowe Żyrardów, oferty Żyrardów, atrakcje Żyrardów">
                                        <small class="form-text text-muted">Oddziel słowa kluczowe przecinkami</small>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Integracja z narzędziami analitycznymi</h3>
                                <div class="settings-options">
                                    <div class="form-group">
                                        <label for="google-analytics">Google Analytics ID</label>
                                        <input type="text" id="google-analytics" class="form-control" placeholder="np. UA-XXXXXXXXX-X">
                                    </div>
                                    <div class="form-group">
                                        <label for="google-tag-manager">Google Tag Manager ID</label>
                                        <input type="text" id="google-tag-manager" class="form-control" placeholder="np. GTM-XXXXXX">
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Mapy witryny</h3>
                                <div class="settings-options">
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Automatyczna generacja sitemap.xml</h4>
                                            <p>Automatycznie generuj i aktualizuj mapę witryny</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-sitemap" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="form-group" style="margin-top: 15px;">
                                        <button type="button" class="btn btn-outline">Generuj sitemap.xml</button>
                                        <a href="../sitemap.xml" target="_blank" class="btn btn-link">Zobacz aktualną mapę witryny</a>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-actions">
                                <button type="button" class="btn btn-primary" id="saveSeoSettings">Zapisz ustawienia</button>
                                <button type="button" class="btn btn-outline">Anuluj</button>
                            </div>
                        </div>

                        <!-- Social Media Settings Tab -->
                        <div class="tab-content" id="social">
                            <h2>Media społecznościowe</h2>
                            <p class="settings-description">Zarządzaj integracją z mediami społecznościowymi</p>

                            <div class="settings-section">
                                <h3>Profile społecznościowe</h3>
                                <div class="settings-options">
                                    <div class="form-group">
                                        <label for="facebook-url">Facebook URL</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fab fa-facebook-f"></i></span>
                                            <input type="url" id="facebook-url" class="form-control" value="https://facebook.com/zyrardow.poleca">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="instagram-url">Instagram URL</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fab fa-instagram"></i></span>
                                            <input type="url" id="instagram-url" class="form-control" value="https://instagram.com/zyrardow.poleca">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="twitter-url">Twitter URL</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fab fa-twitter"></i></span>
                                            <input type="url" id="twitter-url" class="form-control" placeholder="https://twitter.com/twojprofil">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="youtube-url">YouTube URL</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fab fa-youtube"></i></span>
                                            <input type="url" id="youtube-url" class="form-control" placeholder="https://youtube.com/channel/twojkanal">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Integracja z Facebookiem</h3>
                                <div class="settings-options">
                                    <div class="form-group">
                                        <label for="facebook-app-id">Facebook App ID</label>
                                        <input type="text" id="facebook-app-id" class="form-control" placeholder="np. 123456789012345">
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Przycisk "Lubię to"</h4>
                                            <p>Wyświetlaj przycisk "Lubię to" na stronach</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-facebook-like" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Komentarze Facebooka</h4>
                                            <p>Włącz komentarze Facebooka na stronach</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-facebook-comments" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Udostępnianie w mediach społecznościowych</h3>
                                <div class="settings-options">
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Przyciski udostępniania</h4>
                                            <p>Wyświetlaj przyciski udostępniania na stronach</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-share-buttons" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>Wybierz platformy do udostępniania</label>
                                        <div class="checkbox-group">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="share-facebook" checked>
                                                <label class="form-check-label" for="share-facebook">Facebook</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="share-twitter" checked>
                                                <label class="form-check-label" for="share-twitter">Twitter</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="share-linkedin">
                                                <label class="form-check-label" for="share-linkedin">LinkedIn</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="share-pinterest">
                                                <label class="form-check-label" for="share-pinterest">Pinterest</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="share-email" checked>
                                                <label class="form-check-label" for="share-email">Email</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-actions">
                                <button type="button" class="btn btn-primary" id="saveSocialSettings">Zapisz ustawienia</button>
                                <button type="button" class="btn btn-outline">Anuluj</button>
                            </div>
                        </div>

                        <!-- Advanced Settings Tab -->
                        <div class="tab-content" id="advanced">
                            <h2>Ustawienia zaawansowane</h2>
                            <p class="settings-description">Zaawansowane opcje konfiguracyjne</p>

                            <div class="settings-section">
                                <h3>Moduł pogody</h3>
                                <div class="settings-options">
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Widget pogody</h4>
                                            <p>Wyświetlaj prognozę pogody na stronie głównej</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-weather" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="weather-city">Miasto</label>
                                        <input type="text" id="weather-city" class="form-control" value="Żyrardów" placeholder="Nazwa miasta">
                                        <small class="form-text text-muted">Nazwa miasta do wyświetlania prognozy pogody</small>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="weather-lat">Szerokość geograficzna</label>
                                            <input type="number" id="weather-lat" class="form-control" step="0.0001" value="52.0500" placeholder="52.0500">
                                        </div>
                                        <div class="form-group">
                                            <label for="weather-lon">Długość geograficzna</label>
                                            <input type="number" id="weather-lon" class="form-control" step="0.0001" value="20.4500" placeholder="20.4500">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="weather-api-key">Klucz API WeatherAPI.com</label>
                                        <div class="input-group">
                                            <input type="password" id="weather-api-key" class="form-control" placeholder="Wprowadź swój klucz API">
                                            <button type="button" class="btn btn-outline" id="toggle-api-key-visibility">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <small class="form-text text-muted">
                                            <strong>Jak uzyskać darmowy klucz API:</strong><br>
                                            1. Zarejestruj się na <a href="https://www.weatherapi.com/signup.aspx" target="_blank">WeatherAPI.com</a><br>
                                            2. Potwierdź email i zaloguj się<br>
                                            3. Skopiuj klucz z dashboardu (np. <code>1234567890abcdef1234567890abcdef</code>)<br>
                                            4. Wklej klucz powyżej i zapisz ustawienia<br>
                                            <em>🎁 Darmowe konto: aktualna pogoda + prognoza 3-dniowa (1000 wywołań/miesiąc).<br>
                                            🔧 Bez klucza używane są statyczne dane testowe.</em>
                                        </small>
                                    </div>
                                    <div class="form-group">
                                        <label for="weather-position">Pozycja na stronie</label>
                                        <select id="weather-position" class="form-control">
                                            <option value="top">Góra strony</option>
                                            <option value="bottom" selected>Dół strony</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <button type="button" id="test-weather-btn" class="btn btn-outline">
                                            <i class="fas fa-cloud-sun"></i> Testuj połączenie
                                        </button>
                                        <button type="button" id="save-weather-btn" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Zapisz ustawienia pogody
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Wydajność</h3>
                                <div class="settings-options">
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Minifikacja CSS i JavaScript</h4>
                                            <p>Zmniejsz rozmiar plików CSS i JavaScript, aby przyspieszyć ładowanie strony</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-minify" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Buforowanie przeglądarki</h4>
                                            <p>Włącz buforowanie zasobów w przeglądarce użytkownika</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-browser-cache" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Lazy loading obrazów</h4>
                                            <p>Ładuj obrazy dopiero gdy są widoczne na ekranie</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-lazy-loading" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Cache i optymalizacja</h3>
                                <div class="settings-options">
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Wyczyść cache strony</h4>
                                            <p>Usuń wszystkie pliki cache, aby wymusić odświeżenie treści</p>
                                        </div>
                                        <div class="setting-control">
                                            <button type="button" class="btn btn-warning" id="clear-cache-btn">
                                                <i class="fas fa-trash-alt"></i> Wyczyść cache
                                            </button>
                                        </div>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Wyczyść cache dashboardu</h4>
                                            <p>Usuń cache panelu administracyjnego i wymuś odświeżenie</p>
                                        </div>
                                        <div class="setting-control">
                                            <button type="button" class="btn btn-warning" id="clear-admin-cache-btn">
                                                <i class="fas fa-broom"></i> Wyczyść cache admin
                                            </button>
                                        </div>
                                    </div>
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Regeneruj pliki CSS/JS</h4>
                                            <p>Przebuduj i zoptymalizuj wszystkie pliki stylów i skryptów</p>
                                        </div>
                                        <div class="setting-control">
                                            <button type="button" class="btn btn-info" id="regenerate-assets-btn">
                                                <i class="fas fa-sync-alt"></i> Regeneruj zasoby
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Bezpieczeństwo</h3>
                                <div class="settings-options">
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Ochrona przed spamem</h4>
                                            <p>Włącz ochronę formularzy przed spamem</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-spam-protection" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="recaptcha-site-key">Google reCAPTCHA Site Key</label>
                                        <input type="text" id="recaptcha-site-key" class="form-control" placeholder="np. 6LdXXXXXXXXXXXXXXXXXXXXXXXXXXXXX">
                                    </div>
                                    <div class="form-group">
                                        <label for="recaptcha-secret-key">Google reCAPTCHA Secret Key</label>
                                        <input type="text" id="recaptcha-secret-key" class="form-control" placeholder="np. 6LdXXXXXXXXXXXXXXXXXXXXXXXXXXXXX">
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Kopie zapasowe</h3>
                                <div class="settings-options">
                                    <div class="setting-item">
                                        <div class="setting-info">
                                            <h4>Automatyczne kopie zapasowe</h4>
                                            <p>Twórz automatyczne kopie zapasowe bazy danych i plików</p>
                                        </div>
                                        <div class="setting-control">
                                            <label class="switch">
                                                <input type="checkbox" id="toggle-auto-backup" checked>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="backup-frequency">Częstotliwość kopii zapasowych</label>
                                        <select id="backup-frequency" class="form-control">
                                            <option value="daily">Codziennie</option>
                                            <option value="weekly" selected>Co tydzień</option>
                                            <option value="monthly">Co miesiąc</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <button type="button" class="btn btn-outline">Utwórz kopię zapasową teraz</button>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-actions">
                                <button type="button" class="btn btn-primary" id="saveAdvancedSettings">Zapisz ustawienia</button>
                                <button type="button" class="btn btn-outline">Anuluj</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="js/admin.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/weather-settings.js"></script>
    <script src="js/section-order.js"></script>
</body>
</html>
