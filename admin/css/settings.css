/* Settings Page Styles */
.settings-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

/* Tabs Navigation */
.settings-tabs {
    display: flex;
    border-bottom: 1px solid #e9ecef;
    padding: 0 20px;
    overflow-x: auto;
}

.tab-btn {
    padding: 15px 20px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-btn:hover {
    color: var(--primary-color);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* Tab Content */
.settings-content {
    padding: 30px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.settings-description {
    color: #6c757d;
    margin-bottom: 30px;
}

/* Settings Sections */
.settings-section {
    margin-bottom: 40px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 30px;
}

.settings-section:last-child {
    border-bottom: none;
}

.settings-section h3 {
    font-size: 18px;
    margin-bottom: 20px;
    color: #343a40;
}

/* Settings Options */
.settings-options {
    display: grid;
    gap: 20px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.setting-item:hover {
    background-color: #f1f3f5;
}

.setting-info {
    flex: 1;
}

.setting-info h4 {
    font-size: 16px;
    margin-bottom: 5px;
    color: #343a40;
}

.setting-info p {
    font-size: 14px;
    color: #6c757d;
    margin: 0;
}

.setting-control {
    margin-left: 20px;
}

/* Toggle Switch */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

/* Settings Actions */
.settings-actions {
    display: flex;
    justify-content: flex-start;
    gap: 15px;
    margin-top: 30px;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #343a40;
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

/* Color Picker */
.color-picker-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-preview {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #ced4da;
}

/* File Upload */
.file-upload {
    position: relative;
    display: inline-block;
    width: 100%;
}

.file-upload-label {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 15px;
    background-color: var(--light-gray);
    border: 1px dashed #ced4da;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-upload-label:hover {
    background-color: var(--medium-gray);
}

.file-upload-label i {
    margin-right: 10px;
}

.file-upload input[type="file"] {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

/* Toggle Group */
.toggle-group {
    display: flex;
    gap: 10px;
}

.toggle-option {
    flex: 1;
    text-align: center;
}

.toggle-option input[type="radio"] {
    display: none;
}

.toggle-option label {
    display: block;
    padding: 10px;
    background-color: var(--light-gray);
    border: 1px solid #ced4da;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toggle-option input[type="radio"]:checked + label {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .settings-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .setting-control {
        margin-left: 0;
        margin-top: 15px;
        align-self: flex-start;
    }

    .settings-actions {
        flex-direction: column;
    }

    .settings-actions .btn {
        width: 100%;
    }
}
