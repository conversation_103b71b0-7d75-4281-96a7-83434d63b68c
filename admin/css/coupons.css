/* 
 * Panel Administracyjny - Żyrardów Poleca
 * Style dla zarządzania kuponami
 */

/* Filters */
.filters-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 20px;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    padding: 20px;
    box-shadow: var(--shadow-sm);
}

.filters {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-group {
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

/* Table Styles */
.table-container {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: 20px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--medium-gray);
}

.data-table th {
    background-color: var(--light-gray);
    font-weight: 600;
    color: var(--text-dark);
}

.data-table tbody tr:hover {
    background-color: rgba(245, 245, 245, 0.5);
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* Coupon Code */
.coupon-code {
    display: inline-block;
    padding: 5px 10px;
    background-color: var(--light-gray);
    border-radius: 4px;
    font-family: monospace;
    font-weight: 600;
    letter-spacing: 1px;
}

/* Discount */
.discount {
    font-weight: 600;
    color: var(--primary-color);
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-badge.active {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
}

.status-badge.expired {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--text-medium);
}

.status-badge.scheduled {
    background-color: rgba(0, 123, 255, 0.1);
    color: var(--info);
}

/* Action Buttons */
.actions {
    white-space: nowrap;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--text-medium);
    font-size: 1rem;
    cursor: pointer;
    padding: 5px;
    transition: var(--transition);
}

.btn-icon:hover {
    color: var(--primary-color);
}

.btn-icon.delete:hover {
    color: var(--danger);
}

/* Bulk Actions */
.bulk-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.bulk-actions-select {
    display: flex;
    align-items: center;
    gap: 10px;
}

.bulk-actions-select span {
    font-weight: 500;
    color: var(--text-dark);
}

.bulk-actions-select select {
    min-width: 150px;
}

/* Pagination */
.pagination {
    display: flex;
    gap: 5px;
}

.btn-page {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--white);
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    color: var(--text-medium);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.btn-page:hover {
    background-color: var(--light-gray);
}

.btn-page.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.btn-page:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--medium-gray);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-medium);
    cursor: pointer;
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--danger);
}

.modal-body {
    padding: 20px;
}

/* Coupon Preview */
.coupon-preview {
    border: 2px dashed var(--primary-color);
    border-radius: var(--border-radius-md);
    padding: 20px;
    background-color: #fff;
}

.coupon-preview-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.coupon-company-logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
}

.coupon-company-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.coupon-company-name {
    margin: 0 0 5px 0;
    font-size: 1.2rem;
}

.coupon-company-address {
    margin: 0;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.coupon-preview-body {
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid var(--light-gray);
    border-bottom: 1px solid var(--light-gray);
}

.coupon-discount {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.coupon-description {
    font-size: 1.2rem;
    margin-bottom: 20px;
}

.coupon-code-container {
    margin-bottom: 10px;
}

.coupon-code-label {
    font-weight: 500;
    margin-right: 10px;
}

.coupon-code-value {
    display: inline-block;
    padding: 8px 15px;
    background-color: var(--light-gray);
    border-radius: 4px;
    font-family: monospace;
    font-weight: 600;
    letter-spacing: 1px;
    font-size: 1.2rem;
}

.coupon-validity {
    margin-top: 15px;
    font-size: 0.9rem;
}

.coupon-validity-label {
    font-weight: 500;
    margin-right: 5px;
}

.coupon-preview-footer {
    padding-top: 15px;
}

.coupon-terms {
    font-size: 0.8rem;
    color: var(--text-medium);
    font-style: italic;
}

/* Form Elements */
.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 0.9rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* Buttons */
.btn {
    padding: 10px 15px;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--medium-gray);
    color: var(--text-medium);
}

.btn-outline:hover {
    background-color: var(--light-gray);
    color: var(--text-dark);
}

.btn i {
    font-size: 0.9rem;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .filters {
        flex-direction: column;
        gap: 10px;
    }
    
    .filter-group {
        width: 100%;
    }
}

@media (max-width: 992px) {
    .filters-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .data-table {
        display: block;
        overflow-x: auto;
    }
    
    .bulk-actions {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .pagination {
        margin-top: 15px;
    }
}

@media (max-width: 768px) {
    .header-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .search-box {
        width: 100%;
    }
    
    .header-actions .btn {
        width: 100%;
    }
}
