/**
 * Style CSS dla systemu pozycji TOP
 * Panel Administracyjny - Żyrardów Poleca
 */

/* Modal pozycji TOP */
#topPositionModal .modal-content {
    max-width: 600px;
    width: 90%;
}

.top-position-info {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.top-position-info p {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.top-positions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.top-position-option {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.top-position-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.top-position-option.selected {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,123,255,0.3);
}

.top-position-option[data-position="remove"] {
    border-color: #dc3545;
}

.top-position-option[data-position="remove"]:hover {
    border-color: #c82333;
    background: #f8d7da;
}

.top-position-option[data-position="remove"].selected {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.position-badge {
    font-size: 14px;
    font-weight: 700;
    padding: 6px 12px;
    border-radius: 20px;
    margin-bottom: 10px;
    display: inline-block;
    background: var(--primary-color);
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.top-position-option[data-position="remove"] .position-badge {
    background: #dc3545;
}

.top-position-option.selected .position-badge {
    background: rgba(255,255,255,0.2);
    color: white;
}

.position-info h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.top-position-option.selected .position-info h4 {
    color: white;
}

.position-info p {
    margin: 0 0 10px 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.top-position-option.selected .position-info p {
    color: rgba(255,255,255,0.9);
}

.current-company {
    margin-top: auto;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
}

.top-position-option.selected .current-company {
    border-top-color: rgba(255,255,255,0.2);
}

.current-company .empty {
    font-size: 11px;
    color: #999;
    font-style: italic;
}

.current-company .occupied {
    font-size: 11px;
    color: #28a745;
    font-weight: 600;
    background: #d4edda;
    padding: 3px 8px;
    border-radius: 12px;
    display: inline-block;
}

.top-position-option.selected .current-company .empty {
    color: rgba(255,255,255,0.7);
}

.top-position-option.selected .current-company .occupied {
    background: rgba(255,255,255,0.2);
    color: white;
}

/* Przyciski TOP w tabeli */
.btn-icon.top-position {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
    border: none;
}

.btn-icon.top-position:hover {
    background: linear-gradient(135deg, #e0a800, #d39e00);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255,193,7,0.3);
}

.btn-icon.top-position i {
    color: #212529;
}

/* Wskaźnik pozycji TOP w tabeli */
.top-position-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    font-weight: 600;
    padding: 3px 8px;
    border-radius: 12px;
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.top-position-indicator i {
    font-size: 10px;
}

/* Responsywność */
@media (max-width: 768px) {
    .top-positions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
    
    .top-position-option {
        padding: 12px;
        min-height: 100px;
    }
    
    .position-badge {
        font-size: 12px;
        padding: 4px 8px;
    }
    
    .position-info h4 {
        font-size: 14px;
    }
    
    .position-info p {
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    .top-positions-grid {
        grid-template-columns: 1fr;
    }
    
    #topPositionModal .modal-content {
        width: 95%;
        margin: 20px auto;
    }
}

/* Animacje */
@keyframes topPositionPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.top-position-option.selected {
    animation: topPositionPulse 0.3s ease-in-out;
}

/* Powiadomienia */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.notification.show {
    transform: translateX(0);
}

.notification.hiding {
    transform: translateX(100%);
}

.notification.success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.notification.error {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.notification.info {
    background: linear-gradient(135deg, #17a2b8, #20c997);
}

.notification.warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

/* Overlay dla modalu */
.modal {
    background: rgba(0,0,0,0.5);
    backdrop-filter: blur(2px);
}

.modal.show {
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}
