/* Slider Edit Page Styles */
.slider-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    padding: 30px;
}

/* Slider Items */
.slider-items {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.slider-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.slider-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.slider-item-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.slider-item-title h3 {
    margin: 0;
    font-size: 18px;
    color: #343a40;
}

.slider-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: #e9ecef;
    color: #6c757d;
}

.slider-status.active {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
}

.slider-item-actions {
    display: flex;
    gap: 10px;
}

.btn-icon {
    width: 36px;
    height: 36px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    background-color: #fff;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-icon:hover {
    background-color: #f8f9fa;
    color: var(--primary-color);
}

.toggle-slide {
    color: var(--success);
}

.delete-slide {
    color: var(--danger);
}

.delete-slide:hover {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger);
}

/* Slider Item Content */
.slider-item-content {
    display: flex;
    padding: 20px;
    gap: 30px;
}

.slider-preview {
    flex: 0 0 300px;
    height: 200px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.slider-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slider-form {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Form Elements */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #343a40;
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
}

.form-row {
    display: flex;
    gap: 20px;
}

.form-row .form-group {
    flex: 1;
}

/* File Input */
.file-input-container {
    position: relative;
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 2;
}

.file-input-custom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: #fff;
}

.file-name {
    color: #6c757d;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
}

/* Slider Actions */
.slider-actions {
    display: flex;
    justify-content: flex-start;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: #fff;
    border-radius: 8px;
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h2 {
    margin: 0;
    font-size: 20px;
    color: #343a40;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .slider-item-content {
        flex-direction: column;
    }
    
    .slider-preview {
        flex: 0 0 auto;
        width: 100%;
    }
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 15px;
    }
    
    .slider-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .slider-item-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .slider-actions {
        flex-direction: column;
    }
    
    .slider-actions .btn {
        width: 100%;
    }
}
