/* Map Management Styles */

.content-body {
    display: flex;
    gap: 20px;
    height: calc(100vh - 140px);
}

.map-container {
    flex: 1;
    position: relative;
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.map {
    width: 100%;
    height: 100%;
    min-height: 500px;
}

.companies-sidebar {
    width: 350px;
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    display: flex;
    flex-direction: column;
}

.companies-sidebar .sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.companies-sidebar .sidebar-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-dark);
}

.companies-count {
    font-size: 0.9rem;
    color: var(--text-medium);
}

.companies-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

/* Map Controls */
.map-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.map-controls select {
    min-width: 150px;
}

/* Map Legend */
.map-legend {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.95);
    padding: 15px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(10px);
    z-index: 1000;
}

.map-legend h4 {
    margin: 0 0 10px 0;
    font-size: 1rem;
    color: var(--text-dark);
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.legend-item:last-child {
    margin-bottom: 0;
}

.legend-marker {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 10px;
    border: 2px solid var(--white);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.legend-marker.top-1 {
    background-color: #FFD700;
}

.legend-marker.top-2 {
    background-color: #C0C0C0;
}

.legend-marker.top-3 {
    background-color: #CD7F32;
}

.legend-marker.regular {
    background-color: var(--primary-color);
}

/* Company List Items */
.company-item {
    background: var(--light-gray);
    border-radius: var(--border-radius-sm);
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: var(--transition);
    border-left: 4px solid transparent;
}

.company-item:hover {
    background: var(--medium-gray);
    transform: translateX(2px);
}

.company-item.active {
    background: rgba(255, 102, 0, 0.1);
    border-left-color: var(--primary-color);
}

.company-item.top-1 {
    border-left-color: #FFD700;
}

.company-item.top-2 {
    border-left-color: #C0C0C0;
}

.company-item.top-3 {
    border-left-color: #CD7F32;
}

.company-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.company-name {
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
    font-size: 1rem;
}

.company-top-badge {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.company-top-badge.top-1 {
    background-color: #FFD700;
    color: #B8860B;
}

.company-top-badge.top-2 {
    background-color: #C0C0C0;
    color: #696969;
}

.company-top-badge.top-3 {
    background-color: #CD7F32;
    color: #8B4513;
}

.company-category {
    font-size: 0.85rem;
    color: var(--text-medium);
    margin-bottom: 5px;
}

.company-address {
    font-size: 0.8rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
}

.company-address i {
    margin-right: 5px;
}

.company-actions {
    display: flex;
    gap: 5px;
    margin-top: 10px;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.75rem;
    border-radius: var(--border-radius-sm);
}

/* Custom Map Markers */
.custom-marker {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 3px solid var(--white);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: bold;
    font-size: 0.8rem;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.custom-marker:hover {
    transform: scale(1.1);
}

.custom-marker.top-1 {
    background-color: #FFD700;
    color: #B8860B;
}

.custom-marker.top-2 {
    background-color: #C0C0C0;
    color: #696969;
}

.custom-marker.top-3 {
    background-color: #CD7F32;
    color: #8B4513;
}

.custom-marker.regular {
    background-color: var(--primary-color);
    color: var(--white);
}

/* Center marker styles */
.center-marker {
    background: transparent !important;
    border: none !important;
}

.center-marker-content {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    border: 3px solid var(--white);
}

/* Temp marker styles */
.temp-marker {
    background: transparent !important;
    border: none !important;
}

.temp-marker-content {
    width: 30px;
    height: 30px;
    background: #e74c3c;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    border: 2px solid var(--white);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 4px 16px rgba(231, 76, 60, 0.5);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
}

/* Map error styles */
.map-error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 400px;
    background: var(--light-gray);
}

.map-error-content {
    text-align: center;
    padding: 40px;
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    max-width: 400px;
}

.map-error-content i {
    font-size: 3rem;
    color: var(--danger);
    margin-bottom: 20px;
}

.map-error-content h3 {
    margin-bottom: 15px;
    color: var(--text-dark);
}

.map-error-content p {
    margin-bottom: 20px;
    color: var(--text-medium);
}

/* Modal Styles */
.form-row {
    display: flex;
    gap: 15px;
}

.form-row .form-group {
    flex: 1;
}

.btn.btn-outline.btn-sm {
    margin-top: 5px;
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* Popup Styles */
.leaflet-popup-content {
    margin: 10px;
    line-height: 1.4;
}

.popup-header {
    margin-bottom: 8px;
}

.popup-title {
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 3px 0;
}

.popup-category {
    font-size: 0.85rem;
    color: var(--text-medium);
    margin: 0;
}

.popup-address {
    font-size: 0.9rem;
    color: var(--text-medium);
    margin: 8px 0;
    display: flex;
    align-items: center;
}

.popup-address i {
    margin-right: 5px;
}

.popup-actions {
    display: flex;
    gap: 8px;
    margin-top: 10px;
}

.popup-actions .btn {
    padding: 4px 8px;
    font-size: 0.75rem;
}

/* Loading State */
.map-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    z-index: 1000;
}

.map-loading i {
    font-size: 2rem;
    color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .content-body {
        flex-direction: column;
        height: auto;
    }

    .companies-sidebar {
        width: 100%;
        max-height: 300px;
    }

    .map-container {
        height: 400px;
    }

    .map-controls {
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .map-legend {
        bottom: 10px;
        left: 10px;
        right: 10px;
        padding: 10px;
    }

    .legend-item {
        font-size: 0.8rem;
    }

    .company-item {
        padding: 10px;
    }

    .header-actions {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .map-controls {
        justify-content: stretch;
    }

    .map-controls select {
        min-width: auto;
        flex: 1;
    }
}

/* Empty State */
.empty-companies {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-medium);
}

.empty-companies i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: var(--border-color);
}

.empty-companies h4 {
    margin-bottom: 8px;
    color: var(--text-dark);
}

.empty-companies p {
    margin-bottom: 15px;
}
