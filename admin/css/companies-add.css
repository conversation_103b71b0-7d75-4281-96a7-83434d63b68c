/* 
 * Panel Administracyjny - Żyrardów Poleca
 * Style dla dodawania/edycji firm
 */

/* Form Container */
.form-container {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: 30px;
}

/* Form Tabs */
.form-tabs {
    display: flex;
    border-bottom: 1px solid var(--medium-gray);
    padding: 0 20px;
    overflow-x: auto;
}

.form-tabs .tab-btn {
    padding: 15px 20px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--text-medium);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.form-tabs .tab-btn:hover {
    color: var(--primary-color);
}

.form-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* Form Content */
.form-content {
    padding: 30px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.form-description {
    color: var(--text-medium);
    margin-bottom: 30px;
}

/* Form Sections */
.form-section {
    margin-bottom: 30px;
}

/* Form Groups */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-group .required {
    color: var(--danger);
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.1);
}

.form-text {
    display: block;
    margin-top: 5px;
    font-size: 0.85rem;
    color: var(--text-medium);
}

/* Form Row */
.form-row {
    display: flex;
    gap: 20px;
}

.form-row .form-group {
    flex: 1;
}

/* Character Counter */
.char-counter {
    text-align: right;
    font-size: 0.85rem;
    color: var(--text-medium);
    margin-top: 5px;
}

/* Radio & Checkbox Groups */
.radio-group,
.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.radio-option,
.checkbox-option {
    display: flex;
    align-items: center;
    margin-right: 20px;
}

.radio-option input,
.checkbox-option input {
    margin-right: 8px;
}

/* Opening Hours */
.opening-hours {
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    padding: 15px;
}

.day-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--light-gray);
}

.day-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.day-name {
    width: 120px;
    font-weight: 500;
}

.hours-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.time-input {
    width: 120px;
}

.closed-checkbox {
    margin-left: 20px;
    display: flex;
    align-items: center;
}

.closed-checkbox input {
    margin-right: 8px;
}

/* File Upload */
.file-upload {
    position: relative;
    margin-bottom: 10px;
}

.file-upload-preview {
    width: 100%;
    height: 200px;
    border: 2px dashed var(--medium-gray);
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background-color: var(--light-gray);
    transition: var(--transition);
}

.file-upload-preview:hover {
    border-color: var(--primary-color);
}

.file-upload.multiple .file-upload-preview {
    height: 300px;
}

.upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--text-medium);
}

.upload-placeholder i {
    font-size: 3rem;
    margin-bottom: 10px;
}

.file-upload input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.preview-images {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px;
}

.preview-image-item {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.preview-image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-image-item .remove-image {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.8rem;
}

/* Input Group */
.input-group {
    display: flex;
}

.input-group-text {
    padding: 10px 15px;
    background-color: var(--light-gray);
    border: 1px solid var(--medium-gray);
    border-right: none;
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
    color: var(--text-medium);
    white-space: nowrap;
}

.input-group .form-control {
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--light-gray);
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--medium-gray);
    color: var(--text-medium);
}

.btn-outline:hover {
    background-color: var(--light-gray);
    color: var(--text-dark);
}

.btn-success {
    background-color: var(--success);
    color: var(--white);
    border: none;
}

.btn-success:hover {
    background-color: #218838;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .day-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .day-name {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .hours-inputs {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .closed-checkbox {
        margin-left: 0;
    }
}

@media (max-width: 768px) {
    .form-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
    }
    
    .form-content {
        padding: 20px;
    }
    
    .form-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .form-actions button {
        width: 100%;
    }
}
