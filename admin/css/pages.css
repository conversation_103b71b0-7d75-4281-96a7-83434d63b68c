/* Pages Management Styles */

.pages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.page-card {
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    padding: 20px;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.page-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.page-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.page-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.2rem;
}

.page-icon.html {
    background-color: rgba(255, 102, 0, 0.1);
    color: var(--primary-color);
}

.page-icon.static {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
}

.page-info h3 {
    margin: 0 0 5px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
}

.page-info p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-medium);
}

.page-card-body {
    margin-bottom: 15px;
}

.page-description {
    font-size: 0.9rem;
    color: var(--text-medium);
    line-height: 1.4;
    margin-bottom: 10px;
}

.page-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-light);
}

.page-status {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.page-status.published {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
}

.page-status.draft {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning);
}

.page-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.page-actions {
    display: flex;
    gap: 8px;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    background-color: var(--light-gray);
    color: var(--text-medium);
}

.btn-icon:hover {
    background-color: var(--medium-gray);
    color: var(--text-dark);
}

.btn-icon.edit {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info);
}

.btn-icon.edit:hover {
    background-color: rgba(23, 162, 184, 0.2);
}

.btn-icon.preview {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
}

.btn-icon.preview:hover {
    background-color: rgba(40, 167, 69, 0.2);
}

.btn-icon.delete {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger);
}

.btn-icon.delete:hover {
    background-color: rgba(220, 53, 69, 0.2);
}

/* Modal Styles */
.modal-content.large {
    max-width: 90vw;
    width: 1200px;
    max-height: 90vh;
}

.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* TinyMCE Editor Styles */
.tox-tinymce {
    border-radius: var(--border-radius-md) !important;
    border-color: var(--border-color) !important;
}

.tox .tox-toolbar {
    background-color: var(--light-gray) !important;
}

.tox .tox-edit-area {
    min-height: 400px !important;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.1);
}

.form-group input[readonly] {
    background-color: var(--light-gray);
    color: var(--text-medium);
}

/* Loading State */
.page-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.page-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .pages-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .modal-content.large {
        width: 95vw;
        max-width: none;
    }
    
    .page-card-footer {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .page-actions {
        justify-content: center;
    }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-medium);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: var(--border-color);
}

.empty-state h3 {
    margin-bottom: 10px;
    color: var(--text-dark);
}

.empty-state p {
    margin-bottom: 20px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Success/Error States */
.page-card.success {
    border-color: var(--success);
    background-color: rgba(40, 167, 69, 0.05);
}

.page-card.error {
    border-color: var(--danger);
    background-color: rgba(220, 53, 69, 0.05);
}
