/*
 * Panel Administracyjny - Żyrardów Poleca
 * Style dla zarządzania firmami
 */

/* Filters */
.filters-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 20px;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    padding: 20px;
    box-shadow: var(--shadow-sm);
}

.filters {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-group {
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

/* Table Styles */
.table-container {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: 20px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--medium-gray);
}

.data-table th {
    background-color: var(--light-gray);
    font-weight: 600;
    color: var(--text-dark);
}

.data-table tbody tr:hover {
    background-color: rgba(245, 245, 245, 0.5);
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* Company Logo */
.company-logo {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    object-fit: cover;
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-badge.active {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
}

.status-badge.pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning);
}

.status-badge.inactive {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--text-medium);
}

/* Action Buttons */
.actions {
    white-space: nowrap;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--text-medium);
    font-size: 1rem;
    cursor: pointer;
    padding: 5px;
    transition: var(--transition);
}

.btn-icon:hover {
    color: var(--primary-color);
}

.btn-icon.delete:hover {
    color: var(--danger);
}

/* Bulk Actions */
.bulk-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.bulk-actions-select {
    display: flex;
    align-items: center;
    gap: 10px;
}

.bulk-actions-select span {
    font-weight: 500;
    color: var(--text-dark);
}

.bulk-actions-select select {
    min-width: 150px;
}

/* Pagination */
.pagination {
    display: flex;
    gap: 5px;
}

.btn-page {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--white);
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    color: var(--text-medium);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.btn-page:hover {
    background-color: var(--light-gray);
}

.btn-page.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.btn-page:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .filters {
        flex-direction: column;
        gap: 10px;
    }

    .filter-group {
        width: 100%;
    }
}

@media (max-width: 992px) {
    .filters-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .data-table {
        display: block;
        overflow-x: auto;
    }

    .bulk-actions {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .pagination {
        margin-top: 15px;
    }
}

@media (max-width: 768px) {
    .header-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .search-box {
        width: 100%;
    }

    .header-actions .btn {
        width: 100%;
    }
}

/* Form Elements */
.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 0.9rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* Buttons */
.btn {
    padding: 10px 15px;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--medium-gray);
    color: var(--text-medium);
}

.btn-outline:hover {
    background-color: var(--light-gray);
    color: var(--text-dark);
}

.btn i {
    font-size: 0.9rem;
}

/* TOP Position Badges */
.top-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 8px;
    text-transform: uppercase;
}

.top-badge.top-1 {
    background-color: #FFD700;
    color: #B8860B;
    border: 1px solid #DAA520;
}

.top-badge.top-2 {
    background-color: #C0C0C0;
    color: #696969;
    border: 1px solid #A9A9A9;
}

.top-badge.top-3 {
    background-color: #CD7F32;
    color: #8B4513;
    border: 1px solid #A0522D;
}

/* Company Name Cell */
.company-name-cell {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.company-name-cell strong {
    margin-right: 8px;
}

/* Manage TOP Button */
.btn-icon.manage-top {
    color: #FFD700;
}

.btn-icon.manage-top:hover {
    color: #DAA520;
}

/* TOP Position Modal Styles */
.top-position-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.top-position-content {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.top-positions-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin: 20px 0;
}

.top-position-slot {
    border: 2px dashed var(--medium-gray);
    border-radius: var(--border-radius-md);
    padding: 20px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.top-position-slot:hover {
    border-color: var(--primary-color);
    background-color: var(--light-gray);
}

.top-position-slot.occupied {
    border-style: solid;
    background-color: var(--light-gray);
}

.position-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.position-badge.top-1 {
    background-color: #FFD700;
    color: #B8860B;
}

.position-badge.top-2 {
    background-color: #C0C0C0;
    color: #696969;
}

.position-badge.top-3 {
    background-color: #CD7F32;
    color: #8B4513;
}

.empty-slot {
    color: var(--text-medium);
    font-style: italic;
}

.empty-slot i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.occupied-slot {
    color: var(--text-dark);
}

.occupied-slot .company-name {
    font-weight: 600;
    margin-bottom: 5px;
}

.occupied-slot .company-info {
    font-size: 0.85rem;
    color: var(--text-medium);
}
