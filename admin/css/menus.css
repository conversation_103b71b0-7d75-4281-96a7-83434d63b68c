/*
 * Panel Administracyjny - Żyrardów Poleca
 * Style dla zarządzania menu
 */

/* Menu Tabs */
.menu-tabs {
    display: flex;
    background-color: var(--white);
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
    overflow: hidden;
    margin-bottom: 0;
    box-shadow: var(--shadow-sm);
}

.menu-tabs .tab-btn {
    flex: 1;
    padding: 15px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    font-weight: 500;
    color: var(--text-medium);
    cursor: pointer;
    transition: var(--transition);
}

.menu-tabs .tab-btn:hover {
    background-color: var(--light-gray);
    color: var(--text-dark);
}

.menu-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* Menu Container */
.menu-container {
    background-color: var(--white);
    border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: 30px;
}

.tab-content {
    display: none;
    padding: 20px;
}

.tab-content.active {
    display: block;
}

/* Menu Editor */
.menu-editor {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.menu-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.menu-editor-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

/* Menu Preview */
.menu-preview {
    background-color: var(--light-gray);
    border-radius: var(--border-radius-md);
    padding: 20px;
}

.menu-preview h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.menu-preview-container {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    padding: 15px;
    box-shadow: var(--shadow-sm);
    min-height: 60px;
}

.footer-preview {
    background-color: var(--text-dark);
    color: var(--white);
}

.menu-items-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    gap: 20px;
}

.menu-items-list li {
    position: relative;
}

.menu-items-list li a {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    padding: 5px 0;
    transition: var(--transition);
}

.menu-items-list li a:hover {
    color: var(--primary-color);
}

.footer-preview .menu-items-list li a {
    color: var(--white);
}

.footer-preview .menu-items-list li a:hover {
    color: var(--primary-color);
}

.menu-items-list li.has-submenu > a::after {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-left: 5px;
}

.menu-items-list li.has-submenu:hover .submenu {
    display: block;
}

.menu-items-list .submenu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 10px 0;
    min-width: 200px;
    z-index: 10;
}

.footer-preview .menu-items-list .submenu {
    background-color: var(--text-dark);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.menu-items-list .submenu li {
    display: block;
    margin: 0;
}

.menu-items-list .submenu li a {
    display: block;
    padding: 8px 15px;
    color: var(--text-dark);
}

.footer-preview .menu-items-list .submenu li a {
    color: var(--white);
}

.menu-items-list .submenu li a:hover {
    background-color: var(--light-gray);
    color: var(--primary-color);
}

.footer-preview .menu-items-list .submenu li a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--primary-color);
}

/* Menu Structure */
.menu-structure {
    background-color: var(--light-gray);
    border-radius: var(--border-radius-md);
    padding: 20px;
}

.menu-structure h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.menu-hint {
    color: var(--text-medium);
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.menu-structure-container {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    padding: 15px;
    box-shadow: var(--shadow-sm);
    min-height: 200px;
}

.sortable-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sortable-menu li {
    margin-bottom: 10px;
}

.sortable-menu li:last-child {
    margin-bottom: 0;
}

.menu-item {
    display: flex;
    align-items: center;
    background-color: var(--light-gray);
    border-radius: var(--border-radius-md);
    padding: 10px 15px;
    cursor: move;
    transition: var(--transition);
}

.menu-item:hover {
    background-color: rgba(255, 102, 0, 0.1);
}

.menu-item-handle {
    margin-right: 10px;
    color: var(--text-medium);
}

.menu-item-content {
    flex: 1;
}

.menu-item-title {
    font-weight: 500;
    margin-bottom: 3px;
}

.menu-item-url {
    font-size: 0.85rem;
    color: var(--text-medium);
}

.menu-item-actions {
    display: flex;
    gap: 5px;
}

.menu-item-actions button {
    background: none;
    border: none;
    color: var(--text-medium);
    cursor: pointer;
    padding: 5px;
    transition: var(--transition);
}

.menu-item-actions button:hover {
    color: var(--primary-color);
}

.menu-item-actions button.delete-item:hover {
    color: var(--danger);
}

.submenu-items {
    list-style: none;
    padding: 0;
    margin: 10px 0 0 30px;
}

.submenu-items .menu-item {
    background-color: rgba(0, 0, 0, 0.03);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--medium-gray);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-medium);
    cursor: pointer;
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--danger);
}

.modal-body {
    padding: 20px;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.1);
}

.checkbox-option {
    display: flex;
    align-items: center;
}

.checkbox-option input {
    margin-right: 8px;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--medium-gray);
    color: var(--text-medium);
}

.btn-outline:hover {
    background-color: var(--light-gray);
    color: var(--text-dark);
}

/* Refresh Button */
.refresh-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    padding: 10px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: var(--shadow-md);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Notifications */
.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 350px;
}

.notification {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 0;
    transform: translateX(120%);
    transition: transform 0.3s ease;
    overflow: hidden;
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    padding: 15px;
}

.notification-message {
    flex: 1;
    margin-right: 10px;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-medium);
    cursor: pointer;
    padding: 5px;
    transition: var(--transition);
}

.notification-close:hover {
    color: var(--text-dark);
}

.notification-success {
    border-left: 4px solid var(--success);
}

.notification-error {
    border-left: 4px solid var(--danger);
}

.notification-info {
    border-left: 4px solid var(--primary-color);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .menu-items-list {
        flex-direction: column;
        gap: 10px;
    }

    .menu-items-list .submenu {
        position: static;
        display: block;
        box-shadow: none;
        padding: 0;
        margin-top: 10px;
        margin-left: 20px;
    }

    .notifications-container {
        max-width: 300px;
    }
}

@media (max-width: 768px) {
    .menu-editor-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .menu-editor-header .btn {
        width: 100%;
    }

    .notifications-container {
        left: 20px;
        right: 20px;
        max-width: none;
    }
}
