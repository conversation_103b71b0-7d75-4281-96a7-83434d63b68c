/* 
 * Panel Administracyjny - Żyrardów Poleca
 * Style dla dashboardu
 */

/* Layout */
.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background-color: var(--white);
    border-right: 1px solid var(--medium-gray);
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 100;
    box-shadow: var(--shadow-md);
}

.sidebar.collapsed {
    width: 80px;
}

.sidebar-header {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--medium-gray);
}

.sidebar-logo {
    max-width: 160px;
    transition: var(--transition);
}

.sidebar.collapsed .sidebar-logo {
    max-width: 40px;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-medium);
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
}

.sidebar-toggle:hover {
    color: var(--primary-color);
}

.sidebar-user {
    padding: 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--medium-gray);
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    flex-shrink: 0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info {
    overflow: hidden;
    transition: var(--transition);
}

.user-info h3 {
    font-size: 1rem;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-info p {
    font-size: 0.85rem;
    color: var(--text-medium);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar.collapsed .user-info {
    opacity: 0;
    width: 0;
    margin-right: 0;
}

.sidebar-nav {
    flex-grow: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.sidebar-nav ul {
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: 5px;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--text-medium);
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.sidebar-nav a:hover {
    background-color: var(--light-gray);
    color: var(--primary-color);
}

.sidebar-nav li.active a {
    background-color: rgba(255, 102, 0, 0.1);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.sidebar-nav i {
    font-size: 1.2rem;
    margin-right: 15px;
    width: 20px;
    text-align: center;
}

.sidebar-nav span {
    transition: var(--transition);
}

.sidebar.collapsed .sidebar-nav span {
    opacity: 0;
    width: 0;
    white-space: nowrap;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid var(--medium-gray);
}

.btn-logout {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 12px;
    background-color: var(--light-gray);
    color: var(--text-medium);
    border: none;
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
}

.btn-logout:hover {
    background-color: var(--medium-gray);
    color: var(--primary-color);
}

.btn-logout i {
    margin-right: 10px;
}

.sidebar.collapsed .btn-logout span {
    opacity: 0;
    width: 0;
}

/* Main Content */
.main-content {
    flex-grow: 1;
    margin-left: 280px;
    transition: var(--transition);
    padding: 20px;
    background-color: var(--light-gray);
}

.sidebar.collapsed + .main-content {
    margin-left: 80px;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    background-color: var(--white);
    padding: 20px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
}

.header-title h1 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    color: var(--text-dark);
}

.header-title p {
    color: var(--text-medium);
}

.header-actions {
    display: flex;
    align-items: center;
}

.search-box {
    position: relative;
    margin-right: 20px;
}

.search-box input {
    padding: 10px 15px 10px 40px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    width: 250px;
    font-size: 0.9rem;
}

.search-box button {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-medium);
    cursor: pointer;
}

.notifications {
    position: relative;
}

.notification-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-medium);
    cursor: pointer;
    padding: 5px;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--primary-color);
    color: var(--white);
    font-size: 0.7rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Dashboard Content */
.content-body {
    padding: 20px 0;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.stat-card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.stat-card-icon i {
    font-size: 1.5rem;
}

.stat-card-content h3 {
    font-size: 1rem;
    color: var(--text-medium);
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 5px;
}

.stat-change {
    font-size: 0.85rem;
    display: flex;
    align-items: center;
}

.stat-change i {
    margin-right: 5px;
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--danger);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

.dashboard-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid var(--medium-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    font-size: 1.2rem;
    color: var(--text-dark);
}

.view-all {
    font-size: 0.9rem;
    color: var(--primary-color);
}

.card-body {
    padding: 20px;
}

/* Activity List */
.activity-list {
    padding: 0;
    margin: 0;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 15px 0;
    border-bottom: 1px solid var(--light-gray);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.activity-content p {
    margin-bottom: 5px;
}

.activity-time {
    font-size: 0.85rem;
    color: var(--text-light);
}

/* Quick Actions */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.quick-action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background-color: var(--light-gray);
    border-radius: var(--border-radius-md);
    transition: var(--transition);
    text-align: center;
}

.quick-action-item:hover {
    background-color: rgba(255, 102, 0, 0.1);
    color: var(--primary-color);
}

.quick-action-icon {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--primary-color);
}

/* Responsywność */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 992px) {
    .sidebar {
        width: 80px;
    }
    
    .sidebar .sidebar-logo {
        max-width: 40px;
    }
    
    .sidebar .user-info,
    .sidebar .sidebar-nav span,
    .sidebar .btn-logout span {
        opacity: 0;
        width: 0;
    }
    
    .main-content {
        margin-left: 80px;
    }
}

@media (max-width: 768px) {
    .content-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .header-title {
        margin-bottom: 20px;
    }
    
    .search-box {
        margin-right: 0;
        margin-bottom: 10px;
        width: 100%;
    }
    
    .search-box input {
        width: 100%;
    }
    
    .stats-cards {
        grid-template-columns: 1fr;
    }
}
