/* 
 * Panel Administracyjny - Żyrardów Poleca
 * Style dla zarządzania wiadomościami
 */

/* News Filters */
.news-filters {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-sm);
}

.filters-row {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-medium);
}

.search-box input {
    width: 100%;
    padding: 10px 15px 10px 45px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
}

.filter-group select {
    padding: 10px 15px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    min-width: 150px;
}

/* News Container */
.news-container {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.news-list {
    display: flex;
    flex-direction: column;
}

.news-item {
    display: flex;
    padding: 20px;
    border-bottom: 1px solid var(--light-gray);
    transition: var(--transition);
}

.news-item:hover {
    background-color: var(--light-gray);
}

.news-item:last-child {
    border-bottom: none;
}

.news-image {
    width: 120px;
    height: 80px;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    margin-right: 20px;
    flex-shrink: 0;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.news-image.no-image {
    background-color: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-medium);
}

.news-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.news-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.news-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
    line-height: 1.4;
}

.news-category {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 4px 8px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
    white-space: nowrap;
}

.news-excerpt {
    color: var(--text-medium);
    margin: 0 0 10px 0;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.news-info {
    display: flex;
    gap: 15px;
    font-size: 0.9rem;
    color: var(--text-medium);
}

.news-status {
    padding: 4px 8px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
}

.news-status.published {
    background-color: var(--success);
    color: var(--white);
}

.news-status.draft {
    background-color: var(--warning);
    color: var(--white);
}

.news-status.archived {
    background-color: var(--text-medium);
    color: var(--white);
}

.news-actions {
    display: flex;
    gap: 10px;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--text-medium);
    font-size: 1rem;
    cursor: pointer;
    padding: 5px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.btn-icon:hover {
    background-color: var(--light-gray);
    color: var(--primary-color);
}

.btn-icon.delete:hover {
    color: var(--danger);
}

/* Modal Styles */
.modal-content.large {
    max-width: 900px;
    width: 90%;
}

.form-row {
    display: flex;
    gap: 20px;
}

.form-row .form-group {
    flex: 1;
}

.seo-section {
    border-top: 1px solid var(--light-gray);
    padding-top: 20px;
    margin-top: 20px;
}

.seo-section h3 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    color: var(--text-dark);
}

.image-preview {
    margin-top: 10px;
    max-width: 200px;
}

.image-preview img {
    width: 100%;
    border-radius: var(--border-radius-sm);
}

/* Import Modal */
.import-info {
    background-color: var(--light-gray);
    padding: 15px;
    border-radius: var(--border-radius-sm);
    margin-bottom: 20px;
}

.import-info p {
    margin: 0;
    color: var(--text-medium);
}

/* Empty State */
.news-empty {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-medium);
}

.news-empty i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: var(--light-gray);
}

.news-empty h3 {
    margin: 0 0 10px 0;
    color: var(--text-dark);
}

.news-empty p {
    margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
    .filters-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .news-item {
        flex-direction: column;
    }
    
    .news-image {
        width: 100%;
        height: 200px;
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .news-header {
        flex-direction: column;
        gap: 10px;
    }
    
    .news-meta {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
    
    .form-row {
        flex-direction: column;
    }
}
