/*
 * Panel Administracyjny - Żyrardów Poleca
 * Główny plik CSS
 */

/* Zmienne CSS */
:root {
    /* Kolory */
    --primary-color: #ff6600;
    --primary-dark: #e65c00;
    --primary-light: #ff8533;
    --secondary-color: #333333;
    --secondary-dark: #262626;
    --secondary-light: #4d4d4d;
    --white: #ffffff;
    --light-gray: #f5f5f5;
    --medium-gray: #e0e0e0;
    --dark-gray: #666666;
    --text-dark: #333333;
    --text-medium: #666666;
    --text-light: #999999;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --info: #17a2b8;

    /* Cienie */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);

    /* Zaokrąglenia */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;

    /* Przejścia */
    --transition: all 0.3s ease;

    /* Czcionki */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Reset i podstawowe style */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--light-gray);
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

/* Strona logowania */
.login-page {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    position: relative;
}

.login-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../../images/hero-bg1.jpg');
    background-size: cover;
    background-position: center;
    z-index: -2;
}

.login-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: -1;
}

.login-container {
    width: 100%;
    max-width: 450px;
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    z-index: 1;
    animation: fadeIn 0.5s ease;
}

.login-logo {
    text-align: center;
    padding: 30px 0;
    background-color: var(--light-gray);
}

.login-logo img {
    max-width: 200px;
}

.login-form-container {
    padding: 30px;
}

.login-form-container h1 {
    text-align: center;
    margin-bottom: 30px;
    color: var(--secondary-color);
    font-size: 1.8rem;
}

.login-form .form-group {
    margin-bottom: 20px;
}

.login-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-medium);
}

.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.login-form input[type="text"],
.login-form input[type="password"] {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    transition: var(--transition);
}

.login-form input[type="text"]:focus,
.login-form input[type="password"]:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.2);
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input[type="checkbox"] {
    margin-right: 10px;
    cursor: pointer;
}

.remember-me label {
    margin-bottom: 0;
    cursor: pointer;
}

.btn-login {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.btn-login:hover {
    background-color: var(--primary-dark);
}

.login-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    font-size: 0.9rem;
}

/* Animacje */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== SECTION ORDER STYLES - WYSOKIE PRIORYTETY ===== */
.section-order-container {
    background: var(--white) !important;
    border-radius: var(--border-radius-md) !important;
    padding: 20px !important;
    border: 1px solid var(--medium-gray) !important;
}

.section-order-container .section-description {
    color: var(--text-medium) !important;
    margin-bottom: 20px !important;
    font-size: 0.95rem !important;
}

.sortable-sections {
    min-height: 400px !important;
    border: 2px dashed var(--medium-gray) !important;
    border-radius: var(--border-radius-md) !important;
    padding: 15px !important;
    background: var(--light-gray) !important;
}

.sortable-sections .section-item {
    display: flex !important;
    align-items: center !important;
    background: var(--white) !important;
    border: 1px solid var(--medium-gray) !important;
    border-radius: var(--border-radius-md) !important;
    padding: 15px !important;
    margin-bottom: 10px !important;
    cursor: move !important;
    transition: all 0.3s ease !important;
    box-shadow: var(--shadow-sm) !important;
    flex-direction: row !important;
    justify-content: flex-start !important;
}

.sortable-sections .section-item:hover {
    box-shadow: var(--shadow-md) !important;
    transform: translateY(-2px) !important;
    background-color: var(--white) !important;
}

.sortable-sections .section-item.sortable-ghost {
    opacity: 0.5 !important;
    background: var(--primary-color) !important;
    color: var(--white) !important;
}

.sortable-sections .section-item.sortable-chosen {
    background: var(--primary-color) !important;
    color: var(--white) !important;
}

.sortable-sections .section-handle {
    margin-right: 15px !important;
    color: var(--text-light) !important;
    font-size: 1.2rem !important;
    cursor: grab !important;
    margin-left: 0 !important;
    margin-top: 0 !important;
}

.sortable-sections .section-handle:active {
    cursor: grabbing !important;
}

.sortable-sections .section-info {
    flex: 1 !important;
    margin-left: 0 !important;
    margin-top: 0 !important;
}

.sortable-sections .section-info h4 {
    margin: 0 0 5px 0 !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    color: var(--text-dark) !important;
}

.sortable-sections .section-info p {
    margin: 0 !important;
    font-size: 0.85rem !important;
    color: var(--text-medium) !important;
}

.sortable-sections .section-status {
    margin-left: 15px !important;
    margin-top: 0 !important;
}

.status-badge {
    padding: 4px 12px !important;
    border-radius: 20px !important;
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    display: inline-block !important;
}

.status-badge.active {
    background: #e8f5e8 !important;
    color: #27ae60 !important;
}

.status-badge.hidden {
    background: #fef2e8 !important;
    color: #f39c12 !important;
}

.status-badge.fixed {
    background: #e8f2ff !important;
    color: #3498db !important;
}

.section-order-actions {
    display: flex !important;
    gap: 15px !important;
    margin-top: 20px !important;
    padding-top: 20px !important;
    border-top: 1px solid var(--medium-gray) !important;
}

/* ===== ADMIN LAYOUT STYLES ===== */

/* Admin Body */
.admin-body {
    font-family: 'Montserrat', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-gray);
    margin: 0;
    padding: 0;
}

/* Admin Container */
.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Admin Sidebar - Alias dla .sidebar */
.admin-sidebar {
    width: 280px;
    background-color: var(--white);
    border-right: 1px solid var(--medium-gray);
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 100;
    box-shadow: var(--shadow-md);
}

.admin-sidebar.collapsed {
    width: 80px;
}

.admin-sidebar .sidebar-header {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--medium-gray);
}

.admin-sidebar .logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.admin-sidebar .logo img {
    max-width: 40px;
    height: auto;
}

.admin-sidebar .logo h2 {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin: 0;
    font-weight: 600;
}

.admin-sidebar .sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-medium);
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
    padding: 5px;
}

.admin-sidebar .sidebar-toggle:hover {
    color: var(--primary-color);
}

.admin-sidebar .sidebar-user {
    padding: 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--medium-gray);
}

.admin-sidebar .user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    flex-shrink: 0;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.admin-sidebar .user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.admin-sidebar .user-info {
    overflow: hidden;
    transition: var(--transition);
}

.admin-sidebar .user-info h3 {
    font-size: 1rem;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-dark);
}

.admin-sidebar .user-info p {
    font-size: 0.85rem;
    color: var(--text-medium);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0;
}

.admin-sidebar.collapsed .user-info {
    opacity: 0;
    width: 0;
    margin-right: 0;
}

.admin-sidebar .sidebar-nav {
    flex-grow: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.admin-sidebar .sidebar-nav ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

.admin-sidebar .sidebar-nav li {
    margin-bottom: 5px;
}

.admin-sidebar .sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--text-medium);
    transition: var(--transition);
    border-left: 3px solid transparent;
    text-decoration: none;
}

.admin-sidebar .sidebar-nav a:hover {
    background-color: var(--light-gray);
    color: var(--primary-color);
}

.admin-sidebar .sidebar-nav li.active a {
    background-color: rgba(255, 102, 0, 0.1);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.admin-sidebar .sidebar-nav i {
    font-size: 1.2rem;
    margin-right: 15px;
    width: 20px;
    text-align: center;
}

.admin-sidebar .sidebar-nav span {
    transition: var(--transition);
}

.admin-sidebar.collapsed .sidebar-nav span {
    opacity: 0;
    width: 0;
    white-space: nowrap;
}

.admin-sidebar .sidebar-footer {
    padding: 20px;
    border-top: 1px solid var(--medium-gray);
}

.admin-sidebar .btn-logout {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 12px;
    background-color: var(--light-gray);
    color: var(--text-medium);
    border: none;
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
}

.admin-sidebar .btn-logout:hover {
    background-color: var(--medium-gray);
    color: var(--primary-color);
}

.admin-sidebar .btn-logout i {
    margin-right: 10px;
}

.admin-sidebar.collapsed .btn-logout span {
    opacity: 0;
    width: 0;
}

/* Main Content */
.main-content {
    flex-grow: 1;
    margin-left: 280px;
    transition: var(--transition);
    padding: 20px;
    background-color: var(--light-gray);
    min-height: 100vh;
}

.admin-sidebar.collapsed + .main-content {
    margin-left: 80px;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    background-color: var(--white);
    padding: 20px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
}

.header-title h1 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    color: var(--text-dark);
}

.header-title p {
    color: var(--text-medium);
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.content-body {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    padding: 20px;
}

/* ===== WYSIWYG EDITOR STYLES ===== */

/* HugeRTE Editor Container */
.tox-tinymce {
    border: 1px solid var(--medium-gray) !important;
    border-radius: var(--border-radius-md) !important;
    box-shadow: var(--shadow-sm) !important;
    overflow: hidden !important;
}

.tox-editor-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-bottom: 1px solid var(--medium-gray) !important;
    padding: 8px !important;
}

.tox-toolbar {
    background: transparent !important;
    border: none !important;
    padding: 8px 12px !important;
}

.tox-toolbar__group {
    border: none !important;
    margin: 0 8px 0 0 !important;
    padding: 0 !important;
}

.tox-tbtn {
    background: var(--white) !important;
    border: 1px solid transparent !important;
    border-radius: var(--border-radius-sm) !important;
    margin: 2px !important;
    padding: 6px 8px !important;
    transition: all 0.2s ease !important;
    color: var(--text-medium) !important;
}

.tox-tbtn:hover {
    background: var(--light-gray) !important;
    border-color: var(--medium-gray) !important;
    color: var(--primary-color) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.tox-tbtn--enabled {
    background: var(--primary-color) !important;
    color: var(--white) !important;
    border-color: var(--primary-dark) !important;
}

.tox-tbtn--enabled:hover {
    background: var(--primary-dark) !important;
}

.tox-split-button {
    border-radius: var(--border-radius-sm) !important;
    overflow: hidden !important;
}

.tox-editor-container {
    background: var(--white) !important;
}

.tox-edit-area {
    border: none !important;
    background: var(--white) !important;
}

.tox-edit-area iframe {
    background: var(--white) !important;
}

/* Modal Styles Enhancement */
.modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(4px) !important;
    display: none !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 1000 !important;
    padding: 20px !important;
    box-sizing: border-box !important;
    opacity: 0 !important;
    transition: all 0.3s ease !important;
}

.modal.show {
    display: flex !important;
    opacity: 1 !important;
}

.modal-content {
    background: var(--white) !important;
    border-radius: var(--border-radius-lg) !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2) !important;
    border: none !important;
    overflow: hidden !important;
}

.modal-content.large {
    max-width: 90vw !important;
    max-height: 90vh !important;
    width: 1200px !important;
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    color: var(--white) !important;
    padding: 20px 30px !important;
    border-bottom: none !important;
}

.modal-header h3 {
    margin: 0 !important;
    font-size: 1.3rem !important;
    font-weight: 600 !important;
}

.modal-close {
    background: none !important;
    border: none !important;
    color: var(--white) !important;
    font-size: 1.5rem !important;
    cursor: pointer !important;
    padding: 5px !important;
    border-radius: 50% !important;
    transition: all 0.2s ease !important;
    width: 35px !important;
    height: 35px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: rotate(90deg) !important;
}

.modal-body {
    padding: 30px !important;
    max-height: 70vh !important;
    overflow-y: auto !important;
}

.modal-footer {
    background: var(--light-gray) !important;
    padding: 20px 30px !important;
    border-top: 1px solid var(--medium-gray) !important;
    display: flex !important;
    gap: 15px !important;
    justify-content: flex-end !important;
}

/* Form Styles Enhancement */
.form-group {
    margin-bottom: 25px !important;
}

.form-group label {
    display: block !important;
    margin-bottom: 8px !important;
    font-weight: 600 !important;
    color: var(--text-dark) !important;
    font-size: 0.95rem !important;
}

.form-group input[type="text"],
.form-group textarea {
    width: 100% !important;
    padding: 12px 15px !important;
    border: 1px solid var(--medium-gray) !important;
    border-radius: var(--border-radius-md) !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: var(--white) !important;
    font-family: inherit !important;
}

.form-group input[type="text"]:focus,
.form-group textarea:focus {
    border-color: var(--primary-color) !important;
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.1) !important;
    transform: translateY(-1px) !important;
}

.form-group input[readonly] {
    background: var(--light-gray) !important;
    color: var(--text-medium) !important;
    cursor: not-allowed !important;
}

/* Button Styles Enhancement */
.btn {
    padding: 12px 24px !important;
    border: none !important;
    border-radius: var(--border-radius-md) !important;
    font-size: 0.95rem !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    text-decoration: none !important;
    position: relative !important;
    overflow: hidden !important;
}

.btn::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent) !important;
    transition: left 0.5s ease !important;
}

.btn:hover::before {
    left: 100% !important;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    color: var(--white) !important;
    box-shadow: 0 4px 8px rgba(255, 102, 0, 0.3) !important;
}

.btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 12px rgba(255, 102, 0, 0.4) !important;
}

.btn-secondary {
    background: var(--medium-gray) !important;
    color: var(--text-dark) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.btn-secondary:hover {
    background: var(--dark-gray) !important;
    color: var(--white) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

.btn-success {
    background: linear-gradient(135deg, var(--success) 0%, #1e7e34 100%) !important;
    color: var(--white) !important;
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3) !important;
}

.btn-success:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 12px rgba(40, 167, 69, 0.4) !important;
}

/* Responsywność */
@media (max-width: 992px) {
    .admin-sidebar {
        width: 80px;
    }

    .admin-sidebar .logo h2,
    .admin-sidebar .user-info,
    .admin-sidebar .sidebar-nav span,
    .admin-sidebar .btn-logout span {
        opacity: 0;
        width: 0;
    }

    .main-content {
        margin-left: 80px;
    }
}

@media (max-width: 768px) {
    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .header-actions {
        width: 100%;
        justify-content: flex-start;
    }
}

@media (max-width: 576px) {
    .login-container {
        max-width: 90%;
    }

    .login-form-container {
        padding: 20px;
    }

    .login-form-container h1 {
        font-size: 1.5rem;
    }

    .section-order-actions {
        flex-direction: column;
    }

    .section-item {
        padding: 12px;
    }

    .section-handle {
        margin-right: 10px;
    }

    .admin-sidebar {
        width: 100%;
        transform: translateX(-100%);
    }

    .admin-sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }
}

/* Style dla modala resetu hasła */
.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    margin-right: 10px;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--medium-gray);
    color: var(--text-dark);
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.btn-secondary:hover {
    background-color: var(--dark-gray);
    color: var(--white);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-light);
    cursor: pointer;
    padding: 5px;
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--text-dark);
}

.login-form input[type="email"] {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    transition: var(--transition);
}

.login-form input[type="email"]:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.2);
}
