/* 
 * Panel Administracyjny - Żyrardów Poleca
 * Style dla zarządzania użytkownikami
 */

/* Filters */
.filters-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 20px;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    padding: 20px;
    box-shadow: var(--shadow-sm);
}

.filters {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-group {
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

/* Table Styles */
.table-container {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: 20px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--medium-gray);
}

.data-table th {
    background-color: var(--light-gray);
    font-weight: 600;
    color: var(--text-dark);
}

.data-table tbody tr:hover {
    background-color: rgba(245, 245, 245, 0.5);
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* User Avatar */
.user-avatar-small {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

/* Role Badges */
.role-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.role-badge.admin {
    background-color: rgba(111, 66, 193, 0.1);
    color: var(--purple);
}

.role-badge.editor {
    background-color: rgba(0, 123, 255, 0.1);
    color: var(--info);
}

.role-badge.business {
    background-color: rgba(255, 102, 0, 0.1);
    color: var(--primary-color);
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-badge.active {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
}

.status-badge.inactive {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--text-medium);
}

.status-badge.pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning);
}

/* Action Buttons */
.actions {
    white-space: nowrap;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--text-medium);
    font-size: 1rem;
    cursor: pointer;
    padding: 5px;
    transition: var(--transition);
}

.btn-icon:hover {
    color: var(--primary-color);
}

.btn-icon.delete:hover {
    color: var(--danger);
}

/* Bulk Actions */
.bulk-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.bulk-actions-select {
    display: flex;
    align-items: center;
    gap: 10px;
}

.bulk-actions-select span {
    font-weight: 500;
    color: var(--text-dark);
}

.bulk-actions-select select {
    min-width: 150px;
}

/* Pagination */
.pagination {
    display: flex;
    gap: 5px;
}

.btn-page {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--white);
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    color: var(--text-medium);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.btn-page:hover {
    background-color: var(--light-gray);
}

.btn-page.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.btn-page:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--medium-gray);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-medium);
    cursor: pointer;
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--danger);
}

.modal-body {
    padding: 20px;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.1);
}

/* Form Row */
.form-row {
    display: flex;
    gap: 20px;
}

.form-row .form-group {
    flex: 1;
}

/* Password Input */
.password-input {
    position: relative;
}

.password-input .form-control {
    padding-right: 40px;
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-medium);
    cursor: pointer;
}

/* Radio & Checkbox Groups */
.radio-group,
.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.radio-option,
.checkbox-option {
    display: flex;
    align-items: center;
    margin-right: 20px;
}

.radio-option input,
.checkbox-option input {
    margin-right: 8px;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--medium-gray);
    color: var(--text-medium);
}

.btn-outline:hover {
    background-color: var(--light-gray);
    color: var(--text-dark);
}

.btn i {
    font-size: 0.9rem;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .filters {
        flex-direction: column;
        gap: 10px;
    }
    
    .filter-group {
        width: 100%;
    }
}

@media (max-width: 992px) {
    .filters-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .data-table {
        display: block;
        overflow-x: auto;
    }
    
    .bulk-actions {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .pagination {
        margin-top: 15px;
    }
}

@media (max-width: 768px) {
    .header-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .search-box {
        width: 100%;
    }
    
    .header-actions .btn {
        width: 100%;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
}
