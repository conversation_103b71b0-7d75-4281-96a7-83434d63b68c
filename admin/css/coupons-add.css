/* 
 * Panel Administracyjny - Żyrardów Poleca
 * Style dla dodawania/edycji k<PERSON>
 */

/* Form Container */
.form-container {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: 30px;
    padding: 30px;
}

/* Form Sections */
.form-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid var(--light-gray);
}

.form-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.form-section h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 1.5rem;
    color: var(--text-dark);
}

/* Form Groups */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-group .required {
    color: var(--danger);
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.1);
}

.form-text {
    display: block;
    margin-top: 5px;
    font-size: 0.85rem;
    color: var(--text-medium);
}

/* Form Row */
.form-row {
    display: flex;
    gap: 20px;
}

.form-row .form-group {
    flex: 1;
}

/* Input Group */
.input-group {
    display: flex;
}

.input-group-text {
    padding: 10px 15px;
    background-color: var(--light-gray);
    border: 1px solid var(--medium-gray);
    border-left: none;
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
    color: var(--text-medium);
}

.input-group .form-control {
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}

/* Radio & Checkbox Groups */
.radio-group,
.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.radio-option,
.checkbox-option {
    display: flex;
    align-items: center;
    margin-right: 20px;
}

.radio-option input,
.checkbox-option input {
    margin-right: 8px;
}

/* Coupon Preview */
.coupon-preview {
    border: 2px dashed var(--primary-color);
    border-radius: var(--border-radius-md);
    padding: 20px;
    background-color: #fff;
    margin-top: 20px;
}

.coupon-preview-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.coupon-company-logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    background-color: var(--light-gray);
}

.coupon-company-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.coupon-company-name {
    margin: 0 0 5px 0;
    font-size: 1.2rem;
}

.coupon-company-address {
    margin: 0;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.coupon-preview-body {
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid var(--light-gray);
    border-bottom: 1px solid var(--light-gray);
}

.coupon-discount {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.coupon-description {
    font-size: 1.2rem;
    margin-bottom: 20px;
}

.coupon-code-container {
    margin-bottom: 10px;
}

.coupon-code-label {
    font-weight: 500;
    margin-right: 10px;
}

.coupon-code-value {
    display: inline-block;
    padding: 8px 15px;
    background-color: var(--light-gray);
    border-radius: 4px;
    font-family: monospace;
    font-weight: 600;
    letter-spacing: 1px;
    font-size: 1.2rem;
}

.coupon-validity {
    margin-top: 15px;
    font-size: 0.9rem;
}

.coupon-validity-label {
    font-weight: 500;
    margin-right: 5px;
}

.coupon-preview-footer {
    padding-top: 15px;
}

.coupon-terms {
    font-size: 0.8rem;
    color: var(--text-medium);
    font-style: italic;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--light-gray);
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--medium-gray);
    color: var(--text-medium);
}

.btn-outline:hover {
    background-color: var(--light-gray);
    color: var(--text-dark);
}

.btn-success {
    background-color: var(--success);
    color: var(--white);
    border: none;
}

.btn-success:hover {
    background-color: #218838;
}

/* Validation Styles */
.form-control.is-invalid {
    border-color: var(--danger);
}

.invalid-feedback {
    display: none;
    color: var(--danger);
    font-size: 0.85rem;
    margin-top: 5px;
}

.form-control.is-invalid + .invalid-feedback {
    display: block;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .coupon-preview-header {
        flex-direction: column;
        text-align: center;
    }
    
    .coupon-company-logo {
        margin-right: 0;
        margin-bottom: 15px;
    }
}

@media (max-width: 768px) {
    .form-container {
        padding: 20px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions button {
        width: 100%;
    }
}
