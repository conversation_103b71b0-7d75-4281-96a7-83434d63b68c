/*
 * Panel Administracyjny - Żyrardów Poleca
 * Style dla zarządzania kategoriami
 */

/* Categories Container */
.categories-container {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
}

/* Categories List */
.categories-list {
    flex: 1;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.categories-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--light-gray);
}

.categories-header h2 {
    margin: 0;
    font-size: 1.2rem;
}

.search-box {
    display: flex;
    align-items: center;
    background-color: var(--light-gray);
    border-radius: var(--border-radius-md);
    padding: 5px 10px;
    width: 250px;
}

.search-box input {
    flex: 1;
    border: none;
    background: none;
    padding: 5px;
    font-size: 0.9rem;
}

.search-box input:focus {
    outline: none;
}

.search-box button {
    background: none;
    border: none;
    color: var(--text-medium);
    cursor: pointer;
}

/* Categories Tree */
#categories-tree {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 600px;
    overflow-y: auto;
}

.category-item {
    border-bottom: 1px solid var(--light-gray);
}

.category-item-header {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    transition: var(--transition);
}

.category-item.active .category-item-header {
    background-color: rgba(255, 102, 0, 0.05);
}

.category-item-header:hover {
    background-color: var(--light-gray);
}

.category-icon {
    margin-right: 10px;
    color: var(--primary-color);
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.category-name {
    flex: 1;
    font-weight: 500;
}

.category-actions {
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: var(--transition);
}

.category-item-header:hover .category-actions {
    opacity: 1;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--text-medium);
    font-size: 0.9rem;
    cursor: pointer;
    padding: 5px;
    transition: var(--transition);
}

.btn-icon:hover {
    color: var(--primary-color);
}

.delete-category:hover,
.delete-subcategory:hover {
    color: var(--danger);
}

/* Subcategories */
.subcategories {
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: var(--light-gray);
    display: none;
}

.category-item.active .subcategories {
    display: block;
}

.subcategory-item {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.subcategory-item:last-child {
    border-bottom: none;
}

.subcategory-item-header {
    display: flex;
    align-items: center;
    padding: 12px 20px 12px 50px;
    cursor: pointer;
    transition: var(--transition);
}

.subcategory-item-header:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.subcategory-name {
    flex: 1;
}

.subcategory-actions {
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: var(--transition);
}

.subcategory-item-header:hover .subcategory-actions {
    opacity: 1;
}

/* Category Details */
.category-details {
    flex: 1;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.category-details-header {
    padding: 20px;
    border-bottom: 1px solid var(--light-gray);
}

.category-details-header h2 {
    margin: 0;
    font-size: 1.2rem;
}

.category-details-content {
    padding: 20px;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.1);
}

/* Input Group */
.input-group {
    display: flex;
}

.input-group-text {
    padding: 10px 15px;
    background-color: var(--light-gray);
    border: 1px solid var(--medium-gray);
    border-right: none;
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
    color: var(--text-medium);
    white-space: nowrap;
    font-size: 0.9rem;
}

.input-group .form-control {
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

/* Icon Selector */
.icon-selector {
    position: relative;
}

.selected-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.icon-dropdown {
    display: none;
    position: absolute;
    top: 60px;
    left: 0;
    background-color: var(--white);
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    padding: 10px;
    box-shadow: var(--shadow-md);
    z-index: 10;
    width: 250px;
    flex-wrap: wrap;
    gap: 10px;
}

.icon-selector.active .icon-dropdown {
    display: flex;
}

.icon-option {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--light-gray);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: 1.2rem;
    color: var(--text-medium);
    transition: var(--transition);
}

.icon-option:hover {
    background-color: var(--light-gray);
    color: var(--primary-color);
}

/* Toggle Switch */
.toggle-switch {
    display: flex;
    align-items: center;
}

.toggle-switch input {
    display: none;
}

.toggle-switch label {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    background-color: var(--medium-gray);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.toggle-switch label::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background-color: var(--white);
    border-radius: 50%;
    transition: var(--transition);
}

.toggle-switch input:checked + label {
    background-color: var(--success);
}

.toggle-switch input:checked + label::after {
    left: 28px;
}

.toggle-label {
    margin-left: 10px;
    font-weight: 500;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--medium-gray);
    color: var(--text-medium);
}

.btn-outline:hover {
    background-color: var(--light-gray);
    color: var(--text-dark);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--medium-gray);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-medium);
    cursor: pointer;
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--danger);
}

.modal-body {
    padding: 20px;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .categories-container {
        flex-direction: column;
    }

    .categories-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .search-box {
        width: 100%;
    }
}
