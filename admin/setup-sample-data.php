<?php
/**
 * Skrypt do dodania przykładowych danych
 * Żyrardów Poleca
 */

require_once __DIR__ . '/config/database.php';

try {
    // Sprawdź czy już są firmy w bazie
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM companies");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    if ($count > 0) {
        echo "Baza już zawiera firmy ($count). Skrypt przerwany.\n";
        exit;
    }
    
    // Sprawdź czy są kategorie
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM categories");
    $stmt->execute();
    $categoriesCount = $stmt->fetchColumn();
    
    if ($categoriesCount === 0) {
        echo "Brak kategorii w bazie. Dodaję przykładowe kategorie...\n";
        
        // Dodaj przykładowe kategorie
        $categories = [
            ['name' => 'Jedzenie i Gastronomia', 'slug' => 'jedzenie-gastronomia', 'icon' => 'fas fa-utensils', 'color' => '#e74c3c'],
            ['name' => 'Zdrowie i Uroda', 'slug' => 'zdrowie-uroda', 'icon' => 'fas fa-heart', 'color' => '#e91e63'],
            ['name' => 'Zakupy i Handel', 'slug' => 'zakupy-handel', 'icon' => 'fas fa-shopping-bag', 'color' => '#9c27b0'],
            ['name' => 'Usługi Biznesowe', 'slug' => 'uslugi-biznesowe', 'icon' => 'fas fa-briefcase', 'color' => '#3f51b5'],
            ['name' => 'Motoryzacja', 'slug' => 'motoryzacja', 'icon' => 'fas fa-car', 'color' => '#2196f3'],
            ['name' => 'Dom i Ogród', 'slug' => 'dom-ogrod', 'icon' => 'fas fa-home', 'color' => '#00bcd4'],
            ['name' => 'Sport i Rekreacja', 'slug' => 'sport-rekreacja', 'icon' => 'fas fa-dumbbell', 'color' => '#009688'],
            ['name' => 'Edukacja', 'slug' => 'edukacja', 'icon' => 'fas fa-graduation-cap', 'color' => '#4caf50']
        ];
        
        foreach ($categories as $index => $category) {
            $stmt = $pdo->prepare("
                INSERT INTO categories (name, slug, icon, color, sortOrder, createdAt, updatedAt) 
                VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            ");
            $stmt->execute([
                $category['name'],
                $category['slug'],
                $category['icon'],
                $category['color'],
                $index + 1
            ]);
        }
        echo "Dodano " . count($categories) . " kategorii.\n";
    }
    
    // Pobierz ID kategorii
    $stmt = $pdo->prepare("SELECT id, name FROM categories ORDER BY sortOrder ASC");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
    // Znajdź ID kategorii
    $categoryIds = [];
    foreach ($categories as $id => $name) {
        switch ($name) {
            case 'Jedzenie i Gastronomia':
                $categoryIds['food'] = $id;
                break;
            case 'Zdrowie i Uroda':
                $categoryIds['health'] = $id;
                break;
            case 'Zakupy i Handel':
                $categoryIds['shopping'] = $id;
                break;
            case 'Usługi Biznesowe':
                $categoryIds['business'] = $id;
                break;
            case 'Motoryzacja':
                $categoryIds['automotive'] = $id;
                break;
        }
    }
    
    echo "Dodaję przykładowe firmy...\n";
    
    // Przykładowe firmy
    $companies = [
        [
            'name' => 'Restauracja Pod Akacjami',
            'categoryId' => $categoryIds['food'] ?? 1,
            'description' => 'Restauracja serwująca tradycyjną polską kuchnię w klimatycznym wnętrzu. Specjalizujemy się w daniach regionalnych przygotowywanych z najświeższych składników.',
            'address' => 'ul. Warszawska 15',
            'postalCode' => '96-300',
            'city' => 'Żyrardów',
            'phone' => '+48 46 855 12 34',
            'email' => '<EMAIL>',
            'website' => 'https://podakacjami.pl',
            'status' => 'active',
            'topPosition' => 1
        ],
        [
            'name' => 'Salon Fryzjerski Bella',
            'categoryId' => $categoryIds['health'] ?? 2,
            'description' => 'Nowoczesny salon fryzjerski oferujący pełen zakres usług fryzjerskich i kosmetycznych. Doświadczeni styliści, najwyższej jakości kosmetyki.',
            'address' => 'ul. Piłsudskiego 8',
            'postalCode' => '96-300',
            'city' => 'Żyrardów',
            'phone' => '+48 46 855 23 45',
            'email' => '<EMAIL>',
            'website' => 'https://bella-salon.pl',
            'status' => 'active',
            'topPosition' => 2
        ],
        [
            'name' => 'Sklep Sportowy Active',
            'categoryId' => $categoryIds['shopping'] ?? 3,
            'description' => 'Największy wybór sprzętu sportowego w Żyrardowie. Odzież, obuwie i akcesoria sportowe najlepszych marek. Profesjonalne doradztwo.',
            'address' => 'ul. Słowackiego 22',
            'postalCode' => '96-300',
            'city' => 'Żyrardów',
            'phone' => '+48 46 855 34 56',
            'email' => '<EMAIL>',
            'website' => 'https://active-sport.pl',
            'status' => 'active',
            'topPosition' => 3
        ],
        [
            'name' => 'Kancelaria Prawna Paragraf',
            'categoryId' => $categoryIds['business'] ?? 4,
            'description' => 'Kompleksowe usługi prawne dla firm i osób prywatnych. Prawo cywilne, gospodarcze, rodzinne. Doświadczeni prawnicy, indywidualne podejście.',
            'address' => 'ul. Mickiewicza 5',
            'postalCode' => '96-300',
            'city' => 'Żyrardów',
            'phone' => '+48 46 855 45 67',
            'email' => '<EMAIL>',
            'website' => 'https://paragraf-zyrardow.pl',
            'status' => 'active',
            'topPosition' => null
        ],
        [
            'name' => 'Warsztat Samochodowy AutoMax',
            'categoryId' => $categoryIds['automotive'] ?? 5,
            'description' => 'Profesjonalny serwis samochodowy. Mechanika, elektromechanika, diagnostyka komputerowa. Serwis wszystkich marek, konkurencyjne ceny.',
            'address' => 'ul. Przemysłowa 12',
            'postalCode' => '96-300',
            'city' => 'Żyrardów',
            'phone' => '+48 46 855 56 78',
            'email' => '<EMAIL>',
            'website' => 'https://automax-zyrardow.pl',
            'status' => 'active',
            'topPosition' => null
        ],
        [
            'name' => 'Pizzeria Mamma Mia',
            'categoryId' => $categoryIds['food'] ?? 1,
            'description' => 'Autentyczna włoska pizzeria w sercu Żyrardowa. Pizza na cienkim cieście, świeże składniki, tradycyjne przepisy. Dostawa do domu.',
            'address' => 'ul. Kościelna 18',
            'postalCode' => '96-300',
            'city' => 'Żyrardów',
            'phone' => '+48 46 855 67 89',
            'email' => '<EMAIL>',
            'website' => 'https://mammamia-zyrardow.pl',
            'status' => 'active',
            'topPosition' => null
        ],
        [
            'name' => 'Apteka Centralna',
            'categoryId' => $categoryIds['health'] ?? 2,
            'description' => 'Apteka z długoletnią tradycją. Szeroki wybór leków, suplementów i kosmetyków. Profesjonalne doradztwo farmaceutyczne.',
            'address' => 'ul. Narutowicza 3',
            'postalCode' => '96-300',
            'city' => 'Żyrardów',
            'phone' => '+48 46 855 78 90',
            'email' => '<EMAIL>',
            'website' => 'https://apteka-centralna.pl',
            'status' => 'pending',
            'topPosition' => null
        ]
    ];
    
    foreach ($companies as $company) {
        $stmt = $pdo->prepare("
            INSERT INTO companies (name, categoryId, description, address, postalCode, city, phone, email, website, status, topPosition, createdAt, updatedAt) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        ");
        
        $stmt->execute([
            $company['name'],
            $company['categoryId'],
            $company['description'],
            $company['address'],
            $company['postalCode'],
            $company['city'],
            $company['phone'],
            $company['email'],
            $company['website'],
            $company['status'],
            $company['topPosition']
        ]);
        
        echo "Dodano firmę: " . $company['name'] . "\n";
    }
    
    echo "\nPomyślnie dodano " . count($companies) . " przykładowych firm!\n";
    echo "Firmy TOP:\n";
    echo "1. Restauracja Pod Akacjami\n";
    echo "2. Salon Fryzjerski Bella\n";
    echo "3. Sklep Sportowy Active\n";
    echo "\nMożesz teraz sprawdzić frontend i panel administracyjny.\n";
    
} catch (PDOException $e) {
    echo "Błąd bazy danych: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "Błąd: " . $e->getMessage() . "\n";
}
?>
