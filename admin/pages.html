<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zarządzanie stronami - Dashboard Żyrardów Poleca</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/pages.css">
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- HugeRTE WYSIWYG Editor (darmowa wersja TinyMCE v6) -->
    <script src="https://cdn.jsdelivr.net/npm/@hugerte/hugerte@6/dist/hugerte.min.js"></script>
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="../images/logo.png" alt="Żyrardów Poleca">
                    <h2>Dashboard</h2>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="Admin Avatar">
                </div>
                <div class="user-info">
                    <h3>Administrator</h3>
                    <p><EMAIL></p>
                </div>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="companies.html">
                            <i class="fas fa-building"></i>
                            <span>Firmy</span>
                        </a>
                    </li>
                    <li>
                        <a href="offers.html">
                            <i class="fas fa-tag"></i>
                            <span>Oferty</span>
                        </a>
                    </li>
                    <li>
                        <a href="coupons.html">
                            <i class="fas fa-ticket-alt"></i>
                            <span>Kupony</span>
                        </a>
                    </li>
                    <li>
                        <a href="categories.html">
                            <i class="fas fa-list"></i>
                            <span>Kategorie</span>
                        </a>
                    </li>
                    <li>
                        <a href="news.html">
                            <i class="fas fa-newspaper"></i>
                            <span>Wiadomości</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Użytkownicy</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="pages.html">
                            <i class="fas fa-file-alt"></i>
                            <span>Strony CMS</span>
                        </a>
                    </li>
                    <li>
                        <a href="map.html">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Mapa TOP firm</span>
                        </a>
                    </li>
                    <li>
                        <a href="menus.html">
                            <i class="fas fa-bars"></i>
                            <span>Menu</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Ustawienia</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <button id="logoutBtn" class="btn-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Wyloguj</span>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-title">
                    <h1>Zarządzanie stronami</h1>
                    <p>Edytuj treść stron internetowych</p>
                </div>
                <div class="header-actions">
                    <div class="search-box">
                        <input type="text" id="page-search" placeholder="Szukaj strony...">
                        <button><i class="fas fa-search"></i></button>
                    </div>
                    <button class="btn btn-primary" id="addPageBtn">
                        <i class="fas fa-plus"></i> Dodaj stronę
                    </button>
                </div>
            </header>

            <div class="content-body">
                <!-- Pages List -->
                <div class="pages-grid" id="pagesGrid">
                    <!-- Strony będą ładowane dynamicznie -->
                </div>
            </div>
        </main>
    </div>

    <!-- Modal edycji strony -->
    <div class="modal" id="pageModal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="pageModalTitle">Edytuj stronę</h3>
                <button class="modal-close" id="pageModalClose">&times;</button>
            </div>
            <div class="modal-body">
                <form id="pageForm">
                    <input type="hidden" id="pageId">

                    <div class="form-group">
                        <label for="pageTitle">Tytuł strony</label>
                        <input type="text" id="pageTitle" readonly>
                    </div>

                    <div class="form-group">
                        <label for="pageFile">Plik</label>
                        <input type="text" id="pageFile" readonly>
                    </div>

                    <div class="form-group">
                        <label for="pageContent">Treść strony</label>
                        <textarea id="pageContent" rows="20"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="pageCancelBtn">Anuluj</button>
                <button type="button" class="btn btn-primary" id="pageSaveBtn">Zapisz zmiany</button>
                <button type="button" class="btn btn-success" id="pagePreviewBtn">Podgląd</button>
            </div>
        </div>
    </div>

    <!-- Powiadomienia -->
    <div class="notifications" id="notifications"></div>

    <script src="js/admin.js"></script>
    <script src="js/pages.js"></script>
</body>
</html>
