<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zarządzanie menu - Panel Administracyjny - Żyrardów Poleca</title>
    <meta name="description" content="Panel administracyjny portalu Żyrardów Poleca - zarządzanie menu nagłówka i stopki">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/menus.css">
    <link rel="icon" href="../favicon.ico" type="image/x-icon">
</head>
<body class="menus-page">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.png" alt="Żyrardów Poleca Logo" class="sidebar-logo">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="sidebar-user">
                <div class="user-info">
                    <h3>Administrator</h3>
                    <p><EMAIL></p>
                </div>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="companies.html">
                            <i class="fas fa-building"></i>
                            <span>Firmy</span>
                        </a>
                    </li>
                    <li>
                        <a href="offers.html">
                            <i class="fas fa-tag"></i>
                            <span>Oferty</span>
                        </a>
                    </li>
                    <li>
                        <a href="coupons.html">
                            <i class="fas fa-ticket-alt"></i>
                            <span>Kupony</span>
                        </a>
                    </li>
                    <li>
                        <a href="categories.html">
                            <i class="fas fa-list"></i>
                            <span>Kategorie</span>
                        </a>
                    </li>
                    <li>
                        <a href="news.html">
                            <i class="fas fa-newspaper"></i>
                            <span>Wiadomości</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Użytkownicy</span>
                        </a>
                    </li>
                    <li>
                        <a href="pages.html">
                            <i class="fas fa-file-alt"></i>
                            <span>Strony CMS</span>
                        </a>
                    </li>
                    <li>
                        <a href="map.html">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Mapa TOP firm</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="menus.html">
                            <i class="fas fa-bars"></i>
                            <span>Menu</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Ustawienia</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <button id="logoutBtn" class="btn-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Wyloguj</span>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-title">
                    <h1>Zarządzanie menu</h1>
                    <p>Edytuj menu nagłówka i stopki strony</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-primary" id="save-menus-btn">
                        <i class="fas fa-save"></i> Zapisz zmiany
                    </button>
                </div>
            </header>

            <div class="content-body">
                <!-- Menu Tabs -->
                <div class="menu-tabs">
                    <button class="tab-btn active" data-tab="header-menu">Menu nagłówka</button>
                    <button class="tab-btn" data-tab="footer-menu">Menu stopki</button>
                </div>

                <div class="menu-container">
                    <!-- Header Menu Tab -->
                    <div class="tab-content active" id="header-menu">
                        <div class="menu-editor">
                            <div class="menu-editor-header">
                                <h2>Edycja menu nagłówka</h2>
                                <button class="btn btn-outline" id="add-header-item">
                                    <i class="fas fa-plus"></i> Dodaj pozycję
                                </button>
                            </div>
                            <div class="menu-preview">
                                <h3>Podgląd menu</h3>
                                <div class="menu-preview-container">
                                    <ul class="menu-items-list" id="header-menu-items">
                                        <!-- Menu items will be added here dynamically -->
                                    </ul>
                                </div>
                            </div>
                            <div class="menu-structure">
                                <h3>Struktura menu</h3>
                                <p class="menu-hint">Przeciągnij elementy, aby zmienić ich kolejność. Kliknij na element, aby go edytować.</p>
                                <div class="menu-structure-container">
                                    <ul class="sortable-menu" id="header-menu-structure">
                                        <!-- Menu structure will be added here dynamically -->
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer Menu Tab -->
                    <div class="tab-content" id="footer-menu">
                        <div class="menu-editor">
                            <div class="menu-editor-header">
                                <h2>Edycja menu stopki</h2>
                                <button class="btn btn-outline" id="add-footer-item">
                                    <i class="fas fa-plus"></i> Dodaj pozycję
                                </button>
                            </div>
                            <div class="menu-preview">
                                <h3>Podgląd menu</h3>
                                <div class="menu-preview-container footer-preview">
                                    <ul class="menu-items-list" id="footer-menu-items">
                                        <!-- Menu items will be added here dynamically -->
                                    </ul>
                                </div>
                            </div>
                            <div class="menu-structure">
                                <h3>Struktura menu</h3>
                                <p class="menu-hint">Przeciągnij elementy, aby zmienić ich kolejność. Kliknij na element, aby go edytować.</p>
                                <div class="menu-structure-container">
                                    <ul class="sortable-menu" id="footer-menu-structure">
                                        <!-- Menu structure will be added here dynamically -->
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Add/Edit Menu Item Modal -->
    <div class="modal" id="menuItemModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="menu-item-modal-title">Dodaj pozycję menu</h2>
                <button class="modal-close"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <form id="menu-item-form">
                    <input type="hidden" id="menu-item-id">
                    <input type="hidden" id="menu-type">
                    <input type="hidden" id="parent-id" value="0">

                    <div class="form-group">
                        <label for="menu-item-text">Tekst</label>
                        <input type="text" id="menu-item-text" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="menu-item-url">URL</label>
                        <input type="text" id="menu-item-url" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="menu-item-target">Otwieranie linku</label>
                        <select id="menu-item-target" class="form-control">
                            <option value="_self">W tym samym oknie</option>
                            <option value="_blank">W nowym oknie</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-option">
                            <input type="checkbox" id="menu-item-has-children">
                            <label for="menu-item-has-children">Pozycja ma podmenu</label>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-outline modal-cancel">Anuluj</button>
                        <button type="submit" class="btn btn-primary">Zapisz</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Submenu Item Modal -->
    <div class="modal" id="submenuItemModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Dodaj pozycję podmenu</h2>
                <button class="modal-close"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <form id="submenu-item-form">
                    <input type="hidden" id="submenu-parent-id">
                    <input type="hidden" id="submenu-menu-type">

                    <div class="form-group">
                        <label for="submenu-item-text">Tekst</label>
                        <input type="text" id="submenu-item-text" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="submenu-item-url">URL</label>
                        <input type="text" id="submenu-item-url" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="submenu-item-target">Otwieranie linku</label>
                        <select id="submenu-item-target" class="form-control">
                            <option value="_self">W tym samym oknie</option>
                            <option value="_blank">W nowym oknie</option>
                        </select>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-outline modal-cancel">Anuluj</button>
                        <button type="submit" class="btn btn-primary">Zapisz</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.14.0/Sortable.min.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/menus.js"></script>
</body>
</html>
