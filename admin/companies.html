<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zarządzanie firmami - Panel Administracyjny - Żyrardów Poleca</title>
    <meta name="description" content="Panel administracyjny portalu Żyrardów Poleca - zarządzanie firmami">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/companies.css">
    <link rel="stylesheet" href="css/top-positions.css">
    <style>
        /* Powiadomienia */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .notification {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-family: 'Montserrat', sans-serif;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            margin-left: 10px;
        }

        /* Znaczniki TOP */
        .top-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            color: white;
            margin-left: 8px;
            vertical-align: middle;
        }

        .top-badge.top-1 {
            background: linear-gradient(45deg, #FFD700, #FFA500);
        }

        .top-badge.top-2 {
            background: linear-gradient(45deg, #C0C0C0, #A9A9A9);
        }

        .top-badge.top-3 {
            background: linear-gradient(45deg, #CD7F32, #B8860B);
        }

        /* Przyciski akcji */
        .btn-icon {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0 2px;
        }

        .btn-icon:hover {
            background: rgba(0,0,0,0.1);
        }

        .btn-icon.edit-company {
            color: #007bff;
        }

        .btn-icon.view-company {
            color: #28a745;
        }

        .btn-icon.top-position {
            color: #ffc107;
        }

        .btn-icon.toggle-status {
            color: #6c757d;
        }

        .btn-icon.delete-company {
            color: #dc3545;
        }

        /* Status badges */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.active {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-badge.inactive {
            background: #f8d7da;
            color: #721c24;
        }

        /* Logo firmy */
        .company-logo {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
        }
    </style>
    <link rel="icon" href="../favicon.ico" type="image/x-icon">
</head>
<body class="companies-page">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.png" alt="Żyrardów Poleca Logo" class="sidebar-logo">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="Admin Avatar">
                </div>
                <div class="user-info">
                    <h3>Administrator</h3>
                    <p><EMAIL></p>
                </div>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="companies.html">
                            <i class="fas fa-building"></i>
                            <span>Firmy</span>
                        </a>
                    </li>
                    <li>
                        <a href="offers.html">
                            <i class="fas fa-tag"></i>
                            <span>Oferty</span>
                        </a>
                    </li>
                    <li>
                        <a href="coupons.html">
                            <i class="fas fa-ticket-alt"></i>
                            <span>Kupony</span>
                        </a>
                    </li>
                    <li>
                        <a href="categories.html">
                            <i class="fas fa-list"></i>
                            <span>Kategorie</span>
                        </a>
                    </li>
                    <li>
                        <a href="news.html">
                            <i class="fas fa-newspaper"></i>
                            <span>Wiadomości</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Użytkownicy</span>
                        </a>
                    </li>
                    <li>
                        <a href="pages.html">
                            <i class="fas fa-file-alt"></i>
                            <span>Strony CMS</span>
                        </a>
                    </li>
                    <li>
                        <a href="map.html">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Mapa TOP firm</span>
                        </a>
                    </li>

                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Ustawienia</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <button id="logoutBtn" class="btn-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Wyloguj</span>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-title">
                    <h1>Zarządzanie firmami</h1>
                    <p>Dodawaj, edytuj i usuwaj firmy w katalogu</p>
                </div>
                <div class="header-actions">
                    <div class="search-box">
                        <input type="text" id="company-search" placeholder="Szukaj firmy...">
                        <button><i class="fas fa-search"></i></button>
                    </div>
                    <a href="companies-add.html" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Dodaj firmę
                    </a>
                </div>
            </header>

            <div class="content-body">
                <!-- Filters -->
                <div class="filters-container">
                    <div class="filters">
                        <div class="filter-group">
                            <label for="category-filter">Kategoria:</label>
                            <select id="category-filter" class="form-control">
                                <option value="">Wszystkie kategorie</option>
                                <option value="1">Restauracje i kawiarnie</option>
                                <option value="2">Sklepy</option>
                                <option value="3">Usługi</option>
                                <option value="4">Zdrowie i uroda</option>
                                <option value="5">Rozrywka i rekreacja</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="status-filter">Status:</label>
                            <select id="status-filter" class="form-control">
                                <option value="">Wszystkie statusy</option>
                                <option value="active">Aktywne</option>
                                <option value="pending">Oczekujące</option>
                                <option value="inactive">Nieaktywne</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="sort-by">Sortuj według:</label>
                            <select id="sort-by" class="form-control">
                                <option value="name">Nazwa (A-Z)</option>
                                <option value="name-desc">Nazwa (Z-A)</option>
                                <option value="date-newest">Data dodania (najnowsze)</option>
                                <option value="date-oldest">Data dodania (najstarsze)</option>
                                <option value="views">Liczba wyświetleń</option>
                            </select>
                        </div>
                    </div>
                    <button id="reset-filters" class="btn btn-outline">
                        <i class="fas fa-redo"></i> Resetuj filtry
                    </button>
                </div>

                <!-- Companies Table -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="select-all">
                                </th>
                                <th>Logo</th>
                                <th>Nazwa firmy</th>
                                <th>Kategoria</th>
                                <th>Adres</th>
                                <th>Status</th>
                                <th>Data dodania</th>
                                <th>Akcje</th>
                            </tr>
                        </thead>
                        <tbody id="companies-table-body">
                            <tr>
                                <td><input type="checkbox" class="company-select"></td>
                                <td><img src="../images/business-logo1.jpg" alt="Logo firmy" class="company-logo"></td>
                                <td>Restauracja Pod Akacjami</td>
                                <td>Restauracje i kawiarnie</td>
                                <td>ul. Przykładowa 1, Żyrardów</td>
                                <td><span class="status-badge active">Aktywna</span></td>
                                <td>12.05.2023</td>
                                <td class="actions">
                                    <button class="btn-icon edit-company" title="Edytuj" data-company-id="1"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon view-company" title="Podgląd" data-company-id="1"><i class="fas fa-eye"></i></button>
                                    <button class="btn-icon toggle-status" title="Zmień status" data-company-id="1"><i class="fas fa-toggle-on"></i></button>
                                    <button class="btn-icon delete-company" title="Usuń" data-company-id="1"><i class="fas fa-trash-alt"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" class="company-select"></td>
                                <td><img src="../images/business-logo2.jpg" alt="Logo firmy" class="company-logo"></td>
                                <td>Salon Fryzjerski Bella</td>
                                <td>Zdrowie i uroda</td>
                                <td>ul. Przykładowa 2, Żyrardów</td>
                                <td><span class="status-badge active">Aktywna</span></td>
                                <td>10.05.2023</td>
                                <td class="actions">
                                    <button class="btn-icon" title="Edytuj"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon" title="Podgląd"><i class="fas fa-eye"></i></button>
                                    <button class="btn-icon delete" title="Usuń"><i class="fas fa-trash-alt"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" class="company-select"></td>
                                <td><img src="../images/business-logo3.jpg" alt="Logo firmy" class="company-logo"></td>
                                <td>Sklep Sportowy Active</td>
                                <td>Sklepy</td>
                                <td>ul. Przykładowa 3, Żyrardów</td>
                                <td><span class="status-badge pending">Oczekująca</span></td>
                                <td>08.05.2023</td>
                                <td class="actions">
                                    <button class="btn-icon" title="Edytuj"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon" title="Podgląd"><i class="fas fa-eye"></i></button>
                                    <button class="btn-icon delete" title="Usuń"><i class="fas fa-trash-alt"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" class="company-select"></td>
                                <td><img src="../images/business-logo1.jpg" alt="Logo firmy" class="company-logo"></td>
                                <td>Kancelaria Prawna Paragraf</td>
                                <td>Usługi</td>
                                <td>ul. Przykładowa 4, Żyrardów</td>
                                <td><span class="status-badge inactive">Nieaktywna</span></td>
                                <td>05.05.2023</td>
                                <td class="actions">
                                    <button class="btn-icon" title="Edytuj"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon" title="Podgląd"><i class="fas fa-eye"></i></button>
                                    <button class="btn-icon delete" title="Usuń"><i class="fas fa-trash-alt"></i></button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Bulk Actions -->
                <div class="bulk-actions">
                    <div class="bulk-actions-select">
                        <span>Z zaznaczonymi:</span>
                        <select id="bulk-action" class="form-control">
                            <option value="">Wybierz akcję</option>
                            <option value="activate">Aktywuj</option>
                            <option value="deactivate">Dezaktywuj</option>
                            <option value="delete">Usuń</option>
                        </select>
                        <button id="apply-bulk-action" class="btn btn-outline">Zastosuj</button>
                    </div>
                    <div class="pagination">
                        <button class="btn-page" disabled><i class="fas fa-chevron-left"></i></button>
                        <button class="btn-page active">1</button>
                        <button class="btn-page">2</button>
                        <button class="btn-page">3</button>
                        <button class="btn-page"><i class="fas fa-chevron-right"></i></button>
                    </div>
                </div>
            </div>
        </main>
    </div>



    <script src="js/admin.js"></script>
    <script src="js/companies.js"></script>
</body>
</html>
