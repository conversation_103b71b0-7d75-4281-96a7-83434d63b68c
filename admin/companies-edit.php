<?php
/**
 * Edy<PERSON>ja firmy - Panel Administracyjny
 * Żyrardów Poleca
 */

// Połączenie z bazą danych
require_once __DIR__ . '/config/database.php';

// Pobierz ID firmy z URL
$companyId = $_GET['id'] ?? null;
$company = null;
$categories = [];

if ($companyId) {
    try {
        // Pobierz dane firmy
        $stmt = $pdo->prepare("
            SELECT c.*, cat.name as category_name 
            FROM companies c
            LEFT JOIN categories cat ON c.categoryId = cat.id
            WHERE c.id = ?
        ");
        $stmt->execute([$companyId]);
        $company = $stmt->fetch();
        
        if (!$company) {
            header('Location: companies.php?error=not_found');
            exit;
        }
    } catch (PDOException $e) {
        header('Location: companies.php?error=database');
        exit;
    }
}

// Pobierz kategorie
try {
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE parentId IS NULL ORDER BY sortOrder ASC, name ASC");
    $stmt->execute();
    $categories = $stmt->fetchAll();
} catch (PDOException $e) {
    $categories = [];
}

// Obsługa zapisywania
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $company) {
    try {
        $stmt = $pdo->prepare("
            UPDATE companies 
            SET name = ?, categoryId = ?, description = ?, address = ?, postalCode = ?, city = ?, 
                phone = ?, email = ?, website = ?, topPosition = ?, status = ?, updatedAt = datetime('now')
            WHERE id = ?
        ");
        
        $topPosition = !empty($_POST['topPosition']) ? (int)$_POST['topPosition'] : null;
        
        $stmt->execute([
            $_POST['name'] ?? '',
            $_POST['categoryId'] ?? null,
            $_POST['description'] ?? '',
            $_POST['address'] ?? '',
            $_POST['postalCode'] ?? '',
            $_POST['city'] ?? 'Żyrardów',
            $_POST['phone'] ?? '',
            $_POST['email'] ?? '',
            $_POST['website'] ?? '',
            $topPosition,
            $_POST['status'] ?? 'pending',
            $companyId
        ]);
        
        $success = "Firma została zaktualizowana pomyślnie!";
        
        // Odśwież dane firmy
        $stmt = $pdo->prepare("
            SELECT c.*, cat.name as category_name 
            FROM companies c
            LEFT JOIN categories cat ON c.categoryId = cat.id
            WHERE c.id = ?
        ");
        $stmt->execute([$companyId]);
        $company = $stmt->fetch();
        
    } catch (PDOException $e) {
        $error = "Błąd podczas zapisywania: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edytuj firmę - Panel Administracyjny - Żyrardów Poleca</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/companies-add.css">
    <link rel="icon" href="../favicon.ico" type="image/x-icon">
</head>
<body class="companies-edit-page">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.png" alt="Żyrardów Poleca Logo" class="sidebar-logo">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="Admin Avatar">
                </div>
                <div class="user-info">
                    <h3>Administrator</h3>
                    <p><EMAIL></p>
                </div>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i><span>Dashboard</span></a></li>
                    <li class="active"><a href="companies.php"><i class="fas fa-building"></i><span>Firmy</span></a></li>
                    <li><a href="offers.php"><i class="fas fa-tag"></i><span>Oferty</span></a></li>
                    <li><a href="coupons.php"><i class="fas fa-ticket-alt"></i><span>Kupony</span></a></li>
                    <li><a href="categories.php"><i class="fas fa-list"></i><span>Kategorie</span></a></li>
                    <li><a href="news.php"><i class="fas fa-newspaper"></i><span>Wiadomości</span></a></li>
                    <li><a href="users.php"><i class="fas fa-users"></i><span>Użytkownicy</span></a></li>
                    <li><a href="pages.php"><i class="fas fa-file-alt"></i><span>Strony CMS</span></a></li>
                    <li><a href="map.php"><i class="fas fa-map-marker-alt"></i><span>Mapa TOP firm</span></a></li>
                    <li><a href="settings.php"><i class="fas fa-cog"></i><span>Ustawienia</span></a></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <button id="logoutBtn" class="btn-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Wyloguj</span>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-title">
                    <h1>Edytuj firmę: <?= htmlspecialchars($company['name'] ?? 'Nieznana firma') ?></h1>
                    <p>Modyfikuj dane firmy w katalogu</p>
                </div>
                <div class="header-actions">
                    <a href="companies.php" class="btn btn-outline">
                        <i class="fas fa-arrow-left"></i> Powrót do listy firm
                    </a>
                </div>
            </header>

            <div class="content-body">
                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?= htmlspecialchars($success) ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <?php if ($company): ?>
                <form method="POST" class="form-container">
                    <!-- Podstawowe informacje -->
                    <div class="form-section">
                        <h2>Podstawowe informacje</h2>
                        
                        <div class="form-group">
                            <label for="name">Nazwa firmy <span class="required">*</span></label>
                            <input type="text" id="name" name="name" class="form-control" 
                                   value="<?= htmlspecialchars($company['name'] ?? '') ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="categoryId">Kategoria <span class="required">*</span></label>
                            <select id="categoryId" name="categoryId" class="form-control" required>
                                <option value="">Wybierz kategorię</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>" 
                                            <?= ($company['categoryId'] == $category['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($category['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="description">Opis firmy <span class="required">*</span></label>
                            <textarea id="description" name="description" class="form-control" rows="5" required><?= htmlspecialchars($company['description'] ?? '') ?></textarea>
                        </div>

                        <div class="form-group">
                            <label>Status</label>
                            <div class="radio-group">
                                <div class="radio-option">
                                    <input type="radio" id="status-active" name="status" value="active" 
                                           <?= ($company['status'] === 'active') ? 'checked' : '' ?>>
                                    <label for="status-active">Aktywna</label>
                                </div>
                                <div class="radio-option">
                                    <input type="radio" id="status-pending" name="status" value="pending"
                                           <?= ($company['status'] === 'pending') ? 'checked' : '' ?>>
                                    <label for="status-pending">Oczekująca</label>
                                </div>
                                <div class="radio-option">
                                    <input type="radio" id="status-inactive" name="status" value="inactive"
                                           <?= ($company['status'] === 'inactive') ? 'checked' : '' ?>>
                                    <label for="status-inactive">Nieaktywna</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="topPosition">Pozycja TOP</label>
                            <select id="topPosition" name="topPosition" class="form-control">
                                <option value="">Brak pozycji TOP</option>
                                <option value="1" <?= ($company['topPosition'] == 1) ? 'selected' : '' ?>>TOP 1 - Najwyższa pozycja</option>
                                <option value="2" <?= ($company['topPosition'] == 2) ? 'selected' : '' ?>>TOP 2 - Druga pozycja</option>
                                <option value="3" <?= ($company['topPosition'] == 3) ? 'selected' : '' ?>>TOP 3 - Trzecia pozycja</option>
                            </select>
                            <small class="form-text">Wybierz pozycję TOP, jeśli firma ma być wyróżniona na stronie głównej</small>
                        </div>
                    </div>

                    <!-- Dane kontaktowe -->
                    <div class="form-section">
                        <h2>Dane kontaktowe</h2>
                        
                        <div class="form-group">
                            <label for="address">Adres <span class="required">*</span></label>
                            <input type="text" id="address" name="address" class="form-control" 
                                   value="<?= htmlspecialchars($company['address'] ?? '') ?>" required>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="postalCode">Kod pocztowy</label>
                                <input type="text" id="postalCode" name="postalCode" class="form-control" 
                                       value="<?= htmlspecialchars($company['postalCode'] ?? '') ?>">
                            </div>
                            <div class="form-group">
                                <label for="city">Miasto</label>
                                <input type="text" id="city" name="city" class="form-control" 
                                       value="<?= htmlspecialchars($company['city'] ?? 'Żyrardów') ?>">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="phone">Telefon</label>
                            <input type="tel" id="phone" name="phone" class="form-control" 
                                   value="<?= htmlspecialchars($company['phone'] ?? '') ?>">
                        </div>

                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" class="form-control" 
                                   value="<?= htmlspecialchars($company['email'] ?? '') ?>">
                        </div>

                        <div class="form-group">
                            <label for="website">Strona internetowa</label>
                            <input type="url" id="website" name="website" class="form-control" 
                                   value="<?= htmlspecialchars($company['website'] ?? '') ?>" placeholder="https://">
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Zapisz zmiany
                        </button>
                        <a href="companies.php" class="btn btn-outline">Anuluj</a>
                    </div>
                </form>
                <?php else: ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        Firma nie została znaleziona.
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <script src="js/admin.js"></script>
</body>
</html>
