/**
 * Panel Administracyjny - Żyrardów Poleca
 * JavaScript dla dodawania/edycji kuponów
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja formularza dodawania/edycji kuponu
    initCouponForm();
});

/**
 * Inicjalizacja formularza dodawania/edycji kuponu
 */
function initCouponForm() {
    // Obsługa podglądu kuponu
    initCouponPreview();

    // Obsługa przycisku anuluj
    initCancelButton();

    // Obsługa przesyłania formularza
    initFormSubmission();

    // Sprawdzenie czy jesteśmy w trybie edycji
    checkEditMode();
}

/**
 * Inicjalizacja podglądu kuponu
 */
function initCouponPreview() {
    // Elementy formularza
    const companySelect = document.getElementById('coupon-company');
    const codeInput = document.getElementById('coupon-code');
    const discountInput = document.getElementById('coupon-discount');
    const descriptionInput = document.getElementById('coupon-description');
    const endDateInput = document.getElementById('coupon-end-date');
    const termsInput = document.getElementById('coupon-terms');

    // Elementy podglądu
    const previewLogo = document.getElementById('preview-logo');
    const previewCompanyName = document.getElementById('preview-company-name');
    const previewCompanyAddress = document.getElementById('preview-company-address');
    const previewDiscount = document.getElementById('preview-discount');
    const previewDescription = document.getElementById('preview-description');
    const previewCode = document.getElementById('preview-code');
    const previewValidity = document.getElementById('preview-validity');
    const previewTerms = document.getElementById('preview-terms');

    // Dane firm (w rzeczywistej implementacji byłyby pobierane z API)
    const companies = {
        '1': {
            name: 'Restauracja Pod Akacjami',
            address: 'ul. Przykładowa 1, Żyrardów',
            logo: '../images/business-logo1.jpg'
        },
        '2': {
            name: 'Salon Fryzjerski Bella',
            address: 'ul. Przykładowa 2, Żyrardów',
            logo: '../images/business-logo2.jpg'
        },
        '3': {
            name: 'Sklep Sportowy Active',
            address: 'ul. Przykładowa 3, Żyrardów',
            logo: '../images/business-logo3.jpg'
        },
        '4': {
            name: 'Kancelaria Prawna Paragraf',
            address: 'ul. Przykładowa 4, Żyrardów',
            logo: '../images/business-logo1.jpg'
        }
    };

    // Aktualizacja podglądu przy zmianie firmy
    if (companySelect) {
        companySelect.addEventListener('change', function() {
            const companyId = this.value;

            if (companyId && companies[companyId]) {
                const company = companies[companyId];

                previewLogo.src = company.logo;
                previewCompanyName.textContent = company.name;
                previewCompanyAddress.textContent = company.address;
            } else {
                previewLogo.src = '../images/business-logo1.jpg';
                previewCompanyName.textContent = 'Wybierz firmę';
                previewCompanyAddress.textContent = 'Adres firmy';
            }
        });
    }

    // Aktualizacja podglądu przy zmianie kodu kuponu
    if (codeInput) {
        codeInput.addEventListener('input', function() {
            previewCode.textContent = this.value || 'KOD';
        });
    }

    // Aktualizacja podglądu przy zmianie rabatu
    if (discountInput) {
        discountInput.addEventListener('input', function() {
            previewDiscount.textContent = (this.value || '0') + '%';
        });
    }

    // Aktualizacja podglądu przy zmianie opisu
    if (descriptionInput) {
        descriptionInput.addEventListener('input', function() {
            previewDescription.textContent = this.value || 'Opis kuponu';
        });
    }

    // Aktualizacja podglądu przy zmianie daty zakończenia
    if (endDateInput) {
        endDateInput.addEventListener('change', function() {
            if (this.value) {
                const date = new Date(this.value);
                const day = date.getDate().toString().padStart(2, '0');
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const year = date.getFullYear();

                previewValidity.textContent = `${day}.${month}.${year}`;
            } else {
                previewValidity.textContent = 'DD.MM.RRRR';
            }
        });
    }

    // Aktualizacja podglądu przy zmianie warunków
    if (termsInput) {
        termsInput.addEventListener('input', function() {
            if (this.value) {
                previewTerms.innerHTML = `<p>* ${this.value}</p>`;
            } else {
                previewTerms.innerHTML = '<p>* Warunki korzystania z kuponu</p>';
            }
        });
    }
}

/**
 * Inicjalizacja przycisku anuluj
 */
function initCancelButton() {
    const cancelButton = document.getElementById('cancel-btn');

    if (cancelButton) {
        cancelButton.addEventListener('click', function() {
            if (confirm('Czy na pewno chcesz anulować? Wszystkie wprowadzone zmiany zostaną utracone.')) {
                window.location.href = 'coupons.html';
            }
        });
    }
}

/**
 * Inicjalizacja przesyłania formularza
 */
function initFormSubmission() {
    const couponForm = document.getElementById('coupon-form');

    if (couponForm) {
        couponForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Walidacja formularza
            if (!validateForm()) {
                return;
            }

            // Zapisz kupon
            saveCoupon();
        });
    }
}

/**
 * Zapisz kupon do localStorage
 */
function saveCoupon() {
    try {
        // Pobierz dane z formularza
        const formData = getCouponFormData();

        // Pobierz istniejące dane kuponów
        const couponsData = JSON.parse(localStorage.getItem('coupons_data') || '{"coupons":[]}');

        // Sprawdź czy to edycja czy nowy kupon
        const urlParams = new URLSearchParams(window.location.search);
        const editId = urlParams.get('edit');

        if (editId) {
            // Edycja istniejącego kuponu
            const couponIndex = couponsData.coupons.findIndex(c => c.id == editId);
            if (couponIndex !== -1) {
                couponsData.coupons[couponIndex] = { ...couponsData.coupons[couponIndex], ...formData };
                showNotification('Kupon został zaktualizowany!', 'success');
            }
        } else {
            // Dodanie nowego kuponu
            const newId = Math.max(...couponsData.coupons.map(c => c.id || 0), 0) + 1;
            formData.id = newId;
            formData.dateAdded = new Date().toISOString().split('T')[0];
            formData.usageCount = 0;
            couponsData.coupons.push(formData);
            showNotification('Kupon został dodany!', 'success');
        }

        // Zapisz dane
        localStorage.setItem('coupons_data', JSON.stringify(couponsData));

        // Synchronizuj z frontend
        syncCouponsWithFrontend();

        // Przekierowanie po 2 sekundach
        setTimeout(() => {
            window.location.href = 'coupons.html';
        }, 2000);

    } catch (error) {
        console.error('Błąd podczas zapisywania kuponu:', error);
        showNotification('Wystąpił błąd podczas zapisywania kuponu', 'error');
    }
}

/**
 * Pobierz dane z formularza kuponu
 */
function getCouponFormData() {
    const formData = {};

    // Podstawowe informacje
    formData.code = document.getElementById('coupon-code')?.value || '';
    formData.companyId = document.getElementById('coupon-company')?.value || '';
    formData.title = document.getElementById('coupon-title')?.value || '';
    formData.description = document.getElementById('coupon-description')?.value || '';

    // Typ rabatu
    const discountType = document.querySelector('input[name="discount-type"]:checked')?.value || 'percentage';
    formData.discountType = discountType;
    formData.discountValue = parseFloat(document.getElementById('discount-value')?.value) || 0;

    // Daty
    formData.startDate = document.getElementById('coupon-start-date')?.value || '';
    formData.endDate = document.getElementById('coupon-end-date')?.value || '';

    // Ograniczenia
    formData.usageLimit = parseInt(document.getElementById('usage-limit')?.value) || null;
    formData.minOrderValue = parseFloat(document.getElementById('min-order-value')?.value) || 0;

    // Status
    const statusRadio = document.querySelector('input[name="coupon-status"]:checked');
    formData.status = statusRadio?.value || 'draft';

    // Pobierz nazwę firmy
    const companySelect = document.getElementById('coupon-company');
    if (companySelect) {
        const selectedOption = companySelect.options[companySelect.selectedIndex];
        formData.companyName = selectedOption?.text || '';
    }

    return formData;
}

/**
 * Synchronizuj kupony z frontend
 */
function syncCouponsWithFrontend() {
    try {
        const couponsData = JSON.parse(localStorage.getItem('coupons_data') || '{"coupons":[]}');
        const companiesData = JSON.parse(localStorage.getItem('companies_data') || '{"companies":[]}');

        // Przygotuj dane dla frontend
        const frontendCoupons = couponsData.coupons.filter(c => c.status === 'active').map(coupon => {
            const company = companiesData.companies.find(comp => comp.id == coupon.companyId);
            return {
                id: coupon.id,
                code: coupon.code,
                title: coupon.title,
                description: coupon.description,
                discountType: coupon.discountType,
                discountValue: coupon.discountValue,
                startDate: coupon.startDate,
                endDate: coupon.endDate,
                usageLimit: coupon.usageLimit,
                usageCount: coupon.usageCount,
                minOrderValue: coupon.minOrderValue,
                company: {
                    id: company?.id || coupon.companyId,
                    name: company?.name || coupon.companyName,
                    logo: company?.logo || '../images/business-logo1.jpg'
                }
            };
        });

        // Zapisz dane dla frontend
        localStorage.setItem('frontend_coupons', JSON.stringify(frontendCoupons));

        // Wyślij event o aktualizacji
        window.dispatchEvent(new CustomEvent('couponsUpdated', {
            detail: { coupons: frontendCoupons }
        }));

    } catch (error) {
        console.error('Błąd podczas synchronizacji kuponów z frontend:', error);
    }
}

/**
 * Wyświetl powiadomienie
 */
function showNotification(message, type = 'info') {
    // Usuń istniejące powiadomienia
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(n => n.remove());

    // Utwórz nowe powiadomienie
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">&times;</button>
    `;

    // Dodaj style
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
        color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
        border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
        border-radius: 8px;
        padding: 15px;
        z-index: 10000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Obsługa zamykania
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => notification.remove());

    // Auto-usuwanie po 5 sekundach
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Walidacja formularza
 * @returns {boolean} - Czy formularz jest poprawny
 */
function validateForm() {
    const requiredFields = document.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value) {
            field.classList.add('is-invalid');

            // Dodanie komunikatu o błędzie, jeśli nie istnieje
            let feedback = field.nextElementSibling;
            if (!feedback || !feedback.classList.contains('invalid-feedback')) {
                feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = 'To pole jest wymagane.';
                field.parentNode.insertBefore(feedback, field.nextSibling);
            }

            isValid = false;
        } else {
            field.classList.remove('is-invalid');

            // Usunięcie komunikatu o błędzie, jeśli istnieje
            const feedback = field.nextElementSibling;
            if (feedback && feedback.classList.contains('invalid-feedback')) {
                feedback.remove();
            }
        }
    });

    // Sprawdzenie, czy data zakończenia jest późniejsza niż data rozpoczęcia
    const startDateInput = document.getElementById('coupon-start-date');
    const endDateInput = document.getElementById('coupon-end-date');

    if (startDateInput && endDateInput && startDateInput.value && endDateInput.value) {
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(endDateInput.value);

        if (endDate < startDate) {
            endDateInput.classList.add('is-invalid');

            // Dodanie komunikatu o błędzie, jeśli nie istnieje
            let feedback = endDateInput.nextElementSibling;
            if (!feedback || !feedback.classList.contains('invalid-feedback')) {
                feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = 'Data zakończenia musi być późniejsza niż data rozpoczęcia.';
                endDateInput.parentNode.insertBefore(feedback, endDateInput.nextSibling);
            } else {
                feedback.textContent = 'Data zakończenia musi być późniejsza niż data rozpoczęcia.';
            }

            isValid = false;
        }
    }

    return isValid;
}

/**
 * Sprawdzenie czy jesteśmy w trybie edycji
 */
function checkEditMode() {
    const urlParams = new URLSearchParams(window.location.search);
    const editCoupon = urlParams.get('edit');

    if (editCoupon) {
        // W rzeczywistej implementacji tutaj byłoby pobieranie danych kuponu z API
        // Na potrzeby demonstracji używamy przykładowych danych
        document.querySelector('h1').textContent = 'Edytuj kupon';
        document.querySelector('.header-title p').textContent = 'Edytuj dane kuponu rabatowego';

        // Wypełnienie formularza przykładowymi danymi
        document.getElementById('coupon-company').value = '1';
        document.getElementById('coupon-code').value = decodeURIComponent(editCoupon);
        document.getElementById('coupon-discount').value = '20';
        document.getElementById('coupon-description').value = '20% zniżki na wszystkie pizze';

        // Ustawienie dat
        const today = new Date();
        const startDate = new Date();
        startDate.setDate(today.getDate() - 30); // 30 dni temu

        const endDate = new Date();
        endDate.setDate(today.getDate() + 30); // 30 dni w przyszłość

        document.getElementById('coupon-start-date').value = startDate.toISOString().split('T')[0];
        document.getElementById('coupon-end-date').value = endDate.toISOString().split('T')[0];

        document.getElementById('coupon-terms').value = 'Kupon ważny tylko w lokalu. Nie łączy się z innymi promocjami.';
        document.getElementById('coupon-usage-limit').value = '100';

        // Wywołanie zdarzeń zmiany, aby zaktualizować podgląd
        const event = new Event('change');
        document.getElementById('coupon-company').dispatchEvent(event);
        document.getElementById('coupon-end-date').dispatchEvent(event);

        const inputEvent = new Event('input');
        document.getElementById('coupon-code').dispatchEvent(inputEvent);
        document.getElementById('coupon-discount').dispatchEvent(inputEvent);
        document.getElementById('coupon-description').dispatchEvent(inputEvent);
        document.getElementById('coupon-terms').dispatchEvent(inputEvent);
    } else {
        // Ustawienie domyślnych dat dla nowego kuponu
        const today = new Date();
        const startDate = today.toISOString().split('T')[0];

        const endDate = new Date();
        endDate.setDate(today.getDate() + 30); // 30 dni w przyszłość
        const endDateStr = endDate.toISOString().split('T')[0];

        document.getElementById('coupon-start-date').value = startDate;
        document.getElementById('coupon-end-date').value = endDateStr;

        // Wywołanie zdarzenia zmiany, aby zaktualizować podgląd
        const event = new Event('change');
        document.getElementById('coupon-end-date').dispatchEvent(event);
    }
}
