/**
 * Panel Administracyjny - Żyrardów Poleca
 * JavaScript dla zarządzania kuponami
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja funkcji zarządzania kuponami
    initCouponsManagement();
});

/**
 * Inicjalizacja funkcji zarządzania kuponami
 */
function initCouponsManagement() {
    // Obsługa wyszukiwania
    initSearch();
    
    // Obsługa filtrów
    initFilters();
    
    // Obsługa zaznaczania kuponów
    initSelections();
    
    // Obsługa akcji na kuponach
    initActions();
    
    // Obsługa paginacji
    initPagination();
    
    // Obsługa modalu podglądu kuponu
    initCouponPreviewModal();
}

/**
 * Inicjalizacja wyszukiwania
 */
function initSearch() {
    const searchInput = document.getElementById('coupon-search');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('#coupons-table-body tr');
            
            tableRows.forEach(row => {
                const couponCode = row.querySelector('.coupon-code').textContent.toLowerCase();
                const companyName = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                const couponDescription = row.querySelector('td:nth-child(5)').textContent.toLowerCase();
                
                if (couponCode.includes(searchTerm) || companyName.includes(searchTerm) || couponDescription.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
}

/**
 * Inicjalizacja filtrów
 */
function initFilters() {
    const companyFilter = document.getElementById('company-filter');
    const statusFilter = document.getElementById('status-filter');
    const discountFilter = document.getElementById('discount-filter');
    const resetFiltersBtn = document.getElementById('reset-filters');
    
    // Funkcja do filtrowania kuponów
    function filterCoupons() {
        const companyValue = companyFilter.value;
        const statusValue = statusFilter.value;
        const discountValue = discountFilter.value;
        const tableRows = document.querySelectorAll('#coupons-table-body tr');
        
        tableRows.forEach(row => {
            const company = row.querySelector('td:nth-child(3)').textContent;
            const statusElement = row.querySelector('.status-badge');
            const status = statusElement ? statusElement.classList.contains('active') ? 'active' : 
                           statusElement.classList.contains('expired') ? 'expired' : 'scheduled' : '';
            const discountText = row.querySelector('.discount').textContent;
            const discount = parseInt(discountText);
            
            let showRow = true;
            
            // Filtrowanie po firmie
            if (companyValue) {
                const companyMatch = company === document.querySelector(`#company-filter option[value="${companyValue}"]`).textContent;
                if (!companyMatch) {
                    showRow = false;
                }
            }
            
            // Filtrowanie po statusie
            if (statusValue && status !== statusValue) {
                showRow = false;
            }
            
            // Filtrowanie po rabacie
            if (discountValue) {
                const [min, max] = discountValue.split('-').map(Number);
                if (max) {
                    if (discount < min || discount > max) {
                        showRow = false;
                    }
                } else {
                    // Dla opcji "Powyżej 50%"
                    if (discount <= min) {
                        showRow = false;
                    }
                }
            }
            
            row.style.display = showRow ? '' : 'none';
        });
    }
    
    // Dodanie nasłuchiwania zdarzeń
    if (companyFilter) {
        companyFilter.addEventListener('change', filterCoupons);
    }
    
    if (statusFilter) {
        statusFilter.addEventListener('change', filterCoupons);
    }
    
    if (discountFilter) {
        discountFilter.addEventListener('change', filterCoupons);
    }
    
    // Resetowanie filtrów
    if (resetFiltersBtn) {
        resetFiltersBtn.addEventListener('click', function() {
            if (companyFilter) companyFilter.value = '';
            if (statusFilter) statusFilter.value = '';
            if (discountFilter) discountFilter.value = '';
            
            const tableRows = document.querySelectorAll('#coupons-table-body tr');
            tableRows.forEach(row => {
                row.style.display = '';
            });
        });
    }
}

/**
 * Inicjalizacja zaznaczania kuponów
 */
function initSelections() {
    const selectAll = document.getElementById('select-all');
    const couponCheckboxes = document.querySelectorAll('.coupon-select');
    
    if (selectAll) {
        selectAll.addEventListener('change', function() {
            couponCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }
    
    // Aktualizacja stanu "zaznacz wszystkie" gdy zmienia się stan pojedynczych checkboxów
    couponCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const allChecked = Array.from(couponCheckboxes).every(cb => cb.checked);
            const anyChecked = Array.from(couponCheckboxes).some(cb => cb.checked);
            
            if (selectAll) {
                selectAll.checked = allChecked;
                selectAll.indeterminate = anyChecked && !allChecked;
            }
        });
    });
}

/**
 * Inicjalizacja akcji na kuponach
 */
function initActions() {
    // Obsługa akcji edycji, podglądu i usuwania
    const actionButtons = document.querySelectorAll('.btn-icon');
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const row = this.closest('tr');
            const couponCode = row.querySelector('.coupon-code').textContent;
            
            if (this.classList.contains('delete')) {
                if (confirm(`Czy na pewno chcesz usunąć kupon "${couponCode}"?`)) {
                    // W rzeczywistej implementacji tutaj byłoby usuwanie kuponu
                    row.remove();
                    showNotification(`Kupon "${couponCode}" został usunięty.`, 'success');
                }
            } else if (this.title === 'Edytuj') {
                // W rzeczywistej implementacji tutaj byłoby przekierowanie do edycji kuponu
                window.location.href = `coupons-add.html?edit=${encodeURIComponent(couponCode)}`;
            } else if (this.title === 'Podgląd') {
                // Otwórz modal z podglądem kuponu
                showCouponPreview(row);
            }
        });
    });
    
    // Obsługa akcji zbiorczych
    const applyBulkAction = document.getElementById('apply-bulk-action');
    const bulkAction = document.getElementById('bulk-action');
    
    if (applyBulkAction && bulkAction) {
        applyBulkAction.addEventListener('click', function() {
            const selectedCheckboxes = document.querySelectorAll('.coupon-select:checked');
            const action = bulkAction.value;
            
            if (!action) {
                alert('Wybierz akcję do wykonania.');
                return;
            }
            
            if (selectedCheckboxes.length === 0) {
                alert('Zaznacz przynajmniej jeden kupon.');
                return;
            }
            
            const selectedRows = Array.from(selectedCheckboxes).map(checkbox => checkbox.closest('tr'));
            const couponCodes = selectedRows.map(row => row.querySelector('.coupon-code').textContent);
            
            switch (action) {
                case 'activate':
                    if (confirm(`Czy na pewno chcesz aktywować ${selectedRows.length} kuponów?`)) {
                        selectedRows.forEach(row => {
                            const statusBadge = row.querySelector('.status-badge');
                            statusBadge.className = 'status-badge active';
                            statusBadge.textContent = 'Aktywny';
                        });
                        showNotification(`Aktywowano ${selectedRows.length} kuponów.`, 'success');
                    }
                    break;
                case 'deactivate':
                    if (confirm(`Czy na pewno chcesz dezaktywować ${selectedRows.length} kuponów?`)) {
                        selectedRows.forEach(row => {
                            const statusBadge = row.querySelector('.status-badge');
                            statusBadge.className = 'status-badge expired';
                            statusBadge.textContent = 'Wygasły';
                        });
                        showNotification(`Dezaktywowano ${selectedRows.length} kuponów.`, 'success');
                    }
                    break;
                case 'delete':
                    if (confirm(`Czy na pewno chcesz usunąć ${selectedRows.length} kuponów?`)) {
                        selectedRows.forEach(row => row.remove());
                        showNotification(`Usunięto ${selectedRows.length} kuponów.`, 'success');
                    }
                    break;
            }
            
            // Resetowanie zaznaczenia
            const selectAll = document.getElementById('select-all');
            if (selectAll) {
                selectAll.checked = false;
                selectAll.indeterminate = false;
            }
            
            // Resetowanie akcji
            bulkAction.value = '';
        });
    }
}

/**
 * Inicjalizacja paginacji
 */
function initPagination() {
    const paginationButtons = document.querySelectorAll('.btn-page');
    
    paginationButtons.forEach(button => {
        if (!button.disabled) {
            button.addEventListener('click', function() {
                // Usunięcie aktywnej klasy z wszystkich przycisków
                paginationButtons.forEach(btn => btn.classList.remove('active'));
                
                // Dodanie aktywnej klasy do klikniętego przycisku
                this.classList.add('active');
                
                // W rzeczywistej implementacji tutaj byłoby ładowanie odpowiedniej strony
                console.log('Przejście do strony:', this.textContent);
            });
        }
    });
}

/**
 * Inicjalizacja modalu podglądu kuponu
 */
function initCouponPreviewModal() {
    const modal = document.getElementById('couponPreviewModal');
    const closeButton = modal.querySelector('.modal-close');
    
    // Zamykanie modalu po kliknięciu przycisku zamknięcia
    if (closeButton) {
        closeButton.addEventListener('click', function() {
            modal.classList.remove('show');
        });
    }
    
    // Zamykanie modalu po kliknięciu poza jego zawartością
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.classList.remove('show');
        }
    });
}

/**
 * Wyświetlanie podglądu kuponu
 * @param {HTMLElement} row - Wiersz tabeli z danymi kuponu
 */
function showCouponPreview(row) {
    const modal = document.getElementById('couponPreviewModal');
    
    if (modal) {
        const couponCode = row.querySelector('.coupon-code').textContent;
        const companyName = row.querySelector('td:nth-child(3)').textContent;
        const discount = row.querySelector('.discount').textContent;
        const description = row.querySelector('td:nth-child(5)').textContent;
        const validUntil = row.querySelector('td:nth-child(6)').textContent;
        
        // Aktualizacja danych w modalu
        modal.querySelector('.coupon-company-name').textContent = companyName;
        modal.querySelector('.coupon-discount').textContent = discount;
        modal.querySelector('.coupon-description').textContent = description;
        modal.querySelector('.coupon-code-value').textContent = couponCode;
        modal.querySelector('.coupon-validity-value').textContent = validUntil;
        
        // Wyświetlenie modalu
        modal.classList.add('show');
    }
}

/**
 * Funkcja do wyświetlania powiadomień
 * @param {string} message - Treść powiadomienia
 * @param {string} type - Typ powiadomienia (success, info, warning, error)
 */
function showNotification(message, type = 'info') {
    // Tworzenie elementu powiadomienia
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'warning' ? 'fa-exclamation-triangle' : type === 'error' ? 'fa-times-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;
    
    // Dodanie do dokumentu
    document.body.appendChild(notification);
    
    // Dodanie nasłuchiwania zdarzenia do przycisku zamykania
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.classList.add('hiding');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
    
    // Automatyczne usunięcie po 5 sekundach
    setTimeout(() => {
        notification.classList.add('hiding');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
    
    // Pokazanie powiadomienia z animacją
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
}
