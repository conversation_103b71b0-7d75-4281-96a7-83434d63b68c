/**
 * Panel Administracyjny - Żyrardów Poleca
 * JavaScript dla dashboardu
 */

document.addEventListener('DOMContentLoaded', function() {
    // Sprawdź autoryzację
    if (!isLoggedIn()) {
        window.location.href = 'index.html';
        return;
    }

    // Inicjalizacja statystyk i aktywności
    updateStats();
    updateRecentActivity();

    // Inicjalizacja wykresów (jeśli są)
    initCharts();
});



/**
 * Funkcja do generowania wykresów (przykład)
 */
function initCharts() {
    // W rzeczywistej implementacji tutaj byłoby generowanie wykresów
    // np. przy użyciu biblioteki Chart.js
    console.log('Inicjalizacja wykresów');
}

/**
 * Funkcja do pobierania danych statystycznych z localStorage
 */
function fetchStats() {
    console.log('Pobieranie rzeczywistych danych statystycznych z localStorage');

    // Pobierz dane z localStorage
    const companies = JSON.parse(localStorage.getItem('companies_data') || '[]');
    const offers = JSON.parse(localStorage.getItem('offers_data') || '[]');
    const coupons = JSON.parse(localStorage.getItem('coupons_data') || '[]');
    const users = JSON.parse(localStorage.getItem('users_data') || '[]');

    // Pobierz poprzednie statystyki dla obliczenia wzrostu
    const prevStats = JSON.parse(localStorage.getItem('prev_stats') || '{}');

    const currentStats = {
        companies: companies.length,
        offers: offers.length,
        coupons: coupons.length,
        users: users.length
    };

    // Oblicz wzrost w porównaniu do poprzednich statystyk
    const growth = {
        companiesGrowth: prevStats.companies ?
            Math.round(((currentStats.companies - prevStats.companies) / prevStats.companies) * 100) : 0,
        offersGrowth: prevStats.offers ?
            Math.round(((currentStats.offers - prevStats.offers) / prevStats.offers) * 100) : 0,
        couponsGrowth: prevStats.coupons ?
            Math.round(((currentStats.coupons - prevStats.coupons) / prevStats.coupons) * 100) : 0,
        usersGrowth: prevStats.users ?
            Math.round(((currentStats.users - prevStats.users) / prevStats.users) * 100) : 0
    };

    // Zapisz aktualne statystyki jako poprzednie dla następnego razu
    localStorage.setItem('prev_stats', JSON.stringify(currentStats));

    return {
        ...currentStats,
        ...growth
    };
}

/**
 * Funkcja do aktualizacji statystyk na dashboardzie z rzeczywistymi danymi
 */
function updateStats() {
    const stats = fetchStats();

    // Aktualizacja liczby firm
    const companiesCard = document.querySelector('.stats-cards .stat-card:nth-child(1)');
    if (companiesCard) {
        const valueElement = companiesCard.querySelector('.stat-value');
        const changeElement = companiesCard.querySelector('.stat-change');
        if (valueElement) valueElement.textContent = stats.companies;
        if (changeElement) {
            changeElement.textContent = `${stats.companiesGrowth >= 0 ? '+' : ''}${stats.companiesGrowth}%`;
            changeElement.className = `stat-change ${stats.companiesGrowth >= 0 ? 'positive' : 'negative'}`;
        }
    }

    // Aktualizacja liczby ofert
    const offersCard = document.querySelector('.stats-cards .stat-card:nth-child(2)');
    if (offersCard) {
        const valueElement = offersCard.querySelector('.stat-value');
        const changeElement = offersCard.querySelector('.stat-change');
        if (valueElement) valueElement.textContent = stats.offers;
        if (changeElement) {
            changeElement.textContent = `${stats.offersGrowth >= 0 ? '+' : ''}${stats.offersGrowth}%`;
            changeElement.className = `stat-change ${stats.offersGrowth >= 0 ? 'positive' : 'negative'}`;
        }
    }

    // Aktualizacja liczby kuponów
    const couponsCard = document.querySelector('.stats-cards .stat-card:nth-child(3)');
    if (couponsCard) {
        const valueElement = couponsCard.querySelector('.stat-value');
        const changeElement = couponsCard.querySelector('.stat-change');
        if (valueElement) valueElement.textContent = stats.coupons;
        if (changeElement) {
            changeElement.textContent = `${stats.couponsGrowth >= 0 ? '+' : ''}${stats.couponsGrowth}%`;
            changeElement.className = `stat-change ${stats.couponsGrowth >= 0 ? 'positive' : 'negative'}`;
        }
    }

    // Aktualizacja liczby użytkowników
    const usersCard = document.querySelector('.stats-cards .stat-card:nth-child(4)');
    if (usersCard) {
        const valueElement = usersCard.querySelector('.stat-value');
        const changeElement = usersCard.querySelector('.stat-change');
        if (valueElement) valueElement.textContent = stats.users;
        if (changeElement) {
            changeElement.textContent = `${stats.usersGrowth >= 0 ? '+' : ''}${stats.usersGrowth}%`;
            changeElement.className = `stat-change ${stats.usersGrowth >= 0 ? 'positive' : 'negative'}`;
        }
    }
}

/**
 * Funkcja do pobierania ostatnich aktywności z localStorage
 */
function fetchRecentActivity() {
    console.log('Pobieranie rzeczywistych aktywności z localStorage');

    // Pobierz aktywności z localStorage
    const activities = JSON.parse(localStorage.getItem('recent_activities') || '[]');

    // Jeśli nie ma aktywności, stwórz przykładowe na podstawie istniejących danych
    if (activities.length === 0) {
        const companies = JSON.parse(localStorage.getItem('companies_data') || '[]');
        const offers = JSON.parse(localStorage.getItem('offers_data') || '[]');
        const coupons = JSON.parse(localStorage.getItem('coupons_data') || '[]');

        const defaultActivities = [];

        // Dodaj ostatnie firmy
        companies.slice(-2).forEach((company, index) => {
            defaultActivities.push({
                type: 'company',
                action: 'add',
                name: company.name || `Firma ${company.id}`,
                time: `${index + 1} ${index === 0 ? 'godzinę' : 'godziny'} temu`,
                timestamp: Date.now() - (index + 1) * 3600000
            });
        });

        // Dodaj ostatnie oferty
        offers.slice(-2).forEach((offer, index) => {
            defaultActivities.push({
                type: 'offer',
                action: 'add',
                name: offer.title || `Oferta ${offer.id}`,
                time: `${index + 3} godzin temu`,
                timestamp: Date.now() - (index + 3) * 3600000
            });
        });

        // Dodaj ostatnie kupony
        coupons.slice(-2).forEach((coupon, index) => {
            defaultActivities.push({
                type: 'coupon',
                action: 'add',
                name: coupon.code || `Kupon ${coupon.id}`,
                time: `${index + 1} ${index === 0 ? 'dzień' : 'dni'} temu`,
                timestamp: Date.now() - (index + 1) * 86400000
            });
        });

        // Sortuj według czasu (najnowsze pierwsze)
        defaultActivities.sort((a, b) => b.timestamp - a.timestamp);

        return defaultActivities.slice(0, 5); // Pokaż tylko 5 ostatnich
    }

    // Sortuj istniejące aktywności według czasu
    return activities.sort((a, b) => b.timestamp - a.timestamp).slice(0, 5);
}

/**
 * Funkcja do aktualizacji listy ostatnich aktywności (przykład)
 */
function updateRecentActivity() {
    const activities = fetchRecentActivity();
    const activityList = document.querySelector('.activity-list');

    if (activityList) {
        activityList.innerHTML = '';

        activities.forEach(activity => {
            const li = document.createElement('li');
            li.className = 'activity-item';

            let iconClass = '';
            let iconColor = '';
            let bgColor = '';

            switch (activity.type) {
                case 'company':
                    iconClass = 'fas fa-building';
                    iconColor = 'var(--primary-color)';
                    bgColor = 'rgba(255, 102, 0, 0.1)';
                    break;
                case 'offer':
                    iconClass = 'fas fa-tag';
                    iconColor = 'var(--success)';
                    bgColor = 'rgba(40, 167, 69, 0.1)';
                    break;
                case 'coupon':
                    iconClass = 'fas fa-ticket-alt';
                    iconColor = 'var(--warning)';
                    bgColor = 'rgba(255, 193, 7, 0.1)';
                    break;
                default:
                    iconClass = 'fas fa-info-circle';
                    iconColor = 'var(--info)';
                    bgColor = 'rgba(23, 162, 184, 0.1)';
            }

            if (activity.action === 'delete') {
                iconClass = 'fas fa-trash-alt';
                iconColor = 'var(--danger)';
                bgColor = 'rgba(220, 53, 69, 0.1)';
            }

            li.innerHTML = `
                <div class="activity-icon" style="background-color: ${bgColor};">
                    <i class="${iconClass}" style="color: ${iconColor};"></i>
                </div>
                <div class="activity-content">
                    <p>${activity.action === 'add' ? 'Nowa' : 'Usunięta'} ${activity.type === 'company' ? 'firma' : activity.type === 'offer' ? 'oferta' : 'kupon'}: <strong>${activity.name}</strong></p>
                    <span class="activity-time">${activity.time}</span>
                </div>
            `;

            activityList.appendChild(li);
        });
    }
}

/**
 * Funkcja do logowania nowej aktywności
 */
function logActivity(type, action, name) {
    const activities = JSON.parse(localStorage.getItem('recent_activities') || '[]');

    const newActivity = {
        type: type,
        action: action,
        name: name,
        time: 'Teraz',
        timestamp: Date.now()
    };

    // Dodaj na początek listy
    activities.unshift(newActivity);

    // Zachowaj tylko 20 ostatnich aktywności
    const limitedActivities = activities.slice(0, 20);

    // Zapisz do localStorage
    localStorage.setItem('recent_activities', JSON.stringify(limitedActivities));

    // Odśwież wyświetlanie aktywności
    updateRecentActivity();
}

// Eksportuj funkcję dla innych modułów
window.dashboardLogger = {
    logActivity: logActivity
};
