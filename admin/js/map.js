/**
 * Map Management - Żyrardów Poleca
 * JavaScript dla zarządzania mapą TOP firm
 */

let map;
let markers = [];
let companies = [];

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, checking Leaflet...');

    // Sprawdź czy Leaflet jest załadowany
    if (typeof L === 'undefined') {
        console.error('Leaflet library not loaded! Waiting...');

        // Spróbuj ponownie po krótkim opóźnieniu
        setTimeout(() => {
            if (typeof L !== 'undefined') {
                console.log('Leaflet loaded after delay');
                initMapManager();
            } else {
                console.error('Leaflet still not loaded');
                showMapError();
            }
        }, 1000);
    } else {
        console.log('Leaflet is available');
        initMapManager();
    }
});

/**
 * Inicjalizacja managera mapy
 */
function initMapManager() {
    // Inicjalizuj mapę
    initMap();

    // Załaduj firmy
    loadCompanies();

    // Inicjalizuj obsługę zdarzeń
    initEventListeners();

    console.log('Map Manager initialized');
}

/**
 * Wyświetl błąd mapy
 * @param {string} message - Wiadomość błędu
 */
function showMapError(message = 'Wystąpił błąd podczas ładowania mapy') {
    const mapContainer = document.getElementById('map');
    if (mapContainer) {
        mapContainer.innerHTML = `
            <div class="map-error">
                <div class="map-error-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Błąd mapy</h3>
                    <p>${message}</p>
                    <button onclick="location.reload()" class="btn btn-primary">
                        <i class="fas fa-refresh"></i> Odśwież stronę
                    </button>
                </div>
            </div>
        `;

        // Dodaj style dla błędu mapy
        const style = document.createElement('style');
        style.textContent = `
            .map-error {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 400px;
                background: #f8f9fa;
                border: 2px dashed #dee2e6;
                border-radius: 8px;
            }
            .map-error-content {
                text-align: center;
                color: #6c757d;
            }
            .map-error-content i {
                font-size: 3rem;
                margin-bottom: 1rem;
                color: #ffc107;
            }
            .map-error-content h3 {
                margin-bottom: 0.5rem;
                color: #495057;
            }
            .map-error-content p {
                margin-bottom: 1rem;
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * Inicjalizacja mapy
 */
function initMap() {
    console.log('Initializing map...');

    // Współrzędne Żyrardowa
    const zyrardowCoords = [52.0484, 20.4464];

    // Sprawdź czy kontener mapy istnieje
    const mapContainer = document.getElementById('map');
    if (!mapContainer) {
        console.error('Map container not found');
        showMapError('Nie znaleziono kontenera mapy');
        return;
    }

    console.log('Map container found:', mapContainer);
    console.log('Container dimensions:', mapContainer.offsetWidth, 'x', mapContainer.offsetHeight);

    try {
        // Inicjalizuj mapę Leaflet
        map = L.map('map', {
            center: zyrardowCoords,
            zoom: 14,
            zoomControl: true,
            scrollWheelZoom: true
        });

        // Dodaj warstwę OpenStreetMap
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 19,
            tileSize: 256,
            zoomOffset: 0
        }).addTo(map);

        // Dodaj marker centrum Żyrardowa
        const centerIcon = L.divIcon({
            className: 'center-marker',
            html: '<div class="center-marker-content"><i class="fas fa-city"></i></div>',
            iconSize: [40, 40],
            iconAnchor: [20, 20],
            popupAnchor: [0, -20]
        });

        const centerMarker = L.marker(zyrardowCoords, {
            icon: centerIcon
        }).addTo(map);

        centerMarker.bindPopup(`
            <div class="popup-header">
                <h4 class="popup-title">Centrum Żyrardowa</h4>
            </div>
            <p>Główny punkt miasta</p>
        `);

        // Obsługa kliknięcia na mapę (dodawanie nowych lokalizacji)
        map.on('click', function(e) {
            if (window.addingLocation) {
                document.getElementById('locationLat').value = e.latlng.lat.toFixed(6);
                document.getElementById('locationLng').value = e.latlng.lng.toFixed(6);
                window.addingLocation = false;
                map.getContainer().style.cursor = '';

                // Dodaj tymczasowy marker
                if (window.tempMarker) {
                    map.removeLayer(window.tempMarker);
                }

                window.tempMarker = L.marker([e.latlng.lat, e.latlng.lng], {
                    icon: L.divIcon({
                        className: 'temp-marker',
                        html: '<div class="temp-marker-content"><i class="fas fa-map-pin"></i></div>',
                        iconSize: [30, 30],
                        iconAnchor: [15, 15]
                    })
                }).addTo(map);

                showNotification('Lokalizacja wybrana! Wypełnij formularz i zapisz.', 'success');
            }
        });

        // Wymuś odświeżenie mapy po załadowaniu
        setTimeout(() => {
            map.invalidateSize();
        }, 100);

        console.log('Map initialized successfully');

    } catch (error) {
        console.error('Error initializing map:', error);
        showMapError();
    }
}

/**
 * Pokaż błąd mapy
 */
function showMapError() {
    const mapContainer = document.getElementById('map');
    if (mapContainer) {
        mapContainer.innerHTML = `
            <div class="map-error">
                <div class="map-error-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Błąd ładowania mapy</h3>
                    <p>Nie udało się załadować mapy. Sprawdź połączenie internetowe i odśwież stronę.</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-refresh"></i> Odśwież stronę
                    </button>
                </div>
            </div>
        `;
    }
}

/**
 * Inicjalizacja nasłuchiwaczy zdarzeń
 */
function initEventListeners() {
    // Filtry
    const categoryFilter = document.getElementById('categoryFilter');
    const topFilter = document.getElementById('topFilter');

    if (categoryFilter) categoryFilter.addEventListener('change', filterCompanies);
    if (topFilter) topFilter.addEventListener('change', filterCompanies);

    // Przycisk dodawania lokalizacji
    const addLocationBtn = document.getElementById('addLocationBtn');
    if (addLocationBtn) {
        addLocationBtn.addEventListener('click', showAddLocationModal);
    }

    // Modal controls
    const locationModalClose = document.getElementById('locationModalClose');
    const locationCancelBtn = document.getElementById('locationCancelBtn');
    const locationSaveBtn = document.getElementById('locationSaveBtn');
    const geocodeBtn = document.getElementById('geocodeBtn');

    if (locationModalClose) locationModalClose.addEventListener('click', closeLocationModal);
    if (locationCancelBtn) locationCancelBtn.addEventListener('click', closeLocationModal);
    if (locationSaveBtn) locationSaveBtn.addEventListener('click', saveLocation);
    if (geocodeBtn) geocodeBtn.addEventListener('click', geocodeAddress);
}

/**
 * Załaduj firmy
 */
function loadCompanies() {
    companies = getCompaniesFromStorage();
    renderCompaniesOnMap();
    renderCompaniesList();
}

/**
 * Pobierz firmy z localStorage
 */
function getCompaniesFromStorage() {
    // Pobierz firmy z localStorage
    const companiesData = JSON.parse(localStorage.getItem('companies_top_data') || '{"companies":[]}');

    // Dodaj przykładowe lokalizacje jeśli nie ma danych
    if (companiesData.companies.length === 0) {
        return getDefaultCompaniesWithLocations();
    }

    return companiesData.companies.map(company => ({
        ...company,
        lat: company.lat || (52.0484 + (Math.random() - 0.5) * 0.02),
        lng: company.lng || (20.4464 + (Math.random() - 0.5) * 0.02),
        address: company.address || `ul. Przykładowa ${company.id}, Żyrardów`
    }));
}

/**
 * Domyślne firmy z lokalizacjami
 */
function getDefaultCompaniesWithLocations() {
    return [
        {
            id: 1,
            name: 'Restauracja Pod Akacjami',
            category: 'Jedzenie i Gastronomia',
            subcategory: 'Restauracje',
            topPosition: 1,
            status: 'active',
            lat: 52.0494,
            lng: 20.4474,
            address: 'ul. Piłsudskiego 15, Żyrardów'
        },
        {
            id: 2,
            name: 'Salon Fryzjerski Bella',
            category: 'Zdrowie i Uroda',
            subcategory: 'Salony fryzjerskie',
            topPosition: 2,
            status: 'active',
            lat: 52.0474,
            lng: 20.4454,
            address: 'ul. Mickiewicza 8, Żyrardów'
        },
        {
            id: 3,
            name: 'Sklep Sportowy Active',
            category: 'Zakupy i Handel',
            subcategory: 'Odzież i obuwie',
            topPosition: 3,
            status: 'active',
            lat: 52.0504,
            lng: 20.4444,
            address: 'ul. Słowackiego 22, Żyrardów'
        },
        {
            id: 4,
            name: 'Apteka Centralna',
            category: 'Zdrowie i Uroda',
            subcategory: 'Apteki',
            topPosition: null,
            status: 'active',
            lat: 52.0484,
            lng: 20.4484,
            address: 'ul. Rynek 5, Żyrardów'
        }
    ];
}

/**
 * Renderuj firmy na mapie
 */
function renderCompaniesOnMap() {
    // Usuń istniejące markery
    markers.forEach(marker => map.removeLayer(marker));
    markers = [];

    companies.forEach(company => {
        if (company.lat && company.lng && company.status === 'active') {
            const marker = createCompanyMarker(company);
            markers.push(marker);
            marker.addTo(map);
        }
    });

    // Aktualizuj licznik
    updateCompaniesCount();
}

/**
 * Utwórz marker firmy
 */
function createCompanyMarker(company) {
    const topClass = company.topPosition ? `top-${company.topPosition}` : 'regular';
    const topText = company.topPosition ? company.topPosition : '•';

    const marker = L.marker([company.lat, company.lng], {
        icon: L.divIcon({
            className: `custom-marker ${topClass}`,
            html: topText,
            iconSize: [30, 30],
            iconAnchor: [15, 15]
        })
    });

    // Popup z informacjami o firmie
    const popupContent = `
        <div class="popup-header">
            <h4 class="popup-title">${company.name}</h4>
            <p class="popup-category">${company.category}</p>
            ${company.topPosition ? `<span class="company-top-badge top-${company.topPosition}">TOP ${company.topPosition}</span>` : ''}
        </div>
        <div class="popup-address">
            <i class="fas fa-map-marker-alt"></i>
            ${company.address}
        </div>
        <div class="popup-actions">
            <button class="btn btn-primary btn-sm" onclick="editCompanyLocation(${company.id})">
                <i class="fas fa-edit"></i> Edytuj
            </button>
            <button class="btn btn-outline btn-sm" onclick="viewCompany(${company.id})">
                <i class="fas fa-eye"></i> Zobacz
            </button>
        </div>
    `;

    marker.bindPopup(popupContent);

    return marker;
}

/**
 * Renderuj listę firm
 */
function renderCompaniesList() {
    const companiesList = document.getElementById('companiesList');
    if (!companiesList) return;

    const activeCompanies = companies.filter(c => c.status === 'active');

    if (activeCompanies.length === 0) {
        companiesList.innerHTML = `
            <div class="empty-companies">
                <i class="fas fa-building"></i>
                <h4>Brak firm</h4>
                <p>Nie znaleziono aktywnych firm z lokalizacjami.</p>
            </div>
        `;
        return;
    }

    companiesList.innerHTML = '';

    activeCompanies.forEach(company => {
        const companyItem = createCompanyListItem(company);
        companiesList.appendChild(companyItem);
    });
}

/**
 * Utwórz element listy firmy
 */
function createCompanyListItem(company) {
    const div = document.createElement('div');
    div.className = `company-item ${company.topPosition ? `top-${company.topPosition}` : ''}`;
    div.dataset.companyId = company.id;

    div.innerHTML = `
        <div class="company-header">
            <h4 class="company-name">${company.name}</h4>
            ${company.topPosition ? `<span class="company-top-badge top-${company.topPosition}">TOP ${company.topPosition}</span>` : ''}
        </div>
        <div class="company-category">${company.category}</div>
        <div class="company-address">
            <i class="fas fa-map-marker-alt"></i>
            ${company.address}
        </div>
        <div class="company-actions">
            <button class="btn btn-primary btn-sm" onclick="focusOnCompany(${company.id})">
                <i class="fas fa-crosshairs"></i> Pokaż
            </button>
            <button class="btn btn-outline btn-sm" onclick="editCompanyLocation(${company.id})">
                <i class="fas fa-edit"></i> Edytuj
            </button>
        </div>
    `;

    // Obsługa kliknięcia
    div.addEventListener('click', function() {
        focusOnCompany(company.id);
    });

    return div;
}

/**
 * Skoncentruj mapę na firmie
 */
function focusOnCompany(companyId) {
    const company = companies.find(c => c.id == companyId);
    if (company && company.lat && company.lng) {
        map.setView([company.lat, company.lng], 16);

        // Znajdź i otwórz popup markera
        const marker = markers.find(m => {
            const markerLatLng = m.getLatLng();
            return Math.abs(markerLatLng.lat - company.lat) < 0.0001 &&
                   Math.abs(markerLatLng.lng - company.lng) < 0.0001;
        });

        if (marker) {
            marker.openPopup();
        }

        // Podświetl element na liście
        document.querySelectorAll('.company-item').forEach(item => {
            item.classList.remove('active');
        });

        const listItem = document.querySelector(`[data-company-id="${companyId}"]`);
        if (listItem) {
            listItem.classList.add('active');
        }
    }
}

/**
 * Edytuj lokalizację firmy
 */
function editCompanyLocation(companyId) {
    const company = companies.find(c => c.id == companyId);
    if (company) {
        // Wypełnij formularz
        document.getElementById('locationId').value = company.id;
        document.getElementById('companySelect').value = company.id;
        document.getElementById('locationAddress').value = company.address || '';
        document.getElementById('locationLat').value = company.lat || '';
        document.getElementById('locationLng').value = company.lng || '';
        document.getElementById('locationDescription').value = company.locationDescription || '';
        document.getElementById('locationVisible').checked = company.visible !== false;

        // Pokaż modal
        document.getElementById('locationModalTitle').textContent = `Edytuj lokalizację: ${company.name}`;
        document.getElementById('locationModal').classList.add('active');

        // Załaduj firmy do select
        loadCompaniesSelect();
    }
}

/**
 * Zobacz firmę
 */
function viewCompany(companyId) {
    // Przekieruj do strony firmy lub otwórz modal z detalami
    window.open(`../firma.html?id=${companyId}`, '_blank');
}

/**
 * Filtruj firmy
 */
function filterCompanies() {
    const categoryFilter = document.getElementById('categoryFilter').value;
    const topFilter = document.getElementById('topFilter').value;

    let filteredCompanies = companies.filter(company => {
        const matchesCategory = !categoryFilter || company.category.toLowerCase().includes(categoryFilter);
        const matchesTop = !topFilter || company.topPosition == topFilter;

        return matchesCategory && matchesTop && company.status === 'active';
    });

    // Aktualizuj mapę
    markers.forEach(marker => map.removeLayer(marker));
    markers = [];

    filteredCompanies.forEach(company => {
        if (company.lat && company.lng) {
            const marker = createCompanyMarker(company);
            markers.push(marker);
            marker.addTo(map);
        }
    });

    // Aktualizuj listę
    companies = filteredCompanies;
    renderCompaniesList();
    updateCompaniesCount();
}

/**
 * Aktualizuj licznik firm
 */
function updateCompaniesCount() {
    const count = companies.filter(c => c.status === 'active').length;
    const companiesCount = document.getElementById('companiesCount');
    if (companiesCount) {
        companiesCount.textContent = `${count} ${count === 1 ? 'firma' : count < 5 ? 'firmy' : 'firm'}`;
    }
}

/**
 * Pokaż modal dodawania lokalizacji
 */
function showAddLocationModal() {
    // Reset formularza
    document.getElementById('locationId').value = '';
    document.getElementById('companySelect').value = '';
    document.getElementById('locationAddress').value = '';
    document.getElementById('locationLat').value = '';
    document.getElementById('locationLng').value = '';
    document.getElementById('locationDescription').value = '';
    document.getElementById('locationVisible').checked = true;

    // Załaduj firmy do select
    loadCompaniesSelect();

    document.getElementById('locationModalTitle').textContent = 'Dodaj lokalizację firmy';
    document.getElementById('locationModal').classList.add('active');

    // Włącz tryb dodawania lokalizacji
    window.addingLocation = true;
    map.getContainer().style.cursor = 'crosshair';
}

/**
 * Załaduj firmy do select
 */
function loadCompaniesSelect() {
    const companySelect = document.getElementById('companySelect');
    if (!companySelect) return;

    // Wyczyść opcje
    companySelect.innerHTML = '<option value="">Wybierz firmę</option>';

    // Pobierz wszystkie firmy
    const allCompanies = JSON.parse(localStorage.getItem('companies_top_data') || '{"companies":[]}').companies;

    allCompanies.forEach(company => {
        const option = document.createElement('option');
        option.value = company.id;
        option.textContent = company.name;
        companySelect.appendChild(option);
    });
}

/**
 * Geokodowanie adresu
 */
function geocodeAddress() {
    const address = document.getElementById('locationAddress').value;
    if (!address) {
        showNotification('Wprowadź adres do wyszukania', 'warning');
        return;
    }

    // Użyj Nominatim API do geokodowania
    const query = encodeURIComponent(`${address}, Żyrardów, Poland`);
    const url = `https://nominatim.openstreetmap.org/search?format=json&q=${query}&limit=1`;

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data && data.length > 0) {
                const result = data[0];
                document.getElementById('locationLat').value = parseFloat(result.lat).toFixed(6);
                document.getElementById('locationLng').value = parseFloat(result.lon).toFixed(6);

                // Pokaż na mapie
                map.setView([result.lat, result.lon], 16);

                showNotification('Lokalizacja znaleziona', 'success');
            } else {
                showNotification('Nie znaleziono lokalizacji', 'error');
            }
        })
        .catch(error => {
            console.error('Geocoding error:', error);
            showNotification('Błąd wyszukiwania lokalizacji', 'error');
        });
}

/**
 * Zapisz lokalizację
 */
function saveLocation() {
    const locationId = document.getElementById('locationId').value;
    const companyId = document.getElementById('companySelect').value;
    const address = document.getElementById('locationAddress').value;
    const lat = parseFloat(document.getElementById('locationLat').value);
    const lng = parseFloat(document.getElementById('locationLng').value);
    const description = document.getElementById('locationDescription').value;
    const visible = document.getElementById('locationVisible').checked;

    if (!companyId || !address || !lat || !lng) {
        showNotification('Wypełnij wszystkie wymagane pola', 'warning');
        return;
    }

    // Aktualizuj dane firmy
    const companiesData = JSON.parse(localStorage.getItem('companies_top_data') || '{"companies":[]}');
    const companyIndex = companiesData.companies.findIndex(c => c.id == companyId);

    if (companyIndex !== -1) {
        companiesData.companies[companyIndex] = {
            ...companiesData.companies[companyIndex],
            address: address,
            lat: lat,
            lng: lng,
            locationDescription: description,
            visible: visible
        };

        localStorage.setItem('companies_top_data', JSON.stringify(companiesData));

        showNotification('Lokalizacja została zapisana', 'success');
        closeLocationModal();
        loadCompanies(); // Odśwież mapę
    } else {
        showNotification('Nie znaleziono firmy', 'error');
    }
}

/**
 * Zamknij modal lokalizacji
 */
function closeLocationModal() {
    document.getElementById('locationModal').classList.remove('active');
    window.addingLocation = false;
    map.getContainer().style.cursor = '';
}

/**
 * Pokaż powiadomienie
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    const container = document.getElementById('notifications');
    if (container) {
        container.appendChild(notification);

        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }
}
