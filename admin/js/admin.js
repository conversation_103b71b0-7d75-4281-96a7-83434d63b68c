/**
 * Panel Administracyjny - Żyrardów Poleca
 * Główny plik JavaScript z obsługą API
 */

// Konfiguracja API
const API_BASE_URL = 'admin/api';
const API_ENDPOINTS = {
    auth: {
        login: `${API_BASE_URL}/auth.php?path=login`,
        logout: `${API_BASE_URL}/auth.php?path=logout`,
        check: `${API_BASE_URL}/auth.php?path=check`,
        changePassword: `${API_BASE_URL}/auth.php?path=change-password`,
        profile: `${API_BASE_URL}/auth.php?path=profile`,
        requestPasswordReset: `${API_BASE_URL}/auth.php?path=request-password-reset`,
        resetPassword: `${API_BASE_URL}/auth.php?path=reset-password`
    },
    companies: {
        list: `${API_BASE_URL}/companies.php?path=all`,
        create: `${API_BASE_URL}/companies.php?path=create`,
        update: `${API_BASE_URL}/companies.php?path=update`,
        delete: `${API_BASE_URL}/companies.php?path=delete`,
        topPosition: `${API_BASE_URL}/companies.php?path=set-top-position`,
        top: `${API_BASE_URL}/companies.php?path=top`,
        categories: `${API_BASE_URL}/companies.php?path=categories`
    },
    offers: {
        list: `${API_BASE_URL}/offers.php`,
        active: `${API_BASE_URL}/offers.php?path=active`
    },
    coupons: {
        list: `${API_BASE_URL}/coupons.php`,
        active: `${API_BASE_URL}/coupons.php?path=active`
    },
    // Inne endpointy będą dodane w przyszłości
};

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja formularza logowania
    initLoginForm();

    // Inicjalizacja funkcji panelu administracyjnego
    initAdminPanel();

    // Inicjalizacja obsługi resetu hasła
    initPasswordReset();
});

/**
 * Inicjalizacja formularza logowania
 */
function initLoginForm() {
    const loginForm = document.getElementById('loginForm');

    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('remember').checked;

            try {
                showLoading(true);

                // Proste logowanie z danymi na stałe
                const validCredentials = [
                    { email: '<EMAIL>', password: 'admin123' },
                    { email: '<EMAIL>', password: 'admin123' },
                    { email: 'admin', password: 'admin123' }
                ];

                const isValid = validCredentials.some(cred =>
                    (cred.email === email || cred.email === email.toLowerCase()) &&
                    cred.password === password
                );

                if (isValid) {
                    // Zapisz dane sesji
                    const adminData = {
                        email: email,
                        name: 'Administrator',
                        role: 'admin',
                        loginTime: new Date().toISOString()
                    };

                    localStorage.setItem('adminToken', 'simple-admin-token-' + Date.now());
                    localStorage.setItem('adminData', JSON.stringify(adminData));

                    showNotification('Logowanie zakończone sukcesem!', 'success');

                    // Przekierowanie do dashboardu
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1000);
                } else {
                    showNotification('Nieprawidłowe dane logowania', 'error');
                }
            } catch (error) {
                console.error('Błąd logowania:', error);
                showNotification('Wystąpił błąd podczas logowania', 'error');
            } finally {
                showLoading(false);
            }
        });
    }
}

/**
 * Sprawdzenie czy użytkownik jest zalogowany
 * @returns {boolean} Czy użytkownik jest zalogowany
 */
function isLoggedIn() {
    const token = localStorage.getItem('adminToken');
    const adminData = localStorage.getItem('adminData');

    // Prosty system - sprawdź czy token i dane istnieją
    if (!token || !adminData) {
        return false;
    }

    try {
        const data = JSON.parse(adminData);
        // Token ważny przez 24 godziny
        const loginTime = new Date(data.loginTime);
        const now = new Date();
        const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

        return hoursDiff < 24;
    } catch (error) {
        return false;
    }
}

/**
 * Sprawdź czy token wygasł (dla kompatybilności)
 */
function isTokenExpired(token) {
    return !isLoggedIn();
}

/**
 * Wylogowanie użytkownika
 */
function logout() {
    // Usuń dane z localStorage
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminData');

    // Przekieruj do strony logowania
    window.location.href = 'index.html';
}

/**
 * Funkcja do wykonywania żądań API
 */
async function apiRequest(url, options = {}) {
    const token = localStorage.getItem('adminToken');

    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            ...(token && { 'Authorization': `Bearer ${token}` })
        }
    };

    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };

    try {
        const response = await fetch(url, finalOptions);

        // Sprawdź czy token został odświeżony
        const newToken = response.headers.get('X-New-Token');
        if (newToken) {
            localStorage.setItem('adminToken', newToken);
        }

        const data = await response.json();

        // Jeśli błąd autoryzacji, wyloguj użytkownika
        if (response.status === 401) {
            logout();
            throw new Error('Sesja wygasła. Zaloguj się ponownie.');
        }

        return data;
    } catch (error) {
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            throw new Error('Błąd połączenia z serwerem');
        }
        throw error;
    }
}

/**
 * Ochrona stron administracyjnych przed nieautoryzowanym dostępem
 */
function protectAdminPages() {
    // Sprawdzenie czy jesteśmy na stronie logowania
    const isLoginPage = window.location.pathname.endsWith('index.html') ||
                        window.location.pathname.endsWith('/admin/');

    // Jeśli nie jesteśmy na stronie logowania i użytkownik nie jest zalogowany,
    // przekieruj do strony logowania
    if (!isLoginPage && !isLoggedIn()) {
        window.location.href = 'index.html';
    }

    // Jeśli jesteśmy na stronie logowania i użytkownik jest zalogowany,
    // przekieruj do dashboardu
    if (isLoginPage && isLoggedIn()) {
        window.location.href = 'dashboard.html';
    }
}

// Usunięto podwójne sprawdzanie autoryzacji - używamy tylko checkAuthOnLoad

/**
 * Inicjalizacja funkcji panelu administracyjnego
 */
function initAdminPanel() {
    // Obsługa przycisku wylogowania
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function() {
            if (confirm('Czy na pewno chcesz się wylogować?')) {
                logout();
            }
        });
    }

    // Obsługa przełączania menu bocznego
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            document.querySelector('.admin-container').classList.toggle('sidebar-collapsed');
        });
    }

    // Obsługa powiadomień
    const notificationBtn = document.querySelector('.notification-btn');
    if (notificationBtn) {
        notificationBtn.addEventListener('click', function() {
            alert('Funkcja powiadomień będzie dostępna wkrótce!');
        });
    }

    // Inicjalizuj cache revalidation
    initCacheRevalidation();
}

/**
 * System cache revalidation - wymusza świeże ładowanie stron
 */
function initCacheRevalidation() {
    // Dodaj timestamp do wszystkich linków wewnętrznych
    addTimestampToLinks();

    // Wymuś przeładowanie przy nawigacji
    forcePageReload();

    // Wyczyść cache przy zmianie danych
    clearCacheOnDataChange();

    console.log('Cache revalidation system initialized');
}

/**
 * Dodaj timestamp do linków wewnętrznych
 */
function addTimestampToLinks() {
    const internalLinks = document.querySelectorAll('a[href*=".html"], a[href^="./"], a[href^="../"]');

    internalLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href && !href.includes('?t=')) {
                const separator = href.includes('?') ? '&' : '?';
                const timestamp = Date.now();
                this.href = href + separator + 't=' + timestamp;
            }
        });
    });
}

/**
 * Wymuś przeładowanie strony przy nawigacji
 */
function forcePageReload() {
    // Dodaj meta tag dla wymuszenia świeżego ładowania
    const metaCache = document.createElement('meta');
    metaCache.httpEquiv = 'Cache-Control';
    metaCache.content = 'no-cache, no-store, must-revalidate';
    document.head.appendChild(metaCache);

    const metaPragma = document.createElement('meta');
    metaPragma.httpEquiv = 'Pragma';
    metaPragma.content = 'no-cache';
    document.head.appendChild(metaPragma);

    const metaExpires = document.createElement('meta');
    metaExpires.httpEquiv = 'Expires';
    metaExpires.content = '0';
    document.head.appendChild(metaExpires);

    // Wymuś przeładowanie przy użyciu przycisku wstecz
    window.addEventListener('pageshow', function(event) {
        if (event.persisted) {
            window.location.reload();
        }
    });
}

/**
 * Wyczyść cache przy zmianie danych
 */
function clearCacheOnDataChange() {
    // Nasłuchuj zmian w localStorage
    window.addEventListener('storage', function(e) {
        if (e.key && (
            e.key.includes('settings') ||
            e.key.includes('companies') ||
            e.key.includes('offers') ||
            e.key.includes('coupons') ||
            e.key.includes('users')
        )) {
            console.log('Data changed, clearing cache:', e.key);
            clearBrowserCache();
        }
    });

    // Nasłuchuj custom events o zmianie danych
    window.addEventListener('dataUpdated', function(e) {
        console.log('Data updated event received:', e.detail);
        clearBrowserCache();
    });
}

/**
 * Wyczyść cache przeglądarki
 */
function clearBrowserCache() {
    // Dodaj timestamp do URL-a dla wymuszenia przeładowania
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set('cache_bust', Date.now());

    // Zaktualizuj historię bez przeładowania
    window.history.replaceState({}, '', currentUrl);

    // Wymuś przeładowanie zasobów CSS i JS
    reloadStylesheets();

    // Wyślij event o wyczyszczeniu cache
    window.dispatchEvent(new CustomEvent('cacheCleared', {
        detail: { timestamp: Date.now() }
    }));
}

/**
 * Przeładuj arkusze stylów
 */
function reloadStylesheets() {
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');

    stylesheets.forEach(stylesheet => {
        const href = stylesheet.href;
        const url = new URL(href);
        url.searchParams.set('v', Date.now());
        stylesheet.href = url.toString();
    });
}

/**
 * Funkcja do wymuszenia przeładowania strony z czyszczeniem cache
 */
function forceReloadWithCacheClear() {
    // Wyczyść localStorage cache
    const cacheKeys = Object.keys(localStorage).filter(key =>
        key.includes('cache') || key.includes('temp')
    );

    cacheKeys.forEach(key => {
        localStorage.removeItem(key);
    });

    // Wymuś przeładowanie z czyszczeniem cache
    window.location.reload(true);
}

/**
 * Dodaj przycisk do czyszczenia cache w dashboardzie
 */
function addCacheClearButton() {
    const adminHeader = document.querySelector('.admin-header');
    if (adminHeader) {
        const clearCacheBtn = document.createElement('button');
        clearCacheBtn.className = 'btn btn-outline btn-sm';
        clearCacheBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Wyczyść cache';
        clearCacheBtn.title = 'Wyczyść cache i przeładuj stronę';

        clearCacheBtn.addEventListener('click', function() {
            if (confirm('Czy chcesz wyczyścić cache i przeładować stronę?')) {
                forceReloadWithCacheClear();
            }
        });

        // Dodaj przycisk do nagłówka
        const actionsDiv = adminHeader.querySelector('.admin-actions') || adminHeader;
        actionsDiv.appendChild(clearCacheBtn);
    }
}

// Dodaj przycisk czyszczenia cache po załadowaniu strony
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(addCacheClearButton, 100);
});

/**
 * Wyświetl powiadomienie
 */
function showNotification(message, type = 'info', duration = 5000) {
    // Usuń istniejące powiadomienia
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(n => n.remove());

    // Utwórz nowe powiadomienie
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">&times;</button>
    `;

    // Dodaj style
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
        color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
        border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
        border-radius: 8px;
        padding: 15px;
        z-index: 10000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Obsługa zamykania
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => notification.remove());

    // Auto-usuwanie
    if (duration > 0) {
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    }
}

/**
 * Pokaż/ukryj loading
 */
function showLoading(show = true) {
    let loader = document.getElementById('global-loader');

    if (show && !loader) {
        loader = document.createElement('div');
        loader.id = 'global-loader';
        loader.innerHTML = `
            <div class="loader-backdrop">
                <div class="loader-content">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Ładowanie...</span>
                    </div>
                    <div class="mt-2">Ładowanie...</div>
                </div>
            </div>
        `;

        loader.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        const backdrop = loader.querySelector('.loader-backdrop');
        backdrop.style.cssText = `
            background: rgba(255, 255, 255, 0.9);
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        const content = loader.querySelector('.loader-content');
        content.style.cssText = `
            text-align: center;
            color: #333;
        `;

        document.body.appendChild(loader);
    } else if (!show && loader) {
        loader.remove();
    }
}

/**
 * Sprawdź autoryzację przy ładowaniu strony
 */
async function checkAuthOnLoad() {
    const isLoginPage = window.location.pathname.endsWith('index.html') ||
                       window.location.pathname.endsWith('/admin/') ||
                       window.location.pathname.endsWith('/admin');

    if (isLoginPage) {
        // Jeśli jesteśmy na stronie logowania i użytkownik jest zalogowany, przekieruj do dashboard
        if (isLoggedIn()) {
            window.location.href = 'dashboard.html';
        }
        return;
    }

    // Dla innych stron sprawdź autoryzację
    if (!isLoggedIn()) {
        window.location.href = 'index.html';
        return;
    }

    // Prosty system JSON - nie sprawdzamy API, tylko localStorage
    // Token jest ważny jeśli isLoggedIn() zwraca true
}

// Eksportuj funkcje dla innych modułów
window.adminAPI = {
    apiRequest,
    showNotification,
    showLoading,
    logout,
    isLoggedIn,
    API_ENDPOINTS
};

window.cacheManager = {
    clearBrowserCache,
    forceReloadWithCacheClear,
    reloadStylesheets
};

/**
 * Inicjalizacja obsługi resetu hasła
 */
function initPasswordReset() {
    const forgotPasswordLink = document.querySelector('.forgot-password');

    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', function(e) {
            e.preventDefault();
            showPasswordResetModal();
        });
    }

    // Sprawdź czy jesteśmy na stronie resetu hasła (z tokenem w URL)
    const urlParams = new URLSearchParams(window.location.search);
    const resetToken = urlParams.get('token');

    if (resetToken) {
        showResetPasswordForm(resetToken);
    }
}

/**
 * Pokaż modal żądania resetu hasła
 */
function showPasswordResetModal() {
    // Usuń istniejący modal jeśli istnieje
    const existingModal = document.getElementById('passwordResetModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Utwórz modal
    const modal = document.createElement('div');
    modal.id = 'passwordResetModal';
    modal.className = 'password-reset-modal';
    modal.innerHTML = `
        <div class="modal-backdrop">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Reset hasła</h3>
                    <button class="modal-close" type="button">&times;</button>
                </div>
                <div class="modal-body">
                    <p>Wprowadź adres email powiązany z Twoim kontem administratora. Wyślemy Ci instrukcje resetu hasła.</p>
                    <form id="passwordResetForm">
                        <div class="form-group">
                            <label for="resetEmail">Adres email</label>
                            <div class="input-with-icon">
                                <i class="fas fa-envelope"></i>
                                <input type="email" id="resetEmail" name="email" placeholder="Wprowadź adres email" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn-primary">Wyślij instrukcje resetu</button>
                            <button type="button" class="btn-secondary modal-cancel">Anuluj</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    // Dodaj style
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    const backdrop = modal.querySelector('.modal-backdrop');
    backdrop.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    const content = modal.querySelector('.modal-content');
    content.style.cssText = `
        background: white;
        border-radius: 8px;
        padding: 0;
        max-width: 500px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        position: relative;
    `;

    const header = modal.querySelector('.modal-header');
    header.style.cssText = `
        padding: 20px 25px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    `;

    const body = modal.querySelector('.modal-body');
    body.style.cssText = `
        padding: 25px;
    `;

    document.body.appendChild(modal);

    // Obsługa zamykania modala
    const closeBtn = modal.querySelector('.modal-close');
    const cancelBtn = modal.querySelector('.modal-cancel');

    function closeModal() {
        modal.remove();
    }

    closeBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);
    backdrop.addEventListener('click', function(e) {
        if (e.target === backdrop) {
            closeModal();
        }
    });

    // Obsługa formularza
    const form = modal.querySelector('#passwordResetForm');
    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        const email = document.getElementById('resetEmail').value;

        try {
            showLoading(true);

            const response = await apiRequest(API_ENDPOINTS.auth.requestPasswordReset, {
                method: 'POST',
                body: JSON.stringify({ email })
            });

            if (response.success) {
                showNotification(response.message, 'success');
                closeModal();

                // W trybie development pokaż token
                if (response.resetToken) {
                    setTimeout(() => {
                        showNotification(`Token resetu (tylko w trybie dev): ${response.resetToken}`, 'info', 10000);
                    }, 1000);
                }
            } else {
                showNotification(response.message || 'Błąd podczas żądania resetu hasła', 'error');
            }
        } catch (error) {
            console.error('Błąd żądania resetu hasła:', error);
            showNotification('Wystąpił błąd podczas żądania resetu hasła', 'error');
        } finally {
            showLoading(false);
        }
    });

    // Focus na pole email
    setTimeout(() => {
        document.getElementById('resetEmail').focus();
    }, 100);
}

/**
 * Pokaż formularz resetu hasła z tokenem
 */
function showResetPasswordForm(token) {
    // Ukryj formularz logowania
    const loginContainer = document.querySelector('.login-container');
    if (loginContainer) {
        loginContainer.style.display = 'none';
    }

    // Utwórz formularz resetu hasła
    const resetContainer = document.createElement('div');
    resetContainer.className = 'login-container';
    resetContainer.innerHTML = `
        <div class="login-logo">
            <img src="../images/logo.png" alt="Żyrardów Poleca Logo">
        </div>
        <div class="login-form-container">
            <h1>Ustaw nowe hasło</h1>
            <p>Wprowadź nowe hasło dla Twojego konta administratora.</p>
            <form id="resetPasswordForm" class="login-form">
                <input type="hidden" id="resetToken" value="${token}">
                <div class="form-group">
                    <label for="newPassword">Nowe hasło</label>
                    <div class="input-with-icon">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="newPassword" name="newPassword" placeholder="Wprowadź nowe hasło" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">Potwierdź hasło</label>
                    <div class="input-with-icon">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Potwierdź nowe hasło" required>
                    </div>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn-login">Ustaw nowe hasło</button>
                </div>
                <div class="login-footer">
                    <a href="index.html" class="back-to-login">Powrót do logowania</a>
                </div>
            </form>
        </div>
    `;

    document.body.appendChild(resetContainer);

    // Obsługa formularza resetu hasła
    const form = document.getElementById('resetPasswordForm');
    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        const token = document.getElementById('resetToken').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (newPassword !== confirmPassword) {
            showNotification('Hasła muszą być identyczne', 'error');
            return;
        }

        if (newPassword.length < 8) {
            showNotification('Hasło musi mieć co najmniej 8 znaków', 'error');
            return;
        }

        try {
            showLoading(true);

            const response = await apiRequest(API_ENDPOINTS.auth.resetPassword, {
                method: 'POST',
                body: JSON.stringify({ token, newPassword, confirmPassword })
            });

            if (response.success) {
                showNotification(response.message, 'success');

                // Przekieruj do strony logowania po 2 sekundach
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 2000);
            } else {
                showNotification(response.message || 'Błąd podczas resetu hasła', 'error');
            }
        } catch (error) {
            console.error('Błąd resetu hasła:', error);
            showNotification('Wystąpił błąd podczas resetu hasła', 'error');
        } finally {
            showLoading(false);
        }
    });
}

// Sprawdź autoryzację przy ładowaniu strony
document.addEventListener('DOMContentLoaded', checkAuthOnLoad);
