document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked button and corresponding content
            button.classList.add('active');
            const tabId = button.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });

    // Homepage section visibility toggles
    const homepageToggles = {
        companies: document.getElementById('toggle-companies'),
        coupons: document.getElementById('toggle-coupons'),
        about: document.getElementById('toggle-about'),
        discover: document.getElementById('toggle-discover'),
        offers: document.getElementById('toggle-offers'),
        places: document.getElementById('toggle-places'),
        stats: document.getElementById('toggle-stats'),
        news: document.getElementById('newsToggle')
    };

    // Obsługa przełącznika wiadomości
    if (homepageToggles.news) {
        homepageToggles.news.addEventListener('change', function() {
            toggleNewsSection(this.checked);
        });
    }

    // Advanced settings toggles
    const advancedToggles = {
        minify: document.getElementById('toggle-minify'),
        browserCache: document.getElementById('toggle-browser-cache'),
        lazyLoading: document.getElementById('toggle-lazy-loading'),
        spamProtection: document.getElementById('toggle-spam-protection'),
        autoBackup: document.getElementById('toggle-auto-backup')
    };

    // SEO settings toggles
    const seoToggles = {
        sitemap: document.getElementById('toggle-sitemap')
    };

    // Social media settings toggles
    const socialToggles = {
        facebookLike: document.getElementById('toggle-facebook-like'),
        facebookComments: document.getElementById('toggle-facebook-comments'),
        shareButtons: document.getElementById('toggle-share-buttons')
    };

    // Social media share checkboxes
    const shareCheckboxes = {
        facebook: document.getElementById('share-facebook'),
        twitter: document.getElementById('share-twitter'),
        linkedin: document.getElementById('share-linkedin'),
        pinterest: document.getElementById('share-pinterest'),
        email: document.getElementById('share-email')
    };

    // Color pickers
    const colorPickers = {
        primary: document.getElementById('primary-color'),
        secondary: document.getElementById('secondary-color')
    };

    // Initialize color pickers
    initColorPickers();

    // Load saved settings from localStorage
    loadSavedSettings();

    // Initialize auto-save functionality
    initAutoSave();

    // Save settings buttons
    const saveButtons = {
        homepage: document.getElementById('saveHomepageSettings'),
        general: document.getElementById('saveGeneralSettings'),
        appearance: document.getElementById('saveAppearanceSettings'),
        seo: document.getElementById('saveSeoSettings'),
        social: document.getElementById('saveSocialSettings'),
        advanced: document.getElementById('saveAdvancedSettings')
    };

    // Add event listeners to save buttons
    if (saveButtons.homepage) {
        saveButtons.homepage.addEventListener('click', () => saveHomepageSettings());
    }
    if (saveButtons.general) {
        saveButtons.general.addEventListener('click', () => saveGeneralSettings());
    }
    if (saveButtons.appearance) {
        saveButtons.appearance.addEventListener('click', () => saveAppearanceSettings());
    }
    if (saveButtons.seo) {
        saveButtons.seo.addEventListener('click', () => saveSeoSettings());
    }
    if (saveButtons.social) {
        saveButtons.social.addEventListener('click', () => saveSocialSettings());
    }
    if (saveButtons.advanced) {
        saveButtons.advanced.addEventListener('click', () => saveAdvancedSettings());
    }

    // Initialize color pickers
    function initColorPickers() {
        if (colorPickers.primary) {
            const primaryColorText = colorPickers.primary.nextElementSibling.nextElementSibling;
            const primaryColorPreview = colorPickers.primary.nextElementSibling;

            colorPickers.primary.addEventListener('input', function() {
                primaryColorText.value = this.value;
                primaryColorPreview.style.backgroundColor = this.value;
            });

            primaryColorText.addEventListener('input', function() {
                colorPickers.primary.value = this.value;
                primaryColorPreview.style.backgroundColor = this.value;
            });
        }

        if (colorPickers.secondary) {
            const secondaryColorText = colorPickers.secondary.nextElementSibling.nextElementSibling;
            const secondaryColorPreview = colorPickers.secondary.nextElementSibling;

            colorPickers.secondary.addEventListener('input', function() {
                secondaryColorText.value = this.value;
                secondaryColorPreview.style.backgroundColor = this.value;
            });

            secondaryColorText.addEventListener('input', function() {
                colorPickers.secondary.value = this.value;
                secondaryColorPreview.style.backgroundColor = this.value;
            });
        }
    }

    // Function to save homepage settings
    function saveHomepageSettings() {
        // Pobierz aktualną kolejność sekcji jeśli istnieje
        let sectionOrder = null;
        try {
            const savedOrder = localStorage.getItem('section_order');
            if (savedOrder) {
                sectionOrder = JSON.parse(savedOrder);
            }
        } catch (error) {
            console.error('Błąd odczytu kolejności sekcji:', error);
        }

        const settings = {
            homepage: {
                sections: {
                    companies: homepageToggles.companies ? homepageToggles.companies.checked : true,
                    coupons: homepageToggles.coupons ? homepageToggles.coupons.checked : true,
                    about: homepageToggles.about ? homepageToggles.about.checked : true,
                    discover: homepageToggles.discover ? homepageToggles.discover.checked : true,
                    offers: homepageToggles.offers ? homepageToggles.offers.checked : true,
                    places: homepageToggles.places ? homepageToggles.places.checked : true,
                    stats: homepageToggles.stats ? homepageToggles.stats.checked : true,
                    news: homepageToggles.news ? homepageToggles.news.checked : false
                },
                sectionOrder: sectionOrder
            }
        };

        // Save to localStorage
        saveSettingsToStorage('homepage', settings.homepage);

        // Zapisz widoczność sekcji w osobnym kluczu dla section-visibility.js
        const sectionVisibility = {
            'hero': true,
            'city-info': true,
            'categories': settings.homepage.sections.companies,
            'coupons': settings.homepage.sections.coupons,
            'girard': true,
            'heritage': true,
            'about': settings.homepage.sections.about,
            'seo': settings.homepage.sections.discover,
            'offers': settings.homepage.sections.offers,
            'news': settings.homepage.sections.news,
            'places': settings.homepage.sections.places,
            'stats': settings.homepage.sections.stats,
            'weather': true
        };

        localStorage.setItem('section_visibility', JSON.stringify(sectionVisibility));

        // Aktualizuj statusy w dashboardzie jeśli istnieje manager
        if (window.sectionOrderManager) {
            window.sectionOrderManager.updateStatusBadges();
        }

        // Show success message
        showNotification('Ustawienia strony głównej zostały zapisane pomyślnie!', 'success');
    }

    // Function to save general settings
    function saveGeneralSettings() {
        const settings = {
            general: {
                siteName: document.getElementById('site-name').value,
                siteDescription: document.getElementById('site-description').value,
                contact: {
                    email: document.getElementById('contact-email').value,
                    phone: document.getElementById('contact-phone').value,
                    address: document.getElementById('contact-address').value
                },
                regional: {
                    language: document.getElementById('site-language').value,
                    timezone: document.getElementById('site-timezone').value
                }
            }
        };

        // Save to localStorage
        saveSettingsToStorage('general', settings.general);

        // Show success message
        showNotification('Ustawienia ogólne zostały zapisane pomyślnie!', 'success');
    }

    // Function to save appearance settings
    function saveAppearanceSettings() {
        const settings = {
            appearance: {
                colors: {
                    primary: colorPickers.primary.value,
                    secondary: colorPickers.secondary.value
                },
                fonts: {
                    family: document.getElementById('font-family').value,
                    size: document.getElementById('font-size').value
                },
                layout: {
                    containerWidth: document.querySelector('input[name="container-width"]:checked').value,
                    menuStyle: document.querySelector('input[name="menu-style"]:checked').value
                }
            }
        };

        // Save to localStorage
        saveSettingsToStorage('appearance', settings.appearance);

        // Show success message
        showNotification('Ustawienia wyglądu zostały zapisane pomyślnie!', 'success');
    }

    // Function to save SEO settings
    function saveSeoSettings() {
        const settings = {
            seo: {
                meta: {
                    title: document.getElementById('meta-title').value,
                    description: document.getElementById('meta-description').value,
                    keywords: document.getElementById('meta-keywords').value
                },
                analytics: {
                    googleAnalytics: document.getElementById('google-analytics').value,
                    googleTagManager: document.getElementById('google-tag-manager').value
                },
                sitemap: {
                    autoGenerate: seoToggles.sitemap ? seoToggles.sitemap.checked : true
                }
            }
        };

        // Save to localStorage
        saveSettingsToStorage('seo', settings.seo);

        // Show success message
        showNotification('Ustawienia SEO zostały zapisane pomyślnie!', 'success');
    }

    // Function to save social media settings
    function saveSocialSettings() {
        const settings = {
            social: {
                profiles: {
                    facebook: document.getElementById('facebook-url').value,
                    instagram: document.getElementById('instagram-url').value,
                    twitter: document.getElementById('twitter-url').value,
                    youtube: document.getElementById('youtube-url').value
                },
                facebook: {
                    appId: document.getElementById('facebook-app-id').value,
                    likeButton: socialToggles.facebookLike ? socialToggles.facebookLike.checked : true,
                    comments: socialToggles.facebookComments ? socialToggles.facebookComments.checked : true
                },
                sharing: {
                    enabled: socialToggles.shareButtons ? socialToggles.shareButtons.checked : true,
                    platforms: {
                        facebook: shareCheckboxes.facebook ? shareCheckboxes.facebook.checked : true,
                        twitter: shareCheckboxes.twitter ? shareCheckboxes.twitter.checked : true,
                        linkedin: shareCheckboxes.linkedin ? shareCheckboxes.linkedin.checked : false,
                        pinterest: shareCheckboxes.pinterest ? shareCheckboxes.pinterest.checked : false,
                        email: shareCheckboxes.email ? shareCheckboxes.email.checked : true
                    }
                }
            }
        };

        // Save to localStorage
        saveSettingsToStorage('social', settings.social);

        // Show success message
        showNotification('Ustawienia mediów społecznościowych zostały zapisane pomyślnie!', 'success');
    }

    // Function to save advanced settings
    function saveAdvancedSettings() {
        const settings = {
            advanced: {
                performance: {
                    minify: advancedToggles.minify ? advancedToggles.minify.checked : true,
                    browserCache: advancedToggles.browserCache ? advancedToggles.browserCache.checked : true,
                    lazyLoading: advancedToggles.lazyLoading ? advancedToggles.lazyLoading.checked : true
                },
                security: {
                    spamProtection: advancedToggles.spamProtection ? advancedToggles.spamProtection.checked : true,
                    recaptchaSiteKey: document.getElementById('recaptcha-site-key').value,
                    recaptchaSecretKey: document.getElementById('recaptcha-secret-key').value
                },
                backup: {
                    autoBackup: advancedToggles.autoBackup ? advancedToggles.autoBackup.checked : true,
                    frequency: document.getElementById('backup-frequency').value
                }
            }
        };

        // Save to localStorage
        saveSettingsToStorage('advanced', settings.advanced);

        // Show success message
        showNotification('Ustawienia zaawansowane zostały zapisane pomyślnie!', 'success');
    }

    // Function to save settings to localStorage
    function saveSettingsToStorage(key, value) {
        let settings = {};
        const savedSettings = localStorage.getItem('siteSettings');

        if (savedSettings) {
            settings = JSON.parse(savedSettings);
        }

        settings[key] = value;
        localStorage.setItem('siteSettings', JSON.stringify(settings));

        // In a real application, this would send the settings to the server
        console.log(`${key} settings saved:`, value);
    }

    // Function to load saved settings
    function loadSavedSettings() {
        const savedSettings = localStorage.getItem('siteSettings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);

            // Apply homepage section visibility settings
            if (settings.homepage && settings.homepage.sections) {
                const sections = settings.homepage.sections;

                if (homepageToggles.companies) homepageToggles.companies.checked = sections.companies !== false;
                if (homepageToggles.coupons) homepageToggles.coupons.checked = sections.coupons !== false;
                if (homepageToggles.about) homepageToggles.about.checked = sections.about !== false;
                if (homepageToggles.discover) homepageToggles.discover.checked = sections.discover !== false;
                if (homepageToggles.offers) homepageToggles.offers.checked = sections.offers !== false;
                if (homepageToggles.places) homepageToggles.places.checked = sections.places !== false;
                if (homepageToggles.stats) homepageToggles.stats.checked = sections.stats !== false;
                if (homepageToggles.news) homepageToggles.news.checked = sections.news || false;
            }

            // Apply general settings
            if (settings.general) {
                const general = settings.general;

                const siteNameField = document.getElementById('site-name');
                if (siteNameField && general.siteName) siteNameField.value = general.siteName;

                const siteDescField = document.getElementById('site-description');
                if (siteDescField && general.siteDescription) siteDescField.value = general.siteDescription;

                if (general.contact) {
                    const emailField = document.getElementById('contact-email');
                    if (emailField && general.contact.email) emailField.value = general.contact.email;

                    const phoneField = document.getElementById('contact-phone');
                    if (phoneField && general.contact.phone) phoneField.value = general.contact.phone;

                    const addressField = document.getElementById('contact-address');
                    if (addressField && general.contact.address) addressField.value = general.contact.address;
                }

                if (general.regional) {
                    const langField = document.getElementById('site-language');
                    if (langField && general.regional.language) langField.value = general.regional.language;

                    const timezoneField = document.getElementById('site-timezone');
                    if (timezoneField && general.regional.timezone) timezoneField.value = general.regional.timezone;
                }
            }

            // Apply appearance settings
            if (settings.appearance) {
                const appearance = settings.appearance;

                if (appearance.colors) {
                    if (colorPickers.primary && appearance.colors.primary) {
                        colorPickers.primary.value = appearance.colors.primary;
                        const primaryText = colorPickers.primary.nextElementSibling.nextElementSibling;
                        if (primaryText) primaryText.value = appearance.colors.primary;
                        const primaryPreview = colorPickers.primary.nextElementSibling;
                        if (primaryPreview) primaryPreview.style.backgroundColor = appearance.colors.primary;
                    }

                    if (colorPickers.secondary && appearance.colors.secondary) {
                        colorPickers.secondary.value = appearance.colors.secondary;
                        const secondaryText = colorPickers.secondary.nextElementSibling.nextElementSibling;
                        if (secondaryText) secondaryText.value = appearance.colors.secondary;
                        const secondaryPreview = colorPickers.secondary.nextElementSibling;
                        if (secondaryPreview) secondaryPreview.style.backgroundColor = appearance.colors.secondary;
                    }
                }

                if (appearance.fonts) {
                    const fontFamilyField = document.getElementById('font-family');
                    if (fontFamilyField && appearance.fonts.family) fontFamilyField.value = appearance.fonts.family;

                    const fontSizeField = document.getElementById('font-size');
                    if (fontSizeField && appearance.fonts.size) fontSizeField.value = appearance.fonts.size;
                }

                if (appearance.layout) {
                    if (appearance.layout.containerWidth) {
                        const containerRadio = document.querySelector(`input[name="container-width"][value="${appearance.layout.containerWidth}"]`);
                        if (containerRadio) containerRadio.checked = true;
                    }

                    if (appearance.layout.menuStyle) {
                        const menuRadio = document.querySelector(`input[name="menu-style"][value="${appearance.layout.menuStyle}"]`);
                        if (menuRadio) menuRadio.checked = true;
                    }
                }
            }

            // Apply SEO settings
            if (settings.seo) {
                const seo = settings.seo;

                if (seo.meta) {
                    const metaTitleField = document.getElementById('meta-title');
                    if (metaTitleField && seo.meta.title) metaTitleField.value = seo.meta.title;

                    const metaDescField = document.getElementById('meta-description');
                    if (metaDescField && seo.meta.description) metaDescField.value = seo.meta.description;

                    const metaKeywordsField = document.getElementById('meta-keywords');
                    if (metaKeywordsField && seo.meta.keywords) metaKeywordsField.value = seo.meta.keywords;
                }

                if (seo.analytics) {
                    const gaField = document.getElementById('google-analytics');
                    if (gaField && seo.analytics.googleAnalytics) gaField.value = seo.analytics.googleAnalytics;

                    const gtmField = document.getElementById('google-tag-manager');
                    if (gtmField && seo.analytics.googleTagManager) gtmField.value = seo.analytics.googleTagManager;
                }

                if (seo.sitemap && seoToggles.sitemap) {
                    seoToggles.sitemap.checked = seo.sitemap.autoGenerate !== false;
                }
            }

            // Apply social media settings
            if (settings.social) {
                const social = settings.social;

                if (social.profiles) {
                    const fbField = document.getElementById('facebook-url');
                    if (fbField && social.profiles.facebook) fbField.value = social.profiles.facebook;

                    const igField = document.getElementById('instagram-url');
                    if (igField && social.profiles.instagram) igField.value = social.profiles.instagram;

                    const twitterField = document.getElementById('twitter-url');
                    if (twitterField && social.profiles.twitter) twitterField.value = social.profiles.twitter;

                    const youtubeField = document.getElementById('youtube-url');
                    if (youtubeField && social.profiles.youtube) youtubeField.value = social.profiles.youtube;
                }

                if (social.facebook) {
                    const fbAppIdField = document.getElementById('facebook-app-id');
                    if (fbAppIdField && social.facebook.appId) fbAppIdField.value = social.facebook.appId;

                    if (socialToggles.facebookLike) socialToggles.facebookLike.checked = social.facebook.likeButton !== false;
                    if (socialToggles.facebookComments) socialToggles.facebookComments.checked = social.facebook.comments !== false;
                }

                if (social.sharing) {
                    if (socialToggles.shareButtons) socialToggles.shareButtons.checked = social.sharing.enabled !== false;

                    if (social.sharing.platforms) {
                        if (shareCheckboxes.facebook) shareCheckboxes.facebook.checked = social.sharing.platforms.facebook !== false;
                        if (shareCheckboxes.twitter) shareCheckboxes.twitter.checked = social.sharing.platforms.twitter !== false;
                        if (shareCheckboxes.linkedin) shareCheckboxes.linkedin.checked = social.sharing.platforms.linkedin || false;
                        if (shareCheckboxes.pinterest) shareCheckboxes.pinterest.checked = social.sharing.platforms.pinterest || false;
                        if (shareCheckboxes.email) shareCheckboxes.email.checked = social.sharing.platforms.email !== false;
                    }
                }
            }

            // Apply advanced settings
            if (settings.advanced) {
                const advanced = settings.advanced;

                if (advanced.performance) {
                    if (advancedToggles.minify) advancedToggles.minify.checked = advanced.performance.minify !== false;
                    if (advancedToggles.browserCache) advancedToggles.browserCache.checked = advanced.performance.browserCache !== false;
                    if (advancedToggles.lazyLoading) advancedToggles.lazyLoading.checked = advanced.performance.lazyLoading !== false;
                }

                if (advanced.security) {
                    if (advancedToggles.spamProtection) advancedToggles.spamProtection.checked = advanced.security.spamProtection !== false;

                    const recaptchaSiteField = document.getElementById('recaptcha-site-key');
                    if (recaptchaSiteField && advanced.security.recaptchaSiteKey) recaptchaSiteField.value = advanced.security.recaptchaSiteKey;

                    const recaptchaSecretField = document.getElementById('recaptcha-secret-key');
                    if (recaptchaSecretField && advanced.security.recaptchaSecretKey) recaptchaSecretField.value = advanced.security.recaptchaSecretKey;
                }

                if (advanced.backup) {
                    if (advancedToggles.autoBackup) advancedToggles.autoBackup.checked = advanced.backup.autoBackup !== false;

                    const backupFreqField = document.getElementById('backup-frequency');
                    if (backupFreqField && advanced.backup.frequency) backupFreqField.value = advanced.backup.frequency;
                }
            }
        }
    }

    // Function to show notification
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close"><i class="fas fa-times"></i></button>
        `;

        // Add to document
        document.body.appendChild(notification);

        // Add event listener to close button
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.classList.add('hiding');
            setTimeout(() => {
                notification.remove();
            }, 300);
        });

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('hiding');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);

        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
    }

    // Add CSS for notifications
    const style = document.createElement('style');
    style.textContent = `
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            z-index: 1000;
            transform: translateX(120%);
            transition: transform 0.3s ease;
            min-width: 300px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.hiding {
            transform: translateX(120%);
        }

        .notification-content {
            display: flex;
            align-items: center;
        }

        .notification-content i {
            margin-right: 10px;
            font-size: 20px;
        }

        .notification.success {
            border-left: 4px solid var(--success);
        }

        .notification.success i {
            color: var(--success);
        }

        .notification.info {
            border-left: 4px solid var(--info);
        }

        .notification.info i {
            color: var(--info);
        }

        .notification.warning {
            border-left: 4px solid var(--warning);
        }

        .notification.warning i {
            color: var(--warning);
        }

        .notification.error {
            border-left: 4px solid var(--danger);
        }

        .notification.error i {
            color: var(--danger);
        }

        .notification-close {
            background: none;
            border: none;
            cursor: pointer;
            color: #6c757d;
            padding: 0;
            margin-left: 15px;
        }

        .notification-close:hover {
            color: #343a40;
        }
    `;
    document.head.appendChild(style);

    /**
     * Funkcja do przełączania widoczności sekcji wiadomości
     */
    function toggleNewsSection(visible) {
        const newsSettings = {
            visible: visible,
            maxItems: 3,
            categories: ['local', 'events', 'business', 'culture', 'sport']
        };

        // Zapisz ustawienia w localStorage
        localStorage.setItem('zyrardow_news_settings', JSON.stringify(newsSettings));

        // Wyświetl powiadomienie
        const message = visible ?
            'Sekcja wiadomości została włączona na stronie głównej' :
            'Sekcja wiadomości została wyłączona na stronie głównej';
        showNotification(message, 'success');

        console.log('News section toggled:', visible);
    }

    // Udostępnij funkcję globalnie
    window.toggleNewsSection = toggleNewsSection;

    /**
     * Initialize auto-save functionality
     */
    function initAutoSave() {
        // Auto-save delay in milliseconds
        const AUTO_SAVE_DELAY = 2000; // 2 seconds
        let autoSaveTimeout;

        // Get all form inputs, selects, and textareas
        const formElements = document.querySelectorAll('input, select, textarea');

        formElements.forEach(element => {
            // Skip buttons and submit inputs
            if (element.type === 'button' || element.type === 'submit') return;

            // Add change event listener
            element.addEventListener('input', function() {
                // Clear existing timeout
                if (autoSaveTimeout) {
                    clearTimeout(autoSaveTimeout);
                }

                // Set new timeout for auto-save
                autoSaveTimeout = setTimeout(() => {
                    autoSaveSettings(element);
                }, AUTO_SAVE_DELAY);

                // Show auto-save indicator
                showAutoSaveIndicator();
            });

            // Also listen for change events (for checkboxes, radios, selects)
            element.addEventListener('change', function() {
                // Clear existing timeout
                if (autoSaveTimeout) {
                    clearTimeout(autoSaveTimeout);
                }

                // Set new timeout for auto-save
                autoSaveTimeout = setTimeout(() => {
                    autoSaveSettings(element);
                }, AUTO_SAVE_DELAY);

                // Show auto-save indicator
                showAutoSaveIndicator();
            });
        });
    }

    /**
     * Auto-save settings based on which element changed
     */
    function autoSaveSettings(element) {
        // Determine which section to save based on element's location
        const tabContent = element.closest('.tab-content');

        if (!tabContent) return;

        const tabId = tabContent.id;

        try {
            switch (tabId) {
                case 'homepage':
                    saveHomepageSettings();
                    break;
                case 'general':
                    saveGeneralSettings();
                    break;
                case 'appearance':
                    saveAppearanceSettings();
                    break;
                case 'seo':
                    saveSeoSettings();
                    break;
                case 'social':
                    saveSocialSettings();
                    break;
                case 'advanced':
                    saveAdvancedSettings();
                    break;
                default:
                    console.log('Unknown tab for auto-save:', tabId);
            }

            // Show auto-save success
            showAutoSaveSuccess();

        } catch (error) {
            console.error('Auto-save error:', error);
            showAutoSaveError();
        }
    }

    /**
     * Show auto-save indicator
     */
    function showAutoSaveIndicator() {
        // Remove existing indicators
        const existingIndicator = document.querySelector('.auto-save-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // Create new indicator
        const indicator = document.createElement('div');
        indicator.className = 'auto-save-indicator saving';
        indicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Zapisywanie...';

        // Add to page
        document.body.appendChild(indicator);

        // Show with animation
        setTimeout(() => {
            indicator.classList.add('show');
        }, 10);
    }

    /**
     * Show auto-save success
     */
    function showAutoSaveSuccess() {
        const indicator = document.querySelector('.auto-save-indicator');
        if (indicator) {
            indicator.className = 'auto-save-indicator success show';
            indicator.innerHTML = '<i class="fas fa-check"></i> Zapisano automatycznie';

            // Hide after 2 seconds
            setTimeout(() => {
                indicator.classList.remove('show');
                setTimeout(() => {
                    indicator.remove();
                }, 300);
            }, 2000);
        }
    }

    /**
     * Show auto-save error
     */
    function showAutoSaveError() {
        const indicator = document.querySelector('.auto-save-indicator');
        if (indicator) {
            indicator.className = 'auto-save-indicator error show';
            indicator.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Błąd zapisu';

            // Hide after 3 seconds
            setTimeout(() => {
                indicator.classList.remove('show');
                setTimeout(() => {
                    indicator.remove();
                }, 300);
            }, 3000);
        }
    }

    // Add CSS for auto-save indicator
    const autoSaveStyle = document.createElement('style');
    autoSaveStyle.textContent = `
        .auto-save-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 10px 15px;
            display: flex;
            align-items: center;
            z-index: 1001;
            transform: translateY(100px);
            opacity: 0;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .auto-save-indicator.show {
            transform: translateY(0);
            opacity: 1;
        }

        .auto-save-indicator i {
            margin-right: 8px;
        }

        .auto-save-indicator.saving {
            border-left: 4px solid #007bff;
            color: #007bff;
        }

        .auto-save-indicator.success {
            border-left: 4px solid #28a745;
            color: #28a745;
        }

        .auto-save-indicator.error {
            border-left: 4px solid #dc3545;
            color: #dc3545;
        }
    `;
    document.head.appendChild(autoSaveStyle);

    // Obsługa przycisków cache
    const clearCacheBtn = document.getElementById('clear-cache-btn');
    const clearAdminCacheBtn = document.getElementById('clear-admin-cache-btn');
    const regenerateAssetsBtn = document.getElementById('regenerate-assets-btn');

    if (clearCacheBtn) {
        clearCacheBtn.addEventListener('click', function() {
            if (confirm('Czy na pewno chcesz wyczyścić cache strony? To może chwilowo wpłynąć na wydajność.')) {
                const btn = this;
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Czyszczenie...';
                btn.disabled = true;

                // Symulacja czyszczenia cache
                setTimeout(() => {
                    // Wyczyść localStorage i sessionStorage
                    localStorage.clear();
                    sessionStorage.clear();

                    // Wymuś odświeżenie cache przeglądarki
                    if ('caches' in window) {
                        caches.keys().then(names => {
                            names.forEach(name => {
                                caches.delete(name);
                            });
                        });
                    }

                    btn.innerHTML = originalText;
                    btn.disabled = false;
                    showNotification('Cache strony został wyczyszczony!', 'success');
                }, 2000);
            }
        });
    }

    if (clearAdminCacheBtn) {
        clearAdminCacheBtn.addEventListener('click', function() {
            if (confirm('Czy na pewno chcesz wyczyścić cache dashboardu?')) {
                const btn = this;
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Czyszczenie...';
                btn.disabled = true;

                // Symulacja czyszczenia cache admin
                setTimeout(() => {
                    // Wyczyść cache związany z adminem
                    const adminKeys = Object.keys(localStorage).filter(key =>
                        key.startsWith('admin_') || key.startsWith('dashboard_')
                    );
                    adminKeys.forEach(key => localStorage.removeItem(key));

                    btn.innerHTML = originalText;
                    btn.disabled = false;
                    showNotification('Cache dashboardu został wyczyszczony!', 'success');
                }, 1500);
            }
        });
    }

    if (regenerateAssetsBtn) {
        regenerateAssetsBtn.addEventListener('click', function() {
            if (confirm('Czy na pewno chcesz zregenerować zasoby CSS/JS? To może potrwać kilka minut.')) {
                const btn = this;
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Regenerowanie...';
                btn.disabled = true;

                // Symulacja regeneracji zasobów
                setTimeout(() => {
                    // Wymuś przeładowanie stylów
                    const links = document.querySelectorAll('link[rel="stylesheet"]');
                    links.forEach(link => {
                        const href = link.href;
                        link.href = href + (href.includes('?') ? '&' : '?') + 'v=' + Date.now();
                    });

                    btn.innerHTML = originalText;
                    btn.disabled = false;
                    showNotification('Zasoby zostały zregenerowane!', 'success');
                }, 3000);
            }
        });
    }
});
