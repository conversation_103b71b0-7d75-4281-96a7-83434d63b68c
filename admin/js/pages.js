/**
 * Pages Management - Żyrardów Poleca
 * JavaScript dla zarządzania treścią stron CMS
 */

document.addEventListener('DOMContentLoaded', function() {
    initPagesManager();
});

/**
 * Inicjalizacja managera stron
 */
function initPagesManager() {
    // Załaduj strony
    loadPages();

    // Inicjalizuj obsługę zdarzeń
    initEventListeners();

    // Inicjalizuj HugeRTE editor
    initHugeRTE();

    console.log('Pages Manager initialized');
}

/**
 * Inicjalizacja nasłuchiwaczy zdarzeń
 */
function initEventListeners() {
    // Wyszukiwanie stron
    const pageSearch = document.getElementById('page-search');
    if (pageSearch) {
        pageSearch.addEventListener('input', filterPages);
    }

    // Przycisk dodawania strony
    const addPageBtn = document.getElementById('addPageBtn');
    if (addPageBtn) {
        addPageBtn.addEventListener('click', showAddPageModal);
    }

    // Modal controls
    const pageModalClose = document.getElementById('pageModalClose');
    const pageCancelBtn = document.getElementById('pageCancelBtn');
    const pageSaveBtn = document.getElementById('pageSaveBtn');
    const pagePreviewBtn = document.getElementById('pagePreviewBtn');

    if (pageModalClose) pageModalClose.addEventListener('click', closePageModal);
    if (pageCancelBtn) pageCancelBtn.addEventListener('click', closePageModal);
    if (pageSaveBtn) pageSaveBtn.addEventListener('click', savePage);
    if (pagePreviewBtn) pagePreviewBtn.addEventListener('click', previewPage);

    // Delegacja zdarzeń dla dynamicznych elementów
    document.addEventListener('click', handleDynamicClicks);
}

/**
 * Obsługa kliknięć w dynamiczne elementy
 */
function handleDynamicClicks(e) {
    const target = e.target.closest('button');
    if (!target) return;

    if (target.classList.contains('edit-page')) {
        const pageCard = target.closest('.page-card');
        const pageId = pageCard.dataset.pageId;
        editPage(pageId);
    }

    if (target.classList.contains('preview-page')) {
        const pageCard = target.closest('.page-card');
        const pageFile = pageCard.dataset.pageFile;
        previewPageInNewTab(pageFile);
    }

    if (target.classList.contains('delete-page')) {
        const pageCard = target.closest('.page-card');
        const pageId = pageCard.dataset.pageId;
        const pageName = pageCard.querySelector('h3').textContent;
        deletePage(pageId, pageName);
    }
}

/**
 * Załaduj strony
 */
function loadPages() {
    const pages = getPagesFromStorage();
    renderPages(pages);
}

/**
 * Pobierz strony z localStorage
 */
function getPagesFromStorage() {
    const stored = localStorage.getItem('cms_pages');
    if (stored) {
        return JSON.parse(stored);
    }

    // Domyślne strony jeśli nie ma w localStorage
    return getDefaultPages();
}

/**
 * Zapisz strony do localStorage
 */
function savePagesToStorage(pages) {
    localStorage.setItem('cms_pages', JSON.stringify(pages));
}

/**
 * Domyślne strony
 */
function getDefaultPages() {
    return [
        {
            id: 'index',
            title: 'Strona główna',
            file: '../index.html',
            description: 'Główna strona portalu Żyrardów Poleca',
            type: 'html',
            status: 'published',
            lastModified: '2023-05-15',
            editable: true
        },
        {
            id: 'o-miescie',
            title: 'O mieście',
            file: '../o-miescie.html',
            description: 'Informacje o Żyrardowie',
            type: 'html',
            status: 'published',
            lastModified: '2023-05-14',
            editable: true
        },
        {
            id: 'katalog-firm',
            title: 'Katalog firm',
            file: '../katalog-firm.html',
            description: 'Katalog lokalnych firm i usług',
            type: 'html',
            status: 'published',
            lastModified: '2023-05-13',
            editable: true
        },
        {
            id: 'oferty',
            title: 'Oferty specjalne',
            file: '../oferty.html',
            description: 'Aktualne oferty i promocje',
            type: 'html',
            status: 'published',
            lastModified: '2023-05-12',
            editable: true
        },
        {
            id: 'kupony',
            title: 'Kupony rabatowe',
            file: '../kupony.html',
            description: 'Kupony i kody rabatowe',
            type: 'html',
            status: 'published',
            lastModified: '2023-05-11',
            editable: true
        },
        {
            id: 'kontakt',
            title: 'Kontakt',
            file: '../kontakt.html',
            description: 'Informacje kontaktowe',
            type: 'html',
            status: 'published',
            lastModified: '2023-05-10',
            editable: true
        }
    ];
}

/**
 * Renderuj strony
 */
function renderPages(pages) {
    const pagesGrid = document.getElementById('pagesGrid');
    if (!pagesGrid) return;

    if (pages.length === 0) {
        pagesGrid.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-file-alt"></i>
                <h3>Brak stron</h3>
                <p>Nie znaleziono żadnych stron do edycji.</p>
                <button class="btn btn-primary" onclick="showAddPageModal()">
                    <i class="fas fa-plus"></i> Dodaj pierwszą stronę
                </button>
            </div>
        `;
        return;
    }

    pagesGrid.innerHTML = '';

    pages.forEach(page => {
        const pageCard = createPageCard(page);
        pagesGrid.appendChild(pageCard);
    });
}

/**
 * Utwórz kartę strony
 */
function createPageCard(page) {
    const div = document.createElement('div');
    div.className = 'page-card';
    div.dataset.pageId = page.id;
    div.dataset.pageFile = page.file;

    const iconClass = page.type === 'html' ? 'html' : 'static';
    const iconName = page.type === 'html' ? 'fa-code' : 'fa-file-alt';

    div.innerHTML = `
        <div class="page-card-header">
            <div class="page-icon ${iconClass}">
                <i class="fas ${iconName}"></i>
            </div>
            <div class="page-info">
                <h3>${page.title}</h3>
                <p>${page.file}</p>
            </div>
        </div>
        <div class="page-card-body">
            <div class="page-description">${page.description}</div>
            <div class="page-meta">
                <span>Ostatnia modyfikacja: ${page.lastModified}</span>
                <span class="page-status ${page.status}">${page.status === 'published' ? 'Opublikowana' : 'Szkic'}</span>
            </div>
        </div>
        <div class="page-card-footer">
            <div class="page-actions">
                <button class="btn-icon edit edit-page" title="Edytuj">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-icon preview preview-page" title="Podgląd">
                    <i class="fas fa-eye"></i>
                </button>
                ${page.editable ? `
                    <button class="btn-icon delete delete-page" title="Usuń">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                ` : ''}
            </div>
        </div>
    `;

    return div;
}

/**
 * Edytuj stronę
 */
function editPage(pageId) {
    const pages = getPagesFromStorage();
    const page = pages.find(p => p.id === pageId);

    if (!page) {
        showNotification('Nie znaleziono strony', 'error');
        return;
    }

    // Załaduj zawartość pliku
    loadPageContent(page.file).then(content => {
        // Wypełnij formularz
        document.getElementById('pageId').value = page.id;
        document.getElementById('pageTitle').value = page.title;
        document.getElementById('pageFile').value = page.file;

        // Ustaw zawartość w HugeRTE
        if (window.hugerte && window.hugerte.get('pageContent')) {
            window.hugerte.get('pageContent').setContent(content);
        } else {
            document.getElementById('pageContent').value = content;
        }

        // Pokaż modal
        document.getElementById('pageModalTitle').textContent = `Edytuj: ${page.title}`;
        document.getElementById('pageModal').classList.add('active');

    }).catch(error => {
        console.error('Error loading page content:', error);
        showNotification('Błąd ładowania zawartości strony', 'error');
    });
}

/**
 * Załaduj zawartość strony
 */
async function loadPageContent(filePath) {
    try {
        const response = await fetch(filePath);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.text();
    } catch (error) {
        console.error('Error fetching page content:', error);
        return '<p>Błąd ładowania zawartości strony</p>';
    }
}

/**
 * Zapisz stronę
 */
function savePage() {
    const pageId = document.getElementById('pageId').value;
    const pageTitle = document.getElementById('pageTitle').value;
    const pageFile = document.getElementById('pageFile').value;

    // Pobierz zawartość z HugeRTE
    let content;
    if (window.hugerte && window.hugerte.get('pageContent')) {
        content = window.hugerte.get('pageContent').getContent();
    } else {
        content = document.getElementById('pageContent').value;
    }

    // Zapisz do localStorage (w rzeczywistej aplikacji byłoby to API)
    const pages = getPagesFromStorage();
    const pageIndex = pages.findIndex(p => p.id === pageId);

    if (pageIndex !== -1) {
        pages[pageIndex].lastModified = new Date().toISOString().split('T')[0];
        savePagesToStorage(pages);
    }

    // Zapisz zawartość strony (symulacja)
    localStorage.setItem(`page_content_${pageId}`, content);

    showNotification('Strona została zapisana', 'success');
    closePageModal();
    loadPages(); // Odśwież listę
}

/**
 * Podgląd strony w nowej karcie
 */
function previewPageInNewTab(pageFile) {
    window.open(pageFile, '_blank');
}

/**
 * Podgląd strony w modal
 */
function previewPage() {
    const pageFile = document.getElementById('pageFile').value;
    previewPageInNewTab(pageFile);
}

/**
 * Usuń stronę
 */
function deletePage(pageId, pageName) {
    if (!confirm(`Czy na pewno chcesz usunąć stronę "${pageName}"?`)) {
        return;
    }

    const pages = getPagesFromStorage();
    const updatedPages = pages.filter(p => p.id !== pageId);
    savePagesToStorage(updatedPages);

    // Usuń zawartość strony
    localStorage.removeItem(`page_content_${pageId}`);

    showNotification(`Strona "${pageName}" została usunięta`, 'success');
    loadPages(); // Odśwież listę
}

/**
 * Pokaż modal dodawania strony
 */
function showAddPageModal() {
    // Reset formularza
    document.getElementById('pageId').value = '';
    document.getElementById('pageTitle').value = '';
    document.getElementById('pageFile').value = '';

    if (window.hugerte && window.hugerte.get('pageContent')) {
        window.hugerte.get('pageContent').setContent('');
    } else {
        document.getElementById('pageContent').value = '';
    }

    document.getElementById('pageModalTitle').textContent = 'Dodaj nową stronę';
    document.getElementById('pageModal').classList.add('active');
}

/**
 * Zamknij modal
 */
function closePageModal() {
    document.getElementById('pageModal').classList.remove('active');
}

/**
 * Filtruj strony
 */
function filterPages() {
    const searchTerm = document.getElementById('page-search').value.toLowerCase();
    const pages = getPagesFromStorage();

    const filteredPages = pages.filter(page =>
        page.title.toLowerCase().includes(searchTerm) ||
        page.description.toLowerCase().includes(searchTerm) ||
        page.file.toLowerCase().includes(searchTerm)
    );

    renderPages(filteredPages);
}

/**
 * Inicjalizuj HugeRTE editor
 */
function initHugeRTE() {
    if (typeof hugerte !== 'undefined') {
        hugerte.init({
            selector: '#pageContent',
            height: 400,
            menubar: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
            content_style: 'body { font-family: Montserrat, Arial, sans-serif; font-size: 14px; }',
            branding: false,
            promotion: false
        });
    }
}

/**
 * Pokaż powiadomienie
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    const container = document.getElementById('notifications');
    if (container) {
        container.appendChild(notification);

        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }
}
