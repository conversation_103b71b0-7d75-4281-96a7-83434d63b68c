/**
 * Zarządzanie kolejnością sekcji na stronie głównej
 */

class SectionOrderManager {
    constructor() {
        this.sortable = null;
        this.defaultOrder = [
            'hero',
            'city-info',
            'categories',
            'coupons',
            'girard',
            'heritage',
            'about',
            'seo',
            'offers',
            'news',
            'places',
            'stats',
            'weather'
        ];

        this.init();
    }

    init() {
        this.loadSortable();
        this.bindEvents();
        this.loadSavedOrder();
        this.updateStatusBadges();
    }

    loadSortable() {
        // Sprawdź czy SortableJS jest dostępne
        if (typeof Sortable === 'undefined') {
            console.warn('SortableJS nie jest załadowane. Ładowanie z CDN...');
            this.loadSortableFromCDN();
            return;
        }

        this.initializeSortable();
    }

    loadSortableFromCDN() {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js';
        script.onload = () => {
            console.log('SortableJS załadowane z CDN');
            this.initializeSortable();
        };
        script.onerror = () => {
            console.error('Nie udało się załadować SortableJS');
            this.showMessage('Błąd ładowania biblioteki sortowania', 'error');
        };
        document.head.appendChild(script);
    }

    initializeSortable() {
        const sortableContainer = document.getElementById('sortable-sections');
        if (!sortableContainer) {
            console.error('Kontener sortowania nie został znaleziony');
            return;
        }

        this.sortable = Sortable.create(sortableContainer, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            handle: '.section-handle',
            filter: '[data-section="hero"]', // Slider główny nie może być przenoszony
            onMove: (evt) => {
                // Zapobiegaj przenoszeniu slidera głównego
                return evt.related.dataset.section !== 'hero';
            },
            onEnd: (evt) => {
                console.log('Sekcja przeniesiona:', evt.oldIndex, '->', evt.newIndex);
                this.updateSectionOrder();
            }
        });
    }

    bindEvents() {
        // Przycisk zapisywania kolejności
        const saveBtn = document.getElementById('save-section-order');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveSectionOrder());
        }

        // Przycisk resetowania kolejności
        const resetBtn = document.getElementById('reset-section-order');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetSectionOrder());
        }
    }

    updateSectionOrder() {
        const sections = document.querySelectorAll('#sortable-sections .section-item');
        const newOrder = Array.from(sections).map(section => section.dataset.section);

        console.log('Nowa kolejność sekcji:', newOrder);

        // Zapisz tymczasowo w localStorage
        localStorage.setItem('temp_section_order', JSON.stringify(newOrder));
    }

    async saveSectionOrder() {
        const saveBtn = document.getElementById('save-section-order');
        const originalText = saveBtn.innerHTML;

        try {
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Zapisywanie...';
            saveBtn.disabled = true;

            const sections = document.querySelectorAll('#sortable-sections .section-item');
            const sectionOrder = Array.from(sections).map(section => section.dataset.section);

            // Symulacja zapisu na serwerze
            await this.saveToServer(sectionOrder);

            // Zapisz w localStorage
            localStorage.setItem('section_order', JSON.stringify(sectionOrder));

            this.showMessage('Kolejność sekcji została zapisana!', 'success');

        } catch (error) {
            console.error('Błąd zapisywania kolejności sekcji:', error);
            this.showMessage('Błąd podczas zapisywania kolejności sekcji!', 'error');
        } finally {
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;
        }
    }

    async resetSectionOrder() {
        const resetBtn = document.getElementById('reset-section-order');
        const originalText = resetBtn.innerHTML;

        try {
            resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetowanie...';
            resetBtn.disabled = true;

            // Przywróć domyślną kolejność
            this.applySectionOrder(this.defaultOrder);

            // Zapisz domyślną kolejność
            localStorage.setItem('section_order', JSON.stringify(this.defaultOrder));
            localStorage.removeItem('temp_section_order');

            this.showMessage('Przywrócono domyślną kolejność sekcji!', 'success');

        } catch (error) {
            console.error('Błąd resetowania kolejności sekcji:', error);
            this.showMessage('Błąd podczas resetowania kolejności sekcji!', 'error');
        } finally {
            resetBtn.innerHTML = originalText;
            resetBtn.disabled = false;
        }
    }

    loadSavedOrder() {
        try {
            // Sprawdź czy jest zapisana kolejność
            const savedOrder = localStorage.getItem('section_order');
            if (savedOrder) {
                const order = JSON.parse(savedOrder);
                this.applySectionOrder(order);
            }
        } catch (error) {
            console.error('Błąd ładowania zapisanej kolejności:', error);
        }
    }

    applySectionOrder(order) {
        const container = document.getElementById('sortable-sections');
        if (!container) return;

        const sections = {};

        // Zbierz wszystkie sekcje
        container.querySelectorAll('.section-item').forEach(section => {
            sections[section.dataset.section] = section;
        });

        // Wyczyść kontener
        container.innerHTML = '';

        // Dodaj sekcje w nowej kolejności
        order.forEach(sectionId => {
            if (sections[sectionId]) {
                container.appendChild(sections[sectionId]);
            }
        });

        // Dodaj sekcje, które nie są w kolejności (na wypadek nowych sekcji)
        Object.keys(sections).forEach(sectionId => {
            if (!order.includes(sectionId)) {
                container.appendChild(sections[sectionId]);
            }
        });
    }

    async saveToServer(sectionOrder) {
        // W rzeczywistej implementacji wysłałoby to dane do PHP
        return new Promise((resolve) => {
            setTimeout(() => {
                console.log('Kolejność sekcji zapisana na serwerze:', sectionOrder);
                resolve();
            }, 1000);
        });
    }

    showMessage(message, type = 'info') {
        // Utwórz lub zaktualizuj element wiadomości
        let messageEl = document.getElementById('section-order-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.id = 'section-order-message';
            messageEl.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 5px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transition: all 0.3s ease;
            `;
            document.body.appendChild(messageEl);
        }

        // Ustaw wiadomość i styl na podstawie typu
        messageEl.textContent = message;
        messageEl.className = `alert alert-${type}`;

        const colors = {
            success: '#27ae60',
            error: '#e74c3c',
            info: '#3498db',
            warning: '#f39c12'
        };

        messageEl.style.backgroundColor = colors[type] || colors.info;
        messageEl.style.display = 'block';
        messageEl.style.opacity = '1';

        // Automatyczne ukrycie po 5 sekundach
        setTimeout(() => {
            messageEl.style.opacity = '0';
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }, 5000);
    }

    updateStatusBadges() {
        try {
            const savedVisibility = localStorage.getItem('section_visibility');
            const visibility = savedVisibility ? JSON.parse(savedVisibility) : this.getDefaultVisibility();

            // Aktualizuj badges na podstawie rzeczywistej widoczności
            document.querySelectorAll('.section-item').forEach(item => {
                const sectionId = item.dataset.section;
                const badge = item.querySelector('.status-badge');

                if (badge && sectionId) {
                    if (sectionId === 'hero') {
                        // Slider zawsze stała pozycja
                        badge.className = 'status-badge fixed';
                        badge.textContent = 'Stała pozycja';
                    } else if (visibility[sectionId] === false) {
                        badge.className = 'status-badge hidden';
                        badge.textContent = 'Ukryta';
                    } else {
                        badge.className = 'status-badge active';
                        badge.textContent = 'Aktywna';
                    }
                }
            });
        } catch (error) {
            console.error('Błąd aktualizacji statusów:', error);
        }
    }

    getDefaultVisibility() {
        return {
            'hero': true,
            'city-info': true,
            'categories': true,
            'coupons': true,
            'girard': true,
            'heritage': true,
            'about': true,
            'seo': true,
            'offers': true,
            'news': false, // Domyślnie ukryta
            'places': true,
            'stats': true,
            'weather': true
        };
    }
}

// Inicjalizacja po załadowaniu DOM
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('sortable-sections')) {
        window.sectionOrderManager = new SectionOrderManager();
        // Aktualizuj statusy po załadowaniu
        setTimeout(() => {
            window.sectionOrderManager.updateStatusBadges();
        }, 100);
    }
});
