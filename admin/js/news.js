/**
 * Panel Administracyjny - Żyrardów Poleca
 * JavaScript dla zarządzania wiadomościami
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja funkcji zarządzania wiadomościami
    initNewsManagement();
});

// <PERSON> w<PERSON> (w rzeczywistej aplikacji byłyby pobierane z API)
let newsData = [
    {
        id: 1,
        title: "Nowe inwestycje w centrum Żyrardowa",
        category: "local",
        excerpt: "Miasto planuje modernizację centrum miasta z nowymi chodnikami i oświetleniem LED.",
        content: "Szczegółowy opis inwestycji...",
        image: "images/news/inwestycje.jpg",
        imageAlt: "Centrum Żyrardowa",
        status: "published",
        publishDate: "2024-01-15T10:00:00",
        slug: "nowe-inwestycje-centrum-zyrardowa",
        metaDescription: "Miasto Żyrardów planuje nowe inwestycje w centrum miasta",
        tags: "żyrardów, inwestycje, centrum, modernizacja"
    },
    {
        id: 2,
        title: "Festiwal Kultury Żyrardowskiej 2024",
        category: "culture",
        excerpt: "Już w czerwcu odbędzie się coroczny Festiwal Kultury z bogatym programem artystycznym.",
        content: "Program festiwalu...",
        image: null,
        imageAlt: "",
        status: "draft",
        publishDate: "2024-06-01T18:00:00",
        slug: "festiwal-kultury-zyrardowskiej-2024",
        metaDescription: "Festiwal Kultury Żyrardowskiej 2024 - program i informacje",
        tags: "żyrardów, kultura, festiwal, wydarzenia"
    }
];

let currentEditingId = null;

/**
 * Inicjalizacja funkcji zarządzania wiadomościami
 */
function initNewsManagement() {
    // Renderowanie listy wiadomości
    renderNewsList();

    // Obsługa przycisków
    initButtons();

    // Obsługa filtrów
    initFilters();

    // Obsługa formularzy
    initForms();

    // Inicjalizuj HugeRTE editor
    initHugeRTE();
}

/**
 * Inicjalizacja przycisków
 */
function initButtons() {
    const addNewsBtn = document.getElementById('addNewsBtn');
    const importNewsBtn = document.getElementById('importNewsBtn');

    if (addNewsBtn) {
        addNewsBtn.addEventListener('click', function() {
            openNewsModal();
        });
    }

    if (importNewsBtn) {
        importNewsBtn.addEventListener('click', function() {
            openImportModal();
        });
    }
}

/**
 * Inicjalizacja filtrów
 */
function initFilters() {
    const searchInput = document.getElementById('newsSearch');
    const statusFilter = document.getElementById('statusFilter');
    const categoryFilter = document.getElementById('categoryFilter');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterNews();
        });
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            filterNews();
        });
    }

    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            filterNews();
        });
    }
}

/**
 * Inicjalizacja formularzy
 */
function initForms() {
    const newsForm = document.getElementById('newsForm');
    const importForm = document.getElementById('importForm');

    if (newsForm) {
        newsForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveNews();
        });

        // Auto-generowanie slug z tytułu
        const titleInput = document.getElementById('newsTitle');
        const slugInput = document.getElementById('newsSlug');

        if (titleInput && slugInput) {
            titleInput.addEventListener('input', function() {
                if (!slugInput.value || slugInput.value === generateSlug(titleInput.dataset.oldValue || '')) {
                    slugInput.value = generateSlug(this.value);
                }
                titleInput.dataset.oldValue = this.value;
            });
        }

        // Podgląd zdjęcia
        const imageInput = document.getElementById('newsImage');
        if (imageInput) {
            imageInput.addEventListener('change', function() {
                previewImage(this);
            });
        }
    }

    if (importForm) {
        importForm.addEventListener('submit', function(e) {
            e.preventDefault();
            importNews();
        });
    }

    // Obsługa zamykania modali
    const modalCloses = document.querySelectorAll('.modal-close, .modal-cancel');
    modalCloses.forEach(btn => {
        btn.addEventListener('click', function() {
            closeModals();
        });
    });

    // Zamykanie modali po kliknięciu poza nimi
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModals();
        }
    });
}

/**
 * Renderowanie listy wiadomości
 */
function renderNewsList() {
    const newsList = document.getElementById('newsList');
    if (!newsList) return;

    if (newsData.length === 0) {
        newsList.innerHTML = `
            <div class="news-empty">
                <i class="fas fa-newspaper"></i>
                <h3>Brak wiadomości</h3>
                <p>Dodaj pierwszą wiadomość, aby rozpocząć.</p>
            </div>
        `;
        return;
    }

    const newsHTML = newsData.map(news => `
        <div class="news-item" data-id="${news.id}">
            <div class="news-image ${news.image ? '' : 'no-image'}">
                ${news.image ?
                    `<img src="${news.image}" alt="${news.imageAlt || news.title}">` :
                    '<i class="fas fa-image"></i>'
                }
            </div>
            <div class="news-content">
                <div class="news-header">
                    <h3 class="news-title">${news.title}</h3>
                    <span class="news-category">${getCategoryName(news.category)}</span>
                </div>
                <p class="news-excerpt">${news.excerpt}</p>
                <div class="news-meta">
                    <div class="news-info">
                        <span><i class="fas fa-calendar"></i> ${formatDate(news.publishDate)}</span>
                        <span class="news-status ${news.status}">${getStatusName(news.status)}</span>
                    </div>
                    <div class="news-actions">
                        <button class="btn-icon edit" onclick="editNews(${news.id})" title="Edytuj">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon duplicate" onclick="duplicateNews(${news.id})" title="Duplikuj">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button class="btn-icon delete" onclick="deleteNews(${news.id})" title="Usuń">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');

    newsList.innerHTML = newsHTML;
}

/**
 * Filtrowanie wiadomości
 */
function filterNews() {
    const searchTerm = document.getElementById('newsSearch').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const categoryFilter = document.getElementById('categoryFilter').value;

    const filteredNews = newsData.filter(news => {
        const matchesSearch = news.title.toLowerCase().includes(searchTerm) ||
                            news.excerpt.toLowerCase().includes(searchTerm);
        const matchesStatus = statusFilter === 'all' || news.status === statusFilter;
        const matchesCategory = categoryFilter === 'all' || news.category === categoryFilter;

        return matchesSearch && matchesStatus && matchesCategory;
    });

    // Tymczasowo zastąp dane do renderowania
    const originalData = newsData;
    newsData = filteredNews;
    renderNewsList();
    newsData = originalData;
}

/**
 * Otwieranie modalu dodawania/edycji wiadomości
 */
function openNewsModal(newsId = null) {
    const modal = document.getElementById('newsModal');
    const modalTitle = document.getElementById('modalTitle');
    const form = document.getElementById('newsForm');

    if (newsId) {
        const news = newsData.find(n => n.id === newsId);
        if (news) {
            modalTitle.textContent = 'Edytuj wiadomość';
            fillNewsForm(news);
            currentEditingId = newsId;
        }
    } else {
        modalTitle.textContent = 'Dodaj nową wiadomość';
        form.reset();
        currentEditingId = null;

        // Ustaw domyślną datę publikacji
        const now = new Date();
        document.getElementById('newsDate').value = now.toISOString().slice(0, 16);
    }

    modal.classList.add('show');
}

/**
 * Wypełnianie formularza danymi wiadomości
 */
function fillNewsForm(news) {
    document.getElementById('newsTitle').value = news.title;
    document.getElementById('newsCategory').value = news.category;
    document.getElementById('newsExcerpt').value = news.excerpt;
    // Ustaw zawartość w HugeRTE
    if (window.hugerte && window.hugerte.get('newsContent')) {
        window.hugerte.get('newsContent').setContent(news.content);
    } else {
        document.getElementById('newsContent').value = news.content;
    }
    document.getElementById('newsImageAlt').value = news.imageAlt || '';
    document.getElementById('newsStatus').value = news.status;
    document.getElementById('newsDate').value = news.publishDate.slice(0, 16);
    document.getElementById('newsSlug').value = news.slug;
    document.getElementById('newsMetaDescription').value = news.metaDescription || '';
    document.getElementById('newsTags').value = news.tags || '';

    // Podgląd zdjęcia
    if (news.image) {
        const preview = document.getElementById('imagePreview');
        preview.innerHTML = `<img src="${news.image}" alt="${news.imageAlt || news.title}">`;
    }
}

/**
 * Zapisywanie wiadomości
 */
function saveNews() {
    const formData = new FormData(document.getElementById('newsForm'));

    // Pobierz zawartość z HugeRTE
    let content;
    if (window.hugerte && window.hugerte.get('newsContent')) {
        content = window.hugerte.get('newsContent').getContent();
    } else {
        content = formData.get('content');
    }

    const newsData_item = {
        title: formData.get('title'),
        category: formData.get('category'),
        excerpt: formData.get('excerpt'),
        content: content,
        imageAlt: formData.get('imageAlt'),
        status: formData.get('status'),
        publishDate: formData.get('publishDate'),
        slug: formData.get('slug'),
        metaDescription: formData.get('metaDescription'),
        tags: formData.get('tags')
    };

    // Walidacja
    if (!newsData_item.title || !newsData_item.category || !newsData_item.excerpt || !newsData_item.content) {
        showNotification('Wypełnij wszystkie wymagane pola', 'error');
        return;
    }

    // Obsługa zdjęcia
    const imageFile = formData.get('image');
    if (imageFile && imageFile.size > 0) {
        // W rzeczywistej aplikacji tutaj byłoby przesyłanie pliku na serwer
        newsData_item.image = URL.createObjectURL(imageFile);
    } else if (currentEditingId) {
        // Zachowaj istniejące zdjęcie przy edycji
        const existingNews = newsData.find(n => n.id === currentEditingId);
        if (existingNews) {
            newsData_item.image = existingNews.image;
        }
    }

    if (currentEditingId) {
        // Edycja istniejącej wiadomości
        const index = newsData.findIndex(n => n.id === currentEditingId);
        if (index !== -1) {
            newsData[index] = { ...newsData[index], ...newsData_item };
            showNotification('Wiadomość została zaktualizowana', 'success');
        }
    } else {
        // Dodawanie nowej wiadomości
        const newId = Math.max(...newsData.map(n => n.id), 0) + 1;
        newsData.push({ id: newId, ...newsData_item });
        showNotification('Wiadomość została dodana', 'success');
    }

    // Zapisz w localStorage
    localStorage.setItem('zyrardow_news', JSON.stringify(newsData));

    // Utwórz stronę HTML dla artykułu jeśli jest opublikowany
    if (newsData_item.status === 'published') {
        const newsItem = currentEditingId ?
            newsData.find(n => n.id === currentEditingId) :
            newsData[newsData.length - 1];
        createNewsPage(newsItem);
    }

    closeModals();
    renderNewsList();
}

/**
 * Edycja wiadomości
 */
function editNews(id) {
    openNewsModal(id);
}

/**
 * Duplikowanie wiadomości
 */
function duplicateNews(id) {
    const news = newsData.find(n => n.id === id);
    if (news) {
        const newId = Math.max(...newsData.map(n => n.id), 0) + 1;
        const duplicatedNews = {
            ...news,
            id: newId,
            title: news.title + ' (kopia)',
            slug: news.slug + '-kopia',
            status: 'draft'
        };

        newsData.push(duplicatedNews);
        localStorage.setItem('zyrardow_news', JSON.stringify(newsData));
        renderNewsList();
        showNotification('Wiadomość została zduplikowana', 'success');
    }
}

/**
 * Usuwanie wiadomości
 */
function deleteNews(id) {
    const news = newsData.find(n => n.id === id);
    if (news && confirm(`Czy na pewno chcesz usunąć wiadomość "${news.title}"?`)) {
        newsData = newsData.filter(n => n.id !== id);
        localStorage.setItem('zyrardow_news', JSON.stringify(newsData));
        renderNewsList();
        showNotification('Wiadomość została usunięta', 'success');
    }
}

/**
 * Otwieranie modalu importu
 */
function openImportModal() {
    const modal = document.getElementById('importModal');
    modal.classList.add('show');
}

/**
 * Import wiadomości z JSON
 */
function importNews() {
    const importData = document.getElementById('importData').value;

    try {
        const newsItem = JSON.parse(importData);

        // Walidacja wymaganych pól
        if (!newsItem.title || !newsItem.category || !newsItem.excerpt || !newsItem.content) {
            showNotification('Dane JSON muszą zawierać: title, category, excerpt, content', 'error');
            return;
        }

        // Generuj ID i slug jeśli nie ma
        const newId = Math.max(...newsData.map(n => n.id), 0) + 1;
        const newNews = {
            id: newId,
            title: newsItem.title,
            category: newsItem.category,
            excerpt: newsItem.excerpt,
            content: newsItem.content,
            image: newsItem.image || null,
            imageAlt: newsItem.imageAlt || '',
            status: newsItem.status || 'draft',
            publishDate: newsItem.publishDate || new Date().toISOString(),
            slug: newsItem.slug || generateSlug(newsItem.title),
            metaDescription: newsItem.metaDescription || '',
            tags: newsItem.tags || ''
        };

        newsData.push(newNews);
        localStorage.setItem('zyrardow_news', JSON.stringify(newsData));

        closeModals();
        renderNewsList();
        showNotification('Wiadomość została zaimportowana', 'success');

    } catch (error) {
        showNotification('Nieprawidłowy format JSON', 'error');
    }
}

/**
 * Zamykanie modali
 */
function closeModals() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.classList.remove('show');
    });

    // Wyczyść formularz importu
    const importForm = document.getElementById('importForm');
    if (importForm) {
        importForm.reset();
    }

    // Wyczyść podgląd zdjęcia
    const imagePreview = document.getElementById('imagePreview');
    if (imagePreview) {
        imagePreview.innerHTML = '';
    }

    currentEditingId = null;
}

/**
 * Podgląd zdjęcia
 */
function previewImage(input) {
    const preview = document.getElementById('imagePreview');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="Podgląd">`;
        };

        reader.readAsDataURL(input.files[0]);
    } else {
        preview.innerHTML = '';
    }
}

/**
 * Generowanie slug z tytułu
 */
function generateSlug(title) {
    return title
        .toLowerCase()
        .replace(/[ąćęłńóśźż]/g, function(match) {
            const map = {
                'ą': 'a', 'ć': 'c', 'ę': 'e', 'ł': 'l', 'ń': 'n',
                'ó': 'o', 'ś': 's', 'ź': 'z', 'ż': 'z'
            };
            return map[match];
        })
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-');
}

/**
 * Formatowanie daty
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('pl-PL', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * Pobieranie nazwy kategorii
 */
function getCategoryName(category) {
    const categories = {
        'local': 'Lokalne',
        'events': 'Wydarzenia',
        'business': 'Biznes',
        'culture': 'Kultura',
        'sport': 'Sport'
    };
    return categories[category] || category;
}

/**
 * Pobieranie nazwy statusu
 */
function getStatusName(status) {
    const statuses = {
        'published': 'Opublikowane',
        'draft': 'Szkic',
        'archived': 'Zarchiwizowane'
    };
    return statuses[status] || status;
}

/**
 * Wyświetlanie powiadomień
 */
function showNotification(message, type = 'info') {
    // Sprawdź, czy istnieje już kontener powiadomień
    let notificationsContainer = document.querySelector('.notifications-container');

    if (!notificationsContainer) {
        notificationsContainer = document.createElement('div');
        notificationsContainer.className = 'notifications-container';
        document.body.appendChild(notificationsContainer);
    }

    // Utwórz nowe powiadomienie
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <div class="notification-message">${message}</div>
            <button class="notification-close"><i class="fas fa-times"></i></button>
        </div>
    `;

    // Dodaj powiadomienie do kontenera
    notificationsContainer.appendChild(notification);

    // Pokaż powiadomienie z animacją
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Dodaj obsługę zamykania powiadomienia
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });

    // Automatycznie zamknij powiadomienie po 5 sekundach
    setTimeout(() => {
        if (notification.parentNode) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}

/**
 * Tworzenie osobnej strony HTML dla artykułu
 */
function createNewsPage(newsItem) {
    if (!newsItem || !newsItem.slug) {
        console.error('Brak danych artykułu lub slug');
        return;
    }

    const categoryName = getCategoryName(newsItem.category);
    const formattedDate = formatDate(newsItem.publishDate);

    // Szablon strony HTML
    const htmlTemplate = `<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${newsItem.title} - Żyrardów Poleca</title>
    <meta name="description" content="${newsItem.metaDescription || newsItem.excerpt}">
    <meta name="keywords" content="${newsItem.tags || 'żyrardów, wiadomości, ' + newsItem.category}">

    <!-- CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/news-article.css">
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Kontener na nagłówek -->
    <div id="header"></div>

    <main class="news-article">
        <section class="breadcrumbs">
            <div class="container">
                <ul class="breadcrumbs-list">
                    <li><a href="../index.html">Strona główna</a></li>
                    <li><a href="../wiadomosci.html">Wiadomości</a></li>
                    <li>${newsItem.title}</li>
                </ul>
            </div>
        </section>

        <article class="article-content">
            <div class="container">
                <header class="article-header">
                    <div class="article-meta">
                        <span class="article-category category-${newsItem.category}">${categoryName}</span>
                        <time class="article-date" datetime="${newsItem.publishDate}">
                            <i class="fas fa-calendar-alt"></i>
                            ${formattedDate}
                        </time>
                    </div>
                    <h1 class="article-title">${newsItem.title}</h1>
                    <p class="article-excerpt">${newsItem.excerpt}</p>
                </header>

                ${newsItem.image ? `
                <div class="article-image">
                    <img src="../${newsItem.image}" alt="${newsItem.imageAlt || newsItem.title}" loading="lazy">
                </div>
                ` : ''}

                <div class="article-body">
                    ${newsItem.content.split('\\n').map(paragraph =>
                        paragraph.trim() ? `<p>${paragraph}</p>` : ''
                    ).join('')}
                </div>

                <nav class="article-navigation">
                    <a href="../wiadomosci.html" class="btn btn-outline">
                        <i class="fas fa-arrow-left"></i>
                        Powrót do wiadomości
                    </a>
                </nav>
            </div>
        </article>
    </main>

    <!-- Kontener na stopkę -->
    <div id="footer"></div>

    <script src="../js/templates.js"></script>
    <script src="../js/menu.js"></script>
</body>
</html>`;

    // Zapisz plik HTML (symulacja)
    console.log('Utworzono stronę HTML dla artykułu:', newsItem.slug);

    // Utwórz link do pobrania
    const blob = new Blob([htmlTemplate], { type: 'text/html' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `${newsItem.slug}.html`;
    link.style.display = 'none';
    document.body.appendChild(link);

    // Pokaż powiadomienie z opcją pobrania
    showNotification(`Strona HTML została utworzona. <a href="${url}" download="${newsItem.slug}.html" style="color: white; text-decoration: underline;">Pobierz plik</a>`, 'success');

    // Usuń link po 10 sekundach
    setTimeout(() => {
        if (document.body.contains(link)) {
            document.body.removeChild(link);
        }
        URL.revokeObjectURL(url);
    }, 10000);
}

/**
 * Inicjalizuj HugeRTE editor
 */
function initHugeRTE() {
    if (typeof hugerte !== 'undefined') {
        hugerte.init({
            selector: '#newsContent',
            height: 300,
            menubar: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image | removeformat | code | help',
            content_style: 'body { font-family: Montserrat, Arial, sans-serif; font-size: 14px; }',
            branding: false,
            promotion: false
        });
    }
}

// Ładowanie danych z localStorage przy starcie
document.addEventListener('DOMContentLoaded', function() {
    const savedNews = localStorage.getItem('zyrardow_news');
    if (savedNews) {
        try {
            newsData = JSON.parse(savedNews);
        } catch (error) {
            console.error('Błąd podczas ładowania wiadomości z localStorage:', error);
        }
    }
});
