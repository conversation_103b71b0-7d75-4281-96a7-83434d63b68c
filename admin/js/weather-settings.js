/**
 * Zarządzanie ustawieniami pogody w dashboardzie
 */

class WeatherSettings {
    constructor() {
        this.configFile = './config/weather_config.json';
        this.testApiUrl = '../api/weather.json'; // Używamy JSON zamiast PHP
        this.weatherApiBaseUrl = 'https://api.weatherapi.com/v1/forecast.json';

        this.init();
    }

    init() {
        this.loadSettings();
        this.bindEvents();
    }

    bindEvents() {
        // Save weather settings
        const saveBtn = document.getElementById('save-weather-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveSettings());
        }

        // Test weather connection
        const testBtn = document.getElementById('test-weather-btn');
        if (testBtn) {
            testBtn.addEventListener('click', () => this.testConnection());
        }

        // Przycisk pokazywania/ukrywania klucza API
        const toggleBtn = document.getElementById('toggle-api-key-visibility');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => this.toggleApiKeyVisibility());
        }

        // Toggle weather widget
        const toggleWeather = document.getElementById('toggle-weather');
        if (toggleWeather) {
            toggleWeather.addEventListener('change', (e) => {
                this.toggleWeatherWidget(e.target.checked);
            });
        }
    }

    async loadSettings() {
        try {
            const response = await fetch(this.configFile);
            if (response.ok) {
                const config = await response.json();
                this.populateForm(config);
            }
        } catch (error) {
            console.log('Using default weather settings');
            this.populateForm({
                enabled: true,
                city: 'Żyrardów',
                lat: 52.0500,
                lon: 20.4500,
                position: 'bottom'
            });
        }
    }

    populateForm(config) {
        const elements = {
            'toggle-weather': config.enabled,
            'weather-city': config.city,
            'weather-lat': config.lat,
            'weather-lon': config.lon,
            'weather-position': config.position,
            'weather-api-key': config.apiKey
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = value;
                } else {
                    element.value = value;
                }
            }
        });
    }

    async saveSettings() {
        const saveBtn = document.getElementById('save-weather-btn');
        const originalText = saveBtn.innerHTML;

        try {
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Zapisywanie...';
            saveBtn.disabled = true;

            const config = this.getFormData();

            // Simulate save (in real implementation, this would save to server)
            await this.saveToServer(config);

            // Show success message
            this.showMessage('Ustawienia pogody zostały zapisane!', 'success');

            // Update weather widget if enabled
            if (config.enabled && window.parent && window.parent.weatherWidget) {
                window.parent.weatherWidget.refresh();
            }

        } catch (error) {
            console.error('Error saving weather settings:', error);
            this.showMessage('Błąd podczas zapisywania ustawień!', 'error');
        } finally {
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;
        }
    }

    async testConnection() {
        const testBtn = document.getElementById('test-weather-btn');
        const originalText = testBtn.innerHTML;

        try {
            testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testowanie...';
            testBtn.disabled = true;

            const config = this.getFormData();

            // Test API connection
            let testUrl, testType;

            if (config.apiKey) {
                // Test WeatherAPI.com with real API key
                testUrl = `${this.weatherApiBaseUrl}?key=${config.apiKey}&q=${config.lat},${config.lon}&days=1&aqi=no&alerts=no`;
                testType = 'WeatherAPI.com';
            } else {
                // Test fallback JSON
                testUrl = this.testApiUrl;
                testType = 'Statyczne dane testowe';
            }

            const response = await fetch(testUrl);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (config.apiKey && data.location) {
                // WeatherAPI.com response
                this.showMessage(`✅ ${testType} - Połączenie udane! Miasto: ${data.location.name}, ${data.location.country}`, 'success');
            } else if (!config.apiKey && data.success) {
                // Fallback JSON response
                this.showMessage(`✅ ${testType} - Połączenie udane! Miasto: ${data.city}`, 'success');
            } else {
                throw new Error(data.error?.message || 'Nieznany błąd API');
            }

        } catch (error) {
            console.error('Weather API test failed:', error);
            this.showMessage(`❌ Błąd połączenia: ${error.message}`, 'error');
        } finally {
            testBtn.innerHTML = originalText;
            testBtn.disabled = false;
        }
    }

    getFormData() {
        return {
            enabled: document.getElementById('toggle-weather')?.checked || false,
            city: document.getElementById('weather-city')?.value || 'Żyrardów',
            lat: parseFloat(document.getElementById('weather-lat')?.value) || 52.0500,
            lon: parseFloat(document.getElementById('weather-lon')?.value) || 20.4500,
            position: document.getElementById('weather-position')?.value || 'bottom',
            apiKey: document.getElementById('weather-api-key')?.value || '',
            updated: new Date().toISOString()
        };
    }

    async saveToServer(config) {
        // In a real implementation, this would send data to a PHP script
        // For now, we'll simulate the save operation
        return new Promise((resolve) => {
            setTimeout(() => {
                localStorage.setItem('weather_config', JSON.stringify(config));
                resolve();
            }, 1000);
        });
    }

    toggleWeatherWidget(enabled) {
        const weatherInputs = [
            'weather-city',
            'weather-lat',
            'weather-lon',
            'weather-position'
        ];

        weatherInputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.disabled = !enabled;
            }
        });

        const testBtn = document.getElementById('test-weather-btn');
        const saveBtn = document.getElementById('save-weather-btn');

        if (testBtn) testBtn.disabled = !enabled;
        if (saveBtn) saveBtn.disabled = !enabled;
    }

    toggleApiKeyVisibility() {
        const apiKeyInput = document.getElementById('weather-api-key');
        const toggleBtn = document.getElementById('toggle-api-key-visibility');
        const icon = toggleBtn.querySelector('i');

        if (apiKeyInput.type === 'password') {
            apiKeyInput.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            apiKeyInput.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }

    showMessage(message, type = 'info') {
        // Create or update message element
        let messageEl = document.getElementById('weather-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.id = 'weather-message';
            messageEl.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 5px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transition: all 0.3s ease;
            `;
            document.body.appendChild(messageEl);
        }

        // Set message and style based on type
        messageEl.textContent = message;
        messageEl.className = `alert alert-${type}`;

        const colors = {
            success: '#27ae60',
            error: '#e74c3c',
            info: '#3498db',
            warning: '#f39c12'
        };

        messageEl.style.backgroundColor = colors[type] || colors.info;
        messageEl.style.display = 'block';
        messageEl.style.opacity = '1';

        // Auto-hide after 5 seconds
        setTimeout(() => {
            messageEl.style.opacity = '0';
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }, 5000);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('toggle-weather')) {
        window.weatherSettings = new WeatherSettings();
    }
});
