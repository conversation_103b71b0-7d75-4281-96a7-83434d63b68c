/**
 * Panel Administracyjny - Żyrardów Poleca
 * JavaScript dla zarządzania kategoriami
 * System SQLite
 */

// Globalne dane kategorii
let categoriesData = [];

document.addEventListener('DOMContentLoaded', function() {
    // Załaduj kategorie z bazy danych SQLite
    loadCategoriesFromDatabase();

    // Inicjalizacja funkcji zarządzania kategoriami
    initCategoriesManagement();
});

/**
 * Załaduj kategorie z bazy danych SQLite
 */
async function loadCategoriesFromDatabase() {
    try {
        showLoading(true);

        const response = await fetch('api/categories.php?path=tree');
        const result = await response.json();

        if (result.success) {
            categoriesData = result.data;
            renderCategoriesFromData();
            showNotification('Kategorie załadowane pomyślnie', 'success');
        } else {
            throw new Error(result.message || 'Błąd ładowania kategorii');
        }
    } catch (error) {
        console.error('Błąd ładowania kategorii:', error);
        showNotification('Błąd ładowania kategorii: ' + error.message, 'error');
        categoriesData = [];
    } finally {
        showLoading(false);
    }
}

/**
 * Zapisz kategorię do bazy danych
 */
async function saveCategory(categoryData, isUpdate = false) {
    try {
        showLoading(true);

        const url = isUpdate ? 'api/categories.php?path=update' : 'api/categories.php?path=create';
        const method = isUpdate ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(categoryData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification(isUpdate ? 'Kategoria zaktualizowana pomyślnie' : 'Kategoria utworzona pomyślnie', 'success');
            // Przeładuj kategorie
            await loadCategoriesFromDatabase();
            return true;
        } else {
            throw new Error(result.message || 'Błąd zapisywania kategorii');
        }
    } catch (error) {
        console.error('Błąd zapisywania kategorii:', error);
        showNotification('Błąd zapisywania: ' + error.message, 'error');
        return false;
    } finally {
        showLoading(false);
    }
}

/**
 * Usuń kategorię z bazy danych
 */
async function deleteCategory(categoryId) {
    try {
        showLoading(true);

        const response = await fetch(`api/categories.php?path=delete&id=${categoryId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showNotification('Kategoria usunięta pomyślnie', 'success');
            // Przeładuj kategorie
            await loadCategoriesFromDatabase();
            return true;
        } else {
            throw new Error(result.message || 'Błąd usuwania kategorii');
        }
    } catch (error) {
        console.error('Błąd usuwania kategorii:', error);
        showNotification('Błąd usuwania: ' + error.message, 'error');
        return false;
    } finally {
        showLoading(false);
    }
}

/**
 * Renderuj kategorie na podstawie danych z bazy
 */
function renderCategoriesFromData() {
    const categoriesTree = document.getElementById('categories-tree');
    if (!categoriesTree) return;

    // Wyczyść istniejące kategorie
    categoriesTree.innerHTML = '';

    // Renderuj każdą kategorię
    categoriesData.forEach((category, index) => {
        const categoryHTML = `
            <li class="category-item ${index === 0 ? 'active' : ''}" data-id="${category.id}">
                <div class="category-item-header">
                    <i class="${category.icon || 'fas fa-folder'} category-icon"></i>
                    <span class="category-name">${category.name}</span>
                    <div class="category-actions">
                        <button class="btn-icon add-subcategory" title="Dodaj podkategorię"><i class="fas fa-plus"></i></button>
                        <button class="btn-icon edit-category" title="Edytuj kategorię"><i class="fas fa-edit"></i></button>
                        <button class="btn-icon delete-category" title="Usuń kategorię"><i class="fas fa-trash-alt"></i></button>
                    </div>
                </div>
                <ul class="subcategories">
                    ${(category.subcategories || []).map(sub => `
                        <li class="subcategory-item" data-id="${sub.id}">
                            <div class="subcategory-item-header">
                                <span class="subcategory-name">${sub.name}</span>
                                <div class="subcategory-actions">
                                    <button class="btn-icon edit-subcategory" title="Edytuj podkategorię"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon delete-subcategory" title="Usuń podkategorię"><i class="fas fa-trash-alt"></i></button>
                                </div>
                            </div>
                        </li>
                    `).join('')}
                </ul>
            </li>
        `;

        categoriesTree.insertAdjacentHTML('beforeend', categoryHTML);
    });

    // Ponownie zainicjalizuj obsługę zdarzeń dla nowo utworzonych elementów
    reinitializeEventHandlers();

    // Załaduj pierwszą kategorię do formularza
    if (categoriesData.length > 0) {
        const firstCategory = document.querySelector('.category-item');
        if (firstCategory) {
            loadCategoryDetails(firstCategory);
        }
    }
}

/**
 * Ponownie zainicjalizuj obsługę zdarzeń
 */
function reinitializeEventHandlers() {
    initCategoryTree();
}

/**
 * Inicjalizacja funkcji zarządzania kategoriami
 */
function initCategoriesManagement() {
    // Obsługa wyszukiwania kategorii
    initCategorySearch();

    // Obsługa drzewa kategorii
    initCategoryTree();

    // Obsługa formularza edycji kategorii
    initCategoryForm();

    // Obsługa modalu dodawania kategorii
    initAddCategoryModal();

    // Obsługa selektora ikon
    initIconSelector();
}

/**
 * Inicjalizacja wyszukiwania kategorii
 */
function initCategorySearch() {
    const searchInput = document.getElementById('category-search');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const categoryItems = document.querySelectorAll('.category-item');

            categoryItems.forEach(item => {
                const categoryName = item.querySelector('.category-name').textContent.toLowerCase();
                const subcategoryItems = item.querySelectorAll('.subcategory-item');
                let hasMatchingSubcategory = false;

                // Sprawdzenie, czy któraś z podkategorii pasuje do wyszukiwania
                subcategoryItems.forEach(subitem => {
                    const subcategoryName = subitem.querySelector('.subcategory-name').textContent.toLowerCase();

                    if (subcategoryName.includes(searchTerm)) {
                        subitem.style.display = '';
                        hasMatchingSubcategory = true;
                    } else {
                        subitem.style.display = 'none';
                    }
                });

                // Wyświetlenie kategorii, jeśli nazwa kategorii pasuje lub ma pasującą podkategorię
                if (categoryName.includes(searchTerm) || hasMatchingSubcategory) {
                    item.style.display = '';
                    if (hasMatchingSubcategory) {
                        item.classList.add('active');
                    }
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }
}

/**
 * Inicjalizacja drzewa kategorii
 */
function initCategoryTree() {
    const categoryItems = document.querySelectorAll('.category-item-header');

    // Obsługa kliknięcia na kategorię
    categoryItems.forEach(item => {
        // Usuń stare event listenery
        item.replaceWith(item.cloneNode(true));
    });

    // Dodaj nowe event listenery
    document.querySelectorAll('.category-item-header').forEach(item => {
        item.addEventListener('click', function(e) {
            // Ignorowanie kliknięć na przyciski akcji
            if (e.target.closest('.category-actions')) {
                return;
            }

            const categoryItem = this.closest('.category-item');

            // Usunięcie aktywnej klasy z wszystkich kategorii
            document.querySelectorAll('.category-item').forEach(item => {
                item.classList.remove('active');
            });

            // Dodanie aktywnej klasy do klikniętej kategorii
            categoryItem.classList.add('active');

            // Załadowanie danych kategorii do formularza
            loadCategoryDetails(categoryItem);
        });
    });

    // Obsługa przycisków akcji
    initCategoryActions();
}

/**
 * Inicjalizacja akcji kategorii
 */
function initCategoryActions() {
    // Edycja kategorii
    document.querySelectorAll('.edit-category').forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const categoryItem = this.closest('.category-item');

            // Usunięcie aktywnej klasy z wszystkich kategorii
            document.querySelectorAll('.category-item').forEach(item => {
                item.classList.remove('active');
            });

            // Dodanie aktywnej klasy do klikniętej kategorii
            categoryItem.classList.add('active');

            // Załadowanie danych kategorii do formularza
            loadCategoryDetails(categoryItem);
        });
    });

    // Usuwanie kategorii
    document.querySelectorAll('.delete-category').forEach(button => {
        button.addEventListener('click', async function(e) {
            e.stopPropagation();
            const categoryItem = this.closest('.category-item');
            const categoryName = categoryItem.querySelector('.category-name').textContent;
            const categoryId = categoryItem.getAttribute('data-id');

            if (confirm(`Czy na pewno chcesz usunąć kategorię "${categoryName}" wraz z wszystkimi podkategoriami?`)) {
                await deleteCategory(categoryId);
            }
        });
    });

    // Dodawanie podkategorii
    document.querySelectorAll('.add-subcategory').forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const categoryItem = this.closest('.category-item');
            const categoryId = categoryItem.getAttribute('data-id');
            const categoryName = categoryItem.querySelector('.category-name').textContent;

            openAddSubcategoryModal(categoryId, categoryName);
        });
    });
}

/**
 * Załaduj szczegóły kategorii do formularza
 */
function loadCategoryDetails(categoryItem) {
    const categoryId = parseInt(categoryItem.getAttribute('data-id'));
    const category = categoriesData.find(cat => cat.id === categoryId);

    if (category) {
        document.getElementById('category-name-input').value = category.name || '';
        document.getElementById('category-description').value = category.description || '';
        document.getElementById('category-slug').value = category.slug || '';

        // Ustaw ikonę
        const iconElement = document.querySelector('.selected-icon i');
        if (iconElement && category.icon) {
            iconElement.className = category.icon;
        }

        // Ustaw kolor
        const colorInput = document.getElementById('category-color');
        if (colorInput && category.color) {
            colorInput.value = category.color;
        }
    }
}

/**
 * Wyczyść formularz kategorii
 */
function clearCategoryForm() {
    document.getElementById('category-name-input').value = '';
    document.getElementById('category-description').value = '';
    document.getElementById('category-slug').value = '';

    const iconElement = document.querySelector('.selected-icon i');
    if (iconElement) {
        iconElement.className = 'fas fa-folder';
    }

    const colorInput = document.getElementById('category-color');
    if (colorInput) {
        colorInput.value = '#6c757d';
    }
}

/**
 * Inicjalizacja formularza edycji kategorii
 */
function initCategoryForm() {
    const categoryForm = document.getElementById('category-form');
    const cancelButton = document.getElementById('cancel-btn');

    if (categoryForm) {
        categoryForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Pobranie danych z formularza
            const categoryName = document.getElementById('category-name-input').value;
            const categoryIcon = document.querySelector('.selected-icon i').className;
            const categoryDescription = document.getElementById('category-description').value;
            const categorySlug = document.getElementById('category-slug').value;
            const categoryColor = document.getElementById('category-color').value;

            // Walidacja formularza
            if (!categoryName.trim()) {
                showNotification('Nazwa kategorii jest wymagana', 'error');
                return;
            }

            // Pobierz ID aktywnej kategorii
            const activeCategory = document.querySelector('.category-item.active');
            if (activeCategory) {
                const categoryId = parseInt(activeCategory.getAttribute('data-id'));

                const categoryData = {
                    id: categoryId,
                    name: categoryName,
                    icon: categoryIcon,
                    description: categoryDescription,
                    slug: categorySlug,
                    color: categoryColor
                };

                await saveCategory(categoryData, true);
            }
        });
    }

    if (cancelButton) {
        cancelButton.addEventListener('click', function() {
            const activeCategory = document.querySelector('.category-item.active');
            if (activeCategory) {
                loadCategoryDetails(activeCategory);
            }
        });
    }
}

/**
 * Inicjalizacja modalu dodawania kategorii
 */
function initAddCategoryModal() {
    const addCategoryBtn = document.getElementById('add-category-btn');
    const addCategoryModal = document.getElementById('addCategoryModal');
    const addCategoryForm = document.getElementById('add-category-form');
    const closeButtons = addCategoryModal?.querySelectorAll('.modal-close, .modal-cancel');

    // Otwieranie modalu
    if (addCategoryBtn) {
        addCategoryBtn.addEventListener('click', function() {
            addCategoryModal.classList.add('show');
        });
    }

    // Zamykanie modalu
    closeButtons?.forEach(button => {
        button.addEventListener('click', function() {
            addCategoryModal.classList.remove('show');
        });
    });

    // Obsługa kliknięcia poza modalem
    window.addEventListener('click', function(e) {
        if (e.target === addCategoryModal) {
            addCategoryModal.classList.remove('show');
        }
    });

    // Obsługa formularza dodawania kategorii
    if (addCategoryForm) {
        addCategoryForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Pobranie danych z formularza
            const categoryName = document.getElementById('new-category-name').value;
            const categoryIcon = document.querySelector('#addCategoryModal .selected-icon i')?.className || 'fas fa-folder';
            const categoryDescription = document.getElementById('new-category-description').value;

            // Walidacja formularza
            if (!categoryName.trim()) {
                showNotification('Nazwa kategorii jest wymagana', 'error');
                return;
            }

            const categoryData = {
                name: categoryName,
                icon: categoryIcon,
                description: categoryDescription
            };

            const success = await saveCategory(categoryData, false);
            if (success) {
                addCategoryModal.classList.remove('show');
                addCategoryForm.reset();
            }
        });
    }
}

/**
 * Inicjalizacja selektora ikon
 */
function initIconSelector() {
    // Implementacja selektora ikon
    const iconButtons = document.querySelectorAll('.icon-option');

    iconButtons.forEach(button => {
        button.addEventListener('click', function() {
            const iconClass = this.querySelector('i').className;
            const selectedIcon = this.closest('.modal, .form-section').querySelector('.selected-icon i');

            if (selectedIcon) {
                selectedIcon.className = iconClass;
            }

            // Usuń aktywną klasę z wszystkich ikon
            iconButtons.forEach(btn => btn.classList.remove('active'));
            // Dodaj aktywną klasę do klikniętej ikony
            this.classList.add('active');
        });
    });
}

/**
 * Otwórz modal dodawania podkategorii
 */
function openAddSubcategoryModal(categoryId, categoryName) {
    // Implementacja modalu podkategorii
    showNotification(`Dodawanie podkategorii dla kategorii: ${categoryName}`, 'info');
}

/**
 * Pokaż/ukryj loading
 */
function showLoading(show) {
    const loadingElements = document.querySelectorAll('.btn');
    loadingElements.forEach(btn => {
        if (show) {
            btn.disabled = true;
            btn.style.opacity = '0.6';
        } else {
            btn.disabled = false;
            btn.style.opacity = '1';
        }
    });
}

/**
 * Pokaż powiadomienie
 */
function showNotification(message, type = 'info') {
    // Utwórz element powiadomienia
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
        <span>${message}</span>
        <button class="notification-close">&times;</button>
    `;

    // Dodaj do kontenera powiadomień
    let container = document.querySelector('.notifications');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notifications';
        document.body.appendChild(container);
    }

    container.appendChild(notification);

    // Obsługa zamykania
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        notification.remove();
    });

    // Auto-ukryj po 5 sekundach
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
