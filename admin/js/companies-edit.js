/**
 * JavaScript dla edycji firm
 * Żyrardów Poleca - Panel Administracyjny
 */

document.addEventListener('DOMContentLoaded', function() {
    // Pobierz ID firmy z URL
    const urlParams = new URLSearchParams(window.location.search);
    const companyId = urlParams.get('id');
    
    if (!companyId) {
        showNotification('Błąd: Brak ID firmy', 'error');
        window.location.href = 'companies.html';
        return;
    }
    
    // Ustaw ID firmy w formularzu
    document.getElementById('company-id').value = companyId;
    
    // Załaduj dane firmy
    loadCompanyData(companyId);
    
    // Obsługa przycisków zapisz
    document.getElementById('quickSaveBtn').addEventListener('click', saveCompany);
    
    // Obsługa przycisków "Zapisz zmiany" w każdej zakładce
    document.querySelectorAll('.save-tab').forEach(btn => {
        btn.addEventListener('click', saveCompany);
    });
    
    // Obsługa nawigacji między zakładkami
    setupTabNavigation();
});

/**
 * Załaduj dane firmy do formularza
 */
async function loadCompanyData(companyId) {
    try {
        showLoading(true);
        
        const response = await fetch(`api/companies.php?path=get&id=${companyId}`);
        const result = await response.json();
        
        if (result.success && result.data) {
            const company = result.data;
            
            // Wypełnij formularz danymi firmy
            document.getElementById('company-name').value = company.name || '';
            document.getElementById('company-category').value = company.categoryId || '';
            document.getElementById('company-description').value = company.description || '';
            document.getElementById('company-address').value = company.address || '';
            document.getElementById('company-postal-code').value = company.postalCode || '';
            document.getElementById('company-city').value = company.city || 'Żyrardów';
            document.getElementById('company-phone').value = company.phone || '';
            document.getElementById('company-email').value = company.email || '';
            document.getElementById('company-website').value = company.website || '';
            document.getElementById('company-top-position').value = company.topPosition || '';
            
            // Ustaw status
            if (company.status) {
                const statusRadio = document.querySelector(`input[name="company-status"][value="${company.status}"]`);
                if (statusRadio) {
                    statusRadio.checked = true;
                }
            }
            
            // Załaduj kategorie
            await loadCategories();
            
            // Ustaw kategorię po załadowaniu
            if (company.categoryId) {
                document.getElementById('company-category').value = company.categoryId;
            }
            
            showNotification('Dane firmy załadowane pomyślnie', 'success');
        } else {
            throw new Error(result.message || 'Błąd ładowania danych firmy');
        }
    } catch (error) {
        console.error('Błąd ładowania firmy:', error);
        showNotification('Błąd ładowania danych firmy: ' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Załaduj kategorie do selecta
 */
async function loadCategories() {
    try {
        const response = await fetch('api/companies.php?path=categories');
        const result = await response.json();
        
        if (result.success && result.data) {
            const categorySelect = document.getElementById('company-category');
            
            // Wyczyść opcje (zostaw pierwszą "Wybierz kategorię")
            while (categorySelect.children.length > 1) {
                categorySelect.removeChild(categorySelect.lastChild);
            }
            
            // Dodaj kategorie
            result.data.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categorySelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Błąd ładowania kategorii:', error);
    }
}

/**
 * Zapisz zmiany w firmie
 */
async function saveCompany() {
    try {
        showLoading(true);
        
        // Pobierz dane z formularza
        const formData = {
            id: document.getElementById('company-id').value,
            name: document.getElementById('company-name').value,
            categoryId: document.getElementById('company-category').value,
            description: document.getElementById('company-description').value,
            address: document.getElementById('company-address').value,
            postalCode: document.getElementById('company-postal-code').value,
            city: document.getElementById('company-city').value,
            phone: document.getElementById('company-phone').value,
            email: document.getElementById('company-email').value,
            website: document.getElementById('company-website').value,
            topPosition: document.getElementById('company-top-position').value || null,
            status: document.querySelector('input[name="company-status"]:checked')?.value || 'pending'
        };
        
        // Walidacja
        if (!formData.name.trim()) {
            throw new Error('Nazwa firmy jest wymagana');
        }
        
        if (!formData.categoryId) {
            throw new Error('Kategoria jest wymagana');
        }
        
        if (!formData.description.trim()) {
            throw new Error('Opis firmy jest wymagany');
        }
        
        // Wyślij dane
        const response = await fetch('api/companies.php?path=update', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('Firma została zaktualizowana pomyślnie', 'success');
        } else {
            throw new Error(result.message || 'Błąd aktualizacji firmy');
        }
        
    } catch (error) {
        console.error('Błąd zapisywania firmy:', error);
        showNotification('Błąd zapisywania: ' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Konfiguracja nawigacji między zakładkami
 */
function setupTabNavigation() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    const nextBtns = document.querySelectorAll('.next-tab');
    const prevBtns = document.querySelectorAll('.prev-tab');
    
    // Obsługa kliknięć w zakładki
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const targetTab = btn.dataset.tab;
            switchTab(targetTab);
        });
    });
    
    // Obsługa przycisków "Dalej"
    nextBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const nextTab = btn.dataset.next;
            switchTab(nextTab);
        });
    });
    
    // Obsługa przycisków "Wstecz"
    prevBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const prevTab = btn.dataset.prev;
            switchTab(prevTab);
        });
    });
}

/**
 * Przełącz zakładkę
 */
function switchTab(tabId) {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    // Usuń aktywne klasy
    tabBtns.forEach(btn => btn.classList.remove('active'));
    tabContents.forEach(content => content.classList.remove('active'));
    
    // Dodaj aktywne klasy
    const targetBtn = document.querySelector(`[data-tab="${tabId}"]`);
    const targetContent = document.getElementById(tabId);
    
    if (targetBtn && targetContent) {
        targetBtn.classList.add('active');
        targetContent.classList.add('active');
    }
}

/**
 * Pokaż/ukryj loading
 */
function showLoading(show) {
    const loadingElements = document.querySelectorAll('.btn');
    loadingElements.forEach(btn => {
        if (show) {
            btn.disabled = true;
            btn.style.opacity = '0.6';
        } else {
            btn.disabled = false;
            btn.style.opacity = '1';
        }
    });
}

/**
 * Pokaż powiadomienie
 */
function showNotification(message, type = 'info') {
    // Utwórz element powiadomienia
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
        <span>${message}</span>
        <button class="notification-close">&times;</button>
    `;
    
    // Dodaj do kontenera powiadomień
    let container = document.querySelector('.notifications');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notifications';
        document.body.appendChild(container);
    }
    
    container.appendChild(notification);
    
    // Obsługa zamykania
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        notification.remove();
    });
    
    // Auto-ukryj po 5 sekundach
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
