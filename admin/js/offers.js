/**
 * Panel Administracyjny - Żyrardów Poleca
 * JavaScript dla zarządzania ofertami
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja funkcji zarządzania ofertami
    initOffersManagement();
});

/**
 * Inicjalizacja funkcji zarządzania ofertami
 */
function initOffersManagement() {
    // Obsługa wyszukiwania
    initSearch();

    // Obsługa filtrów
    initFilters();

    // Obsługa zaznaczania ofert
    initSelections();

    // Obsługa akcji na ofertach
    initActions();

    // Obsługa paginacji
    initPagination();

    // Obsługa modalu podglądu oferty
    initOfferPreviewModal();

    // Inicjalizuj HugeRTE editor (jeśli jesteśmy na stronie dodawania/edycji)
    if (document.getElementById('offer-description')) {
        initHugeRTE();
    }
}

/**
 * Inicjalizacja wyszukiwania
 */
function initSearch() {
    const searchInput = document.getElementById('offer-search');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('#offers-table-body tr');

            tableRows.forEach(row => {
                const offerTitle = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                const companyName = row.querySelector('td:nth-child(4)').textContent.toLowerCase();

                if (offerTitle.includes(searchTerm) || companyName.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
}

/**
 * Inicjalizacja filtrów
 */
function initFilters() {
    const companyFilter = document.getElementById('company-filter');
    const statusFilter = document.getElementById('status-filter');
    const typeFilter = document.getElementById('type-filter');
    const resetFiltersBtn = document.getElementById('reset-filters');

    // Funkcja do filtrowania ofert
    function filterOffers() {
        const companyValue = companyFilter.value;
        const statusValue = statusFilter.value;
        const typeValue = typeFilter.value;
        const tableRows = document.querySelectorAll('#offers-table-body tr');

        tableRows.forEach(row => {
            const company = row.querySelector('td:nth-child(4)').textContent;
            const statusElement = row.querySelector('.status-badge');
            const status = statusElement ? statusElement.classList.contains('active') ? 'active' :
                           statusElement.classList.contains('expired') ? 'expired' : 'scheduled' : '';
            const typeElement = row.querySelector('.offer-type');
            const type = typeElement ? typeElement.classList.contains('promotion') ? 'promotion' :
                         typeElement.classList.contains('sale') ? 'sale' :
                         typeElement.classList.contains('new') ? 'new' : 'event' : '';

            let showRow = true;

            // Filtrowanie po firmie
            if (companyValue) {
                const companyMatch = company === document.querySelector(`#company-filter option[value="${companyValue}"]`).textContent;
                if (!companyMatch) {
                    showRow = false;
                }
            }

            // Filtrowanie po statusie
            if (statusValue && status !== statusValue) {
                showRow = false;
            }

            // Filtrowanie po typie oferty
            if (typeValue && type !== typeValue) {
                showRow = false;
            }

            row.style.display = showRow ? '' : 'none';
        });
    }

    // Dodanie nasłuchiwania zdarzeń
    if (companyFilter) {
        companyFilter.addEventListener('change', filterOffers);
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', filterOffers);
    }

    if (typeFilter) {
        typeFilter.addEventListener('change', filterOffers);
    }

    // Resetowanie filtrów
    if (resetFiltersBtn) {
        resetFiltersBtn.addEventListener('click', function() {
            if (companyFilter) companyFilter.value = '';
            if (statusFilter) statusFilter.value = '';
            if (typeFilter) typeFilter.value = '';

            const tableRows = document.querySelectorAll('#offers-table-body tr');
            tableRows.forEach(row => {
                row.style.display = '';
            });
        });
    }
}

/**
 * Inicjalizacja zaznaczania ofert
 */
function initSelections() {
    const selectAll = document.getElementById('select-all');
    const offerCheckboxes = document.querySelectorAll('.offer-select');

    if (selectAll) {
        selectAll.addEventListener('change', function() {
            offerCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }

    // Aktualizacja stanu "zaznacz wszystkie" gdy zmienia się stan pojedynczych checkboxów
    offerCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const allChecked = Array.from(offerCheckboxes).every(cb => cb.checked);
            const anyChecked = Array.from(offerCheckboxes).some(cb => cb.checked);

            if (selectAll) {
                selectAll.checked = allChecked;
                selectAll.indeterminate = anyChecked && !allChecked;
            }
        });
    });
}

/**
 * Inicjalizacja akcji na ofertach
 */
function initActions() {
    // Obsługa akcji edycji, podglądu i usuwania
    const actionButtons = document.querySelectorAll('.btn-icon');

    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const row = this.closest('tr');
            const offerTitle = row.querySelector('td:nth-child(3)').textContent;

            if (this.classList.contains('delete')) {
                if (confirm(`Czy na pewno chcesz usunąć ofertę "${offerTitle}"?`)) {
                    // W rzeczywistej implementacji tutaj byłoby usuwanie oferty
                    row.remove();
                    showNotification(`Oferta "${offerTitle}" została usunięta.`, 'success');
                }
            } else if (this.title === 'Edytuj') {
                // Pobierz ID oferty z wiersza (dodamy data-id do wierszy)
                const offerId = row.dataset.offerId || row.rowIndex;
                editOffer(offerId, row);
            } else if (this.title === 'Podgląd') {
                // Otwórz modal z podglądem oferty
                showOfferPreview(row);
            }
        });
    });

    // Obsługa akcji zbiorczych
    const applyBulkAction = document.getElementById('apply-bulk-action');
    const bulkAction = document.getElementById('bulk-action');

    if (applyBulkAction && bulkAction) {
        applyBulkAction.addEventListener('click', function() {
            const selectedCheckboxes = document.querySelectorAll('.offer-select:checked');
            const action = bulkAction.value;

            if (!action) {
                alert('Wybierz akcję do wykonania.');
                return;
            }

            if (selectedCheckboxes.length === 0) {
                alert('Zaznacz przynajmniej jedną ofertę.');
                return;
            }

            const selectedRows = Array.from(selectedCheckboxes).map(checkbox => checkbox.closest('tr'));
            const offerTitles = selectedRows.map(row => row.querySelector('td:nth-child(3)').textContent);

            switch (action) {
                case 'activate':
                    if (confirm(`Czy na pewno chcesz aktywować ${selectedRows.length} ofert?`)) {
                        selectedRows.forEach(row => {
                            const statusBadge = row.querySelector('.status-badge');
                            statusBadge.className = 'status-badge active';
                            statusBadge.textContent = 'Aktywna';
                        });
                        showNotification(`Aktywowano ${selectedRows.length} ofert.`, 'success');
                    }
                    break;
                case 'deactivate':
                    if (confirm(`Czy na pewno chcesz dezaktywować ${selectedRows.length} ofert?`)) {
                        selectedRows.forEach(row => {
                            const statusBadge = row.querySelector('.status-badge');
                            statusBadge.className = 'status-badge expired';
                            statusBadge.textContent = 'Wygasła';
                        });
                        showNotification(`Dezaktywowano ${selectedRows.length} ofert.`, 'success');
                    }
                    break;
                case 'delete':
                    if (confirm(`Czy na pewno chcesz usunąć ${selectedRows.length} ofert?`)) {
                        selectedRows.forEach(row => row.remove());
                        showNotification(`Usunięto ${selectedRows.length} ofert.`, 'success');
                    }
                    break;
            }

            // Resetowanie zaznaczenia
            const selectAll = document.getElementById('select-all');
            if (selectAll) {
                selectAll.checked = false;
                selectAll.indeterminate = false;
            }

            // Resetowanie akcji
            bulkAction.value = '';
        });
    }
}

/**
 * Inicjalizacja paginacji
 */
function initPagination() {
    const paginationButtons = document.querySelectorAll('.btn-page');

    paginationButtons.forEach(button => {
        if (!button.disabled) {
            button.addEventListener('click', function() {
                // Usunięcie aktywnej klasy z wszystkich przycisków
                paginationButtons.forEach(btn => btn.classList.remove('active'));

                // Dodanie aktywnej klasy do klikniętego przycisku
                this.classList.add('active');

                // W rzeczywistej implementacji tutaj byłoby ładowanie odpowiedniej strony
                console.log('Przejście do strony:', this.textContent);
            });
        }
    });
}

/**
 * Inicjalizacja modalu podglądu oferty
 */
function initOfferPreviewModal() {
    const modal = document.getElementById('offerPreviewModal');
    const closeButton = modal.querySelector('.modal-close');

    // Zamykanie modalu po kliknięciu przycisku zamknięcia
    if (closeButton) {
        closeButton.addEventListener('click', function() {
            modal.classList.remove('show');
        });
    }

    // Zamykanie modalu po kliknięciu poza jego zawartością
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.classList.remove('show');
        }
    });
}

/**
 * Wyświetlanie podglądu oferty
 * @param {HTMLElement} row - Wiersz tabeli z danymi oferty
 */
function showOfferPreview(row) {
    const modal = document.getElementById('offerPreviewModal');

    if (modal) {
        const offerImage = row.querySelector('.offer-image').src;
        const offerTitle = row.querySelector('td:nth-child(3)').textContent;
        const companyName = row.querySelector('td:nth-child(4)').textContent;
        const offerTypeElement = row.querySelector('.offer-type');
        const offerType = offerTypeElement.textContent;
        const offerTypeClass = Array.from(offerTypeElement.classList).find(cls => cls !== 'offer-type');
        const validUntil = row.querySelector('td:nth-child(6)').textContent;

        // Dane firm (w rzeczywistej implementacji byłyby pobierane z API)
        const companies = {
            'Restauracja Pod Akacjami': {
                logo: '../images/business-logo1.jpg',
                address: 'ul. Przykładowa 1, Żyrardów',
                phone: '+48 ***********'
            },
            'Salon Fryzjerski Bella': {
                logo: '../images/business-logo2.jpg',
                address: 'ul. Przykładowa 2, Żyrardów',
                phone: '+48 ***********'
            },
            'Sklep Sportowy Active': {
                logo: '../images/business-logo3.jpg',
                address: 'ul. Przykładowa 3, Żyrardów',
                phone: '+**************'
            },
            'Kancelaria Prawna Paragraf': {
                logo: '../images/business-logo1.jpg',
                address: 'ul. Przykładowa 4, Żyrardów',
                phone: '+**************'
            }
        };

        // Przykładowe opisy ofert (w rzeczywistej implementacji byłyby pobierane z API)
        const descriptions = {
            'Dwie pizze w cenie jednej': 'Tylko w ten weekend, kup jedną dużą pizzę, a drugą otrzymasz gratis! Oferta obowiązuje na wszystkie pizze z naszego menu. Nie przegap tej okazji!',
            'Rabat 30% na strzyżenie i koloryzację': 'Skorzystaj z wyjątkowej oferty i zadbaj o swoje włosy! Rabat 30% na strzyżenie i koloryzację. Oferta ważna do końca miesiąca.',
            'Nowa kolekcja odzieży sportowej': 'Już jest! Nowa kolekcja odzieży sportowej na sezon jesień/zima. Przyjdź i wybierz coś dla siebie z szerokiej gamy produktów.',
            'Bezpłatne konsultacje prawne': 'Potrzebujesz porady prawnej? Skorzystaj z bezpłatnych konsultacji w naszej kancelarii. Wystarczy umówić się na spotkanie telefonicznie.'
        };

        // Aktualizacja danych w modalu
        modal.querySelector('#preview-image').src = offerImage;
        modal.querySelector('#preview-type').textContent = offerType;
        modal.querySelector('#preview-type').className = `offer-type ${offerTypeClass}`;
        modal.querySelector('#preview-title').textContent = offerTitle;
        modal.querySelector('#preview-company').textContent = companyName;
        modal.querySelector('#preview-validity').textContent = validUntil;

        // Dane firmy
        if (companies[companyName]) {
            modal.querySelector('#preview-logo').src = companies[companyName].logo;
            modal.querySelector('#preview-address').textContent = companies[companyName].address;
            modal.querySelector('#preview-phone').textContent = companies[companyName].phone;
        }

        // Opis oferty
        if (descriptions[offerTitle]) {
            modal.querySelector('#preview-description').innerHTML = `<p>${descriptions[offerTitle]}</p>`;
        } else {
            modal.querySelector('#preview-description').innerHTML = '<p>Brak szczegółowego opisu oferty.</p>';
        }

        // Wyświetlenie modalu
        modal.classList.add('show');
    }
}

/**
 * Funkcja do wyświetlania powiadomień
 * @param {string} message - Treść powiadomienia
 * @param {string} type - Typ powiadomienia (success, info, warning, error)
 */
function showNotification(message, type = 'info') {
    // Tworzenie elementu powiadomienia
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'warning' ? 'fa-exclamation-triangle' : type === 'error' ? 'fa-times-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;

    // Dodanie do dokumentu
    document.body.appendChild(notification);

    // Dodanie nasłuchiwania zdarzenia do przycisku zamykania
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.classList.add('hiding');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });

    // Automatyczne usunięcie po 5 sekundach
    setTimeout(() => {
        notification.classList.add('hiding');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);

    // Pokazanie powiadomienia z animacją
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
}

/**
 * Funkcja do edycji oferty
 * @param {string|number} offerId - ID oferty do edycji
 * @param {HTMLElement} row - Wiersz tabeli z danymi oferty
 */
function editOffer(offerId, row) {
    // Pobierz dane oferty z wiersza
    const offerData = {
        id: offerId,
        image: row.querySelector('.offer-image').src,
        title: row.querySelector('td:nth-child(3)').textContent,
        company: row.querySelector('td:nth-child(4)').textContent,
        type: row.querySelector('.offer-type').textContent,
        validUntil: row.querySelector('td:nth-child(6)').textContent,
        status: row.querySelector('.status-badge').textContent,
        views: row.querySelector('td:nth-child(8)').textContent
    };

    // Zapisz dane oferty do localStorage dla strony edycji
    localStorage.setItem('editOfferData', JSON.stringify(offerData));

    // Przekieruj do strony edycji z parametrem ID
    window.location.href = `offers-add.html?edit=${offerId}`;
}

/**
 * Funkcja do ładowania danych oferty na stronie edycji
 */
function loadOfferForEdit() {
    // Sprawdź czy jesteśmy na stronie edycji
    const urlParams = new URLSearchParams(window.location.search);
    const editId = urlParams.get('edit');

    if (editId) {
        // Pobierz dane oferty z localStorage
        const offerData = JSON.parse(localStorage.getItem('editOfferData') || '{}');

        if (offerData.id == editId) {
            // Wypełnij formularz danymi oferty
            fillEditForm(offerData);

            // Zmień tytuł strony
            const pageTitle = document.querySelector('h1');
            if (pageTitle) {
                pageTitle.innerHTML = '<i class="fas fa-edit"></i> Edytuj ofertę';
            }

            // Zmień tekst przycisku
            const submitBtn = document.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-save"></i> Zapisz zmiany';
            }
        }
    }
}

/**
 * Funkcja do wypełniania formularza danymi oferty
 * @param {Object} offerData - Dane oferty
 */
function fillEditForm(offerData) {
    // Wypełnij pola formularza (nazwy pól mogą się różnić w zależności od implementacji)
    const titleField = document.getElementById('offer-title') || document.querySelector('input[name="title"]');
    if (titleField) titleField.value = offerData.title;

    const companyField = document.getElementById('offer-company') || document.querySelector('select[name="company"]');
    if (companyField) {
        // Znajdź opcję z odpowiednią firmą
        const options = companyField.querySelectorAll('option');
        options.forEach(option => {
            if (option.textContent === offerData.company) {
                option.selected = true;
            }
        });
    }

    const typeField = document.getElementById('offer-type') || document.querySelector('select[name="type"]');
    if (typeField) {
        const typeMap = {
            'Promocja': 'promotion',
            'Wyprzedaż': 'sale',
            'Nowość': 'new',
            'Wydarzenie': 'event'
        };
        typeField.value = typeMap[offerData.type] || 'promotion';
    }

    const validUntilField = document.getElementById('offer-valid-until') || document.querySelector('input[name="validUntil"]');
    if (validUntilField) {
        // Konwertuj datę do formatu YYYY-MM-DD
        const dateParts = offerData.validUntil.split('.');
        if (dateParts.length === 3) {
            const formattedDate = `${dateParts[2]}-${dateParts[1].padStart(2, '0')}-${dateParts[0].padStart(2, '0')}`;
            validUntilField.value = formattedDate;
        }
    }

    const statusField = document.getElementById('offer-status') || document.querySelector('select[name="status"]');
    if (statusField) {
        const statusMap = {
            'Aktywna': 'active',
            'Wygasła': 'expired',
            'Zaplanowana': 'scheduled'
        };
        statusField.value = statusMap[offerData.status] || 'active';
    }

    // Dodaj ukryte pole z ID oferty
    let idField = document.getElementById('offer-id');
    if (!idField) {
        idField = document.createElement('input');
        idField.type = 'hidden';
        idField.id = 'offer-id';
        idField.name = 'id';
        const form = document.querySelector('form');
        if (form) form.appendChild(idField);
    }
    idField.value = offerData.id;
}

/**
 * Inicjalizuj HugeRTE editor
 */
function initHugeRTE() {
    if (typeof hugerte !== 'undefined') {
        hugerte.init({
            selector: '#offer-description',
            height: 300,
            menubar: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image | removeformat | code | help',
            content_style: 'body { font-family: Montserrat, Arial, sans-serif; font-size: 14px; }',
            branding: false,
            promotion: false
        });
    }
}

// Automatyczne ładowanie danych oferty jeśli jesteśmy na stronie edycji
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadOfferForEdit);
} else {
    loadOfferForEdit();
}
