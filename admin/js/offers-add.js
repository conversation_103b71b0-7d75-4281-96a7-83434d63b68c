/**
 * Panel Administracyjny - Żyrardów Poleca
 * JavaScript dla dodawania/edycji ofert
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja formularza dodawania/edycji oferty
    initOfferForm();
});

/**
 * Inicjalizacja formularza dodawania/edycji oferty
 */
function initOfferForm() {
    // Obsługa przesyłania formularza
    initFormSubmission();
    
    // Obsługa przycisku anuluj
    initCancelButton();
    
    // Inicjalizacja HugeRTE editor
    initHugeRTE();
    
    // Sprawdzenie czy jesteśmy w trybie edycji
    checkEditMode();
    
    // Załaduj firmy do selecta
    loadCompaniesSelect();
}

/**
 * Inicjalizacja przesyłania formularza
 */
function initFormSubmission() {
    const offerForm = document.getElementById('addOfferForm');
    
    if (offerForm) {
        offerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Walidacja formularza
            if (!validateForm()) {
                return;
            }
            
            // Zapisz ofertę
            saveOffer();
        });
    }
}

/**
 * Zapisz ofertę do localStorage
 */
function saveOffer() {
    try {
        // Pobierz dane z formularza
        const formData = getOfferFormData();
        
        // Pobierz istniejące dane ofert
        const offersData = JSON.parse(localStorage.getItem('offers_data') || '{"offers":[]}');
        
        // Sprawdź czy to edycja czy nowa oferta
        const urlParams = new URLSearchParams(window.location.search);
        const editId = urlParams.get('edit');
        
        if (editId) {
            // Edycja istniejącej oferty
            const offerIndex = offersData.offers.findIndex(o => o.id == editId);
            if (offerIndex !== -1) {
                offersData.offers[offerIndex] = { ...offersData.offers[offerIndex], ...formData };
                showNotification('Oferta została zaktualizowana!', 'success');
            }
        } else {
            // Dodanie nowej oferty
            const newId = Math.max(...offersData.offers.map(o => o.id || 0), 0) + 1;
            formData.id = newId;
            formData.dateAdded = new Date().toISOString().split('T')[0];
            formData.views = 0;
            offersData.offers.push(formData);
            showNotification('Oferta została dodana!', 'success');
        }
        
        // Zapisz dane
        localStorage.setItem('offers_data', JSON.stringify(offersData));
        
        // Synchronizuj z frontend
        syncOffersWithFrontend();
        
        // Przekierowanie po 2 sekundach
        setTimeout(() => {
            window.location.href = 'offers.html';
        }, 2000);
        
    } catch (error) {
        console.error('Błąd podczas zapisywania oferty:', error);
        showNotification('Wystąpił błąd podczas zapisywania oferty', 'error');
    }
}

/**
 * Pobierz dane z formularza oferty
 */
function getOfferFormData() {
    const formData = {};
    
    // Podstawowe informacje
    formData.title = document.getElementById('offer-title')?.value || '';
    formData.companyId = document.getElementById('offer-company')?.value || '';
    formData.category = document.getElementById('offer-category')?.value || '';
    formData.description = document.getElementById('offer-description')?.value || '';
    
    // Szczegóły oferty
    formData.regularPrice = parseFloat(document.getElementById('offer-price')?.value) || 0;
    formData.salePrice = parseFloat(document.getElementById('offer-sale-price')?.value) || 0;
    formData.startDate = document.getElementById('offer-start-date')?.value || '';
    formData.endDate = document.getElementById('offer-end-date')?.value || '';
    formData.terms = document.getElementById('offer-terms')?.value || '';
    
    // Status i wyróżnienie
    const statusRadio = document.querySelector('input[name="offer-status"]:checked');
    formData.status = statusRadio?.value || 'draft';
    formData.featured = document.getElementById('offer-featured')?.checked || false;
    
    // Oblicz oszczędności
    if (formData.regularPrice > 0 && formData.salePrice > 0) {
        formData.savings = formData.regularPrice - formData.salePrice;
        formData.discountPercent = Math.round((formData.savings / formData.regularPrice) * 100);
    }
    
    // Pobierz nazwę firmy
    const companySelect = document.getElementById('offer-company');
    if (companySelect) {
        const selectedOption = companySelect.options[companySelect.selectedIndex];
        formData.companyName = selectedOption?.text || '';
    }
    
    // Symulacja uploadu zdjęcia
    const imageInput = document.getElementById('offer-image');
    if (imageInput?.files?.[0]) {
        formData.image = `../images/offer-${Math.floor(Math.random() * 5) + 1}.jpg`;
    }
    
    return formData;
}

/**
 * Walidacja formularza
 */
function validateForm() {
    const requiredFields = document.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    // Walidacja dat
    const startDate = document.getElementById('offer-start-date')?.value;
    const endDate = document.getElementById('offer-end-date')?.value;
    
    if (startDate && endDate && new Date(startDate) >= new Date(endDate)) {
        showNotification('Data zakończenia musi być późniejsza niż data rozpoczęcia', 'error');
        isValid = false;
    }
    
    // Walidacja cen
    const regularPrice = parseFloat(document.getElementById('offer-price')?.value) || 0;
    const salePrice = parseFloat(document.getElementById('offer-sale-price')?.value) || 0;
    
    if (regularPrice > 0 && salePrice >= regularPrice) {
        showNotification('Cena promocyjna musi być niższa niż cena regularna', 'error');
        isValid = false;
    }
    
    return isValid;
}

/**
 * Załaduj firmy do selecta
 */
function loadCompaniesSelect() {
    const companySelect = document.getElementById('offer-company');
    if (!companySelect) return;
    
    try {
        const companiesData = JSON.parse(localStorage.getItem('companies_data') || '{"companies":[]}');
        const activeCompanies = companiesData.companies.filter(c => c.status === 'active');
        
        // Wyczyść opcje (zostaw pierwszą "Wybierz firmę")
        while (companySelect.children.length > 1) {
            companySelect.removeChild(companySelect.lastChild);
        }
        
        // Dodaj firmy
        activeCompanies.forEach(company => {
            const option = document.createElement('option');
            option.value = company.id;
            option.textContent = company.name;
            companySelect.appendChild(option);
        });
        
    } catch (error) {
        console.error('Błąd podczas ładowania firm:', error);
    }
}

/**
 * Synchronizuj oferty z frontend
 */
function syncOffersWithFrontend() {
    try {
        const offersData = JSON.parse(localStorage.getItem('offers_data') || '{"offers":[]}');
        const companiesData = JSON.parse(localStorage.getItem('companies_data') || '{"companies":[]}');
        
        // Przygotuj dane dla frontend
        const frontendOffers = offersData.offers.filter(o => o.status === 'active').map(offer => {
            const company = companiesData.companies.find(c => c.id == offer.companyId);
            return {
                id: offer.id,
                title: offer.title,
                description: offer.description,
                regularPrice: offer.regularPrice,
                salePrice: offer.salePrice,
                savings: offer.savings,
                discountPercent: offer.discountPercent,
                startDate: offer.startDate,
                endDate: offer.endDate,
                terms: offer.terms,
                featured: offer.featured,
                image: offer.image || '../images/offer-1.jpg',
                company: {
                    id: company?.id || offer.companyId,
                    name: company?.name || offer.companyName,
                    logo: company?.logo || '../images/business-logo1.jpg'
                }
            };
        });
        
        // Zapisz dane dla frontend
        localStorage.setItem('frontend_offers', JSON.stringify(frontendOffers));
        
        // Wyślij event o aktualizacji
        window.dispatchEvent(new CustomEvent('offersUpdated', {
            detail: { offers: frontendOffers }
        }));
        
    } catch (error) {
        console.error('Błąd podczas synchronizacji ofert z frontend:', error);
    }
}

/**
 * Obsługa przycisku anuluj
 */
function initCancelButton() {
    const cancelBtn = document.getElementById('cancelBtn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            if (confirm('Czy na pewno chcesz anulować? Niezapisane zmiany zostaną utracone.')) {
                window.location.href = 'offers.html';
            }
        });
    }
}

/**
 * Inicjalizacja HugeRTE editor
 */
function initHugeRTE() {
    if (typeof hugerte !== 'undefined') {
        hugerte.init({
            selector: '#offer-description',
            height: 300,
            menubar: false,
            plugins: 'lists link',
            toolbar: 'undo redo | bold italic | bullist numlist | link',
            language: 'pl'
        });
    }
}

/**
 * Sprawdzenie czy jesteśmy w trybie edycji
 */
function checkEditMode() {
    const urlParams = new URLSearchParams(window.location.search);
    const editId = urlParams.get('edit');
    
    if (editId) {
        loadOfferForEdit(editId);
    }
}

/**
 * Załaduj ofertę do edycji
 */
function loadOfferForEdit(offerId) {
    try {
        const offersData = JSON.parse(localStorage.getItem('offers_data') || '{"offers":[]}');
        const offer = offersData.offers.find(o => o.id == offerId);
        
        if (offer) {
            // Wypełnij formularz danymi oferty
            fillOfferForm(offer);
            
            // Zmień tytuł strony
            const pageTitle = document.querySelector('h1');
            if (pageTitle) {
                pageTitle.innerHTML = '<i class="fas fa-edit"></i> Edytuj ofertę';
            }
            
            // Zmień tekst przycisku
            const submitBtn = document.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-save"></i> Zapisz zmiany';
            }
        }
    } catch (error) {
        console.error('Błąd podczas ładowania oferty do edycji:', error);
    }
}

/**
 * Wypełnij formularz danymi oferty
 */
function fillOfferForm(offer) {
    // Podstawowe informacje
    if (document.getElementById('offer-title')) {
        document.getElementById('offer-title').value = offer.title || '';
    }
    if (document.getElementById('offer-company')) {
        document.getElementById('offer-company').value = offer.companyId || '';
    }
    if (document.getElementById('offer-category')) {
        document.getElementById('offer-category').value = offer.category || '';
    }
    if (document.getElementById('offer-description')) {
        document.getElementById('offer-description').value = offer.description || '';
    }
    
    // Szczegóły oferty
    if (document.getElementById('offer-price')) {
        document.getElementById('offer-price').value = offer.regularPrice || '';
    }
    if (document.getElementById('offer-sale-price')) {
        document.getElementById('offer-sale-price').value = offer.salePrice || '';
    }
    if (document.getElementById('offer-start-date')) {
        document.getElementById('offer-start-date').value = offer.startDate || '';
    }
    if (document.getElementById('offer-end-date')) {
        document.getElementById('offer-end-date').value = offer.endDate || '';
    }
    if (document.getElementById('offer-terms')) {
        document.getElementById('offer-terms').value = offer.terms || '';
    }
    
    // Status
    const statusRadio = document.querySelector(`input[name="offer-status"][value="${offer.status}"]`);
    if (statusRadio) {
        statusRadio.checked = true;
    }
    
    // Wyróżnienie
    if (document.getElementById('offer-featured')) {
        document.getElementById('offer-featured').checked = offer.featured || false;
    }
}

/**
 * Wyświetl powiadomienie
 */
function showNotification(message, type = 'info') {
    // Usuń istniejące powiadomienia
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(n => n.remove());
    
    // Utwórz nowe powiadomienie
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">&times;</button>
    `;
    
    // Dodaj style
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
        color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
        border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
        border-radius: 8px;
        padding: 15px;
        z-index: 10000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Obsługa zamykania
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => notification.remove());
    
    // Auto-usuwanie po 5 sekundach
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
