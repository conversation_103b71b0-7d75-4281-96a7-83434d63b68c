/**
 * Panel Administracyjny - Żyrardów Poleca
 * JavaScript dla dodawania/edycji firm
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja formularza dodawania/edycji firmy
    initCompanyForm();
});

/**
 * Inicjalizacja formularza dodawania/edycji firmy
 */
function initCompanyForm() {
    // Obsługa przełączania zakładek
    initTabSwitching();

    // Obsługa licznika znaków
    initCharCounter();

    // Obsługa godzin otwarcia
    initOpeningHours();

    // Obsługa przesyłania plików
    initFileUploads();

    // Obsługa generowania przyjaznego URL
    initSlugGenerator();

    // Obsługa dynamicznych podkategorii
    initDynamicSubcategories();

    // Obsługa przesyłania formularza
    initFormSubmission();

    // Inicjalizuj HugeRTE editor
    initHugeRTE();

    // Sprawdzenie czy jesteśmy w trybie edycji
    checkEditMode();
}

/**
 * Inicjalizacja przełączania zakładek
 */
function initTabSwitching() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    const nextButtons = document.querySelectorAll('.next-tab');
    const prevButtons = document.querySelectorAll('.prev-tab');

    // Przełączanie zakładek po kliknięciu na przycisk zakładki
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Usunięcie aktywnej klasy z wszystkich przycisków i zawartości
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Dodanie aktywnej klasy do klikniętego przycisku i odpowiedniej zawartości
            button.classList.add('active');
            const tabId = button.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });

    // Przejście do następnej zakładki
    nextButtons.forEach(button => {
        button.addEventListener('click', () => {
            const nextTabId = button.getAttribute('data-next');
            const nextTab = document.querySelector(`.tab-btn[data-tab="${nextTabId}"]`);

            if (nextTab) {
                // Usunięcie aktywnej klasy z wszystkich przycisków i zawartości
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));

                // Dodanie aktywnej klasy do następnego przycisku i odpowiedniej zawartości
                nextTab.classList.add('active');
                document.getElementById(nextTabId).classList.add('active');
            }
        });
    });

    // Przejście do poprzedniej zakładki
    prevButtons.forEach(button => {
        button.addEventListener('click', () => {
            const prevTabId = button.getAttribute('data-prev');
            const prevTab = document.querySelector(`.tab-btn[data-tab="${prevTabId}"]`);

            if (prevTab) {
                // Usunięcie aktywnej klasy z wszystkich przycisków i zawartości
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));

                // Dodanie aktywnej klasy do poprzedniego przycisku i odpowiedniej zawartości
                prevTab.classList.add('active');
                document.getElementById(prevTabId).classList.add('active');
            }
        });
    });
}

/**
 * Inicjalizacja licznika znaków
 */
function initCharCounter() {
    const shortDescInput = document.getElementById('company-short-description');
    const shortDescCounter = document.getElementById('short-desc-counter');

    if (shortDescInput && shortDescCounter) {
        shortDescInput.addEventListener('input', function() {
            const currentLength = this.value.length;
            shortDescCounter.textContent = currentLength;

            if (currentLength > 150) {
                shortDescCounter.style.color = 'var(--danger)';
            } else {
                shortDescCounter.style.color = '';
            }
        });
    }
}

/**
 * Inicjalizacja godzin otwarcia
 */
function initOpeningHours() {
    const closedCheckboxes = document.querySelectorAll('.closed-checkbox input');

    closedCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const dayRow = this.closest('.day-row');
            const timeInputs = dayRow.querySelectorAll('.time-input');

            if (this.checked) {
                timeInputs.forEach(input => {
                    input.disabled = true;
                    input.value = '';
                });
            } else {
                timeInputs.forEach(input => {
                    input.disabled = false;
                });
            }
        });
    });
}

/**
 * Inicjalizacja przesyłania plików
 */
function initFileUploads() {
    // Logo firmy
    const logoInput = document.getElementById('company-logo');
    const logoPreview = document.getElementById('logo-preview');

    if (logoInput && logoPreview) {
        logoInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    logoPreview.innerHTML = `<img src="${e.target.result}" alt="Logo podgląd" class="preview-image">`;
                };

                reader.readAsDataURL(this.files[0]);
            }
        });

        // Obsługa przeciągnij i upuść
        logoPreview.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.style.borderColor = 'var(--primary-color)';
            this.style.backgroundColor = 'rgba(255, 102, 0, 0.05)';
        });

        logoPreview.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.style.borderColor = '';
            this.style.backgroundColor = '';
        });

        logoPreview.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.borderColor = '';
            this.style.backgroundColor = '';

            if (e.dataTransfer.files && e.dataTransfer.files[0]) {
                logoInput.files = e.dataTransfer.files;

                const reader = new FileReader();

                reader.onload = function(e) {
                    logoPreview.innerHTML = `<img src="${e.target.result}" alt="Logo podgląd" class="preview-image">`;
                };

                reader.readAsDataURL(e.dataTransfer.files[0]);
            }
        });
    }

    // Zdjęcia firmy
    const photosInput = document.getElementById('company-photos');
    const photosPreview = document.getElementById('photos-preview');

    if (photosInput && photosPreview) {
        photosInput.addEventListener('change', function() {
            if (this.files && this.files.length > 0) {
                let previewHTML = '<div class="preview-images">';

                for (let i = 0; i < this.files.length; i++) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        previewHTML += `
                            <div class="preview-image-item">
                                <img src="${e.target.result}" alt="Zdjęcie ${i+1}">
                                <span class="remove-image" data-index="${i}"><i class="fas fa-times"></i></span>
                            </div>
                        `;

                        if (i === this.files.length - 1) {
                            photosPreview.innerHTML = previewHTML + '</div>';

                            // Dodanie obsługi usuwania zdjęć
                            const removeButtons = photosPreview.querySelectorAll('.remove-image');
                            removeButtons.forEach(button => {
                                button.addEventListener('click', function() {
                                    const index = parseInt(this.getAttribute('data-index'));
                                    // W rzeczywistej implementacji tutaj byłoby usuwanie zdjęcia
                                    this.closest('.preview-image-item').remove();
                                });
                            });
                        }
                    }.bind(this);

                    reader.readAsDataURL(this.files[i]);
                }
            }
        });

        // Obsługa przeciągnij i upuść
        photosPreview.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.style.borderColor = 'var(--primary-color)';
            this.style.backgroundColor = 'rgba(255, 102, 0, 0.05)';
        });

        photosPreview.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.style.borderColor = '';
            this.style.backgroundColor = '';
        });

        photosPreview.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.borderColor = '';
            this.style.backgroundColor = '';

            if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                photosInput.files = e.dataTransfer.files;

                let previewHTML = '<div class="preview-images">';

                for (let i = 0; i < e.dataTransfer.files.length; i++) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        previewHTML += `
                            <div class="preview-image-item">
                                <img src="${e.target.result}" alt="Zdjęcie ${i+1}">
                                <span class="remove-image" data-index="${i}"><i class="fas fa-times"></i></span>
                            </div>
                        `;

                        if (i === this.files.length - 1) {
                            photosPreview.innerHTML = previewHTML + '</div>';

                            // Dodanie obsługi usuwania zdjęć
                            const removeButtons = photosPreview.querySelectorAll('.remove-image');
                            removeButtons.forEach(button => {
                                button.addEventListener('click', function() {
                                    const index = parseInt(this.getAttribute('data-index'));
                                    // W rzeczywistej implementacji tutaj byłoby usuwanie zdjęcia
                                    this.closest('.preview-image-item').remove();
                                });
                            });
                        }
                    }.bind({files: e.dataTransfer.files});

                    reader.readAsDataURL(e.dataTransfer.files[i]);
                }
            }
        });
    }
}

/**
 * Inicjalizacja generowania przyjaznego URL
 */
function initSlugGenerator() {
    const companyNameInput = document.getElementById('company-name');
    const slugInput = document.getElementById('company-slug');

    if (companyNameInput && slugInput) {
        companyNameInput.addEventListener('blur', function() {
            if (!slugInput.value) {
                const slug = generateSlug(this.value);
                slugInput.value = slug;
            }
        });
    }
}

/**
 * Generowanie przyjaznego URL
 * @param {string} text - Tekst do przekształcenia
 * @returns {string} - Przyjazny URL
 */
function generateSlug(text) {
    return text
        .toLowerCase()
        .replace(/ą/g, 'a')
        .replace(/ć/g, 'c')
        .replace(/ę/g, 'e')
        .replace(/ł/g, 'l')
        .replace(/ń/g, 'n')
        .replace(/ó/g, 'o')
        .replace(/ś/g, 's')
        .replace(/ź/g, 'z')
        .replace(/ż/g, 'z')
        .replace(/\s+/g, '-')
        .replace(/[^\w\-]+/g, '')
        .replace(/\-\-+/g, '-')
        .replace(/^-+/, '')
        .replace(/-+$/, '');
}

/**
 * Inicjalizacja dynamicznych podkategorii
 */
function initDynamicSubcategories() {
    const categorySelect = document.getElementById('company-category');
    const subcategorySelect = document.getElementById('company-subcategory');

    if (categorySelect && subcategorySelect) {
        categorySelect.addEventListener('change', function() {
            const categoryId = this.value;

            // Wyczyszczenie podkategorii
            subcategorySelect.innerHTML = '<option value="">Wybierz podkategorię</option>';

            if (categoryId) {
                // W rzeczywistej implementacji tutaj byłoby pobieranie podkategorii z API
                // Na potrzeby demonstracji używamy przykładowych danych
                const subcategories = getSubcategories(categoryId);

                subcategories.forEach(subcategory => {
                    const option = document.createElement('option');
                    option.value = subcategory.id;
                    option.textContent = subcategory.name;
                    subcategorySelect.appendChild(option);
                });
            }
        });
    }
}

/**
 * Pobieranie podkategorii dla danej kategorii (przykładowe dane)
 * @param {string} categoryId - ID kategorii
 * @returns {Array} - Tablica podkategorii
 */
function getSubcategories(categoryId) {
    const subcategoriesMap = {
        '1': [ // Restauracje i kawiarnie
            { id: '101', name: 'Restauracje' },
            { id: '102', name: 'Kawiarnie' },
            { id: '103', name: 'Fast food' },
            { id: '104', name: 'Pizzerie' },
            { id: '105', name: 'Bary' }
        ],
        '2': [ // Sklepy
            { id: '201', name: 'Spożywcze' },
            { id: '202', name: 'Odzieżowe' },
            { id: '203', name: 'Elektroniczne' },
            { id: '204', name: 'Meblowe' },
            { id: '205', name: 'Sportowe' }
        ],
        '3': [ // Usługi
            { id: '301', name: 'Prawne' },
            { id: '302', name: 'Finansowe' },
            { id: '303', name: 'Remontowe' },
            { id: '304', name: 'Transportowe' },
            { id: '305', name: 'Edukacyjne' }
        ],
        '4': [ // Zdrowie i uroda
            { id: '401', name: 'Salony fryzjerskie' },
            { id: '402', name: 'Salony kosmetyczne' },
            { id: '403', name: 'Gabinety lekarskie' },
            { id: '404', name: 'Apteki' },
            { id: '405', name: 'Siłownie i fitness' }
        ],
        '5': [ // Rozrywka i rekreacja
            { id: '501', name: 'Kina' },
            { id: '502', name: 'Teatry' },
            { id: '503', name: 'Muzea' },
            { id: '504', name: 'Parki' },
            { id: '505', name: 'Kluby' }
        ]
    };

    return subcategoriesMap[categoryId] || [];
}

/**
 * Inicjalizacja przesyłania formularza
 */
function initFormSubmission() {
    const companyForm = document.getElementById('company-form');

    if (companyForm) {
        companyForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Walidacja formularza
            if (!validateForm()) {
                return;
            }

            // Zapisz firmę
            saveCompany();
        });
    }
}

/**
 * Zapisz firmę do localStorage
 */
function saveCompany() {
    try {
        // Pobierz dane z formularza
        const formData = getFormData();

        // Pobierz istniejące dane firm
        const companiesData = JSON.parse(localStorage.getItem('companies_data') || '{"companies":[]}');

        // Sprawdź czy to edycja czy nowa firma
        const urlParams = new URLSearchParams(window.location.search);
        const editId = urlParams.get('edit');

        if (editId) {
            // Edycja istniejącej firmy
            const companyIndex = companiesData.companies.findIndex(c => c.id == editId);
            if (companyIndex !== -1) {
                companiesData.companies[companyIndex] = { ...companiesData.companies[companyIndex], ...formData };
                showNotification('Firma została zaktualizowana!', 'success');
            }
        } else {
            // Dodanie nowej firmy
            const newId = Math.max(...companiesData.companies.map(c => c.id || 0), 0) + 1;
            formData.id = newId;
            formData.dateAdded = new Date().toISOString().split('T')[0];
            formData.status = 'active';
            companiesData.companies.push(formData);
            showNotification('Firma została dodana!', 'success');
        }

        // Zapisz dane
        localStorage.setItem('companies_data', JSON.stringify(companiesData));

        // Aktualizuj dane TOP firm jeśli istnieją
        updateTopCompaniesData(formData);

        // Synchronizuj z frontend
        syncWithFrontend();

        // Przekierowanie po 2 sekundach
        setTimeout(() => {
            window.location.href = 'companies.html';
        }, 2000);

    } catch (error) {
        console.error('Błąd podczas zapisywania firmy:', error);
        showNotification('Wystąpił błąd podczas zapisywania firmy', 'error');
    }
}

/**
 * Walidacja formularza
 * @returns {boolean} - Czy formularz jest poprawny
 */
function validateForm() {
    const requiredFields = document.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value) {
            field.classList.add('is-invalid');
            isValid = false;

            // Przejście do zakładki z błędem
            const tabContent = field.closest('.tab-content');
            if (tabContent) {
                const tabId = tabContent.id;
                const tabButton = document.querySelector(`.tab-btn[data-tab="${tabId}"]`);

                if (tabButton) {
                    tabButton.click();
                }
            }
        } else {
            field.classList.remove('is-invalid');
        }
    });

    return isValid;
}

/**
 * Pobierz dane z formularza
 */
function getFormData() {
    const formData = {};

    // Podstawowe informacje
    formData.name = document.getElementById('company-name')?.value || '';
    formData.slug = document.getElementById('company-slug')?.value || '';
    formData.category = document.getElementById('company-category')?.value || '';
    formData.subcategory = document.getElementById('company-subcategory')?.value || '';
    formData.description = document.getElementById('company-description')?.value || '';

    // Kontakt
    formData.address = document.getElementById('company-address')?.value || '';
    formData.phone = document.getElementById('company-phone')?.value || '';
    formData.email = document.getElementById('company-email')?.value || '';
    formData.website = document.getElementById('company-website')?.value || '';

    // Social media
    formData.facebook = document.getElementById('company-facebook')?.value || '';
    formData.instagram = document.getElementById('company-instagram')?.value || '';
    formData.twitter = document.getElementById('company-twitter')?.value || '';

    // Godziny otwarcia
    formData.openingHours = getOpeningHours();

    // Logo (symulacja - w rzeczywistej implementacji byłby upload)
    const logoInput = document.getElementById('company-logo');
    if (logoInput?.files?.[0]) {
        formData.logo = `../images/business-logo${Math.floor(Math.random() * 3) + 1}.jpg`;
    }

    // SEO
    formData.metaTitle = document.getElementById('meta-title')?.value || '';
    formData.metaDescription = document.getElementById('meta-description')?.value || '';
    formData.metaKeywords = document.getElementById('meta-keywords')?.value || '';

    return formData;
}

/**
 * Pobierz godziny otwarcia z formularza
 */
function getOpeningHours() {
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const openingHours = {};

    days.forEach(day => {
        const checkbox = document.getElementById(`${day}-closed`);
        const openTime = document.getElementById(`${day}-open`);
        const closeTime = document.getElementById(`${day}-close`);

        if (checkbox?.checked) {
            openingHours[day] = { closed: true };
        } else {
            openingHours[day] = {
                closed: false,
                open: openTime?.value || '09:00',
                close: closeTime?.value || '17:00'
            };
        }
    });

    return openingHours;
}

/**
 * Aktualizuj dane TOP firm
 */
function updateTopCompaniesData(companyData) {
    try {
        const topData = JSON.parse(localStorage.getItem('companies_top_data') || '{"companies":[]}');
        const existingCompany = topData.companies.find(c => c.id == companyData.id);

        if (existingCompany) {
            // Aktualizuj istniejącą firmę w danych TOP
            Object.assign(existingCompany, {
                name: companyData.name,
                category: companyData.category,
                subcategory: companyData.subcategory,
                address: companyData.address,
                phone: companyData.phone,
                email: companyData.email
            });

            localStorage.setItem('companies_top_data', JSON.stringify(topData));
        }
    } catch (error) {
        console.error('Błąd podczas aktualizacji danych TOP:', error);
    }
}

/**
 * Synchronizuj dane z frontend
 */
function syncWithFrontend() {
    try {
        const companiesData = JSON.parse(localStorage.getItem('companies_data') || '{"companies":[]}');

        // Przygotuj dane dla frontend
        const frontendData = companiesData.companies.filter(c => c.status === 'active').map(company => ({
            id: company.id,
            name: company.name,
            category: company.category,
            subcategory: company.subcategory,
            description: company.description,
            address: company.address,
            phone: company.phone,
            email: company.email,
            website: company.website,
            logo: company.logo || '../images/business-logo1.jpg',
            openingHours: company.openingHours
        }));

        // Zapisz dane dla frontend
        localStorage.setItem('frontend_companies', JSON.stringify(frontendData));

        // Wyślij event o aktualizacji
        window.dispatchEvent(new CustomEvent('companiesUpdated', {
            detail: { companies: frontendData }
        }));

    } catch (error) {
        console.error('Błąd podczas synchronizacji z frontend:', error);
    }
}

/**
 * Wyświetl powiadomienie
 */
function showNotification(message, type = 'info') {
    // Usuń istniejące powiadomienia
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(n => n.remove());

    // Utwórz nowe powiadomienie
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">&times;</button>
    `;

    // Dodaj style
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
        color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
        border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
        border-radius: 8px;
        padding: 15px;
        z-index: 10000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Obsługa zamykania
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => notification.remove());

    // Auto-usuwanie po 5 sekundach
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Sprawdzenie czy jesteśmy w trybie edycji
 */
function checkEditMode() {
    const urlParams = new URLSearchParams(window.location.search);
    const editId = urlParams.get('edit');

    if (editId) {
        loadCompanyForEdit(editId);
    }
}

/**
 * Załaduj firmę do edycji
 */
function loadCompanyForEdit(companyId) {
    try {
        const companiesData = JSON.parse(localStorage.getItem('companies_data') || '{"companies":[]}');
        const company = companiesData.companies.find(c => c.id == companyId);

        if (company) {
            // Wypełnij formularz danymi firmy
            fillCompanyForm(company);

            // Zmień tytuł strony
            const pageTitle = document.querySelector('h1');
            if (pageTitle) {
                pageTitle.innerHTML = '<i class="fas fa-edit"></i> Edytuj firmę';
            }

            // Zmień tekst przycisku
            const submitBtn = document.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-save"></i> Zapisz zmiany';
            }
        }
    } catch (error) {
        console.error('Błąd podczas ładowania firmy do edycji:', error);
    }
}

/**
 * Wypełnij formularz danymi firmy
 */
function fillCompanyForm(company) {
    // Podstawowe informacje
    if (document.getElementById('company-name')) {
        document.getElementById('company-name').value = company.name || '';
    }
    if (document.getElementById('company-slug')) {
        document.getElementById('company-slug').value = company.slug || '';
    }
    if (document.getElementById('company-category')) {
        document.getElementById('company-category').value = company.category || '';
        // Trigger change event to load subcategories
        document.getElementById('company-category').dispatchEvent(new Event('change'));
    }
    if (document.getElementById('company-subcategory')) {
        setTimeout(() => {
            document.getElementById('company-subcategory').value = company.subcategory || '';
        }, 100);
    }
    if (document.getElementById('company-description')) {
        document.getElementById('company-description').value = company.description || '';
    }

    // Kontakt
    if (document.getElementById('company-address')) {
        document.getElementById('company-address').value = company.address || '';
    }
    if (document.getElementById('company-phone')) {
        document.getElementById('company-phone').value = company.phone || '';
    }
    if (document.getElementById('company-email')) {
        document.getElementById('company-email').value = company.email || '';
    }
    if (document.getElementById('company-website')) {
        document.getElementById('company-website').value = company.website || '';
    }

    // Social media
    if (document.getElementById('company-facebook')) {
        document.getElementById('company-facebook').value = company.facebook || '';
    }
    if (document.getElementById('company-instagram')) {
        document.getElementById('company-instagram').value = company.instagram || '';
    }
    if (document.getElementById('company-twitter')) {
        document.getElementById('company-twitter').value = company.twitter || '';
    }

    // Godziny otwarcia
    if (company.openingHours) {
        fillOpeningHours(company.openingHours);
    }

    // SEO
    if (document.getElementById('meta-title')) {
        document.getElementById('meta-title').value = company.metaTitle || '';
    }
    if (document.getElementById('meta-description')) {
        document.getElementById('meta-description').value = company.metaDescription || '';
    }
    if (document.getElementById('meta-keywords')) {
        document.getElementById('meta-keywords').value = company.metaKeywords || '';
    }
}

/**
 * Wypełnij godziny otwarcia
 */
function fillOpeningHours(openingHours) {
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    days.forEach(day => {
        const dayData = openingHours[day];
        if (dayData) {
            const checkbox = document.getElementById(`${day}-closed`);
            const openTime = document.getElementById(`${day}-open`);
            const closeTime = document.getElementById(`${day}-close`);

            if (checkbox) {
                checkbox.checked = dayData.closed || false;
            }
            if (openTime && !dayData.closed) {
                openTime.value = dayData.open || '09:00';
            }
            if (closeTime && !dayData.closed) {
                closeTime.value = dayData.close || '17:00';
            }
        }
    });
}

/**
 * Inicjalizuj HugeRTE editor
 */
function initHugeRTE() {
    if (typeof hugerte !== 'undefined') {
        hugerte.init({
            selector: '#company-description',
            height: 300,
            menubar: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image | removeformat | code | help',
            content_style: 'body { font-family: Montserrat, Arial, sans-serif; font-size: 14px; }',
            branding: false,
            promotion: false
        });
    }
}
