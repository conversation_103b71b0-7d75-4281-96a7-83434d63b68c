/**
 * Panel Administracyjny - Żyrardów Poleca
 * Skrypt do zarządzania menu nagłówka i stopki
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elementy DOM
    const headerMenuItems = document.getElementById('header-menu-items');
    const footerMenuItems = document.getElementById('footer-menu-items');
    const headerMenuStructure = document.getElementById('header-menu-structure');
    const footerMenuStructure = document.getElementById('footer-menu-structure');
    const addHeaderItemBtn = document.getElementById('add-header-item');
    const addFooterItemBtn = document.getElementById('add-footer-item');
    const saveMenusBtn = document.getElementById('save-menus-btn');
    const menuItemModal = document.getElementById('menuItemModal');
    const submenuItemModal = document.getElementById('submenuItemModal');
    const menuItemForm = document.getElementById('menu-item-form');
    const submenuItemForm = document.getElementById('submenu-item-form');
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    // Dane menu
    let headerMenuData = [];
    let footerMenuData = [];
    let nextItemId = 1;

    // Inicjalizacja
    initTabSwitching();
    initSortable();
    loadMenuData();
    setupEventListeners();

    /**
     * Inicjalizacja przełączania zakładek
     */
    function initTabSwitching() {
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const tabId = this.getAttribute('data-tab');

                // Usuń klasę active ze wszystkich przycisków i zawartości
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));

                // Dodaj klasę active do klikniętego przycisku i odpowiedniej zawartości
                this.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });
    }

    /**
     * Inicjalizacja funkcjonalności drag-and-drop
     */
    function initSortable() {
        // Inicjalizacja sortowania dla głównego menu nagłówka
        new Sortable(headerMenuStructure, {
            group: 'header-menu',
            animation: 150,
            handle: '.menu-item-handle',
            onEnd: function() {
                updateMenuData('header');
                renderMenuPreview('header');
            }
        });

        // Inicjalizacja sortowania dla głównego menu stopki
        new Sortable(footerMenuStructure, {
            group: 'footer-menu',
            animation: 150,
            handle: '.menu-item-handle',
            onEnd: function() {
                updateMenuData('footer');
                renderMenuPreview('footer');
            }
        });

        // Inicjalizacja sortowania dla podmenu będzie dodawana dynamicznie
    }

    /**
     * Inicjalizacja sortowania dla podmenu
     */
    function initSubmenuSortables() {
        // Znajdź wszystkie listy podmenu
        const submenuLists = document.querySelectorAll('.submenu-items');

        // Inicjalizuj sortowanie dla każdej listy podmenu
        submenuLists.forEach(list => {
            new Sortable(list, {
                group: 'submenu',
                animation: 150,
                handle: '.menu-item-handle',
                onEnd: function() {
                    // Określ typ menu na podstawie najbliższego rodzica
                    const menuType = list.closest('#header-menu-structure') ? 'header' : 'footer';
                    updateMenuData(menuType);
                    renderMenuPreview(menuType);
                }
            });
        });
    }

    /**
     * Konfiguracja nasłuchiwania zdarzeń
     */
    function setupEventListeners() {
        // Dodawanie nowej pozycji do menu nagłówka
        addHeaderItemBtn.addEventListener('click', function() {
            openAddItemModal('header');
        });

        // Dodawanie nowej pozycji do menu stopki
        addFooterItemBtn.addEventListener('click', function() {
            openAddItemModal('footer');
        });

        // Zapisywanie zmian w menu
        saveMenusBtn.addEventListener('click', function() {
            saveMenuChanges();
        });

        // Obsługa formularza dodawania/edycji pozycji menu
        menuItemForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveMenuItem();
        });

        // Obsługa formularza dodawania/edycji pozycji podmenu
        submenuItemForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveSubmenuItem();
        });

        // Zamykanie modali
        document.querySelectorAll('.modal-close, .modal-cancel').forEach(element => {
            element.addEventListener('click', function() {
                closeAllModals();
            });
        });

        // Zamykanie modali po kliknięciu poza nimi
        window.addEventListener('click', function(e) {
            if (e.target === menuItemModal) {
                closeAllModals();
            }
            if (e.target === submenuItemModal) {
                closeAllModals();
            }
        });
    }

    /**
     * Ładowanie danych menu z plików szablonów lub localStorage
     */
    function loadMenuData() {
        // Sprawdź, czy dane menu są zapisane w localStorage
        const savedHeaderMenu = localStorage.getItem('zyrardow_header_menu');
        const savedFooterMenu = localStorage.getItem('zyrardow_footer_menu');

        if (savedHeaderMenu && savedFooterMenu) {
            try {
                // Ładowanie danych z localStorage
                headerMenuData = JSON.parse(savedHeaderMenu);
                footerMenuData = JSON.parse(savedFooterMenu);

                // Renderowanie menu
                renderMenuStructure('header');
                renderMenuPreview('header');
                renderMenuStructure('footer');
                renderMenuPreview('footer');
                initSubmenuSortables();

                showNotification('Załadowano zapisane menu z przeglądarki', 'info');
                console.log('Załadowano menu z localStorage');
            } catch (error) {
                console.error('Błąd podczas ładowania menu z localStorage:', error);
                loadMenuFromTemplates();
            }
        } else {
            // Jeśli nie ma zapisanych danych w localStorage, załaduj z szablonów
            loadMenuFromTemplates();
        }
    }

    /**
     * Ładowanie danych menu z plików szablonów
     */
    function loadMenuFromTemplates() {
        // Ładowanie menu nagłówka
        fetch('../templates/header.html')
            .then(response => response.text())
            .then(html => {
                headerMenuData = parseMenuFromHTML(html, 'header');
                renderMenuStructure('header');
                renderMenuPreview('header');
                initSubmenuSortables();
            })
            .catch(error => {
                console.error('Błąd ładowania menu nagłówka:', error);
                showNotification('Błąd ładowania menu nagłówka', 'error');
            });

        // Ładowanie menu stopki
        fetch('../templates/footer.html')
            .then(response => response.text())
            .then(html => {
                footerMenuData = parseMenuFromHTML(html, 'footer');
                renderMenuStructure('footer');
                renderMenuPreview('footer');
                initSubmenuSortables();
            })
            .catch(error => {
                console.error('Błąd ładowania menu stopki:', error);
                showNotification('Błąd ładowania menu stopki', 'error');
            });
    }

    /**
     * Parsowanie HTML do struktury danych menu
     * @param {string} html - Kod HTML szablonu
     * @param {string} menuType - Typ menu ('header' lub 'footer')
     * @returns {Array} - Tablica obiektów reprezentujących pozycje menu
     */
    function parseMenuFromHTML(html, menuType) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        let menuItems = [];

        if (menuType === 'header') {
            // Pobierz wszystkie elementy li z głównego menu nagłówka
            const navItems = doc.querySelectorAll('.nav-links > li');

            navItems.forEach((item, index) => {
                const link = item.querySelector('a');
                if (!link) return;

                const menuItem = {
                    id: nextItemId++,
                    text: link.textContent.trim(),
                    url: link.getAttribute('href') || '#',
                    target: link.getAttribute('target') || '_self',
                    children: []
                };

                // Sprawdź, czy element ma podmenu
                const submenu = item.querySelector('.submenu');
                if (submenu) {
                    const submenuItems = submenu.querySelectorAll('li');
                    submenuItems.forEach(subItem => {
                        const subLink = subItem.querySelector('a');
                        if (!subLink) return;

                        menuItem.children.push({
                            id: nextItemId++,
                            text: subLink.textContent.trim(),
                            url: subLink.getAttribute('href') || '#',
                            target: subLink.getAttribute('target') || '_self'
                        });
                    });
                }

                menuItems.push(menuItem);
            });
        } else if (menuType === 'footer') {
            // Pobierz wszystkie kolumny menu stopki
            const footerColumns = doc.querySelectorAll('.footer-nav-column');

            footerColumns.forEach((column, index) => {
                const columnTitle = column.querySelector('h3');
                if (!columnTitle) return;

                const menuItem = {
                    id: nextItemId++,
                    text: columnTitle.textContent.trim(),
                    url: '#',
                    target: '_self',
                    children: []
                };

                // Pobierz wszystkie linki w kolumnie
                const links = column.querySelectorAll('ul li a');
                links.forEach(link => {
                    menuItem.children.push({
                        id: nextItemId++,
                        text: link.textContent.trim(),
                        url: link.getAttribute('href') || '#',
                        target: link.getAttribute('target') || '_self'
                    });
                });

                menuItems.push(menuItem);
            });

            // Dodaj również linki z dolnej części stopki
            const footerLinks = doc.querySelectorAll('.footer-links li a');
            if (footerLinks.length > 0) {
                const menuItem = {
                    id: nextItemId++,
                    text: 'Informacje',
                    url: '#',
                    target: '_self',
                    children: []
                };

                footerLinks.forEach(link => {
                    menuItem.children.push({
                        id: nextItemId++,
                        text: link.textContent.trim(),
                        url: link.getAttribute('href') || '#',
                        target: link.getAttribute('target') || '_self'
                    });
                });

                menuItems.push(menuItem);
            }
        }

        return menuItems;
    }

    /**
     * Renderowanie struktury menu w panelu administracyjnym
     * @param {string} menuType - Typ menu ('header' lub 'footer')
     */
    function renderMenuStructure(menuType) {
        const menuData = menuType === 'header' ? headerMenuData : footerMenuData;
        const menuStructureElement = menuType === 'header' ? headerMenuStructure : footerMenuStructure;

        // Wyczyść istniejącą strukturę
        menuStructureElement.innerHTML = '';

        // Renderuj każdy element menu
        menuData.forEach(item => {
            const menuItemElement = createMenuItemElement(item, menuType);
            menuStructureElement.appendChild(menuItemElement);
        });
    }

    /**
     * Tworzenie elementu HTML dla pozycji menu
     * @param {Object} item - Obiekt reprezentujący pozycję menu
     * @param {string} menuType - Typ menu ('header' lub 'footer')
     * @returns {HTMLElement} - Element HTML reprezentujący pozycję menu
     */
    function createMenuItemElement(item, menuType) {
        const li = document.createElement('li');
        li.setAttribute('data-id', item.id);

        // Główny element menu
        const menuItem = document.createElement('div');
        menuItem.className = 'menu-item';

        // Uchwyt do przeciągania
        const handle = document.createElement('div');
        handle.className = 'menu-item-handle';
        handle.innerHTML = '<i class="fas fa-grip-vertical"></i>';
        menuItem.appendChild(handle);

        // Zawartość elementu menu
        const content = document.createElement('div');
        content.className = 'menu-item-content';

        const title = document.createElement('div');
        title.className = 'menu-item-title';
        title.textContent = item.text;
        content.appendChild(title);

        const url = document.createElement('div');
        url.className = 'menu-item-url';
        url.textContent = item.url;
        content.appendChild(url);

        menuItem.appendChild(content);

        // Przyciski akcji
        const actions = document.createElement('div');
        actions.className = 'menu-item-actions';

        // Przycisk edycji
        const editBtn = document.createElement('button');
        editBtn.innerHTML = '<i class="fas fa-edit"></i>';
        editBtn.title = 'Edytuj';
        editBtn.addEventListener('click', function() {
            openEditItemModal(item.id, menuType);
        });
        actions.appendChild(editBtn);

        // Przycisk dodawania podmenu
        const addSubmenuBtn = document.createElement('button');
        addSubmenuBtn.innerHTML = '<i class="fas fa-plus-circle"></i>';
        addSubmenuBtn.title = 'Dodaj podmenu';
        addSubmenuBtn.addEventListener('click', function() {
            openAddSubmenuModal(item.id, menuType);
        });
        actions.appendChild(addSubmenuBtn);

        // Przycisk usuwania
        const deleteBtn = document.createElement('button');
        deleteBtn.innerHTML = '<i class="fas fa-trash-alt"></i>';
        deleteBtn.title = 'Usuń';
        deleteBtn.className = 'delete-item';
        deleteBtn.addEventListener('click', function() {
            if (confirm('Czy na pewno chcesz usunąć tę pozycję menu i wszystkie jej podmenu?')) {
                deleteMenuItem(item.id, menuType);
            }
        });
        actions.appendChild(deleteBtn);

        menuItem.appendChild(actions);
        li.appendChild(menuItem);

        // Dodaj podmenu, jeśli istnieje
        if (item.children && item.children.length > 0) {
            const submenuList = document.createElement('ul');
            submenuList.className = 'submenu-items';

            item.children.forEach(subItem => {
                const subLi = document.createElement('li');
                subLi.setAttribute('data-id', subItem.id);

                const subMenuItem = document.createElement('div');
                subMenuItem.className = 'menu-item';

                // Uchwyt do przeciągania
                const subHandle = document.createElement('div');
                subHandle.className = 'menu-item-handle';
                subHandle.innerHTML = '<i class="fas fa-grip-vertical"></i>';
                subMenuItem.appendChild(subHandle);

                // Zawartość elementu podmenu
                const subContent = document.createElement('div');
                subContent.className = 'menu-item-content';

                const subTitle = document.createElement('div');
                subTitle.className = 'menu-item-title';
                subTitle.textContent = subItem.text;
                subContent.appendChild(subTitle);

                const subUrl = document.createElement('div');
                subUrl.className = 'menu-item-url';
                subUrl.textContent = subItem.url;
                subContent.appendChild(subUrl);

                subMenuItem.appendChild(subContent);

                // Przyciski akcji dla podmenu
                const subActions = document.createElement('div');
                subActions.className = 'menu-item-actions';

                // Przycisk edycji podmenu
                const subEditBtn = document.createElement('button');
                subEditBtn.innerHTML = '<i class="fas fa-edit"></i>';
                subEditBtn.title = 'Edytuj';
                subEditBtn.addEventListener('click', function() {
                    openEditSubmenuModal(subItem.id, item.id, menuType);
                });
                subActions.appendChild(subEditBtn);

                // Przycisk usuwania podmenu
                const subDeleteBtn = document.createElement('button');
                subDeleteBtn.innerHTML = '<i class="fas fa-trash-alt"></i>';
                subDeleteBtn.title = 'Usuń';
                subDeleteBtn.className = 'delete-item';
                subDeleteBtn.addEventListener('click', function() {
                    if (confirm('Czy na pewno chcesz usunąć tę pozycję podmenu?')) {
                        deleteSubmenuItem(subItem.id, item.id, menuType);
                    }
                });
                subActions.appendChild(subDeleteBtn);

                subMenuItem.appendChild(subActions);
                subLi.appendChild(subMenuItem);
                submenuList.appendChild(subLi);
            });

            li.appendChild(submenuList);
        }

        return li;
    }

    /**
     * Renderowanie podglądu menu
     * @param {string} menuType - Typ menu ('header' lub 'footer')
     */
    function renderMenuPreview(menuType) {
        const menuData = menuType === 'header' ? headerMenuData : footerMenuData;
        const menuPreviewElement = menuType === 'header' ? headerMenuItems : footerMenuItems;

        // Wyczyść istniejący podgląd
        menuPreviewElement.innerHTML = '';

        // Renderuj każdy element menu w podglądzie
        menuData.forEach(item => {
            const li = document.createElement('li');
            if (item.children && item.children.length > 0) {
                li.className = 'has-submenu';
            }

            const a = document.createElement('a');
            a.href = item.url;
            a.textContent = item.text;
            if (item.target === '_blank') {
                a.target = '_blank';
            }
            li.appendChild(a);

            // Dodaj podmenu, jeśli istnieje
            if (item.children && item.children.length > 0) {
                const submenu = document.createElement('ul');
                submenu.className = 'submenu';

                item.children.forEach(subItem => {
                    const subLi = document.createElement('li');
                    const subA = document.createElement('a');
                    subA.href = subItem.url;
                    subA.textContent = subItem.text;
                    if (subItem.target === '_blank') {
                        subA.target = '_blank';
                    }
                    subLi.appendChild(subA);
                    submenu.appendChild(subLi);
                });

                li.appendChild(submenu);
            }

            menuPreviewElement.appendChild(li);
        });
    }

    /**
     * Aktualizacja danych menu na podstawie struktury DOM
     * @param {string} menuType - Typ menu ('header' lub 'footer')
     */
    function updateMenuData(menuType) {
        const menuStructureElement = menuType === 'header' ? headerMenuStructure : footerMenuStructure;
        const menuItems = menuStructureElement.querySelectorAll(':scope > li');
        const menuData = [];

        menuItems.forEach(item => {
            const itemId = parseInt(item.getAttribute('data-id'));
            const oldItem = findMenuItem(itemId, menuType);

            if (!oldItem) return;

            const newItem = {
                id: oldItem.id,
                text: oldItem.text,
                url: oldItem.url,
                target: oldItem.target,
                children: []
            };

            // Pobierz podmenu, jeśli istnieje
            const submenuItems = item.querySelectorAll('.submenu-items > li');
            submenuItems.forEach(subItem => {
                const subItemId = parseInt(subItem.getAttribute('data-id'));
                const oldSubItem = findSubmenuItem(subItemId, itemId, menuType);

                if (!oldSubItem) return;

                newItem.children.push({
                    id: oldSubItem.id,
                    text: oldSubItem.text,
                    url: oldSubItem.url,
                    target: oldSubItem.target
                });
            });

            menuData.push(newItem);
        });

        // Aktualizuj dane menu
        if (menuType === 'header') {
            headerMenuData = menuData;
        } else {
            footerMenuData = menuData;
        }
    }

    /**
     * Znajdowanie pozycji menu po ID
     * @param {number} id - ID pozycji menu
     * @param {string} menuType - Typ menu ('header' lub 'footer')
     * @returns {Object|null} - Znaleziona pozycja menu lub null
     */
    function findMenuItem(id, menuType) {
        const menuData = menuType === 'header' ? headerMenuData : footerMenuData;
        return menuData.find(item => item.id === id) || null;
    }

    /**
     * Znajdowanie pozycji podmenu po ID
     * @param {number} id - ID pozycji podmenu
     * @param {number} parentId - ID rodzica
     * @param {string} menuType - Typ menu ('header' lub 'footer')
     * @returns {Object|null} - Znaleziona pozycja podmenu lub null
     */
    function findSubmenuItem(id, parentId, menuType) {
        const menuData = menuType === 'header' ? headerMenuData : footerMenuData;
        const parent = menuData.find(item => item.id === parentId);

        if (!parent || !parent.children) return null;

        return parent.children.find(item => item.id === id) || null;
    }

    /**
     * Otwieranie modalu dodawania nowej pozycji menu
     * @param {string} menuType - Typ menu ('header' lub 'footer')
     */
    function openAddItemModal(menuType) {
        // Resetuj formularz
        menuItemForm.reset();

        // Ustaw typ menu
        document.getElementById('menu-type').value = menuType;
        document.getElementById('menu-item-id').value = '';
        document.getElementById('parent-id').value = '0';

        // Zmień tytuł modalu
        document.getElementById('menu-item-modal-title').textContent = 'Dodaj pozycję menu';

        // Pokaż modal
        menuItemModal.classList.add('show');
    }

    /**
     * Otwieranie modalu edycji pozycji menu
     * @param {number} id - ID pozycji menu
     * @param {string} menuType - Typ menu ('header' lub 'footer')
     */
    function openEditItemModal(id, menuType) {
        const item = findMenuItem(id, menuType);
        if (!item) return;

        // Wypełnij formularz danymi
        document.getElementById('menu-item-id').value = item.id;
        document.getElementById('menu-type').value = menuType;
        document.getElementById('parent-id').value = '0';
        document.getElementById('menu-item-text').value = item.text;
        document.getElementById('menu-item-url').value = item.url;
        document.getElementById('menu-item-target').value = item.target;
        document.getElementById('menu-item-has-children').checked = item.children && item.children.length > 0;

        // Zmień tytuł modalu
        document.getElementById('menu-item-modal-title').textContent = 'Edytuj pozycję menu';

        // Pokaż modal
        menuItemModal.classList.add('show');
    }

    /**
     * Otwieranie modalu dodawania nowej pozycji podmenu
     * @param {number} parentId - ID rodzica
     * @param {string} menuType - Typ menu ('header' lub 'footer')
     */
    function openAddSubmenuModal(parentId, menuType) {
        // Resetuj formularz
        submenuItemForm.reset();

        // Ustaw typ menu i rodzica
        document.getElementById('submenu-menu-type').value = menuType;
        document.getElementById('submenu-parent-id').value = parentId;

        // Pokaż modal
        submenuItemModal.classList.add('show');
    }

    /**
     * Otwieranie modalu edycji pozycji podmenu
     * @param {number} id - ID pozycji podmenu
     * @param {number} parentId - ID rodzica
     * @param {string} menuType - Typ menu ('header' lub 'footer')
     */
    function openEditSubmenuModal(id, parentId, menuType) {
        const item = findSubmenuItem(id, parentId, menuType);
        if (!item) return;

        // Wypełnij formularz danymi
        document.getElementById('submenu-parent-id').value = parentId;
        document.getElementById('submenu-menu-type').value = menuType;
        document.getElementById('submenu-item-text').value = item.text;
        document.getElementById('submenu-item-url').value = item.url;
        document.getElementById('submenu-item-target').value = item.target;

        // Pokaż modal
        submenuItemModal.classList.add('show');
    }

    /**
     * Zamykanie wszystkich modali
     */
    function closeAllModals() {
        menuItemModal.classList.remove('show');
        submenuItemModal.classList.remove('show');
    }

    /**
     * Zapisywanie nowej lub edytowanej pozycji menu
     */
    function saveMenuItem() {
        const id = document.getElementById('menu-item-id').value;
        const menuType = document.getElementById('menu-type').value;
        const text = document.getElementById('menu-item-text').value.trim();
        const url = document.getElementById('menu-item-url').value.trim();
        const target = document.getElementById('menu-item-target').value;
        const hasChildren = document.getElementById('menu-item-has-children').checked;

        if (!text || !url) {
            alert('Wypełnij wszystkie wymagane pola!');
            return;
        }

        if (id) {
            // Edycja istniejącej pozycji
            const menuData = menuType === 'header' ? headerMenuData : footerMenuData;
            const item = menuData.find(item => item.id === parseInt(id));

            if (item) {
                item.text = text;
                item.url = url;
                item.target = target;
            }
        } else {
            // Dodawanie nowej pozycji
            const newItem = {
                id: nextItemId++,
                text: text,
                url: url,
                target: target,
                children: []
            };

            if (menuType === 'header') {
                headerMenuData.push(newItem);
            } else {
                footerMenuData.push(newItem);
            }
        }

        // Aktualizuj widok
        renderMenuStructure(menuType);
        renderMenuPreview(menuType);
        initSubmenuSortables();

        // Zamknij modal
        closeAllModals();
    }

    /**
     * Zapisywanie nowej lub edytowanej pozycji podmenu
     */
    function saveSubmenuItem() {
        const parentId = parseInt(document.getElementById('submenu-parent-id').value);
        const menuType = document.getElementById('submenu-menu-type').value;
        const text = document.getElementById('submenu-item-text').value.trim();
        const url = document.getElementById('submenu-item-url').value.trim();
        const target = document.getElementById('submenu-item-target').value;

        if (!text || !url) {
            alert('Wypełnij wszystkie wymagane pola!');
            return;
        }

        const menuData = menuType === 'header' ? headerMenuData : footerMenuData;
        const parent = menuData.find(item => item.id === parentId);

        if (!parent) {
            alert('Nie znaleziono pozycji nadrzędnej!');
            return;
        }

        // Dodaj nową pozycję podmenu
        if (!parent.children) {
            parent.children = [];
        }

        parent.children.push({
            id: nextItemId++,
            text: text,
            url: url,
            target: target
        });

        // Aktualizuj widok
        renderMenuStructure(menuType);
        renderMenuPreview(menuType);
        initSubmenuSortables();

        // Zamknij modal
        closeAllModals();
    }

    /**
     * Usuwanie pozycji menu
     * @param {number} id - ID pozycji menu
     * @param {string} menuType - Typ menu ('header' lub 'footer')
     */
    function deleteMenuItem(id, menuType) {
        const menuData = menuType === 'header' ? headerMenuData : footerMenuData;
        const index = menuData.findIndex(item => item.id === id);

        if (index !== -1) {
            menuData.splice(index, 1);

            // Aktualizuj widok
            renderMenuStructure(menuType);
            renderMenuPreview(menuType);
        }
    }

    /**
     * Usuwanie pozycji podmenu
     * @param {number} id - ID pozycji podmenu
     * @param {number} parentId - ID rodzica
     * @param {string} menuType - Typ menu ('header' lub 'footer')
     */
    function deleteSubmenuItem(id, parentId, menuType) {
        const menuData = menuType === 'header' ? headerMenuData : footerMenuData;
        const parent = menuData.find(item => item.id === parentId);

        if (!parent || !parent.children) return;

        const index = parent.children.findIndex(item => item.id === id);

        if (index !== -1) {
            parent.children.splice(index, 1);

            // Aktualizuj widok
            renderMenuStructure(menuType);
            renderMenuPreview(menuType);
        }
    }

    /**
     * Zapisywanie zmian w menu do plików szablonów
     */
    function saveMenuChanges() {
        try {
            // Pobierz aktualne szablony i wygeneruj nowe HTML
            Promise.all([
                fetch('../templates/header.html').then(response => response.text()),
                fetch('../templates/footer.html').then(response => response.text())
            ])
            .then(([headerTemplate, footerTemplate]) => {
                // Generowanie HTML dla menu nagłówka
                const headerHtml = generateHeaderMenuHtml(headerTemplate);

                // Generowanie HTML dla menu stopki
                const footerHtml = generateFooterMenuHtml(footerTemplate);

                // Zapisz dane w localStorage
                localStorage.setItem('zyrardow_header_menu', JSON.stringify(headerMenuData));
                localStorage.setItem('zyrardow_footer_menu', JSON.stringify(footerMenuData));

                // Wyświetl powiadomienie o sukcesie
                showNotification('Menu zostało zapisane pomyślnie w przeglądarce. Zmiany będą widoczne po odświeżeniu strony.', 'success');

                // Symulacja zapisywania na serwerze - w rzeczywistej implementacji
                // tutaj byłoby wysyłanie danych do serwera
                console.log('Dane menu zapisane w localStorage:');
                console.log('Header Menu:', headerMenuData);
                console.log('Footer Menu:', footerMenuData);

                // Dodaj przycisk do odświeżenia strony
                addRefreshButton();
            })
            .catch(error => {
                console.error('Błąd podczas pobierania szablonów:', error);
                showNotification('Błąd podczas pobierania szablonów', 'error');
            });
        } catch (error) {
            console.error('Błąd podczas zapisywania menu:', error);
            showNotification('Błąd podczas zapisywania menu', 'error');
        }
    }

    /**
     * Dodaje przycisk do odświeżenia strony
     */
    function addRefreshButton() {
        // Sprawdź, czy przycisk już istnieje
        if (document.getElementById('refresh-page-button')) return;

        // Utwórz przycisk
        const refreshButton = document.createElement('button');
        refreshButton.id = 'refresh-page-button';
        refreshButton.className = 'btn btn-success refresh-button';
        refreshButton.innerHTML = '<i class="fas fa-sync-alt"></i> Odśwież stronę, aby zobaczyć zmiany';

        // Dodaj obsługę kliknięcia
        refreshButton.addEventListener('click', function() {
            window.location.reload();
        });

        // Dodaj przycisk do strony
        const contentHeader = document.querySelector('.content-header');
        if (contentHeader) {
            contentHeader.appendChild(refreshButton);
        } else {
            document.body.appendChild(refreshButton);
        }
    }

    /**
     * Generowanie HTML dla menu nagłówka
     * @param {string} templateHtml - Kod HTML szablonu nagłówka
     * @returns {string} - Kod HTML menu nagłówka
     */
    function generateHeaderMenuHtml(templateHtml) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(templateHtml, 'text/html');
        const navLinks = doc.querySelector('.nav-links');

        if (!navLinks) return templateHtml;

        // Wyczyść istniejące menu
        navLinks.innerHTML = '';

        // Dodaj nowe pozycje menu
        headerMenuData.forEach(item => {
            const li = document.createElement('li');
            if (item.children && item.children.length > 0) {
                li.className = 'has-submenu';
            }

            const a = document.createElement('a');
            a.href = item.url;
            a.textContent = item.text;
            if (item.target === '_blank') {
                a.target = '_blank';
            }
            li.appendChild(a);

            // Dodaj podmenu, jeśli istnieje
            if (item.children && item.children.length > 0) {
                const submenu = document.createElement('ul');
                submenu.className = 'submenu';

                item.children.forEach(subItem => {
                    const subLi = document.createElement('li');
                    const subA = document.createElement('a');
                    subA.href = subItem.url;
                    subA.textContent = subItem.text;
                    if (subItem.target === '_blank') {
                        subA.target = '_blank';
                    }
                    subLi.appendChild(subA);
                    submenu.appendChild(subLi);
                });

                li.appendChild(submenu);
            }

            navLinks.appendChild(li);
        });

        return doc.documentElement.outerHTML;
    }

    /**
     * Generowanie HTML dla menu stopki
     * @param {string} templateHtml - Kod HTML szablonu stopki
     * @returns {string} - Kod HTML menu stopki
     */
    function generateFooterMenuHtml(templateHtml) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(templateHtml, 'text/html');
        const footerNav = doc.querySelector('.footer-nav');
        const footerLinks = doc.querySelector('.footer-links');

        if (!footerNav || !footerLinks) return templateHtml;

        // Wyczyść istniejące menu
        footerNav.innerHTML = '';
        footerLinks.innerHTML = '';

        // Dodaj nowe pozycje menu
        footerMenuData.forEach((item, index) => {
            // Ostatnia pozycja to linki w dolnej części stopki
            if (index === footerMenuData.length - 1 && item.text === 'Informacje') {
                item.children.forEach(subItem => {
                    const li = document.createElement('li');
                    const a = document.createElement('a');
                    a.href = subItem.url;
                    a.textContent = subItem.text;
                    if (subItem.target === '_blank') {
                        a.target = '_blank';
                    }
                    li.appendChild(a);
                    footerLinks.appendChild(li);
                });
            } else {
                // Kolumny menu w górnej części stopki
                const column = document.createElement('div');
                column.className = 'footer-nav-column';

                const title = document.createElement('h3');
                title.textContent = item.text;
                column.appendChild(title);

                const ul = document.createElement('ul');

                item.children.forEach(subItem => {
                    const li = document.createElement('li');
                    const a = document.createElement('a');
                    a.href = subItem.url;
                    a.textContent = subItem.text;
                    if (subItem.target === '_blank') {
                        a.target = '_blank';
                    }
                    li.appendChild(a);
                    ul.appendChild(li);
                });

                column.appendChild(ul);
                footerNav.appendChild(column);
            }
        });

        return doc.documentElement.outerHTML;
    }

    /**
     * Wyświetlanie powiadomienia
     * @param {string} message - Treść powiadomienia
     * @param {string} type - Typ powiadomienia ('success', 'error', 'info')
     */
    function showNotification(message, type = 'info') {
        // Sprawdź, czy istnieje już kontener powiadomień
        let notificationsContainer = document.querySelector('.notifications-container');

        if (!notificationsContainer) {
            notificationsContainer = document.createElement('div');
            notificationsContainer.className = 'notifications-container';
            document.body.appendChild(notificationsContainer);
        }

        // Utwórz nowe powiadomienie
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-message">${message}</div>
                <button class="notification-close"><i class="fas fa-times"></i></button>
            </div>
        `;

        // Dodaj powiadomienie do kontenera
        notificationsContainer.appendChild(notification);

        // Pokaż powiadomienie z animacją
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Dodaj obsługę zamykania powiadomienia
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        });

        // Automatycznie zamknij powiadomienie po 5 sekundach
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }
        }, 5000);
    }
});