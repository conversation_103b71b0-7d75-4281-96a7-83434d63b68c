document.addEventListener('DOMContentLoaded', function() {
    // Modal functionality
    const addSlideBtn = document.getElementById('addSlideBtn');
    const addSlideModal = document.getElementById('addSlideModal');
    const modalCloseButtons = document.querySelectorAll('.modal-close, .modal-close-btn');
    const confirmAddSlideBtn = document.getElementById('confirmAddSlide');
    
    // Slider items container
    const sliderItems = document.getElementById('sliderItems');
    
    // Save button
    const saveSliderBtn = document.getElementById('saveSliderBtn');
    
    // Open modal when Add Slide button is clicked
    if (addSlideBtn) {
        addSlideBtn.addEventListener('click', () => {
            addSlideModal.classList.add('show');
        });
    }
    
    // Close modal when close buttons are clicked
    modalCloseButtons.forEach(button => {
        button.addEventListener('click', () => {
            addSlideModal.classList.remove('show');
        });
    });
    
    // Close modal when clicking outside the modal content
    window.addEventListener('click', (e) => {
        if (e.target === addSlideModal) {
            addSlideModal.classList.remove('show');
        }
    });
    
    // Add new slide when confirm button is clicked
    if (confirmAddSlideBtn) {
        confirmAddSlideBtn.addEventListener('click', addNewSlide);
    }
    
    // Save slider changes
    if (saveSliderBtn) {
        saveSliderBtn.addEventListener('click', saveSliderChanges);
    }
    
    // Initialize slider item functionality
    initSliderItems();
    
    // Function to add a new slide
    function addNewSlide() {
        // Get values from the form
        const title = document.getElementById('new-slide-title').value || 'Nowy slajd';
        const subtitle = document.getElementById('new-slide-subtitle').value || 'Podtytuł slajdu';
        const btnText = document.getElementById('new-slide-btn-text').value || 'Dowiedz się więcej';
        const btnUrl = document.getElementById('new-slide-btn-url').value || '#';
        
        // Get the file name (in a real app, you would handle file upload)
        const fileInput = document.getElementById('new-slide-image');
        let fileName = 'hero-bg3.jpg'; // Default image
        if (fileInput.files.length > 0) {
            fileName = fileInput.files[0].name;
        }
        
        // Get the number of existing slides to determine the new slide ID
        const slideCount = document.querySelectorAll('.slider-item').length;
        const newSlideId = slideCount + 1;
        
        // Create new slide HTML
        const newSlideHTML = `
            <div class="slider-item" data-id="${newSlideId}">
                <div class="slider-item-header">
                    <div class="slider-item-title">
                        <h3>Slajd ${newSlideId}</h3>
                        <span class="slider-status active">Aktywny</span>
                    </div>
                    <div class="slider-item-actions">
                        <button class="btn-icon" title="Przesuń w górę">
                            <i class="fas fa-arrow-up"></i>
                        </button>
                        <button class="btn-icon" title="Przesuń w dół">
                            <i class="fas fa-arrow-down"></i>
                        </button>
                        <button class="btn-icon toggle-slide" title="Włącz/wyłącz slajd">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-icon delete-slide" title="Usuń slajd">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="slider-item-content">
                    <div class="slider-preview">
                        <img src="../images/${fileName}" alt="Podgląd slajdu">
                    </div>
                    <div class="slider-form">
                        <div class="form-group">
                            <label for="slide${newSlideId}-title">Tytuł</label>
                            <input type="text" id="slide${newSlideId}-title" class="form-control" value="${title}">
                        </div>
                        <div class="form-group">
                            <label for="slide${newSlideId}-subtitle">Podtytuł</label>
                            <input type="text" id="slide${newSlideId}-subtitle" class="form-control" value="${subtitle}">
                        </div>
                        <div class="form-group">
                            <label for="slide${newSlideId}-image">Obraz tła</label>
                            <div class="file-input-container">
                                <input type="file" id="slide${newSlideId}-image" class="file-input">
                                <div class="file-input-custom">
                                    <span class="file-name">${fileName}</span>
                                    <button class="btn btn-sm btn-outline">Wybierz plik</button>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="slide${newSlideId}-btn-text">Tekst przycisku</label>
                                <input type="text" id="slide${newSlideId}-btn-text" class="form-control" value="${btnText}">
                            </div>
                            <div class="form-group">
                                <label for="slide${newSlideId}-btn-url">URL przycisku</label>
                                <input type="text" id="slide${newSlideId}-btn-url" class="form-control" value="${btnUrl}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Add the new slide to the slider items container
        sliderItems.insertAdjacentHTML('beforeend', newSlideHTML);
        
        // Initialize the new slide's functionality
        initSliderItems();
        
        // Reset the form
        document.getElementById('new-slide-title').value = '';
        document.getElementById('new-slide-subtitle').value = '';
        document.getElementById('new-slide-btn-text').value = '';
        document.getElementById('new-slide-btn-url').value = '';
        document.getElementById('new-slide-image').value = '';
        document.querySelector('#addSlideModal .file-name').textContent = 'Nie wybrano pliku';
        
        // Close the modal
        addSlideModal.classList.remove('show');
        
        // Show success notification
        showNotification('Nowy slajd został dodany pomyślnie!', 'success');
    }
    
    // Function to initialize slider items functionality
    function initSliderItems() {
        // File input handling
        const fileInputs = document.querySelectorAll('.file-input');
        fileInputs.forEach(input => {
            input.addEventListener('change', function() {
                const fileName = this.files.length > 0 ? this.files[0].name : 'Nie wybrano pliku';
                const fileNameElement = this.parentElement.querySelector('.file-name');
                if (fileNameElement) {
                    fileNameElement.textContent = fileName;
                }
            });
        });
        
        // Toggle slide visibility
        const toggleButtons = document.querySelectorAll('.toggle-slide');
        toggleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const slideItem = this.closest('.slider-item');
                const statusElement = slideItem.querySelector('.slider-status');
                
                if (statusElement.classList.contains('active')) {
                    statusElement.classList.remove('active');
                    statusElement.textContent = 'Nieaktywny';
                    this.innerHTML = '<i class="fas fa-eye-slash"></i>';
                } else {
                    statusElement.classList.add('active');
                    statusElement.textContent = 'Aktywny';
                    this.innerHTML = '<i class="fas fa-eye"></i>';
                }
            });
        });
        
        // Delete slide
        const deleteButtons = document.querySelectorAll('.delete-slide');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                if (confirm('Czy na pewno chcesz usunąć ten slajd?')) {
                    const slideItem = this.closest('.slider-item');
                    slideItem.remove();
                    
                    // Renumber remaining slides
                    const remainingSlides = document.querySelectorAll('.slider-item');
                    remainingSlides.forEach((slide, index) => {
                        const slideNumber = index + 1;
                        slide.setAttribute('data-id', slideNumber);
                        slide.querySelector('.slider-item-title h3').textContent = `Slajd ${slideNumber}`;
                    });
                    
                    showNotification('Slajd został usunięty pomyślnie!', 'success');
                }
            });
        });
        
        // Move slide up
        const moveUpButtons = document.querySelectorAll('.slider-item-actions .btn-icon:nth-child(1)');
        moveUpButtons.forEach(button => {
            button.addEventListener('click', function() {
                const slideItem = this.closest('.slider-item');
                const prevSlide = slideItem.previousElementSibling;
                
                if (prevSlide) {
                    sliderItems.insertBefore(slideItem, prevSlide);
                    
                    // Renumber slides
                    const allSlides = document.querySelectorAll('.slider-item');
                    allSlides.forEach((slide, index) => {
                        const slideNumber = index + 1;
                        slide.setAttribute('data-id', slideNumber);
                        slide.querySelector('.slider-item-title h3').textContent = `Slajd ${slideNumber}`;
                    });
                }
            });
        });
        
        // Move slide down
        const moveDownButtons = document.querySelectorAll('.slider-item-actions .btn-icon:nth-child(2)');
        moveDownButtons.forEach(button => {
            button.addEventListener('click', function() {
                const slideItem = this.closest('.slider-item');
                const nextSlide = slideItem.nextElementSibling;
                
                if (nextSlide) {
                    sliderItems.insertBefore(nextSlide, slideItem);
                    
                    // Renumber slides
                    const allSlides = document.querySelectorAll('.slider-item');
                    allSlides.forEach((slide, index) => {
                        const slideNumber = index + 1;
                        slide.setAttribute('data-id', slideNumber);
                        slide.querySelector('.slider-item-title h3').textContent = `Slajd ${slideNumber}`;
                    });
                }
            });
        });
    }
    
    // Function to save slider changes
    function saveSliderChanges() {
        // In a real application, you would collect all the data and send it to the server
        // For this demo, we'll just show a success notification
        
        // Collect slider data
        const slides = [];
        const slideElements = document.querySelectorAll('.slider-item');
        
        slideElements.forEach((slideElement, index) => {
            const slideId = index + 1;
            const isActive = slideElement.querySelector('.slider-status').classList.contains('active');
            const title = slideElement.querySelector(`#slide${slideId}-title`).value;
            const subtitle = slideElement.querySelector(`#slide${slideId}-subtitle`).value;
            const btnText = slideElement.querySelector(`#slide${slideId}-btn-text`).value;
            const btnUrl = slideElement.querySelector(`#slide${slideId}-btn-url`).value;
            const fileName = slideElement.querySelector('.file-name').textContent;
            
            slides.push({
                id: slideId,
                active: isActive,
                title: title,
                subtitle: subtitle,
                image: fileName,
                button: {
                    text: btnText,
                    url: btnUrl
                }
            });
        });
        
        // Log the collected data (in a real app, you would send this to the server)
        console.log('Slider data:', slides);
        
        // Show success notification
        showNotification('Zmiany zostały zapisane pomyślnie!', 'success');
    }
    
    // Function to show notification
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close"><i class="fas fa-times"></i></button>
        `;
        
        // Add to document
        document.body.appendChild(notification);
        
        // Add event listener to close button
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.classList.add('hiding');
            setTimeout(() => {
                notification.remove();
            }, 300);
        });
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('hiding');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
        
        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
    }
    
    // Add CSS for notifications
    const style = document.createElement('style');
    style.textContent = `
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            z-index: 1000;
            transform: translateX(120%);
            transition: transform 0.3s ease;
            min-width: 300px;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.hiding {
            transform: translateX(120%);
        }
        
        .notification-content {
            display: flex;
            align-items: center;
        }
        
        .notification-content i {
            margin-right: 10px;
            font-size: 20px;
        }
        
        .notification.success {
            border-left: 4px solid var(--success);
        }
        
        .notification.success i {
            color: var(--success);
        }
        
        .notification.info {
            border-left: 4px solid var(--info);
        }
        
        .notification.info i {
            color: var(--info);
        }
        
        .notification.warning {
            border-left: 4px solid var(--warning);
        }
        
        .notification.warning i {
            color: var(--warning);
        }
        
        .notification.error {
            border-left: 4px solid var(--danger);
        }
        
        .notification.error i {
            color: var(--danger);
        }
        
        .notification-close {
            background: none;
            border: none;
            cursor: pointer;
            color: #6c757d;
            padding: 0;
            margin-left: 15px;
        }
        
        .notification-close:hover {
            color: #343a40;
        }
    `;
    document.head.appendChild(style);
});
