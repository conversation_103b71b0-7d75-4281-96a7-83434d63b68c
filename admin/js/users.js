/**
 * Panel Administracyjny - Żyrardów Poleca
 * JavaScript dla zarządzania użytkownikami
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja funkcji zarządzania użytkownikami
    initUsersManagement();
});

/**
 * Inicjalizacja funkcji zarządzania użytkownikami
 */
function initUsersManagement() {
    // Obsługa wyszukiwania
    initSearch();
    
    // Obsługa filtrów
    initFilters();
    
    // Obsługa zaznaczania użytkowników
    initSelections();
    
    // Obsługa akcji na użytkownikach
    initActions();
    
    // Obsługa paginacji
    initPagination();
    
    // Obsługa modalu dodawania/edycji użytkownika
    initUserModal();
    
    // Obsługa modalu resetowania hasła
    initResetPasswordModal();
}

/**
 * Inicjalizacja wyszukiwania
 */
function initSearch() {
    const searchInput = document.getElementById('user-search');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('#users-table-body tr');
            
            tableRows.forEach(row => {
                const userName = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                const userEmail = row.querySelector('td:nth-child(4)').textContent.toLowerCase();
                
                if (userName.includes(searchTerm) || userEmail.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
}

/**
 * Inicjalizacja filtrów
 */
function initFilters() {
    const roleFilter = document.getElementById('role-filter');
    const statusFilter = document.getElementById('status-filter');
    const resetFiltersBtn = document.getElementById('reset-filters');
    
    // Funkcja do filtrowania użytkowników
    function filterUsers() {
        const roleValue = roleFilter.value;
        const statusValue = statusFilter.value;
        const tableRows = document.querySelectorAll('#users-table-body tr');
        
        tableRows.forEach(row => {
            const roleElement = row.querySelector('.role-badge');
            const role = roleElement ? roleElement.classList.contains('admin') ? 'admin' : 
                        roleElement.classList.contains('editor') ? 'editor' : 'business' : '';
            
            const statusElement = row.querySelector('.status-badge');
            const status = statusElement ? statusElement.classList.contains('active') ? 'active' : 
                           statusElement.classList.contains('inactive') ? 'inactive' : 'pending' : '';
            
            let showRow = true;
            
            // Filtrowanie po roli
            if (roleValue && role !== roleValue) {
                showRow = false;
            }
            
            // Filtrowanie po statusie
            if (statusValue && status !== statusValue) {
                showRow = false;
            }
            
            row.style.display = showRow ? '' : 'none';
        });
    }
    
    // Dodanie nasłuchiwania zdarzeń
    if (roleFilter) {
        roleFilter.addEventListener('change', filterUsers);
    }
    
    if (statusFilter) {
        statusFilter.addEventListener('change', filterUsers);
    }
    
    // Resetowanie filtrów
    if (resetFiltersBtn) {
        resetFiltersBtn.addEventListener('click', function() {
            if (roleFilter) roleFilter.value = '';
            if (statusFilter) statusFilter.value = '';
            
            const tableRows = document.querySelectorAll('#users-table-body tr');
            tableRows.forEach(row => {
                row.style.display = '';
            });
        });
    }
}

/**
 * Inicjalizacja zaznaczania użytkowników
 */
function initSelections() {
    const selectAll = document.getElementById('select-all');
    const userCheckboxes = document.querySelectorAll('.user-select');
    
    if (selectAll) {
        selectAll.addEventListener('change', function() {
            userCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }
    
    // Aktualizacja stanu "zaznacz wszystkie" gdy zmienia się stan pojedynczych checkboxów
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const allChecked = Array.from(userCheckboxes).every(cb => cb.checked);
            const anyChecked = Array.from(userCheckboxes).some(cb => cb.checked);
            
            if (selectAll) {
                selectAll.checked = allChecked;
                selectAll.indeterminate = anyChecked && !allChecked;
            }
        });
    });
}

/**
 * Inicjalizacja akcji na użytkownikach
 */
function initActions() {
    // Obsługa akcji edycji, resetowania hasła i usuwania
    const actionButtons = document.querySelectorAll('.btn-icon');
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const row = this.closest('tr');
            const userName = row.querySelector('td:nth-child(3)').textContent;
            const userEmail = row.querySelector('td:nth-child(4)').textContent;
            
            if (this.classList.contains('delete')) {
                if (confirm(`Czy na pewno chcesz usunąć użytkownika "${userName}"?`)) {
                    // W rzeczywistej implementacji tutaj byłoby usuwanie użytkownika
                    row.remove();
                    showNotification(`Użytkownik "${userName}" został usunięty.`, 'success');
                }
            } else if (this.title === 'Edytuj') {
                // Otwarcie modalu edycji użytkownika
                openEditUserModal(row);
            } else if (this.title === 'Resetuj hasło') {
                // Otwarcie modalu resetowania hasła
                openResetPasswordModal(userName);
            }
        });
    });
    
    // Obsługa akcji zbiorczych
    const applyBulkAction = document.getElementById('apply-bulk-action');
    const bulkAction = document.getElementById('bulk-action');
    
    if (applyBulkAction && bulkAction) {
        applyBulkAction.addEventListener('click', function() {
            const selectedCheckboxes = document.querySelectorAll('.user-select:checked');
            const action = bulkAction.value;
            
            if (!action) {
                alert('Wybierz akcję do wykonania.');
                return;
            }
            
            if (selectedCheckboxes.length === 0) {
                alert('Zaznacz przynajmniej jednego użytkownika.');
                return;
            }
            
            const selectedRows = Array.from(selectedCheckboxes).map(checkbox => checkbox.closest('tr'));
            const userNames = selectedRows.map(row => row.querySelector('td:nth-child(3)').textContent);
            
            switch (action) {
                case 'activate':
                    if (confirm(`Czy na pewno chcesz aktywować ${selectedRows.length} użytkowników?`)) {
                        selectedRows.forEach(row => {
                            const statusBadge = row.querySelector('.status-badge');
                            statusBadge.className = 'status-badge active';
                            statusBadge.textContent = 'Aktywny';
                        });
                        showNotification(`Aktywowano ${selectedRows.length} użytkowników.`, 'success');
                    }
                    break;
                case 'deactivate':
                    if (confirm(`Czy na pewno chcesz dezaktywować ${selectedRows.length} użytkowników?`)) {
                        selectedRows.forEach(row => {
                            const statusBadge = row.querySelector('.status-badge');
                            statusBadge.className = 'status-badge inactive';
                            statusBadge.textContent = 'Nieaktywny';
                        });
                        showNotification(`Dezaktywowano ${selectedRows.length} użytkowników.`, 'success');
                    }
                    break;
                case 'delete':
                    if (confirm(`Czy na pewno chcesz usunąć ${selectedRows.length} użytkowników?`)) {
                        selectedRows.forEach(row => row.remove());
                        showNotification(`Usunięto ${selectedRows.length} użytkowników.`, 'success');
                    }
                    break;
            }
            
            // Resetowanie zaznaczenia
            const selectAll = document.getElementById('select-all');
            if (selectAll) {
                selectAll.checked = false;
                selectAll.indeterminate = false;
            }
            
            // Resetowanie akcji
            bulkAction.value = '';
        });
    }
}

/**
 * Inicjalizacja paginacji
 */
function initPagination() {
    const paginationButtons = document.querySelectorAll('.btn-page');
    
    paginationButtons.forEach(button => {
        if (!button.disabled) {
            button.addEventListener('click', function() {
                // Usunięcie aktywnej klasy z wszystkich przycisków
                paginationButtons.forEach(btn => btn.classList.remove('active'));
                
                // Dodanie aktywnej klasy do klikniętego przycisku
                this.classList.add('active');
                
                // W rzeczywistej implementacji tutaj byłoby ładowanie odpowiedniej strony
                console.log('Przejście do strony:', this.textContent);
            });
        }
    });
}

/**
 * Inicjalizacja modalu dodawania/edycji użytkownika
 */
function initUserModal() {
    const addUserBtn = document.getElementById('add-user-btn');
    const userModal = document.getElementById('userModal');
    const userForm = document.getElementById('user-form');
    const closeButtons = userModal.querySelectorAll('.modal-close, .modal-cancel');
    const userRole = document.getElementById('user-role');
    const businessFields = document.querySelector('.business-fields');
    const togglePasswordButtons = document.querySelectorAll('.toggle-password');
    
    // Otwieranie modalu dodawania użytkownika
    if (addUserBtn) {
        addUserBtn.addEventListener('click', function() {
            // Resetowanie formularza
            userForm.reset();
            document.getElementById('user-id').value = '';
            document.getElementById('modal-title').textContent = 'Dodaj użytkownika';
            
            // Ukrycie pól dla właściciela firmy
            if (businessFields) {
                businessFields.style.display = 'none';
            }
            
            // Pokazanie pola hasła
            document.querySelector('.password-group').style.display = '';
            
            // Otwarcie modalu
            userModal.classList.add('show');
        });
    }
    
    // Zamykanie modalu
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            userModal.classList.remove('show');
        });
    });
    
    // Obsługa kliknięcia poza modalem
    window.addEventListener('click', function(e) {
        if (e.target === userModal) {
            userModal.classList.remove('show');
        }
    });
    
    // Obsługa zmiany roli użytkownika
    if (userRole) {
        userRole.addEventListener('change', function() {
            if (this.value === 'business' && businessFields) {
                businessFields.style.display = '';
            } else if (businessFields) {
                businessFields.style.display = 'none';
            }
        });
    }
    
    // Obsługa przycisków pokazywania/ukrywania hasła
    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', function() {
            const passwordInput = this.previousElementSibling;
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                icon.className = 'fas fa-eye';
            }
        });
    });
    
    // Obsługa formularza dodawania/edycji użytkownika
    if (userForm) {
        userForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Pobranie danych z formularza
            const userId = document.getElementById('user-id').value;
            const firstName = document.getElementById('user-first-name').value;
            const lastName = document.getElementById('user-last-name').value;
            const email = document.getElementById('user-email').value;
            const password = document.getElementById('user-password').value;
            const role = document.getElementById('user-role').value;
            const company = document.getElementById('user-company').value;
            const status = document.querySelector('input[name="user-status"]:checked').value;
            
            // Walidacja formularza
            if (!firstName || !lastName || !email || (!userId && !password) || !role) {
                alert('Wypełnij wszystkie wymagane pola.');
                return;
            }
            
            // W rzeczywistej implementacji tutaj byłoby zapisywanie danych do API
            if (userId) {
                // Aktualizacja istniejącego użytkownika
                const row = document.querySelector(`tr[data-id="${userId}"]`);
                if (row) {
                    row.querySelector('td:nth-child(3)').textContent = `${firstName} ${lastName}`;
                    row.querySelector('td:nth-child(4)').textContent = email;
                    
                    const roleBadge = row.querySelector('.role-badge');
                    roleBadge.className = `role-badge ${role}`;
                    roleBadge.textContent = role === 'admin' ? 'Administrator' : role === 'editor' ? 'Redaktor' : 'Właściciel firmy';
                    
                    const statusBadge = row.querySelector('.status-badge');
                    statusBadge.className = `status-badge ${status}`;
                    statusBadge.textContent = status === 'active' ? 'Aktywny' : status === 'inactive' ? 'Nieaktywny' : 'Oczekujący';
                    
                    showNotification(`Użytkownik "${firstName} ${lastName}" został zaktualizowany.`, 'success');
                }
            } else {
                // Dodanie nowego użytkownika
                const newUserId = Date.now();
                const roleText = role === 'admin' ? 'Administrator' : role === 'editor' ? 'Redaktor' : 'Właściciel firmy';
                const statusText = status === 'active' ? 'Aktywny' : status === 'inactive' ? 'Nieaktywny' : 'Oczekujący';
                const avatarSrc = role === 'admin' ? 'images/admin-avatar.png' : role === 'editor' ? 'images/editor-avatar.png' : 'images/business-avatar.png';
                const today = new Date();
                const dateString = `${today.getDate().toString().padStart(2, '0')}.${(today.getMonth() + 1).toString().padStart(2, '0')}.${today.getFullYear()}`;
                
                const newRow = `
                    <tr data-id="${newUserId}">
                        <td><input type="checkbox" class="user-select"></td>
                        <td><img src="${avatarSrc}" alt="Avatar użytkownika" class="user-avatar-small"></td>
                        <td>${firstName} ${lastName}</td>
                        <td>${email}</td>
                        <td><span class="role-badge ${role}">${roleText}</span></td>
                        <td><span class="status-badge ${status}">${statusText}</span></td>
                        <td>${dateString}</td>
                        <td class="actions">
                            <button class="btn-icon" title="Edytuj"><i class="fas fa-edit"></i></button>
                            <button class="btn-icon" title="Resetuj hasło"><i class="fas fa-key"></i></button>
                            <button class="btn-icon delete" title="Usuń"><i class="fas fa-trash-alt"></i></button>
                        </td>
                    </tr>
                `;
                
                document.getElementById('users-table-body').insertAdjacentHTML('beforeend', newRow);
                
                // Dodanie obsługi zdarzeń dla nowego wiersza
                const newRowElement = document.querySelector(`tr[data-id="${newUserId}"]`);
                
                newRowElement.querySelector('.user-select').addEventListener('change', function() {
                    const allChecked = Array.from(document.querySelectorAll('.user-select')).every(cb => cb.checked);
                    const anyChecked = Array.from(document.querySelectorAll('.user-select')).some(cb => cb.checked);
                    
                    const selectAll = document.getElementById('select-all');
                    if (selectAll) {
                        selectAll.checked = allChecked;
                        selectAll.indeterminate = anyChecked && !allChecked;
                    }
                });
                
                newRowElement.querySelector('.btn-icon[title="Edytuj"]').addEventListener('click', function() {
                    openEditUserModal(newRowElement);
                });
                
                newRowElement.querySelector('.btn-icon[title="Resetuj hasło"]').addEventListener('click', function() {
                    openResetPasswordModal(`${firstName} ${lastName}`);
                });
                
                newRowElement.querySelector('.btn-icon.delete').addEventListener('click', function() {
                    if (confirm(`Czy na pewno chcesz usunąć użytkownika "${firstName} ${lastName}"?`)) {
                        newRowElement.remove();
                        showNotification(`Użytkownik "${firstName} ${lastName}" został usunięty.`, 'success');
                    }
                });
                
                showNotification(`Użytkownik "${firstName} ${lastName}" został dodany.`, 'success');
            }
            
            // Zamknięcie modalu
            userModal.classList.remove('show');
        });
    }
}

/**
 * Inicjalizacja modalu resetowania hasła
 */
function initResetPasswordModal() {
    const resetPasswordModal = document.getElementById('resetPasswordModal');
    const resetPasswordForm = document.getElementById('reset-password-form');
    const closeButtons = resetPasswordModal.querySelectorAll('.modal-close, .modal-cancel');
    const togglePasswordButtons = resetPasswordModal.querySelectorAll('.toggle-password');
    
    // Zamykanie modalu
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            resetPasswordModal.classList.remove('show');
        });
    });
    
    // Obsługa kliknięcia poza modalem
    window.addEventListener('click', function(e) {
        if (e.target === resetPasswordModal) {
            resetPasswordModal.classList.remove('show');
        }
    });
    
    // Obsługa przycisków pokazywania/ukrywania hasła
    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', function() {
            const passwordInput = this.previousElementSibling;
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                icon.className = 'fas fa-eye';
            }
        });
    });
    
    // Obsługa formularza resetowania hasła
    if (resetPasswordForm) {
        resetPasswordForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Pobranie danych z formularza
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            const sendEmail = document.getElementById('send-email').checked;
            const userName = document.getElementById('reset-user-name').textContent;
            
            // Walidacja formularza
            if (!newPassword || !confirmPassword) {
                alert('Wypełnij wszystkie wymagane pola.');
                return;
            }
            
            if (newPassword !== confirmPassword) {
                alert('Hasła nie są identyczne.');
                return;
            }
            
            // W rzeczywistej implementacji tutaj byłoby resetowanie hasła w API
            showNotification(`Hasło dla użytkownika "${userName}" zostało zresetowane.${sendEmail ? ' Email z nowym hasłem został wysłany.' : ''}`, 'success');
            
            // Zamknięcie modalu
            resetPasswordModal.classList.remove('show');
        });
    }
}

/**
 * Otwieranie modalu edycji użytkownika
 * @param {HTMLElement} row - Wiersz tabeli z danymi użytkownika
 */
function openEditUserModal(row) {
    const userModal = document.getElementById('userModal');
    
    if (userModal) {
        // Pobranie danych użytkownika z wiersza
        const userId = row.getAttribute('data-id') || Date.now();
        const userName = row.querySelector('td:nth-child(3)').textContent;
        const userEmail = row.querySelector('td:nth-child(4)').textContent;
        const userRole = row.querySelector('.role-badge').classList.contains('admin') ? 'admin' : 
                         row.querySelector('.role-badge').classList.contains('editor') ? 'editor' : 'business';
        const userStatus = row.querySelector('.status-badge').classList.contains('active') ? 'active' : 
                           row.querySelector('.status-badge').classList.contains('inactive') ? 'inactive' : 'pending';
        
        // Podział imienia i nazwiska
        const nameParts = userName.split(' ');
        const firstName = nameParts[0];
        const lastName = nameParts.slice(1).join(' ');
        
        // Wypełnienie formularza danymi użytkownika
        document.getElementById('user-id').value = userId;
        document.getElementById('user-first-name').value = firstName;
        document.getElementById('user-last-name').value = lastName;
        document.getElementById('user-email').value = userEmail;
        document.getElementById('user-role').value = userRole;
        document.querySelector(`input[name="user-status"][value="${userStatus}"]`).checked = true;
        
        // Ukrycie pola hasła przy edycji
        document.querySelector('.password-group').style.display = 'none';
        
        // Pokazanie/ukrycie pól dla właściciela firmy
        const businessFields = document.querySelector('.business-fields');
        if (businessFields) {
            businessFields.style.display = userRole === 'business' ? '' : 'none';
        }
        
        // Aktualizacja tytułu modalu
        document.getElementById('modal-title').textContent = 'Edytuj użytkownika';
        
        // Otwarcie modalu
        userModal.classList.add('show');
    }
}

/**
 * Otwieranie modalu resetowania hasła
 * @param {string} userName - Nazwa użytkownika
 */
function openResetPasswordModal(userName) {
    const resetPasswordModal = document.getElementById('resetPasswordModal');
    
    if (resetPasswordModal) {
        // Wypełnienie formularza danymi użytkownika
        document.getElementById('reset-user-name').textContent = userName;
        document.getElementById('new-password').value = '';
        document.getElementById('confirm-password').value = '';
        document.getElementById('send-email').checked = true;
        
        // Otwarcie modalu
        resetPasswordModal.classList.add('show');
    }
}

/**
 * Funkcja do wyświetlania powiadomień
 * @param {string} message - Treść powiadomienia
 * @param {string} type - Typ powiadomienia (success, info, warning, error)
 */
function showNotification(message, type = 'info') {
    // Tworzenie elementu powiadomienia
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'warning' ? 'fa-exclamation-triangle' : type === 'error' ? 'fa-times-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;
    
    // Dodanie do dokumentu
    document.body.appendChild(notification);
    
    // Dodanie nasłuchiwania zdarzenia do przycisku zamykania
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.classList.add('hiding');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
    
    // Automatyczne usunięcie po 5 sekundach
    setTimeout(() => {
        notification.classList.add('hiding');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
    
    // Pokazanie powiadomienia z animacją
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
}
