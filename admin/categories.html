<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zarządzanie kategoriami - Panel Administracyjny - Żyrardów Poleca</title>
    <meta name="description" content="Panel administracyjny portalu Żyrardów Poleca - zarządzanie kategoriami">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/categories.css">
    <link rel="icon" href="../favicon.ico" type="image/x-icon">
</head>
<body class="categories-page">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.png" alt="Żyrardów Poleca Logo" class="sidebar-logo">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="Admin Avatar">
                </div>
                <div class="user-info">
                    <h3>Administrator</h3>
                    <p><EMAIL></p>
                </div>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="companies.html">
                            <i class="fas fa-building"></i>
                            <span>Firmy</span>
                        </a>
                    </li>
                    <li>
                        <a href="offers.html">
                            <i class="fas fa-tag"></i>
                            <span>Oferty</span>
                        </a>
                    </li>
                    <li>
                        <a href="coupons.html">
                            <i class="fas fa-ticket-alt"></i>
                            <span>Kupony</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="categories.html">
                            <i class="fas fa-list"></i>
                            <span>Kategorie</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Użytkownicy</span>
                        </a>
                    </li>
                                        <li>
                        <a href="news.html">
                            <i class="fas fa-newspaper"></i>
                            <span>Wiadomości</span>
                        </a>
                    </li>
<li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Ustawienia</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <button id="logoutBtn" class="btn-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Wyloguj</span>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-title">
                    <h1>Zarządzanie kategoriami</h1>
                    <p>Dodawaj, edytuj i usuwaj kategorie i podkategorie</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-primary" id="add-category-btn">
                        <i class="fas fa-plus"></i> Dodaj kategorię
                    </button>
                </div>
            </header>

            <div class="content-body">
                <div class="categories-container">
                    <!-- Categories List -->
                    <div class="categories-list">
                        <div class="categories-header">
                            <h2>Kategorie</h2>
                            <div class="search-box">
                                <input type="text" id="category-search" placeholder="Szukaj kategorii...">
                                <button><i class="fas fa-search"></i></button>
                            </div>
                        </div>
                        <ul id="categories-tree">
                            <li class="category-item active" data-id="1">
                                <div class="category-item-header">
                                    <i class="fas fa-utensils category-icon"></i>
                                    <span class="category-name">Jedzenie i Gastronomia</span>
                                    <div class="category-actions">
                                        <button class="btn-icon add-subcategory" title="Dodaj podkategorię"><i class="fas fa-plus"></i></button>
                                        <button class="btn-icon edit-category" title="Edytuj kategorię"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon delete-category" title="Usuń kategorię"><i class="fas fa-trash-alt"></i></button>
                                    </div>
                                </div>
                                <ul class="subcategories">
                                    <li class="subcategory-item" data-id="101">
                                        <div class="subcategory-item-header">
                                            <span class="subcategory-name">Restauracje</span>
                                            <div class="subcategory-actions">
                                                <button class="btn-icon edit-subcategory" title="Edytuj podkategorię"><i class="fas fa-edit"></i></button>
                                                <button class="btn-icon delete-subcategory" title="Usuń podkategorię"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                    </li>
                                    <li class="subcategory-item" data-id="102">
                                        <div class="subcategory-item-header">
                                            <span class="subcategory-name">Pizzerie</span>
                                            <div class="subcategory-actions">
                                                <button class="btn-icon edit-subcategory" title="Edytuj podkategorię"><i class="fas fa-edit"></i></button>
                                                <button class="btn-icon delete-subcategory" title="Usuń podkategorię"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                    </li>
                                    <li class="subcategory-item" data-id="103">
                                        <div class="subcategory-item-header">
                                            <span class="subcategory-name">Fast Food</span>
                                            <div class="subcategory-actions">
                                                <button class="btn-icon edit-subcategory" title="Edytuj podkategorię"><i class="fas fa-edit"></i></button>
                                                <button class="btn-icon delete-subcategory" title="Usuń podkategorię"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                    </li>
                                    <li class="subcategory-item" data-id="104">
                                        <div class="subcategory-item-header">
                                            <span class="subcategory-name">Kawiarnie i herbaciarnie</span>
                                            <div class="subcategory-actions">
                                                <button class="btn-icon edit-subcategory" title="Edytuj podkategorię"><i class="fas fa-edit"></i></button>
                                                <button class="btn-icon delete-subcategory" title="Usuń podkategorię"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                    </li>
                                    <li class="subcategory-item" data-id="105">
                                        <div class="subcategory-item-header">
                                            <span class="subcategory-name">Cukiernie i piekarnie</span>
                                            <div class="subcategory-actions">
                                                <button class="btn-icon edit-subcategory" title="Edytuj podkategorię"><i class="fas fa-edit"></i></button>
                                                <button class="btn-icon delete-subcategory" title="Usuń podkategorię"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </li>
                            <li class="category-item" data-id="2">
                                <div class="category-item-header">
                                    <i class="fas fa-heartbeat category-icon"></i>
                                    <span class="category-name">Zdrowie i Uroda</span>
                                    <div class="category-actions">
                                        <button class="btn-icon add-subcategory" title="Dodaj podkategorię"><i class="fas fa-plus"></i></button>
                                        <button class="btn-icon edit-category" title="Edytuj kategorię"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon delete-category" title="Usuń kategorię"><i class="fas fa-trash-alt"></i></button>
                                    </div>
                                </div>
                                <ul class="subcategories">
                                    <li class="subcategory-item" data-id="201">
                                        <div class="subcategory-item-header">
                                            <span class="subcategory-name">Przychodnie i gabinety</span>
                                            <div class="subcategory-actions">
                                                <button class="btn-icon edit-subcategory" title="Edytuj podkategorię"><i class="fas fa-edit"></i></button>
                                                <button class="btn-icon delete-subcategory" title="Usuń podkategorię"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                    </li>
                                    <li class="subcategory-item" data-id="202">
                                        <div class="subcategory-item-header">
                                            <span class="subcategory-name">Apteki</span>
                                            <div class="subcategory-actions">
                                                <button class="btn-icon edit-subcategory" title="Edytuj podkategorię"><i class="fas fa-edit"></i></button>
                                                <button class="btn-icon delete-subcategory" title="Usuń podkategorię"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                    </li>
                                    <li class="subcategory-item" data-id="203">
                                        <div class="subcategory-item-header">
                                            <span class="subcategory-name">Salony kosmetyczne</span>
                                            <div class="subcategory-actions">
                                                <button class="btn-icon edit-subcategory" title="Edytuj podkategorię"><i class="fas fa-edit"></i></button>
                                                <button class="btn-icon delete-subcategory" title="Usuń podkategorię"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </li>
                            <li class="category-item" data-id="3">
                                <div class="category-item-header">
                                    <i class="fas fa-home category-icon"></i>
                                    <span class="category-name">Dom i Ogród</span>
                                    <div class="category-actions">
                                        <button class="btn-icon add-subcategory" title="Dodaj podkategorię"><i class="fas fa-plus"></i></button>
                                        <button class="btn-icon edit-category" title="Edytuj kategorię"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon delete-category" title="Usuń kategorię"><i class="fas fa-trash-alt"></i></button>
                                    </div>
                                </div>
                                <ul class="subcategories">
                                    <li class="subcategory-item" data-id="301">
                                        <div class="subcategory-item-header">
                                            <span class="subcategory-name">Sklepy meblowe</span>
                                            <div class="subcategory-actions">
                                                <button class="btn-icon edit-subcategory" title="Edytuj podkategorię"><i class="fas fa-edit"></i></button>
                                                <button class="btn-icon delete-subcategory" title="Usuń podkategorię"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                    </li>
                                    <li class="subcategory-item" data-id="302">
                                        <div class="subcategory-item-header">
                                            <span class="subcategory-name">Sklepy budowlane</span>
                                            <div class="subcategory-actions">
                                                <button class="btn-icon edit-subcategory" title="Edytuj podkategorię"><i class="fas fa-edit"></i></button>
                                                <button class="btn-icon delete-subcategory" title="Usuń podkategorię"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </div>

                    <!-- Category Details -->
                    <div class="category-details">
                        <div class="category-details-header">
                            <h2>Szczegóły kategorii</h2>
                        </div>
                        <div class="category-details-content">
                            <form id="category-form">
                                <div class="form-group">
                                    <label for="category-name-input">Nazwa kategorii</label>
                                    <input type="text" id="category-name-input" class="form-control" value="Restauracje i kawiarnie">
                                </div>
                                <div class="form-group">
                                    <label for="category-icon">Ikona kategorii</label>
                                    <div class="icon-selector">
                                        <div class="selected-icon">
                                            <i class="fas fa-utensils"></i>
                                        </div>
                                        <div class="icon-dropdown">
                                            <div class="icon-option" data-icon="fa-utensils"><i class="fas fa-utensils"></i></div>
                                            <div class="icon-option" data-icon="fa-shopping-bag"><i class="fas fa-shopping-bag"></i></div>
                                            <div class="icon-option" data-icon="fa-briefcase"><i class="fas fa-briefcase"></i></div>
                                            <div class="icon-option" data-icon="fa-heartbeat"><i class="fas fa-heartbeat"></i></div>
                                            <div class="icon-option" data-icon="fa-gamepad"><i class="fas fa-gamepad"></i></div>
                                            <div class="icon-option" data-icon="fa-graduation-cap"><i class="fas fa-graduation-cap"></i></div>
                                            <div class="icon-option" data-icon="fa-car"><i class="fas fa-car"></i></div>
                                            <div class="icon-option" data-icon="fa-home"><i class="fas fa-home"></i></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="category-description">Opis kategorii</label>
                                    <textarea id="category-description" class="form-control" rows="3">Restauracje, kawiarnie, bary i inne lokale gastronomiczne w Żyrardowie.</textarea>
                                </div>
                                <div class="form-group">
                                    <label for="category-slug">Przyjazny URL</label>
                                    <div class="input-group">
                                        <span class="input-group-text">https://zyrardow.poleca.to/katalog-firm/</span>
                                        <input type="text" id="category-slug" class="form-control" value="restauracje-i-kawiarnie">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Status</label>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="category-status" checked>
                                        <label for="category-status"></label>
                                        <span class="toggle-label">Aktywna</span>
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="button" class="btn btn-outline" id="cancel-btn">Anuluj</button>
                                    <button type="submit" class="btn btn-primary">Zapisz zmiany</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Add Category Modal -->
    <div class="modal" id="addCategoryModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Dodaj nową kategorię</h2>
                <button class="modal-close"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <form id="add-category-form">
                    <div class="form-group">
                        <label for="new-category-name">Nazwa kategorii</label>
                        <input type="text" id="new-category-name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="new-category-icon">Ikona kategorii</label>
                        <div class="icon-selector">
                            <div class="selected-icon">
                                <i class="fas fa-list"></i>
                            </div>
                            <div class="icon-dropdown">
                                <div class="icon-option" data-icon="fa-utensils"><i class="fas fa-utensils"></i></div>
                                <div class="icon-option" data-icon="fa-shopping-bag"><i class="fas fa-shopping-bag"></i></div>
                                <div class="icon-option" data-icon="fa-briefcase"><i class="fas fa-briefcase"></i></div>
                                <div class="icon-option" data-icon="fa-heartbeat"><i class="fas fa-heartbeat"></i></div>
                                <div class="icon-option" data-icon="fa-gamepad"><i class="fas fa-gamepad"></i></div>
                                <div class="icon-option" data-icon="fa-graduation-cap"><i class="fas fa-graduation-cap"></i></div>
                                <div class="icon-option" data-icon="fa-car"><i class="fas fa-car"></i></div>
                                <div class="icon-option" data-icon="fa-home"><i class="fas fa-home"></i></div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="new-category-description">Opis kategorii</label>
                        <textarea id="new-category-description" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline modal-cancel">Anuluj</button>
                        <button type="submit" class="btn btn-primary">Dodaj kategorię</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Subcategory Modal -->
    <div class="modal" id="addSubcategoryModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Dodaj nową podkategorię</h2>
                <button class="modal-close"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <form id="add-subcategory-form">
                    <input type="hidden" id="parent-category-id">
                    <div class="form-group">
                        <label for="parent-category-name">Kategoria nadrzędna</label>
                        <input type="text" id="parent-category-name" class="form-control" readonly>
                    </div>
                    <div class="form-group">
                        <label for="new-subcategory-name">Nazwa podkategorii</label>
                        <input type="text" id="new-subcategory-name" class="form-control" required>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline modal-cancel">Anuluj</button>
                        <button type="submit" class="btn btn-primary">Dodaj podkategorię</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/admin.js"></script>
    <script src="js/categories-sqlite.js"></script>
</body>
</html>
