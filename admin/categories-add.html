<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dodaj kategorię - Panel Administracyjny - Żyrardów Poleca</title>
    <meta name="description" content="Panel administracyjny portalu Żyrardów Poleca - dodawanie nowej kategorii">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/categories.css">
    <link rel="icon" href="../favicon.ico" type="image/x-icon">
</head>
<body class="categories-add-page">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo-zyrardow-poleca.png" alt="Żyrardów Poleca Logo" class="sidebar-logo" style="max-height: 40px;">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="Admin Avatar">
                </div>
                <div class="user-info">
                    <h3>Administrator</h3>
                    <p><EMAIL></p>
                </div>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="companies.html">
                            <i class="fas fa-building"></i>
                            <span>Firmy</span>
                        </a>
                    </li>
                    <li>
                        <a href="offers.html">
                            <i class="fas fa-tag"></i>
                            <span>Oferty</span>
                        </a>
                    </li>
                    <li>
                        <a href="coupons.html">
                            <i class="fas fa-ticket-alt"></i>
                            <span>Kupony</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="categories.html">
                            <i class="fas fa-list"></i>
                            <span>Kategorie</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Użytkownicy</span>
                        </a>
                    </li>
                    <li>
                        <a href="menus.html">
                            <i class="fas fa-bars"></i>
                            <span>Menu</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Ustawienia</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <button id="logoutBtn" class="btn-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Wyloguj</span>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-title">
                    <h1>Dodaj nową kategorię</h1>
                    <p>Uzupełnij formularz, aby dodać nową kategorię</p>
                </div>
                <div class="header-actions">
                    <a href="categories.html" class="btn btn-outline">
                        <i class="fas fa-arrow-left"></i> Powrót do listy
                    </a>
                </div>
            </header>

            <div class="content-body">
                <div class="card">
                    <div class="card-body">
                        <form id="addCategoryForm" class="form">
                            <div class="form-section">
                                <h2>Informacje podstawowe</h2>
                                
                                <div class="form-group">
                                    <label for="category-name">Nazwa kategorii <span class="required">*</span></label>
                                    <input type="text" id="category-name" name="category-name" class="form-control" required>
                                    <small class="form-text">Nazwa kategorii widoczna dla użytkowników</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="category-slug">Slug <span class="required">*</span></label>
                                    <input type="text" id="category-slug" name="category-slug" class="form-control" required>
                                    <small class="form-text">Unikalny identyfikator kategorii używany w URL (np. "zdrowie-i-uroda")</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="category-parent">Kategoria nadrzędna</label>
                                    <select id="category-parent" name="category-parent" class="form-control">
                                        <option value="0">Brak (kategoria główna)</option>
                                        <option value="1">Jedzenie i Gastronomia</option>
                                        <option value="2">Zdrowie i Uroda</option>
                                        <option value="3">Dom i Ogród</option>
                                        <option value="4">Motoryzacja</option>
                                        <option value="5">Edukacja i Nauka</option>
                                    </select>
                                    <small class="form-text">Wybierz kategorię nadrzędną, jeśli dodajesz podkategorię</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="category-icon">Ikona</label>
                                    <div class="icon-selector">
                                        <div class="selected-icon">
                                            <i class="fas fa-utensils"></i>
                                        </div>
                                        <button type="button" class="btn btn-outline btn-sm" id="chooseIconBtn">
                                            <i class="fas fa-icons"></i> Wybierz ikonę
                                        </button>
                                    </div>
                                    <input type="hidden" id="category-icon" name="category-icon" value="fa-utensils">
                                    <small class="form-text">Ikona wyświetlana obok nazwy kategorii</small>
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <h2>Opis i SEO</h2>
                                
                                <div class="form-group">
                                    <label for="category-description">Opis kategorii</label>
                                    <textarea id="category-description" name="category-description" class="form-control" rows="4"></textarea>
                                    <small class="form-text">Krótki opis kategorii widoczny dla użytkowników</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="category-meta-title">Meta tytuł</label>
                                    <input type="text" id="category-meta-title" name="category-meta-title" class="form-control">
                                    <small class="form-text">Tytuł strony kategorii (SEO)</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="category-meta-description">Meta opis</label>
                                    <textarea id="category-meta-description" name="category-meta-description" class="form-control" rows="3"></textarea>
                                    <small class="form-text">Opis strony kategorii (SEO)</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="category-keywords">Słowa kluczowe</label>
                                    <input type="text" id="category-keywords" name="category-keywords" class="form-control">
                                    <small class="form-text">Słowa kluczowe oddzielone przecinkami</small>
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button type="button" class="btn btn-outline" id="cancelBtn">Anuluj</button>
                                <button type="submit" class="btn btn-primary">Dodaj kategorię</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Icon Picker Modal -->
    <div class="modal" id="iconPickerModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Wybierz ikonę</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="icon-search">
                    <input type="text" placeholder="Szukaj ikon..." class="form-control">
                </div>
                <div class="icons-grid">
                    <!-- Icons will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline modal-cancel">Anuluj</button>
                <button class="btn btn-primary" id="selectIconBtn">Wybierz</button>
            </div>
        </div>
    </div>

    <script src="js/admin.js"></script>
    <script src="js/categories.js"></script>
</body>
</html>
