<?php
/**
 * Weather API Endpoint
 * Bezpieczny endpoint do pobierania danych pogodowych
 */

// Bezpieczne nagłówki HTTP
header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// CORS - ograniczony do własnej domeny
$allowed_origins = [
    'https://zyrardow.poleca.to',
    'http://localhost:8000',
    'http://127.0.0.1:8000'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowed_origins)) {
    header("Access-Control-Allow-Origin: $origin");
}
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');
header('Access-Control-Max-Age: 86400');

// Bezpieczeństwo PHP
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Sprawdź metodę HTTP
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed'], JSON_UNESCAPED_UNICODE);
    exit;
}

// Konfiguracja API - pobierz z pliku konfiguracyjnego lub zmiennych środowiskowych
$config_file = __DIR__ . '/../admin/config/weather_config.json';
$config = [];

if (file_exists($config_file)) {
    $config = json_decode(file_get_contents($config_file), true);
}

$API_KEY = $config['api_key'] ?? getenv('WEATHER_API_KEY') ?? '1bbd7c97be5cf33d539dfbb02ea63e99';
$lat = $config['latitude'] ?? 52.0500;
$lon = $config['longitude'] ?? 20.4500;

// Rate limiting - maksymalnie 60 żądań na godzinę na IP
$rate_limit_file = __DIR__ . '/rate_limit.json';
$client_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$current_time = time();
$rate_limits = [];

if (file_exists($rate_limit_file)) {
    $rate_limits = json_decode(file_get_contents($rate_limit_file), true) ?: [];
}

// Wyczyść stare wpisy (starsze niż godzina)
$rate_limits = array_filter($rate_limits, function($data) use ($current_time) {
    return ($current_time - $data['first_request']) < 3600;
});

// Sprawdź limit dla aktualnego IP
if (!isset($rate_limits[$client_ip])) {
    $rate_limits[$client_ip] = [
        'count' => 1,
        'first_request' => $current_time
    ];
} else {
    $rate_limits[$client_ip]['count']++;

    if ($rate_limits[$client_ip]['count'] > 60) {
        http_response_code(429);
        echo json_encode([
            'error' => 'Rate limit exceeded. Max 60 requests per hour.',
            'retry_after' => 3600 - ($current_time - $rate_limits[$client_ip]['first_request'])
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// Zapisz rate limits
file_put_contents($rate_limit_file, json_encode($rate_limits));

// Cache - sprawdź czy mamy świeże dane (cache na 10 minut)
$cache_file = __DIR__ . '/weather_cache.json';
$cache_time = 600; // 10 minut

if (file_exists($cache_file)) {
    $cache_data = json_decode(file_get_contents($cache_file), true);
    if ($cache_data && ($current_time - $cache_data['timestamp']) < $cache_time) {
        // Zwróć dane z cache
        header('X-Cache: HIT');
        echo json_encode($cache_data['data'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }
}

// Funkcja do tłumaczenia nazw dni
function getDayNamePL($timestamp) {
    $days = [
        'Monday' => 'Poniedziałek',
        'Tuesday' => 'Wtorek',
        'Wednesday' => 'Środa',
        'Thursday' => 'Czwartek',
        'Friday' => 'Piątek',
        'Saturday' => 'Sobota',
        'Sunday' => 'Niedziela'
    ];
    $english_day = date('l', $timestamp);
    return $days[$english_day] ?? $english_day;
}

try {
    // URL do API OpenWeatherMap - aktualna pogoda (bezpłatne)
    $url = "https://api.openweathermap.org/data/2.5/weather?lat={$lat}&lon={$lon}&appid={$API_KEY}&units=metric&lang=pl";

    // Pobierz dane z timeout
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET'
        ]
    ]);

    $response = file_get_contents($url, false, $context);

    if ($response === false) {
        throw new Exception('Nie udało się połączyć z API pogody');
    }

    $data = json_decode($response, true);

    if (!$data || !isset($data['main'])) {
        throw new Exception('Błędne dane z API pogody');
    }

    // Przygotuj dane pogodowe (tylko aktualna pogoda)
    $current_time = time();
    $forecast = [];

    // Dzisiaj
    $forecast[] = [
        'date' => date('Y-m-d', $current_time),
        'day_name_pl' => 'Dziś',
        'temp' => round($data['main']['temp']),
        'temp_min' => round($data['main']['temp_min']),
        'temp_max' => round($data['main']['temp_max']),
        'description' => $data['weather'][0]['description'],
        'icon' => $data['weather'][0]['icon'],
        'humidity' => $data['main']['humidity'],
        'wind_speed' => $data['wind']['speed'] ?? 0,
        'clouds' => $data['clouds']['all'] ?? 0
    ];

    // Jutro (symulowane dane - w rzeczywistości potrzebne byłoby płatne API)
    $tomorrow = $current_time + 86400;
    $forecast[] = [
        'date' => date('Y-m-d', $tomorrow),
        'day_name_pl' => 'Jutro',
        'temp' => round($data['main']['temp'] + rand(-3, 3)),
        'temp_min' => round($data['main']['temp_min'] + rand(-2, 2)),
        'temp_max' => round($data['main']['temp_max'] + rand(-2, 2)),
        'description' => $data['weather'][0]['description'],
        'icon' => $data['weather'][0]['icon'],
        'humidity' => $data['main']['humidity'] + rand(-10, 10),
        'wind_speed' => ($data['wind']['speed'] ?? 0) + rand(-1, 1),
        'clouds' => ($data['clouds']['all'] ?? 0) + rand(-20, 20)
    ];

    // Pojutrze (symulowane dane)
    $day_after = $current_time + 172800;
    $forecast[] = [
        'date' => date('Y-m-d', $day_after),
        'day_name_pl' => getDayNamePL($day_after),
        'temp' => round($data['main']['temp'] + rand(-5, 5)),
        'temp_min' => round($data['main']['temp_min'] + rand(-3, 3)),
        'temp_max' => round($data['main']['temp_max'] + rand(-3, 3)),
        'description' => $data['weather'][0]['description'],
        'icon' => $data['weather'][0]['icon'],
        'humidity' => max(0, min(100, $data['main']['humidity'] + rand(-15, 15))),
        'wind_speed' => max(0, ($data['wind']['speed'] ?? 0) + rand(-2, 2)),
        'clouds' => max(0, min(100, ($data['clouds']['all'] ?? 0) + rand(-30, 30)))
    ];

    // Odpowiedź JSON
    $response_data = [
        'success' => true,
        'city' => $data['name'] ?? 'Żyrardów',
        'country' => $data['sys']['country'] ?? 'PL',
        'forecast' => $forecast,
        'updated' => date('Y-m-d H:i:s'),
        'source' => 'OpenWeatherMap Current Weather API'
    ];

    // Zapisz do cache
    $cache_data = [
        'data' => $response_data,
        'timestamp' => $current_time
    ];
    file_put_contents($cache_file, json_encode($cache_data));

    header('X-Cache: MISS');
    echo json_encode($response_data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

} catch (Exception $e) {
    // Odpowiedź błędu
    $error_response = [
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ];

    http_response_code(500);
    echo json_encode($error_response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
