# Bezpieczeństwo API
<Files "weather.php">
    # <PERSON>ez<PERSON><PERSON>l dostęp dla wszystkich podczas testów
    Require all granted
</Files>

# Ogranicz dostęp do plików konfiguracyjnych
<Files "*.json">
    Require all denied
</Files>

# Nagłówki bezpieczeństwa
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Cache dla API (krótki czas)
<Files "weather.php">
    Header set Cache-Control "public, max-age=1800"
    Header set Expires "Thu, 01 Jan 2025 00:00:00 GMT"
</Files>
