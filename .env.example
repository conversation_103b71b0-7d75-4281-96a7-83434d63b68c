# Konfiguracja bazy danych MySQL
DB_HOST=localhost
DB_PORT=3306
DB_NAME=zyrardow_poleca_db
DB_USER=zyrardow_admin
DB_PASSWORD=ZyrardowPoleca2024!@#

# Konfiguracja JWT
JWT_SECRET=super_secret_jwt_key_zyrardow_poleca_2024_very_long_and_secure
JWT_EXPIRES_IN=24h

# Konfiguracja serwera
PORT=3000
NODE_ENV=development

# Konfiguracja admina (domyślne dane logowania)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=AdminZyrardow2024!

# Konfiguracja uploadów
UPLOAD_MAX_SIZE=5242880
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp

# Konfiguracja bezpieczeństwa
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# Konfiguracja CORS
CORS_ORIGIN=http://localhost:3000

# Hasło root MySQL (dla skryptu setup-database.js)
MYSQL_ROOT_PASSWORD=

# Konfiguracja SSL (dla produkcji)
SSL_CERT_PATH=/etc/ssl/certs/zyrardow.poleca.to.crt
SSL_KEY_PATH=/etc/ssl/private/zyrardow.poleca.to.key
