# 🚀 Instrukcja wdrożenia na VPS Aruba

## Wymagania systemowe

- **System operacyjny**: Ubuntu 20.04 LTS lub nowszy
- **RAM**: Minimum 2GB (zalecane 4GB)
- **Dysk**: Minimum 20GB wolnego miejsca
- **CPU**: 2 vCPU
- **Dostęp**: SSH z uprawnieniami sudo

## 1. Przygotowanie serwera VPS

### Aktualizacja systemu
```bash
sudo apt update && sudo apt upgrade -y
```

### Instalacja podstawowych narzędzi
```bash
sudo apt install -y curl wget git unzip software-properties-common
```

### Konfiguracja firewall
```bash
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 3000  # Port aplikacji (tymczasowo)
```

## 2. Instalacja Node.js

### Instalacja Node.js 18.x LTS
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### Weryfikacja instalacji
```bash
node --version  # Powinno pokazać v18.x.x
npm --version   # Powinno pokazać 9.x.x lub nowszy
```

### Instalacja PM2 (Process Manager)
```bash
sudo npm install -g pm2
```

## 3. Instalacja MySQL

### Instalacja MySQL Server
```bash
sudo apt install -y mysql-server
```

### Zabezpieczenie MySQL
```bash
sudo mysql_secure_installation
```

**Odpowiedzi na pytania:**
- Remove anonymous users? **Y**
- Disallow root login remotely? **Y**
- Remove test database? **Y**
- Reload privilege tables? **Y**

### Konfiguracja MySQL
```bash
sudo mysql -u root -p
```

```sql
-- Utwórz bazę danych
CREATE DATABASE zyrardow_poleca_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Utwórz użytkownika
CREATE USER 'zyrardow_admin'@'localhost' IDENTIFIED BY 'ZyrardowPoleca2024!@#';

-- Nadaj uprawnienia
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER, CREATE TEMPORARY TABLES, LOCK TABLES ON zyrardow_poleca_db.* TO 'zyrardow_admin'@'localhost';

-- Odśwież uprawnienia
FLUSH PRIVILEGES;

-- Wyjdź
EXIT;
```

## 4. Instalacja Nginx

### Instalacja Nginx
```bash
sudo apt install -y nginx
```

### Uruchomienie i włączenie autostartu
```bash
sudo systemctl start nginx
sudo systemctl enable nginx
```

## 5. Wdrożenie aplikacji

### Utworzenie użytkownika aplikacji
```bash
sudo adduser --system --group --home /var/www/zyrardow-poleca zyrardow
```

### Klonowanie repozytorium
```bash
sudo -u zyrardow git clone https://github.com/twoje-repo/portal-zyrardow-poleca-to.git /var/www/zyrardow-poleca/app
```

### Przejście do katalogu aplikacji
```bash
cd /var/www/zyrardow-poleca/app
```

### Instalacja zależności
```bash
sudo -u zyrardow npm install --production
```

### Konfiguracja zmiennych środowiskowych
```bash
sudo -u zyrardow cp .env.example .env
sudo -u zyrardow nano .env
```

**Edytuj plik .env:**
```env
# Konfiguracja bazy danych MySQL
DB_HOST=localhost
DB_PORT=3306
DB_NAME=zyrardow_poleca_db
DB_USER=zyrardow_admin
DB_PASSWORD=ZyrardowPoleca2024!@#

# Konfiguracja JWT (wygeneruj nowy klucz!)
JWT_SECRET=WYGENERUJ_BARDZO_DŁUGI_I_BEZPIECZNY_KLUCZ_JWT_TUTAJ
JWT_EXPIRES_IN=24h

# Konfiguracja serwera
PORT=3000
NODE_ENV=production

# Konfiguracja admina
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=ZMIEŃ_TO_HASŁO_NA_BEZPIECZNE

# Konfiguracja CORS
CORS_ORIGIN=https://zyrardow.poleca.to

# Hasło root MySQL (dla skryptu setup)
MYSQL_ROOT_PASSWORD=TWOJE_HASŁO_ROOT_MYSQL
```

### Inicjalizacja bazy danych
```bash
sudo -u zyrardow npm run setup-db
```

### Test aplikacji
```bash
sudo -u zyrardow npm start
```

Sprawdź czy aplikacja działa: `http://TWÓJ_IP:3000`

## 6. Konfiguracja PM2

### Utworzenie pliku konfiguracyjnego PM2
```bash
sudo -u zyrardow nano /var/www/zyrardow-poleca/app/ecosystem.config.js
```

```javascript
module.exports = {
  apps: [{
    name: 'zyrardow-poleca',
    script: 'server/app.js',
    cwd: '/var/www/zyrardow-poleca/app',
    user: 'zyrardow',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/zyrardow-poleca/error.log',
    out_file: '/var/log/zyrardow-poleca/out.log',
    log_file: '/var/log/zyrardow-poleca/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

### Utworzenie katalogu logów
```bash
sudo mkdir -p /var/log/zyrardow-poleca
sudo chown zyrardow:zyrardow /var/log/zyrardow-poleca
```

### Uruchomienie aplikacji przez PM2
```bash
sudo -u zyrardow pm2 start ecosystem.config.js
sudo -u zyrardow pm2 save
sudo pm2 startup
```

## 7. Konfiguracja Nginx

### Utworzenie konfiguracji Nginx
```bash
sudo nano /etc/nginx/sites-available/zyrardow-poleca
```

```nginx
server {
    listen 80;
    server_name zyrardow.poleca.to www.zyrardow.poleca.to;
    
    # Przekierowanie HTTP na HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name zyrardow.poleca.to www.zyrardow.poleca.to;
    
    # Certyfikaty SSL (będą skonfigurowane przez Certbot)
    ssl_certificate /etc/letsencrypt/live/zyrardow.poleca.to/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/zyrardow.poleca.to/privkey.pem;
    
    # Konfiguracja SSL
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Bezpieczeństwo
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Kompresja
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Limity
    client_max_body_size 10M;
    
    # Statyczne pliki
    location /uploads/ {
        alias /var/www/zyrardow-poleca/app/server/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /admin/ {
        alias /var/www/zyrardow-poleca/app/admin/;
        try_files $uri $uri/ /admin/index.html;
        expires 1h;
    }
    
    location / {
        root /var/www/zyrardow-poleca/app/public;
        try_files $uri $uri/ @proxy;
        expires 1h;
    }
    
    # Proxy do aplikacji Node.js
    location @proxy {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # API endpoints
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### Aktywacja konfiguracji
```bash
sudo ln -s /etc/nginx/sites-available/zyrardow-poleca /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 8. Konfiguracja SSL (Let's Encrypt)

### Instalacja Certbot
```bash
sudo apt install -y certbot python3-certbot-nginx
```

### Uzyskanie certyfikatu SSL
```bash
sudo certbot --nginx -d zyrardow.poleca.to -d www.zyrardow.poleca.to
```

### Automatyczne odnawianie certyfikatu
```bash
sudo crontab -e
```

Dodaj linię:
```
0 12 * * * /usr/bin/certbot renew --quiet
```

## 9. Konfiguracja backupów

### Skrypt backup bazy danych
```bash
sudo nano /usr/local/bin/backup-zyrardow-db.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/zyrardow-poleca"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="zyrardow_poleca_db"
DB_USER="zyrardow_admin"
DB_PASS="ZyrardowPoleca2024!@#"

mkdir -p $BACKUP_DIR

# Backup bazy danych
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Backup plików uploads
tar -czf $BACKUP_DIR/uploads_backup_$DATE.tar.gz -C /var/www/zyrardow-poleca/app/server uploads/

# Usuń backupy starsze niż 7 dni
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

```bash
sudo chmod +x /usr/local/bin/backup-zyrardow-db.sh
```

### Automatyczne backupy
```bash
sudo crontab -e
```

Dodaj linię:
```
0 2 * * * /usr/local/bin/backup-zyrardow-db.sh
```

## 10. Monitorowanie

### Konfiguracja logrotate
```bash
sudo nano /etc/logrotate.d/zyrardow-poleca
```

```
/var/log/zyrardow-poleca/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 zyrardow zyrardow
    postrotate
        sudo -u zyrardow pm2 reloadLogs
    endscript
}
```

### Sprawdzenie statusu aplikacji
```bash
sudo -u zyrardow pm2 status
sudo -u zyrardow pm2 logs
sudo -u zyrardow pm2 monit
```

## 11. Aktualizacje

### Skrypt aktualizacji
```bash
sudo nano /usr/local/bin/update-zyrardow.sh
```

```bash
#!/bin/bash
APP_DIR="/var/www/zyrardow-poleca/app"
cd $APP_DIR

echo "Pobieranie najnowszych zmian..."
sudo -u zyrardow git pull origin main

echo "Instalacja zależności..."
sudo -u zyrardow npm install --production

echo "Restart aplikacji..."
sudo -u zyrardow pm2 restart zyrardow-poleca

echo "Aktualizacja zakończona!"
```

```bash
sudo chmod +x /usr/local/bin/update-zyrardow.sh
```

## 12. Bezpieczeństwo

### Konfiguracja fail2ban
```bash
sudo apt install -y fail2ban
sudo nano /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/error.log
maxretry = 10
findtime = 600
bantime = 7200
```

```bash
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

## 13. Finalne sprawdzenie

### Test wszystkich usług
```bash
# Status MySQL
sudo systemctl status mysql

# Status Nginx
sudo systemctl status nginx

# Status aplikacji
sudo -u zyrardow pm2 status

# Test połączenia z bazą danych
cd /var/www/zyrardow-poleca/app
sudo -u zyrardow node server/scripts/setup-database.js --test

# Test aplikacji
curl -k https://zyrardow.poleca.to/api/health
```

## 🎉 Gotowe!

Aplikacja powinna być teraz dostępna pod adresem:
- **Strona główna**: https://zyrardow.poleca.to
- **Panel admin**: https://zyrardow.poleca.to/admin

### Dane logowania do panelu admin:
- **Email**: <EMAIL>
- **Hasło**: (ustawione w .env)

**⚠️ WAŻNE**: Zmień hasło administratora po pierwszym logowaniu!

### Przydatne komendy:
```bash
# Restart aplikacji
sudo -u zyrardow pm2 restart zyrardow-poleca

# Logi aplikacji
sudo -u zyrardow pm2 logs

# Status serwera
sudo -u zyrardow pm2 monit

# Backup ręczny
sudo /usr/local/bin/backup-zyrardow-db.sh

# Aktualizacja aplikacji
sudo /usr/local/bin/update-zyrardow.sh
```
