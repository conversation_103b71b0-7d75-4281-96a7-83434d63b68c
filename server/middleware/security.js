const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const config = require('../config/config');

/**
 * Konfiguracja Helmet dla bezpieczeństwa nagłówków HTTP
 */
const helmetConfig = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdnjs.cloudflare.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      connectSrc: ["'self'"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null
    }
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

/**
 * Rate limiting dla API
 */
const apiLimiter = rateLimit({
  windowMs: config.security.rateLimitWindow * 60 * 1000, // minuty na milisekundy
  max: config.security.rateLimitMax,
  message: {
    success: false,
    message: 'Zbyt wiele żądań z tego adresu IP. Spróbuj ponownie później.',
    retryAfter: config.security.rateLimitWindow
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    console.warn(`Rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`);
    res.status(429).json({
      success: false,
      message: 'Zbyt wiele żądań z tego adresu IP. Spróbuj ponownie później.',
      retryAfter: config.security.rateLimitWindow
    });
  }
});

/**
 * Bardziej restrykcyjny rate limiting dla logowania
 */
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minut
  max: 5, // maksymalnie 5 prób logowania na 15 minut
  skipSuccessfulRequests: true,
  message: {
    success: false,
    message: 'Zbyt wiele nieudanych prób logowania. Spróbuj ponownie za 15 minut.',
    retryAfter: 15
  },
  handler: (req, res) => {
    console.warn(`Login rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      success: false,
      message: 'Zbyt wiele nieudanych prób logowania. Spróbuj ponownie za 15 minut.',
      retryAfter: 15
    });
  }
});

/**
 * Rate limiting dla uploadów plików
 */
const uploadLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minuta
  max: 10, // maksymalnie 10 uploadów na minutę
  message: {
    success: false,
    message: 'Zbyt wiele uploadów plików. Spróbuj ponownie za minutę.',
    retryAfter: 1
  }
});

/**
 * Middleware walidacji IP (opcjonalne - do użycia w produkcji)
 */
const ipWhitelist = (allowedIPs = []) => {
  return (req, res, next) => {
    if (allowedIPs.length === 0) {
      return next(); // Jeśli brak listy, przepuść wszystkie IP
    }
    
    const clientIP = req.ip || req.connection.remoteAddress;
    
    if (!allowedIPs.includes(clientIP)) {
      console.warn(`Blocked request from unauthorized IP: ${clientIP}`);
      return res.status(403).json({
        success: false,
        message: 'Dostęp zabroniony z tego adresu IP.'
      });
    }
    
    next();
  };
};

/**
 * Middleware sanityzacji danych wejściowych
 */
const sanitizeInput = (req, res, next) => {
  // Funkcja rekurencyjna do sanityzacji obiektów
  const sanitize = (obj) => {
    if (typeof obj === 'string') {
      // Usuń potencjalnie niebezpieczne znaki
      return obj
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Usuń tagi script
        .replace(/javascript:/gi, '') // Usuń javascript: URLs
        .replace(/on\w+\s*=/gi, '') // Usuń event handlery
        .trim();
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitize);
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          sanitized[key] = sanitize(obj[key]);
        }
      }
      return sanitized;
    }
    
    return obj;
  };
  
  // Sanityzuj body, query i params
  if (req.body) {
    req.body = sanitize(req.body);
  }
  
  if (req.query) {
    req.query = sanitize(req.query);
  }
  
  if (req.params) {
    req.params = sanitize(req.params);
  }
  
  next();
};

/**
 * Middleware logowania żądań bezpieczeństwa
 */
const securityLogger = (req, res, next) => {
  const startTime = Date.now();
  
  // Loguj podejrzane żądania
  const suspiciousPatterns = [
    /\.\./,  // Path traversal
    /<script/i,  // XSS attempts
    /union.*select/i,  // SQL injection
    /exec\(/i,  // Code execution
    /eval\(/i   // Code evaluation
  ];
  
  const url = req.originalUrl || req.url;
  const userAgent = req.get('User-Agent') || '';
  const referer = req.get('Referer') || '';
  
  // Sprawdź czy żądanie jest podejrzane
  const isSuspicious = suspiciousPatterns.some(pattern => 
    pattern.test(url) || 
    pattern.test(userAgent) || 
    pattern.test(JSON.stringify(req.body || {}))
  );
  
  if (isSuspicious) {
    console.warn('🚨 Podejrzane żądanie:', {
      ip: req.ip,
      method: req.method,
      url: url,
      userAgent: userAgent,
      referer: referer,
      body: req.body
    });
  }
  
  // Loguj czas odpowiedzi
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    if (duration > 5000) { // Żądania dłuższe niż 5 sekund
      console.warn('⏱️ Wolne żądanie:', {
        method: req.method,
        url: url,
        duration: `${duration}ms`,
        status: res.statusCode
      });
    }
  });
  
  next();
};

/**
 * Middleware sprawdzenia rozmiaru żądania
 */
const requestSizeLimit = (maxSize = '10mb') => {
  return (req, res, next) => {
    const contentLength = parseInt(req.get('Content-Length') || '0');
    const maxSizeBytes = typeof maxSize === 'string' ? 
      parseInt(maxSize) * (maxSize.includes('mb') ? 1024 * 1024 : 1024) : 
      maxSize;
    
    if (contentLength > maxSizeBytes) {
      return res.status(413).json({
        success: false,
        message: 'Żądanie jest zbyt duże.',
        maxSize: maxSize
      });
    }
    
    next();
  };
};

/**
 * Middleware CORS z konfiguracją bezpieczeństwa
 */
const corsConfig = {
  origin: (origin, callback) => {
    // W development pozwól na wszystkie origins
    if (process.env.NODE_ENV === 'development') {
      return callback(null, true);
    }
    
    // W production sprawdź listę dozwolonych origins
    const allowedOrigins = [
      config.cors.origin,
      'https://www.zyrardow.poleca.to',
      'https://zyrardow.poleca.to',
      'http://localhost:3000',
      'http://127.0.0.1:3000'
    ];
    
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked origin: ${origin}`);
      callback(new Error('Nie dozwolone przez CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-New-Token']
};

module.exports = {
  helmetConfig,
  apiLimiter,
  loginLimiter,
  uploadLimiter,
  ipWhitelist,
  sanitizeInput,
  securityLogger,
  requestSizeLimit,
  corsConfig
};
