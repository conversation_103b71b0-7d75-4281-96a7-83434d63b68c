const jwt = require('jsonwebtoken');
const { Admin } = require('../models');
const config = require('../config/config');

/**
 * Middleware weryfikacji tokenu JWT
 */
const verifyToken = async (req, res, next) => {
  try {
    // Pobierz token z nagłówka Authorization lub z ciasteczek
    let token = req.headers.authorization;
    
    if (token && token.startsWith('Bearer ')) {
      token = token.slice(7); // Usuń "Bearer " z początku
    } else if (req.cookies && req.cookies.adminToken) {
      token = req.cookies.adminToken;
    }
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Brak tokenu autoryzacji. Dostęp zabroniony.'
      });
    }
    
    // Weryfikuj token
    const decoded = jwt.verify(token, config.jwt.secret);
    
    // Sprawdź czy administrator nadal istnieje i jest aktywny
    const admin = await Admin.findByPk(decoded.adminId);
    
    if (!admin) {
      return res.status(401).json({
        success: false,
        message: 'Token jest nieprawidłowy. Administrator nie istnieje.'
      });
    }
    
    if (!admin.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Konto administratora zostało dezaktywowane.'
      });
    }
    
    if (admin.isLocked()) {
      return res.status(401).json({
        success: false,
        message: 'Konto administratora zostało tymczasowo zablokowane.'
      });
    }
    
    // Dodaj dane administratora do obiektu request
    req.admin = admin;
    req.adminId = admin.id;
    
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Token jest nieprawidłowy.'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token wygasł. Zaloguj się ponownie.'
      });
    }
    
    console.error('Błąd weryfikacji tokenu:', error);
    return res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas weryfikacji tokenu.'
    });
  }
};

/**
 * Middleware sprawdzenia roli administratora
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.admin) {
      return res.status(401).json({
        success: false,
        message: 'Brak autoryzacji.'
      });
    }
    
    // Jeśli roles jest stringiem, konwertuj na tablicę
    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!allowedRoles.includes(req.admin.role)) {
      return res.status(403).json({
        success: false,
        message: 'Brak uprawnień do wykonania tej operacji.'
      });
    }
    
    next();
  };
};

/**
 * Middleware sprawdzenia czy administrator jest super adminem
 */
const requireSuperAdmin = requireRole('super_admin');

/**
 * Middleware sprawdzenia czy administrator ma uprawnienia admin lub wyższe
 */
const requireAdmin = requireRole(['super_admin', 'admin']);

/**
 * Middleware sprawdzenia czy administrator ma uprawnienia moderator lub wyższe
 */
const requireModerator = requireRole(['super_admin', 'admin', 'moderator']);

/**
 * Middleware opcjonalnej autoryzacji (nie wymaga logowania)
 */
const optionalAuth = async (req, res, next) => {
  try {
    let token = req.headers.authorization;
    
    if (token && token.startsWith('Bearer ')) {
      token = token.slice(7);
    } else if (req.cookies && req.cookies.adminToken) {
      token = req.cookies.adminToken;
    }
    
    if (token) {
      try {
        const decoded = jwt.verify(token, config.jwt.secret);
        const admin = await Admin.findByPk(decoded.adminId);
        
        if (admin && admin.isActive && !admin.isLocked()) {
          req.admin = admin;
          req.adminId = admin.id;
        }
      } catch (error) {
        // Ignoruj błędy tokenu w opcjonalnej autoryzacji
      }
    }
    
    next();
  } catch (error) {
    // Ignoruj błędy w opcjonalnej autoryzacji
    next();
  }
};

/**
 * Generowanie tokenu JWT
 */
const generateToken = (admin) => {
  const payload = {
    adminId: admin.id,
    email: admin.email,
    role: admin.role
  };
  
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn
  });
};

/**
 * Weryfikacja tokenu bez middleware (do użycia w innych miejscach)
 */
const verifyTokenSync = (token) => {
  try {
    return jwt.verify(token, config.jwt.secret);
  } catch (error) {
    return null;
  }
};

/**
 * Sprawdzenie czy token wygasł
 */
const isTokenExpired = (token) => {
  try {
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) return true;
    
    const currentTime = Math.floor(Date.now() / 1000);
    return decoded.exp < currentTime;
  } catch (error) {
    return true;
  }
};

/**
 * Odświeżenie tokenu (jeśli jest bliski wygaśnięcia)
 */
const refreshTokenIfNeeded = async (req, res, next) => {
  try {
    if (!req.admin) {
      return next();
    }
    
    let token = req.headers.authorization;
    if (token && token.startsWith('Bearer ')) {
      token = token.slice(7);
    } else if (req.cookies && req.cookies.adminToken) {
      token = req.cookies.adminToken;
    }
    
    if (token) {
      const decoded = jwt.decode(token);
      if (decoded && decoded.exp) {
        const currentTime = Math.floor(Date.now() / 1000);
        const timeUntilExpiry = decoded.exp - currentTime;
        
        // Jeśli token wygasa w ciągu 1 godziny, wygeneruj nowy
        if (timeUntilExpiry < 3600) {
          const newToken = generateToken(req.admin);
          
          // Ustaw nowy token w nagłówku odpowiedzi
          res.setHeader('X-New-Token', newToken);
          
          // Opcjonalnie ustaw nowe ciasteczko
          if (req.cookies && req.cookies.adminToken) {
            res.cookie('adminToken', newToken, {
              httpOnly: true,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'strict',
              maxAge: 24 * 60 * 60 * 1000 // 24 godziny
            });
          }
        }
      }
    }
    
    next();
  } catch (error) {
    // Ignoruj błędy odświeżania tokenu
    next();
  }
};

module.exports = {
  verifyToken,
  requireRole,
  requireSuperAdmin,
  requireAdmin,
  requireModerator,
  optionalAuth,
  generateToken,
  verifyTokenSync,
  isTokenExpired,
  refreshTokenIfNeeded
};
