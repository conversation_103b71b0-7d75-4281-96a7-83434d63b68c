const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;
const config = require('../config/config');

// Upewnij się, że katalog uploads istnieje
const ensureUploadDir = async () => {
  try {
    await fs.access(config.upload.destination);
  } catch (error) {
    await fs.mkdir(config.upload.destination, { recursive: true });
    console.log('✅ Utworzono katalog uploads:', config.upload.destination);
  }
};

// Inicjalizuj katalog uploads
ensureUploadDir();

/**
 * Konfiguracja storage dla multer
 */
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    try {
      // Utwórz podkatalog na podstawie typu pliku
      let subDir = 'misc';
      
      if (file.fieldname === 'logo' || file.fieldname === 'company-logo') {
        subDir = 'companies/logos';
      } else if (file.fieldname === 'company-images') {
        subDir = 'companies/images';
      } else if (file.fieldname === 'offer-image') {
        subDir = 'offers';
      } else if (file.fieldname === 'category-icon') {
        subDir = 'categories';
      }
      
      const fullPath = path.join(config.upload.destination, subDir);
      await fs.mkdir(fullPath, { recursive: true });
      
      cb(null, fullPath);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    // Generuj unikalną nazwę pliku
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname).toLowerCase();
    const baseName = path.basename(file.originalname, ext)
      .replace(/[^a-zA-Z0-9]/g, '-')
      .substring(0, 50);
    
    cb(null, `${baseName}-${uniqueSuffix}${ext}`);
  }
});

/**
 * Filtr plików - sprawdza typ i rozmiar
 */
const fileFilter = (req, file, cb) => {
  // Sprawdź typ pliku
  const allowedTypes = config.upload.allowedTypes;
  const ext = path.extname(file.originalname).toLowerCase().substring(1);
  
  if (!allowedTypes.includes(ext)) {
    const error = new Error(`Niedozwolony typ pliku. Dozwolone: ${allowedTypes.join(', ')}`);
    error.code = 'INVALID_FILE_TYPE';
    return cb(error, false);
  }
  
  // Sprawdź MIME type
  const allowedMimeTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
  ];
  
  if (!allowedMimeTypes.includes(file.mimetype)) {
    const error = new Error('Niedozwolony MIME type pliku');
    error.code = 'INVALID_MIME_TYPE';
    return cb(error, false);
  }
  
  cb(null, true);
};

/**
 * Konfiguracja multer
 */
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: config.upload.maxSize,
    files: 10 // maksymalnie 10 plików na raz
  }
});

/**
 * Middleware do optymalizacji obrazów
 */
const optimizeImage = async (req, res, next) => {
  if (!req.file && !req.files) {
    return next();
  }
  
  try {
    const files = req.files ? Object.values(req.files).flat() : [req.file];
    
    for (const file of files) {
      if (!file) continue;
      
      const inputPath = file.path;
      const outputPath = inputPath.replace(/\.[^/.]+$/, '.webp');
      
      // Optymalizuj obraz
      await sharp(inputPath)
        .resize(1200, 1200, {
          fit: 'inside',
          withoutEnlargement: true
        })
        .webp({
          quality: 85,
          effort: 4
        })
        .toFile(outputPath);
      
      // Usuń oryginalny plik
      await fs.unlink(inputPath);
      
      // Zaktualizuj informacje o pliku
      file.path = outputPath;
      file.filename = path.basename(outputPath);
      file.mimetype = 'image/webp';
      
      // Dodaj URL do pliku
      const relativePath = path.relative(config.upload.destination, outputPath);
      file.url = config.upload.publicPath + relativePath.replace(/\\/g, '/');
    }
    
    next();
  } catch (error) {
    console.error('Błąd podczas optymalizacji obrazu:', error);
    next(error);
  }
};

/**
 * Middleware do tworzenia miniaturek
 */
const createThumbnails = async (req, res, next) => {
  if (!req.file && !req.files) {
    return next();
  }
  
  try {
    const files = req.files ? Object.values(req.files).flat() : [req.file];
    
    for (const file of files) {
      if (!file || !file.mimetype.startsWith('image/')) continue;
      
      const inputPath = file.path;
      const dir = path.dirname(inputPath);
      const name = path.basename(inputPath, path.extname(inputPath));
      
      // Utwórz katalog thumbnails
      const thumbDir = path.join(dir, 'thumbnails');
      await fs.mkdir(thumbDir, { recursive: true });
      
      // Utwórz różne rozmiary miniaturek
      const sizes = [
        { name: 'small', width: 150, height: 150 },
        { name: 'medium', width: 300, height: 300 },
        { name: 'large', width: 600, height: 600 }
      ];
      
      const thumbnails = {};
      
      for (const size of sizes) {
        const thumbPath = path.join(thumbDir, `${name}-${size.name}.webp`);
        
        await sharp(inputPath)
          .resize(size.width, size.height, {
            fit: 'cover',
            position: 'center'
          })
          .webp({
            quality: 80,
            effort: 4
          })
          .toFile(thumbPath);
        
        const relativePath = path.relative(config.upload.destination, thumbPath);
        thumbnails[size.name] = config.upload.publicPath + relativePath.replace(/\\/g, '/');
      }
      
      // Dodaj informacje o miniaturkach do obiektu pliku
      file.thumbnails = thumbnails;
    }
    
    next();
  } catch (error) {
    console.error('Błąd podczas tworzenia miniaturek:', error);
    next(error);
  }
};

/**
 * Middleware do usuwania plików w przypadku błędu
 */
const cleanupOnError = (err, req, res, next) => {
  if (err && (req.file || req.files)) {
    const files = req.files ? Object.values(req.files).flat() : [req.file];
    
    // Usuń pliki asynchronicznie
    files.forEach(async (file) => {
      if (file && file.path) {
        try {
          await fs.unlink(file.path);
          
          // Usuń także miniaturki jeśli istnieją
          if (file.thumbnails) {
            const dir = path.dirname(file.path);
            const thumbDir = path.join(dir, 'thumbnails');
            
            try {
              const thumbFiles = await fs.readdir(thumbDir);
              const name = path.basename(file.path, path.extname(file.path));
              
              for (const thumbFile of thumbFiles) {
                if (thumbFile.startsWith(name)) {
                  await fs.unlink(path.join(thumbDir, thumbFile));
                }
              }
            } catch (thumbError) {
              // Ignoruj błędy usuwania miniaturek
            }
          }
        } catch (unlinkError) {
          console.error('Błąd podczas usuwania pliku:', unlinkError);
        }
      }
    });
  }
  
  next(err);
};

/**
 * Middleware do usuwania starych plików
 */
const deleteFile = async (filePath) => {
  try {
    if (!filePath) return;
    
    // Usuń główny plik
    const fullPath = path.join(config.upload.destination, filePath.replace(config.upload.publicPath, ''));
    await fs.unlink(fullPath);
    
    // Usuń miniaturki
    const dir = path.dirname(fullPath);
    const name = path.basename(fullPath, path.extname(fullPath));
    const thumbDir = path.join(dir, 'thumbnails');
    
    try {
      const thumbFiles = await fs.readdir(thumbDir);
      
      for (const thumbFile of thumbFiles) {
        if (thumbFile.startsWith(name)) {
          await fs.unlink(path.join(thumbDir, thumbFile));
        }
      }
    } catch (thumbError) {
      // Ignoruj błędy jeśli katalog thumbnails nie istnieje
    }
    
    console.log('✅ Usunięto plik:', filePath);
  } catch (error) {
    console.error('❌ Błąd podczas usuwania pliku:', error.message);
  }
};

/**
 * Konfiguracje upload dla różnych typów plików
 */
const uploadConfigs = {
  // Logo firmy - pojedynczy plik
  companyLogo: upload.single('logo'),
  
  // Zdjęcia firmy - wiele plików
  companyImages: upload.array('images', 10),
  
  // Zdjęcie oferty - pojedynczy plik
  offerImage: upload.single('image'),
  
  // Ikona kategorii - pojedynczy plik
  categoryIcon: upload.single('icon'),
  
  // Uniwersalny upload - wiele plików różnych typów
  multiple: upload.fields([
    { name: 'logo', maxCount: 1 },
    { name: 'images', maxCount: 10 },
    { name: 'icon', maxCount: 1 }
  ])
};

module.exports = {
  upload,
  uploadConfigs,
  optimizeImage,
  createThumbnails,
  cleanupOnError,
  deleteFile
};
