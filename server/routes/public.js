const express = require('express');
const { Company, Category, Offer, Coupon } = require('../models');
const { Op } = require('sequelize');

const router = express.Router();

/**
 * @route   GET /api/public/companies
 * @desc    Pobierz aktywne firmy dla strony głównej
 * @access  Public
 */
router.get('/companies', async (req, res) => {
  try {
    const { categoryId, subcategoryId, featured, limit = 20, page = 1 } = req.query;
    
    const where = { status: 'active' };
    if (categoryId) where.categoryId = categoryId;
    if (subcategoryId) where.subcategoryId = subcategoryId;
    if (featured) where.featured = true;
    
    const offset = (page - 1) * limit;
    
    const { count, rows } = await Company.findAndCountAll({
      where,
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Category,
          as: 'subcategory',
          attributes: ['id', 'name', 'slug'],
          required: false
        }
      ],
      order: [
        ['topPosition', 'ASC NULLS LAST'],
        ['featured', 'DESC'],
        ['views', 'DESC']
      ],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    res.json({
      success: true,
      data: {
        companies: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
    
  } catch (error) {
    console.error('Błąd podczas pobierania firm:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera'
    });
  }
});

/**
 * @route   GET /api/public/companies/top
 * @desc    Pobierz firmy TOP
 * @access  Public
 */
router.get('/companies/top', async (req, res) => {
  try {
    const topCompanies = await Company.findAll({
      where: {
        status: 'active',
        topPosition: { [Op.not]: null }
      },
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        }
      ],
      order: [['topPosition', 'ASC']]
    });
    
    res.json({
      success: true,
      data: { companies: topCompanies }
    });
    
  } catch (error) {
    console.error('Błąd podczas pobierania firm TOP:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera'
    });
  }
});

/**
 * @route   GET /api/public/offers
 * @desc    Pobierz aktywne oferty
 * @access  Public
 */
router.get('/offers', async (req, res) => {
  try {
    const { companyId, categoryId, featured, limit = 20, page = 1 } = req.query;
    
    const where = { status: 'active' };
    const now = new Date();
    
    // Sprawdź daty ważności
    where[Op.or] = [
      { endDate: null },
      { endDate: { [Op.gt]: now } }
    ];
    
    if (companyId) where.companyId = companyId;
    if (categoryId) where.categoryId = categoryId;
    if (featured) where.featured = true;
    
    const offset = (page - 1) * limit;
    
    const { count, rows } = await Offer.findAndCountAll({
      where,
      include: [
        {
          model: Company,
          as: 'company',
          attributes: ['id', 'name', 'slug', 'logo'],
          where: { status: 'active' }
        }
      ],
      order: [
        ['featured', 'DESC'],
        ['publishedAt', 'DESC']
      ],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    res.json({
      success: true,
      data: {
        offers: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
    
  } catch (error) {
    console.error('Błąd podczas pobierania ofert:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera'
    });
  }
});

/**
 * @route   GET /api/public/categories
 * @desc    Pobierz kategorie z podkategoriami
 * @access  Public
 */
router.get('/categories', async (req, res) => {
  try {
    const categories = await Category.findAll({
      where: {
        parentId: null,
        isActive: true
      },
      include: [{
        model: Category,
        as: 'subcategories',
        where: { isActive: true },
        required: false
      }],
      order: [
        ['sortOrder', 'ASC'],
        ['name', 'ASC'],
        [{ model: Category, as: 'subcategories' }, 'sortOrder', 'ASC'],
        [{ model: Category, as: 'subcategories' }, 'name', 'ASC']
      ]
    });
    
    res.json({
      success: true,
      data: { categories }
    });
    
  } catch (error) {
    console.error('Błąd podczas pobierania kategorii:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera'
    });
  }
});

/**
 * @route   GET /api/public/search
 * @desc    Wyszukiwanie firm i ofert
 * @access  Public
 */
router.get('/search', async (req, res) => {
  try {
    const { q, type = 'all', limit = 10 } = req.query;
    
    if (!q || q.length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Zapytanie musi mieć co najmniej 2 znaki'
      });
    }
    
    const results = {};
    
    // Wyszukaj firmy
    if (type === 'all' || type === 'companies') {
      const companies = await Company.findAll({
        where: {
          status: 'active',
          [Op.or]: [
            { name: { [Op.like]: `%${q}%` } },
            { description: { [Op.like]: `%${q}%` } }
          ]
        },
        include: [
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name', 'slug']
          }
        ],
        limit: parseInt(limit),
        order: [['views', 'DESC']]
      });
      
      results.companies = companies;
    }
    
    // Wyszukaj oferty
    if (type === 'all' || type === 'offers') {
      const offers = await Offer.findAll({
        where: {
          status: 'active',
          [Op.or]: [
            { title: { [Op.like]: `%${q}%` } },
            { description: { [Op.like]: `%${q}%` } }
          ],
          [Op.or]: [
            { endDate: null },
            { endDate: { [Op.gt]: new Date() } }
          ]
        },
        include: [
          {
            model: Company,
            as: 'company',
            attributes: ['id', 'name', 'slug', 'logo'],
            where: { status: 'active' }
          }
        ],
        limit: parseInt(limit),
        order: [['views', 'DESC']]
      });
      
      results.offers = offers;
    }
    
    res.json({
      success: true,
      data: results
    });
    
  } catch (error) {
    console.error('Błąd podczas wyszukiwania:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera'
    });
  }
});

module.exports = router;
