const express = require('express');
const { body } = require('express-validator');
const {
  login,
  logout,
  checkAuth,
  changePassword,
  updateProfile,
  refreshToken,
  requestPasswordReset,
  resetPassword
} = require('../controllers/authController');
const { verifyToken, refreshTokenIfNeeded } = require('../middleware/auth');
const { loginLimiter } = require('../middleware/security');

const router = express.Router();

/**
 * @route   POST /api/auth/login
 * @desc    Logowanie administratora
 * @access  Public
 */
router.post('/login',
  loginLimiter,
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Podaj prawidłowy adres email'),
    body('password')
      .isLength({ min: 8 })
      .withMessage('Hasło musi mieć co najmniej 8 znaków')
  ],
  login
);

/**
 * @route   POST /api/auth/logout
 * @desc    Wylogowanie administratora
 * @access  Private
 */
router.post('/logout', verifyToken, logout);

/**
 * @route   GET /api/auth/check
 * @desc    Sprawdzenie statusu autoryzacji
 * @access  Private
 */
router.get('/check', verifyToken, refreshTokenIfNeeded, checkAuth);

/**
 * @route   POST /api/auth/change-password
 * @desc    Zmiana hasła administratora
 * @access  Private
 */
router.post('/change-password',
  verifyToken,
  [
    body('currentPassword')
      .notEmpty()
      .withMessage('Obecne hasło jest wymagane'),
    body('newPassword')
      .isLength({ min: 8 })
      .withMessage('Nowe hasło musi mieć co najmniej 8 znaków')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('Nowe hasło musi zawierać co najmniej jedną małą literę, jedną wielką literę i jedną cyfrę'),
    body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.newPassword) {
          throw new Error('Potwierdzenie hasła musi być identyczne z nowym hasłem');
        }
        return true;
      })
  ],
  changePassword
);

/**
 * @route   PUT /api/auth/profile
 * @desc    Aktualizacja profilu administratora
 * @access  Private
 */
router.put('/profile',
  verifyToken,
  [
    body('firstName')
      .optional()
      .isLength({ min: 2, max: 100 })
      .withMessage('Imię musi mieć od 2 do 100 znaków'),
    body('lastName')
      .optional()
      .isLength({ min: 2, max: 100 })
      .withMessage('Nazwisko musi mieć od 2 do 100 znaków'),
    body('email')
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage('Podaj prawidłowy adres email')
  ],
  updateProfile
);

/**
 * @route   POST /api/auth/refresh-token
 * @desc    Odświeżenie tokenu JWT
 * @access  Private
 */
router.post('/refresh-token', verifyToken, refreshToken);

/**
 * @route   POST /api/auth/request-password-reset
 * @desc    Żądanie resetu hasła
 * @access  Public
 */
router.post('/request-password-reset',
  loginLimiter,
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Podaj prawidłowy adres email')
  ],
  requestPasswordReset
);

/**
 * @route   POST /api/auth/reset-password
 * @desc    Reset hasła z tokenem
 * @access  Public
 */
router.post('/reset-password',
  loginLimiter,
  [
    body('token')
      .notEmpty()
      .withMessage('Token resetu jest wymagany'),
    body('newPassword')
      .isLength({ min: 8 })
      .withMessage('Nowe hasło musi mieć co najmniej 8 znaków')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('Nowe hasło musi zawierać co najmniej jedną małą literę, jedną wielką literę i jedną cyfrę'),
    body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.newPassword) {
          throw new Error('Potwierdzenie hasła musi być identyczne z nowym hasłem');
        }
        return true;
      })
  ],
  resetPassword
);

module.exports = router;
