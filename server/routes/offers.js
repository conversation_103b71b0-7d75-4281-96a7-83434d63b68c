const express = require('express');
const { body, param, query } = require('express-validator');
const { Offer, Company, Category } = require('../models');
const { verifyToken, requireAdmin } = require('../middleware/auth');
const { uploadConfigs, optimizeImage, cleanupOnError } = require('../middleware/upload');
const { uploadLimiter } = require('../middleware/security');
const { Op } = require('sequelize');

const router = express.Router();

// Middleware walidacji błędów
const handleValidationErrors = (req, res, next) => {
  const { validationResult } = require('express-validator');
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Błędy walidacji',
      errors: errors.array()
    });
  }
  
  next();
};

/**
 * @route   GET /api/offers
 * @desc    Pobierz wszystkie oferty
 * @access  Private (Admin)
 */
router.get('/',
  verifyToken,
  requireAdmin,
  async (req, res) => {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        companyId,
        categoryId,
        search
      } = req.query;
      
      const offset = (page - 1) * limit;
      const where = {};
      
      if (status) where.status = status;
      if (companyId) where.companyId = companyId;
      if (categoryId) where.categoryId = categoryId;
      
      if (search) {
        where[Op.or] = [
          { title: { [Op.like]: `%${search}%` } },
          { description: { [Op.like]: `%${search}%` } }
        ];
      }
      
      const { count, rows } = await Offer.findAndCountAll({
        where,
        include: [
          {
            model: Company,
            as: 'company',
            attributes: ['id', 'name', 'slug', 'logo']
          },
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name', 'slug'],
            required: false
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });
      
      res.json({
        success: true,
        data: {
          offers: rows,
          pagination: {
            total: count,
            page: parseInt(page),
            limit: parseInt(limit),
            pages: Math.ceil(count / limit)
          }
        }
      });
      
    } catch (error) {
      console.error('Błąd podczas pobierania ofert:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd serwera podczas pobierania ofert.'
      });
    }
  }
);

/**
 * @route   GET /api/offers/:id
 * @desc    Pobierz ofertę po ID
 * @access  Private (Admin)
 */
router.get('/:id',
  verifyToken,
  requireAdmin,
  [param('id').isInt().withMessage('ID oferty musi być liczbą')],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { id } = req.params;
      
      const offer = await Offer.findByPk(id, {
        include: [
          {
            model: Company,
            as: 'company',
            attributes: ['id', 'name', 'slug', 'logo']
          },
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name', 'slug'],
            required: false
          }
        ]
      });
      
      if (!offer) {
        return res.status(404).json({
          success: false,
          message: 'Oferta nie została znaleziona.'
        });
      }
      
      res.json({
        success: true,
        data: { offer }
      });
      
    } catch (error) {
      console.error('Błąd podczas pobierania oferty:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd serwera podczas pobierania oferty.'
      });
    }
  }
);

/**
 * @route   POST /api/offers
 * @desc    Utwórz nową ofertę
 * @access  Private (Admin)
 */
router.post('/',
  verifyToken,
  requireAdmin,
  uploadLimiter,
  uploadConfigs.offerImage,
  [
    body('title')
      .notEmpty()
      .isLength({ min: 5, max: 255 })
      .withMessage('Tytuł oferty musi mieć od 5 do 255 znaków'),
    body('companyId')
      .isInt()
      .withMessage('ID firmy jest wymagane i musi być liczbą'),
    body('description')
      .optional()
      .isLength({ max: 5000 })
      .withMessage('Opis nie może być dłuższy niż 5000 znaków'),
    body('regularPrice')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Cena regularna musi być liczbą większą lub równą 0'),
    body('salePrice')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Cena promocyjna musi być liczbą większą lub równą 0'),
    body('startDate')
      .optional()
      .isISO8601()
      .withMessage('Data rozpoczęcia musi być w formacie ISO 8601'),
    body('endDate')
      .optional()
      .isISO8601()
      .withMessage('Data zakończenia musi być w formacie ISO 8601'),
    body('status')
      .optional()
      .isIn(['draft', 'active', 'expired', 'suspended'])
      .withMessage('Nieprawidłowy status')
  ],
  handleValidationErrors,
  optimizeImage,
  async (req, res) => {
    try {
      const offerData = req.body;
      
      // Sprawdź czy firma istnieje
      const company = await Company.findByPk(offerData.companyId);
      if (!company) {
        return res.status(400).json({
          success: false,
          message: 'Wybrana firma nie istnieje.'
        });
      }
      
      // Dodaj zdjęcie jeśli zostało przesłane
      if (req.file) {
        offerData.image = req.file.url;
      }
      
      const offer = await Offer.create(offerData);
      
      // Pobierz utworzoną ofertę z relacjami
      const createdOffer = await Offer.findByPk(offer.id, {
        include: [
          {
            model: Company,
            as: 'company',
            attributes: ['id', 'name', 'slug', 'logo']
          }
        ]
      });
      
      res.status(201).json({
        success: true,
        message: 'Oferta została utworzona pomyślnie.',
        data: { offer: createdOffer }
      });
      
      console.log(`✅ Utworzono ofertę: ${offer.title} (ID: ${offer.id})`);
      
    } catch (error) {
      console.error('Błąd podczas tworzenia oferty:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd serwera podczas tworzenia oferty.'
      });
    }
  },
  cleanupOnError
);

/**
 * @route   PUT /api/offers/:id
 * @desc    Aktualizuj ofertę
 * @access  Private (Admin)
 */
router.put('/:id',
  verifyToken,
  requireAdmin,
  uploadLimiter,
  uploadConfigs.offerImage,
  [
    param('id').isInt().withMessage('ID oferty musi być liczbą'),
    body('title')
      .optional()
      .isLength({ min: 5, max: 255 })
      .withMessage('Tytuł oferty musi mieć od 5 do 255 znaków'),
    body('companyId')
      .optional()
      .isInt()
      .withMessage('ID firmy musi być liczbą'),
    body('description')
      .optional()
      .isLength({ max: 5000 })
      .withMessage('Opis nie może być dłuższy niż 5000 znaków'),
    body('regularPrice')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Cena regularna musi być liczbą większą lub równą 0'),
    body('salePrice')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Cena promocyjna musi być liczbą większą lub równą 0'),
    body('status')
      .optional()
      .isIn(['draft', 'active', 'expired', 'suspended'])
      .withMessage('Nieprawidłowy status')
  ],
  handleValidationErrors,
  optimizeImage,
  async (req, res) => {
    try {
      const { id } = req.params;
      const updateData = req.body;
      
      const offer = await Offer.findByPk(id);
      
      if (!offer) {
        return res.status(404).json({
          success: false,
          message: 'Oferta nie została znaleziona.'
        });
      }
      
      // Sprawdź firmę jeśli została zmieniona
      if (updateData.companyId && updateData.companyId !== offer.companyId) {
        const company = await Company.findByPk(updateData.companyId);
        if (!company) {
          return res.status(400).json({
            success: false,
            message: 'Wybrana firma nie istnieje.'
          });
        }
      }
      
      // Obsługa nowego zdjęcia
      if (req.file) {
        // Usuń stare zdjęcie
        if (offer.image) {
          const { deleteFile } = require('../middleware/upload');
          await deleteFile(offer.image);
        }
        updateData.image = req.file.url;
      }
      
      await offer.update(updateData);
      
      // Pobierz zaktualizowaną ofertę z relacjami
      const updatedOffer = await Offer.findByPk(id, {
        include: [
          {
            model: Company,
            as: 'company',
            attributes: ['id', 'name', 'slug', 'logo']
          }
        ]
      });
      
      res.json({
        success: true,
        message: 'Oferta została zaktualizowana pomyślnie.',
        data: { offer: updatedOffer }
      });
      
      console.log(`✅ Zaktualizowano ofertę: ${offer.title} (ID: ${offer.id})`);
      
    } catch (error) {
      console.error('Błąd podczas aktualizacji oferty:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd serwera podczas aktualizacji oferty.'
      });
    }
  },
  cleanupOnError
);

/**
 * @route   DELETE /api/offers/:id
 * @desc    Usuń ofertę
 * @access  Private (Admin)
 */
router.delete('/:id',
  verifyToken,
  requireAdmin,
  [param('id').isInt().withMessage('ID oferty musi być liczbą')],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { id } = req.params;
      
      const offer = await Offer.findByPk(id);
      
      if (!offer) {
        return res.status(404).json({
          success: false,
          message: 'Oferta nie została znaleziona.'
        });
      }
      
      // Usuń zdjęcie jeśli istnieje
      if (offer.image) {
        const { deleteFile } = require('../middleware/upload');
        await deleteFile(offer.image);
      }
      
      await offer.destroy();
      
      res.json({
        success: true,
        message: 'Oferta została usunięta pomyślnie.'
      });
      
      console.log(`✅ Usunięto ofertę: ${offer.title} (ID: ${offer.id})`);
      
    } catch (error) {
      console.error('Błąd podczas usuwania oferty:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd serwera podczas usuwania oferty.'
      });
    }
  }
);

module.exports = router;
