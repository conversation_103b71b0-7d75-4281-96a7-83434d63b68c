const express = require('express');
const { body, param, query } = require('express-validator');
const {
  getCompanies,
  getCompanyById,
  createCompany,
  updateCompany,
  deleteCompany,
  getTopCompanies,
  setTopPosition
} = require('../controllers/companiesController');
const { verifyToken, requireAdmin } = require('../middleware/auth');
const { uploadConfigs, optimizeImage, createThumbnails, cleanupOnError } = require('../middleware/upload');
const { uploadLimiter } = require('../middleware/security');

const router = express.Router();

// Middleware walidacji błędów
const handleValidationErrors = (req, res, next) => {
  const { validationResult } = require('express-validator');
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '<PERSON><PERSON><PERSON><PERSON> walidac<PERSON>',
      errors: errors.array()
    });
  }
  
  next();
};

/**
 * @route   GET /api/companies
 * @desc    Pobierz wszystkie firmy (z paginacją i filtrami)
 * @access  Private (Admin)
 */
router.get('/',
  verifyToken,
  requireAdmin,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Strona musi być liczbą większą od 0'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit musi być liczbą od 1 do 100'),
    query('status').optional().isIn(['active', 'pending', 'inactive', 'suspended']).withMessage('Nieprawidłowy status'),
    query('categoryId').optional().isInt().withMessage('ID kategorii musi być liczbą'),
    query('subcategoryId').optional().isInt().withMessage('ID podkategorii musi być liczbą'),
    query('topPosition').optional().isIn(['1', '2', '3', 'null']).withMessage('Pozycja TOP musi być 1, 2, 3 lub null'),
    query('featured').optional().isBoolean().withMessage('Featured musi być wartością boolean')
  ],
  handleValidationErrors,
  getCompanies
);

/**
 * @route   GET /api/companies/top
 * @desc    Pobierz firmy TOP
 * @access  Private (Admin)
 */
router.get('/top',
  verifyToken,
  requireAdmin,
  getTopCompanies
);

/**
 * @route   GET /api/companies/:id
 * @desc    Pobierz firmę po ID
 * @access  Private (Admin)
 */
router.get('/:id',
  verifyToken,
  requireAdmin,
  [
    param('id').isInt().withMessage('ID firmy musi być liczbą')
  ],
  handleValidationErrors,
  getCompanyById
);

/**
 * @route   POST /api/companies
 * @desc    Utwórz nową firmę
 * @access  Private (Admin)
 */
router.post('/',
  verifyToken,
  requireAdmin,
  uploadLimiter,
  uploadConfigs.multiple,
  [
    body('name')
      .notEmpty()
      .isLength({ min: 2, max: 255 })
      .withMessage('Nazwa firmy musi mieć od 2 do 255 znaków'),
    body('categoryId')
      .isInt()
      .withMessage('ID kategorii jest wymagane i musi być liczbą'),
    body('subcategoryId')
      .optional()
      .isInt()
      .withMessage('ID podkategorii musi być liczbą'),
    body('description')
      .optional()
      .isLength({ max: 5000 })
      .withMessage('Opis nie może być dłuższy niż 5000 znaków'),
    body('address')
      .optional()
      .isLength({ max: 500 })
      .withMessage('Adres nie może być dłuższy niż 500 znaków'),
    body('postalCode')
      .optional()
      .matches(/^\d{2}-\d{3}$/)
      .withMessage('Kod pocztowy musi być w formacie XX-XXX'),
    body('city')
      .optional()
      .isLength({ max: 100 })
      .withMessage('Miasto nie może być dłuższe niż 100 znaków'),
    body('phone')
      .optional()
      .isMobilePhone('pl-PL')
      .withMessage('Podaj prawidłowy numer telefonu'),
    body('email')
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage('Podaj prawidłowy adres email'),
    body('website')
      .optional()
      .isURL()
      .withMessage('Podaj prawidłowy adres strony internetowej'),
    body('topPosition')
      .optional()
      .isIn([1, 2, 3])
      .withMessage('Pozycja TOP musi być 1, 2 lub 3'),
    body('status')
      .optional()
      .isIn(['active', 'pending', 'inactive', 'suspended'])
      .withMessage('Nieprawidłowy status'),
    body('featured')
      .optional()
      .isBoolean()
      .withMessage('Featured musi być wartością boolean')
  ],
  handleValidationErrors,
  optimizeImage,
  createThumbnails,
  createCompany,
  cleanupOnError
);

/**
 * @route   PUT /api/companies/:id
 * @desc    Aktualizuj firmę
 * @access  Private (Admin)
 */
router.put('/:id',
  verifyToken,
  requireAdmin,
  uploadLimiter,
  uploadConfigs.multiple,
  [
    param('id').isInt().withMessage('ID firmy musi być liczbą'),
    body('name')
      .optional()
      .isLength({ min: 2, max: 255 })
      .withMessage('Nazwa firmy musi mieć od 2 do 255 znaków'),
    body('categoryId')
      .optional()
      .isInt()
      .withMessage('ID kategorii musi być liczbą'),
    body('subcategoryId')
      .optional()
      .isInt()
      .withMessage('ID podkategorii musi być liczbą'),
    body('description')
      .optional()
      .isLength({ max: 5000 })
      .withMessage('Opis nie może być dłuższy niż 5000 znaków'),
    body('address')
      .optional()
      .isLength({ max: 500 })
      .withMessage('Adres nie może być dłuższy niż 500 znaków'),
    body('postalCode')
      .optional()
      .matches(/^\d{2}-\d{3}$/)
      .withMessage('Kod pocztowy musi być w formacie XX-XXX'),
    body('city')
      .optional()
      .isLength({ max: 100 })
      .withMessage('Miasto nie może być dłuższe niż 100 znaków'),
    body('phone')
      .optional()
      .isMobilePhone('pl-PL')
      .withMessage('Podaj prawidłowy numer telefonu'),
    body('email')
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage('Podaj prawidłowy adres email'),
    body('website')
      .optional()
      .isURL()
      .withMessage('Podaj prawidłowy adres strony internetowej'),
    body('topPosition')
      .optional()
      .isIn([1, 2, 3, null])
      .withMessage('Pozycja TOP musi być 1, 2, 3 lub null'),
    body('status')
      .optional()
      .isIn(['active', 'pending', 'inactive', 'suspended'])
      .withMessage('Nieprawidłowy status'),
    body('featured')
      .optional()
      .isBoolean()
      .withMessage('Featured musi być wartością boolean')
  ],
  handleValidationErrors,
  optimizeImage,
  createThumbnails,
  updateCompany,
  cleanupOnError
);

/**
 * @route   DELETE /api/companies/:id
 * @desc    Usuń firmę
 * @access  Private (Admin)
 */
router.delete('/:id',
  verifyToken,
  requireAdmin,
  [
    param('id').isInt().withMessage('ID firmy musi być liczbą')
  ],
  handleValidationErrors,
  deleteCompany
);

/**
 * @route   PATCH /api/companies/:id/top-position
 * @desc    Ustaw pozycję TOP dla firmy
 * @access  Private (Admin)
 */
router.patch('/:id/top-position',
  verifyToken,
  requireAdmin,
  [
    param('id').isInt().withMessage('ID firmy musi być liczbą'),
    body('topPosition')
      .optional()
      .isIn([1, 2, 3, null])
      .withMessage('Pozycja TOP musi być 1, 2, 3 lub null')
  ],
  handleValidationErrors,
  setTopPosition
);

module.exports = router;
