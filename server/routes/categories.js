const express = require('express');
const { body, param } = require('express-validator');
const { Category } = require('../models');
const { verifyToken, requireAdmin } = require('../middleware/auth');
const { Op } = require('sequelize');

const router = express.Router();

const handleValidationErrors = (req, res, next) => {
  const { validationResult } = require('express-validator');
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '<PERSON><PERSON><PERSON><PERSON> walidacji',
      errors: errors.array()
    });
  }
  
  next();
};

// GET /api/categories - Pobierz wszystkie kategorie
router.get('/', verifyToken, requireAdmin, async (req, res) => {
  try {
    const { tree = false } = req.query;
    
    if (tree === 'true') {
      // <PERSON><PERSON><PERSON><PERSON><PERSON> drzewo kategorii
      const categoryTree = await Category.getCategoryTree();
      res.json({
        success: true,
        data: { categories: categoryTree }
      });
    } else {
      // Zwróć płaską listę kategorii
      const categories = await Category.findAll({
        include: [{
          model: Category,
          as: 'subcategories',
          required: false
        }],
        order: [
          ['parentId', 'ASC NULLS FIRST'],
          ['sortOrder', 'ASC'],
          ['name', 'ASC']
        ]
      });
      
      res.json({
        success: true,
        data: { categories }
      });
    }
    
  } catch (error) {
    console.error('Błąd podczas pobierania kategorii:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas pobierania kategorii.'
    });
  }
});

// GET /api/categories/main - Pobierz główne kategorie
router.get('/main', verifyToken, requireAdmin, async (req, res) => {
  try {
    const mainCategories = await Category.getMainCategories();
    
    res.json({
      success: true,
      data: { categories: mainCategories }
    });
    
  } catch (error) {
    console.error('Błąd podczas pobierania głównych kategorii:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas pobierania głównych kategorii.'
    });
  }
});

// GET /api/categories/:id - Pobierz kategorię po ID
router.get('/:id', verifyToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    const category = await Category.findByPk(id, {
      include: [
        {
          model: Category,
          as: 'subcategories',
          required: false
        },
        {
          model: Category,
          as: 'parent',
          required: false
        }
      ]
    });
    
    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Kategoria nie została znaleziona.'
      });
    }
    
    res.json({
      success: true,
      data: { category }
    });
    
  } catch (error) {
    console.error('Błąd podczas pobierania kategorii:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas pobierania kategorii.'
    });
  }
});

// POST /api/categories - Utwórz nową kategorię
router.post('/',
  verifyToken,
  requireAdmin,
  [
    body('name')
      .notEmpty()
      .isLength({ min: 2, max: 100 })
      .withMessage('Nazwa kategorii musi mieć od 2 do 100 znaków'),
    body('parentId')
      .optional()
      .isInt()
      .withMessage('ID kategorii nadrzędnej musi być liczbą'),
    body('description')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('Opis nie może być dłuższy niż 1000 znaków'),
    body('icon')
      .optional()
      .isLength({ max: 50 })
      .withMessage('Ikona nie może być dłuższa niż 50 znaków'),
    body('color')
      .optional()
      .matches(/^#[0-9A-F]{6}$/i)
      .withMessage('Kolor musi być w formacie hex (#RRGGBB)'),
    body('sortOrder')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Kolejność sortowania musi być liczbą większą lub równą 0')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const categoryData = req.body;
      
      // Sprawdź czy kategoria nadrzędna istnieje
      if (categoryData.parentId) {
        const parentCategory = await Category.findByPk(categoryData.parentId);
        if (!parentCategory) {
          return res.status(400).json({
            success: false,
            message: 'Wybrana kategoria nadrzędna nie istnieje.'
          });
        }
      }
      
      const category = await Category.create(categoryData);
      
      const createdCategory = await Category.findByPk(category.id, {
        include: [
          {
            model: Category,
            as: 'parent',
            required: false
          }
        ]
      });
      
      res.status(201).json({
        success: true,
        message: 'Kategoria została utworzona pomyślnie.',
        data: { category: createdCategory }
      });
      
      console.log(`✅ Utworzono kategorię: ${category.name} (ID: ${category.id})`);
      
    } catch (error) {
      console.error('Błąd podczas tworzenia kategorii:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd serwera podczas tworzenia kategorii.'
      });
    }
  }
);

// PUT /api/categories/:id - Aktualizuj kategorię
router.put('/:id',
  verifyToken,
  requireAdmin,
  [
    param('id').isInt().withMessage('ID kategorii musi być liczbą'),
    body('name')
      .optional()
      .isLength({ min: 2, max: 100 })
      .withMessage('Nazwa kategorii musi mieć od 2 do 100 znaków'),
    body('parentId')
      .optional()
      .isInt()
      .withMessage('ID kategorii nadrzędnej musi być liczbą'),
    body('description')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('Opis nie może być dłuższy niż 1000 znaków'),
    body('icon')
      .optional()
      .isLength({ max: 50 })
      .withMessage('Ikona nie może być dłuższa niż 50 znaków'),
    body('color')
      .optional()
      .matches(/^#[0-9A-F]{6}$/i)
      .withMessage('Kolor musi być w formacie hex (#RRGGBB)'),
    body('sortOrder')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Kolejność sortowania musi być liczbą większą lub równą 0')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { id } = req.params;
      const updateData = req.body;
      
      const category = await Category.findByPk(id);
      
      if (!category) {
        return res.status(404).json({
          success: false,
          message: 'Kategoria nie została znaleziona.'
        });
      }
      
      // Sprawdź czy kategoria nadrzędna istnieje (jeśli została zmieniona)
      if (updateData.parentId && updateData.parentId !== category.parentId) {
        // Nie pozwól na ustawienie siebie jako rodzica
        if (updateData.parentId == id) {
          return res.status(400).json({
            success: false,
            message: 'Kategoria nie może być swoim własnym rodzicem.'
          });
        }
        
        const parentCategory = await Category.findByPk(updateData.parentId);
        if (!parentCategory) {
          return res.status(400).json({
            success: false,
            message: 'Wybrana kategoria nadrzędna nie istnieje.'
          });
        }
      }
      
      await category.update(updateData);
      
      const updatedCategory = await Category.findByPk(id, {
        include: [
          {
            model: Category,
            as: 'parent',
            required: false
          },
          {
            model: Category,
            as: 'subcategories',
            required: false
          }
        ]
      });
      
      res.json({
        success: true,
        message: 'Kategoria została zaktualizowana pomyślnie.',
        data: { category: updatedCategory }
      });
      
      console.log(`✅ Zaktualizowano kategorię: ${category.name} (ID: ${category.id})`);
      
    } catch (error) {
      console.error('Błąd podczas aktualizacji kategorii:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd serwera podczas aktualizacji kategorii.'
      });
    }
  }
);

// DELETE /api/categories/:id - Usuń kategorię
router.delete('/:id',
  verifyToken,
  requireAdmin,
  [param('id').isInt().withMessage('ID kategorii musi być liczbą')],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { id } = req.params;
      
      const category = await Category.findByPk(id);
      
      if (!category) {
        return res.status(404).json({
          success: false,
          message: 'Kategoria nie została znaleziona.'
        });
      }
      
      // Sprawdź czy kategoria ma podkategorie
      const hasChildren = await category.hasChildren();
      if (hasChildren) {
        return res.status(400).json({
          success: false,
          message: 'Nie można usunąć kategorii, która ma podkategorie. Usuń najpierw podkategorie.'
        });
      }
      
      // Sprawdź czy kategoria ma przypisane firmy
      const { Company } = require('../models');
      const companiesCount = await Company.count({
        where: {
          [Op.or]: [
            { categoryId: id },
            { subcategoryId: id }
          ]
        }
      });
      
      if (companiesCount > 0) {
        return res.status(400).json({
          success: false,
          message: `Nie można usunąć kategorii, która ma przypisane firmy (${companiesCount}). Przenieś firmy do innych kategorii.`
        });
      }
      
      await category.destroy();
      
      res.json({
        success: true,
        message: 'Kategoria została usunięta pomyślnie.'
      });
      
      console.log(`✅ Usunięto kategorię: ${category.name} (ID: ${category.id})`);
      
    } catch (error) {
      console.error('Błąd podczas usuwania kategorii:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd serwera podczas usuwania kategorii.'
      });
    }
  }
);

module.exports = router;
