const express = require('express');
const { body, param } = require('express-validator');
const { Coupon, Company } = require('../models');
const { verifyToken, requireAdmin } = require('../middleware/auth');
const { Op } = require('sequelize');

const router = express.Router();

const handleValidationErrors = (req, res, next) => {
  const { validationResult } = require('express-validator');
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '<PERSON><PERSON><PERSON><PERSON> walidacji',
      errors: errors.array()
    });
  }
  
  next();
};

// GET /api/coupons - Pobierz wszystkie kupony
router.get('/', verifyToken, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 20, status, companyId } = req.query;
    const offset = (page - 1) * limit;
    const where = {};
    
    if (status) where.status = status;
    if (companyId) where.companyId = companyId;
    
    const { count, rows } = await Coupon.findAndCountAll({
      where,
      include: [{
        model: Company,
        as: 'company',
        attributes: ['id', 'name', 'slug', 'logo']
      }],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    res.json({
      success: true,
      data: {
        coupons: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
    
  } catch (error) {
    console.error('Błąd podczas pobierania kuponów:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas pobierania kuponów.'
    });
  }
});

// GET /api/coupons/:id - Pobierz kupon po ID
router.get('/:id', verifyToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    const coupon = await Coupon.findByPk(id, {
      include: [{
        model: Company,
        as: 'company',
        attributes: ['id', 'name', 'slug', 'logo']
      }]
    });
    
    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: 'Kupon nie został znaleziony.'
      });
    }
    
    res.json({
      success: true,
      data: { coupon }
    });
    
  } catch (error) {
    console.error('Błąd podczas pobierania kuponu:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas pobierania kuponu.'
    });
  }
});

// POST /api/coupons - Utwórz nowy kupon
router.post('/', 
  verifyToken, 
  requireAdmin,
  [
    body('code')
      .notEmpty()
      .isLength({ min: 3, max: 50 })
      .isAlphanumeric()
      .withMessage('Kod kuponu musi mieć od 3 do 50 znaków alfanumerycznych'),
    body('title')
      .notEmpty()
      .isLength({ min: 5, max: 255 })
      .withMessage('Tytuł kuponu musi mieć od 5 do 255 znaków'),
    body('companyId')
      .isInt()
      .withMessage('ID firmy jest wymagane i musi być liczbą'),
    body('discountType')
      .isIn(['percentage', 'fixed_amount', 'free_shipping'])
      .withMessage('Nieprawidłowy typ rabatu'),
    body('discountValue')
      .isFloat({ min: 0 })
      .withMessage('Wartość rabatu musi być liczbą większą lub równą 0')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const couponData = req.body;
      
      // Sprawdź czy firma istnieje
      const company = await Company.findByPk(couponData.companyId);
      if (!company) {
        return res.status(400).json({
          success: false,
          message: 'Wybrana firma nie istnieje.'
        });
      }
      
      // Sprawdź czy kod kuponu jest unikalny
      const existingCoupon = await Coupon.findOne({
        where: { code: couponData.code.toUpperCase() }
      });
      
      if (existingCoupon) {
        return res.status(400).json({
          success: false,
          message: 'Kupon z tym kodem już istnieje.'
        });
      }
      
      const coupon = await Coupon.create(couponData);
      
      const createdCoupon = await Coupon.findByPk(coupon.id, {
        include: [{
          model: Company,
          as: 'company',
          attributes: ['id', 'name', 'slug', 'logo']
        }]
      });
      
      res.status(201).json({
        success: true,
        message: 'Kupon został utworzony pomyślnie.',
        data: { coupon: createdCoupon }
      });
      
      console.log(`✅ Utworzono kupon: ${coupon.code} (ID: ${coupon.id})`);
      
    } catch (error) {
      console.error('Błąd podczas tworzenia kuponu:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd serwera podczas tworzenia kuponu.'
      });
    }
  }
);

// PUT /api/coupons/:id - Aktualizuj kupon
router.put('/:id',
  verifyToken,
  requireAdmin,
  [
    param('id').isInt().withMessage('ID kuponu musi być liczbą'),
    body('code')
      .optional()
      .isLength({ min: 3, max: 50 })
      .isAlphanumeric()
      .withMessage('Kod kuponu musi mieć od 3 do 50 znaków alfanumerycznych'),
    body('title')
      .optional()
      .isLength({ min: 5, max: 255 })
      .withMessage('Tytuł kuponu musi mieć od 5 do 255 znaków'),
    body('discountType')
      .optional()
      .isIn(['percentage', 'fixed_amount', 'free_shipping'])
      .withMessage('Nieprawidłowy typ rabatu'),
    body('discountValue')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Wartość rabatu musi być liczbą większą lub równą 0')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { id } = req.params;
      const updateData = req.body;
      
      const coupon = await Coupon.findByPk(id);
      
      if (!coupon) {
        return res.status(404).json({
          success: false,
          message: 'Kupon nie został znaleziony.'
        });
      }
      
      // Sprawdź unikalność kodu jeśli został zmieniony
      if (updateData.code && updateData.code.toUpperCase() !== coupon.code) {
        const existingCoupon = await Coupon.findOne({
          where: { 
            code: updateData.code.toUpperCase(),
            id: { [Op.ne]: id }
          }
        });
        
        if (existingCoupon) {
          return res.status(400).json({
            success: false,
            message: 'Kupon z tym kodem już istnieje.'
          });
        }
      }
      
      await coupon.update(updateData);
      
      const updatedCoupon = await Coupon.findByPk(id, {
        include: [{
          model: Company,
          as: 'company',
          attributes: ['id', 'name', 'slug', 'logo']
        }]
      });
      
      res.json({
        success: true,
        message: 'Kupon został zaktualizowany pomyślnie.',
        data: { coupon: updatedCoupon }
      });
      
      console.log(`✅ Zaktualizowano kupon: ${coupon.code} (ID: ${coupon.id})`);
      
    } catch (error) {
      console.error('Błąd podczas aktualizacji kuponu:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd serwera podczas aktualizacji kuponu.'
      });
    }
  }
);

// DELETE /api/coupons/:id - Usuń kupon
router.delete('/:id',
  verifyToken,
  requireAdmin,
  [param('id').isInt().withMessage('ID kuponu musi być liczbą')],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { id } = req.params;
      
      const coupon = await Coupon.findByPk(id);
      
      if (!coupon) {
        return res.status(404).json({
          success: false,
          message: 'Kupon nie został znaleziony.'
        });
      }
      
      await coupon.destroy();
      
      res.json({
        success: true,
        message: 'Kupon został usunięty pomyślnie.'
      });
      
      console.log(`✅ Usunięto kupon: ${coupon.code} (ID: ${coupon.id})`);
      
    } catch (error) {
      console.error('Błąd podczas usuwania kuponu:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd serwera podczas usuwania kuponu.'
      });
    }
  }
);

module.exports = router;
