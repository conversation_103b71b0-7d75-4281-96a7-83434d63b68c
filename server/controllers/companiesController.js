const { Company, Category, Offer, Coupon } = require('../models');
const { deleteFile } = require('../middleware/upload');
const { Op } = require('sequelize');

/**
 * Pobierz wszystkie firmy (z paginacją i filtrami)
 */
const getCompanies = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      categoryId,
      subcategoryId,
      search,
      topPosition,
      featured
    } = req.query;
    
    const offset = (page - 1) * limit;
    const where = {};
    
    // Filtry
    if (status) where.status = status;
    if (categoryId) where.categoryId = categoryId;
    if (subcategoryId) where.subcategoryId = subcategoryId;
    if (topPosition !== undefined) {
      where.topPosition = topPosition === 'null' ? null : topPosition;
    }
    if (featured !== undefined) where.featured = featured === 'true';
    
    // Wyszukiwanie
    if (search) {
      where[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } },
        { address: { [Op.like]: `%${search}%` } }
      ];
    }
    
    const { count, rows } = await Company.findAndCountAll({
      where,
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Category,
          as: 'subcategory',
          attributes: ['id', 'name', 'slug'],
          required: false
        }
      ],
      order: [
        ['topPosition', 'ASC NULLS LAST'],
        ['featured', 'DESC'],
        ['createdAt', 'DESC']
      ],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    res.json({
      success: true,
      data: {
        companies: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
    
  } catch (error) {
    console.error('Błąd podczas pobierania firm:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas pobierania firm.'
    });
  }
};

/**
 * Pobierz firmę po ID
 */
const getCompanyById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const company = await Company.findByPk(id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Category,
          as: 'subcategory',
          attributes: ['id', 'name', 'slug'],
          required: false
        },
        {
          model: Offer,
          as: 'offers',
          where: { status: 'active' },
          required: false,
          limit: 5
        },
        {
          model: Coupon,
          as: 'coupons',
          where: { status: 'active' },
          required: false,
          limit: 5
        }
      ]
    });
    
    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Firma nie została znaleziona.'
      });
    }
    
    res.json({
      success: true,
      data: { company }
    });
    
  } catch (error) {
    console.error('Błąd podczas pobierania firmy:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas pobierania firmy.'
    });
  }
};

/**
 * Utwórz nową firmę
 */
const createCompany = async (req, res) => {
  try {
    const companyData = req.body;
    
    // Walidacja wymaganych pól
    if (!companyData.name || !companyData.categoryId) {
      return res.status(400).json({
        success: false,
        message: 'Nazwa firmy i kategoria są wymagane.'
      });
    }
    
    // Sprawdź czy kategoria istnieje
    const category = await Category.findByPk(companyData.categoryId);
    if (!category) {
      return res.status(400).json({
        success: false,
        message: 'Wybrana kategoria nie istnieje.'
      });
    }
    
    // Sprawdź podkategorię jeśli została podana
    if (companyData.subcategoryId) {
      const subcategory = await Category.findByPk(companyData.subcategoryId);
      if (!subcategory) {
        return res.status(400).json({
          success: false,
          message: 'Wybrana podkategoria nie istnieje.'
        });
      }
    }
    
    // Dodaj logo jeśli zostało przesłane
    if (req.file) {
      companyData.logo = req.file.url;
    }
    
    // Dodaj zdjęcia jeśli zostały przesłane
    if (req.files && req.files.images) {
      companyData.images = req.files.images.map(file => file.url);
    }
    
    // Sprawdź pozycję TOP
    if (companyData.topPosition) {
      const existingTopCompany = await Company.findOne({
        where: { topPosition: companyData.topPosition }
      });
      
      if (existingTopCompany) {
        return res.status(400).json({
          success: false,
          message: `Pozycja TOP ${companyData.topPosition} jest już zajęta.`
        });
      }
    }
    
    const company = await Company.create(companyData);
    
    // Pobierz utworzoną firmę z relacjami
    const createdCompany = await Company.findByPk(company.id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Category,
          as: 'subcategory',
          attributes: ['id', 'name', 'slug'],
          required: false
        }
      ]
    });
    
    res.status(201).json({
      success: true,
      message: 'Firma została utworzona pomyślnie.',
      data: { company: createdCompany }
    });
    
    console.log(`✅ Utworzono firmę: ${company.name} (ID: ${company.id})`);
    
  } catch (error) {
    console.error('Błąd podczas tworzenia firmy:', error);
    
    // Usuń przesłane pliki w przypadku błędu
    if (req.file) {
      await deleteFile(req.file.url);
    }
    if (req.files && req.files.images) {
      for (const file of req.files.images) {
        await deleteFile(file.url);
      }
    }
    
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas tworzenia firmy.'
    });
  }
};

/**
 * Aktualizuj firmę
 */
const updateCompany = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const company = await Company.findByPk(id);
    
    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Firma nie została znaleziona.'
      });
    }
    
    // Sprawdź kategorię jeśli została zmieniona
    if (updateData.categoryId && updateData.categoryId !== company.categoryId) {
      const category = await Category.findByPk(updateData.categoryId);
      if (!category) {
        return res.status(400).json({
          success: false,
          message: 'Wybrana kategoria nie istnieje.'
        });
      }
    }
    
    // Sprawdź podkategorię jeśli została zmieniona
    if (updateData.subcategoryId && updateData.subcategoryId !== company.subcategoryId) {
      const subcategory = await Category.findByPk(updateData.subcategoryId);
      if (!subcategory) {
        return res.status(400).json({
          success: false,
          message: 'Wybrana podkategoria nie istnieje.'
        });
      }
    }
    
    // Sprawdź pozycję TOP
    if (updateData.topPosition && updateData.topPosition !== company.topPosition) {
      const existingTopCompany = await Company.findOne({
        where: { 
          topPosition: updateData.topPosition,
          id: { [Op.ne]: id }
        }
      });
      
      if (existingTopCompany) {
        return res.status(400).json({
          success: false,
          message: `Pozycja TOP ${updateData.topPosition} jest już zajęta.`
        });
      }
    }
    
    // Obsługa nowego logo
    if (req.file) {
      // Usuń stare logo
      if (company.logo) {
        await deleteFile(company.logo);
      }
      updateData.logo = req.file.url;
    }
    
    // Obsługa nowych zdjęć
    if (req.files && req.files.images) {
      // Usuń stare zdjęcia
      if (company.images && company.images.length > 0) {
        for (const imageUrl of company.images) {
          await deleteFile(imageUrl);
        }
      }
      updateData.images = req.files.images.map(file => file.url);
    }
    
    await company.update(updateData);
    
    // Pobierz zaktualizowaną firmę z relacjami
    const updatedCompany = await Company.findByPk(id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Category,
          as: 'subcategory',
          attributes: ['id', 'name', 'slug'],
          required: false
        }
      ]
    });
    
    res.json({
      success: true,
      message: 'Firma została zaktualizowana pomyślnie.',
      data: { company: updatedCompany }
    });
    
    console.log(`✅ Zaktualizowano firmę: ${company.name} (ID: ${company.id})`);
    
  } catch (error) {
    console.error('Błąd podczas aktualizacji firmy:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas aktualizacji firmy.'
    });
  }
};

/**
 * Usuń firmę
 */
const deleteCompany = async (req, res) => {
  try {
    const { id } = req.params;
    
    const company = await Company.findByPk(id);
    
    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Firma nie została znaleziona.'
      });
    }
    
    // Usuń pliki związane z firmą
    if (company.logo) {
      await deleteFile(company.logo);
    }
    
    if (company.images && company.images.length > 0) {
      for (const imageUrl of company.images) {
        await deleteFile(imageUrl);
      }
    }
    
    await company.destroy();
    
    res.json({
      success: true,
      message: 'Firma została usunięta pomyślnie.'
    });
    
    console.log(`✅ Usunięto firmę: ${company.name} (ID: ${company.id})`);
    
  } catch (error) {
    console.error('Błąd podczas usuwania firmy:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas usuwania firmy.'
    });
  }
};

/**
 * Pobierz firmy TOP
 */
const getTopCompanies = async (req, res) => {
  try {
    const topCompanies = await Company.getTopCompanies();
    
    res.json({
      success: true,
      data: { companies: topCompanies }
    });
    
  } catch (error) {
    console.error('Błąd podczas pobierania firm TOP:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas pobierania firm TOP.'
    });
  }
};

/**
 * Ustaw pozycję TOP dla firmy
 */
const setTopPosition = async (req, res) => {
  try {
    const { id } = req.params;
    const { topPosition } = req.body;
    
    const company = await Company.findByPk(id);
    
    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Firma nie została znaleziona.'
      });
    }
    
    // Sprawdź czy pozycja jest już zajęta
    if (topPosition) {
      const existingTopCompany = await Company.findOne({
        where: { 
          topPosition: topPosition,
          id: { [Op.ne]: id }
        }
      });
      
      if (existingTopCompany) {
        // Usuń pozycję TOP z istniejącej firmy
        await existingTopCompany.update({ topPosition: null });
      }
    }
    
    await company.update({ topPosition });
    
    res.json({
      success: true,
      message: `Pozycja TOP ${topPosition || 'została usunięta'} została ustawiona dla firmy.`,
      data: { company }
    });
    
    console.log(`✅ Ustawiono pozycję TOP ${topPosition} dla firmy: ${company.name}`);
    
  } catch (error) {
    console.error('Błąd podczas ustawiania pozycji TOP:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas ustawiania pozycji TOP.'
    });
  }
};

module.exports = {
  getCompanies,
  getCompanyById,
  createCompany,
  updateCompany,
  deleteCompany,
  getTopCompanies,
  setTopPosition
};
