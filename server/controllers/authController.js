const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { Admin } = require('../models');
const { generateToken } = require('../middleware/auth');
const config = require('../config/config');

/**
 * Logowanie administratora
 */
const login = async (req, res) => {
  try {
    const { email, password, rememberMe } = req.body;

    // Walidacja danych wejściowych
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email i hasło są wymagane.'
      });
    }

    // Znajdź administratora po emailu
    const admin = await Admin.findOne({ where: { email: email.toLowerCase() } });

    if (!admin) {
      return res.status(401).json({
        success: false,
        message: 'Nieprawidłowy email lub hasło.'
      });
    }

    // Sprawdź czy konto nie jest zablokowane
    if (admin.isLocked()) {
      return res.status(423).json({
        success: false,
        message: 'Konto zostało tymczasowo zablokowane z powodu zbyt wielu nieudanych prób logowania.',
        lockedUntil: admin.lockedUntil
      });
    }

    // Sprawdź czy konto jest aktywne
    if (!admin.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Konto zostało dezaktywowane.'
      });
    }

    // Sprawdź hasło
    const isPasswordValid = await admin.comparePassword(password);

    if (!isPasswordValid) {
      // Zwiększ liczbę nieudanych prób logowania
      await admin.incrementLoginAttempts();

      return res.status(401).json({
        success: false,
        message: 'Nieprawidłowy email lub hasło.'
      });
    }

    // Resetuj licznik nieudanych prób logowania
    await admin.resetLoginAttempts();

    // Wygeneruj token JWT
    const token = generateToken(admin);

    // Ustaw ciasteczko z tokenem (opcjonalne)
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: rememberMe ? 7 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000 // 7 dni lub 24 godziny
    };

    res.cookie('adminToken', token, cookieOptions);

    // Zwróć odpowiedź z tokenem
    res.json({
      success: true,
      message: 'Logowanie zakończone sukcesem.',
      data: {
        admin: admin.toJSON(),
        token: token,
        expiresIn: config.jwt.expiresIn
      }
    });

    console.log(`✅ Administrator zalogowany: ${admin.email} (ID: ${admin.id})`);

  } catch (error) {
    console.error('Błąd podczas logowania:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas logowania.'
    });
  }
};

/**
 * Wylogowanie administratora
 */
const logout = async (req, res) => {
  try {
    // Usuń ciasteczko z tokenem
    res.clearCookie('adminToken');

    res.json({
      success: true,
      message: 'Wylogowanie zakończone sukcesem.'
    });

    console.log(`✅ Administrator wylogowany: ${req.admin?.email || 'Nieznany'}`);

  } catch (error) {
    console.error('Błąd podczas wylogowania:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas wylogowania.'
    });
  }
};

/**
 * Sprawdzenie statusu autoryzacji
 */
const checkAuth = async (req, res) => {
  try {
    if (!req.admin) {
      return res.status(401).json({
        success: false,
        message: 'Brak autoryzacji.'
      });
    }

    res.json({
      success: true,
      data: {
        admin: req.admin.toJSON(),
        isAuthenticated: true
      }
    });

  } catch (error) {
    console.error('Błąd podczas sprawdzania autoryzacji:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas sprawdzania autoryzacji.'
    });
  }
};

/**
 * Zmiana hasła administratora
 */
const changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword, confirmPassword } = req.body;

    // Walidacja danych wejściowych
    if (!currentPassword || !newPassword || !confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'Wszystkie pola są wymagane.'
      });
    }

    if (newPassword !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'Nowe hasło i potwierdzenie hasła muszą być identyczne.'
      });
    }

    if (newPassword.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Nowe hasło musi mieć co najmniej 8 znaków.'
      });
    }

    // Sprawdź obecne hasło
    const isCurrentPasswordValid = await req.admin.comparePassword(currentPassword);

    if (!isCurrentPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Obecne hasło jest nieprawidłowe.'
      });
    }

    // Zaktualizuj hasło
    await req.admin.update({ password: newPassword });

    res.json({
      success: true,
      message: 'Hasło zostało zmienione pomyślnie.'
    });

    console.log(`✅ Hasło zmienione dla administratora: ${req.admin.email}`);

  } catch (error) {
    console.error('Błąd podczas zmiany hasła:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas zmiany hasła.'
    });
  }
};

/**
 * Aktualizacja profilu administratora
 */
const updateProfile = async (req, res) => {
  try {
    const { firstName, lastName, email } = req.body;

    // Walidacja emaila
    if (email && email !== req.admin.email) {
      const existingAdmin = await Admin.findOne({
        where: {
          email: email.toLowerCase(),
          id: { [require('sequelize').Op.ne]: req.admin.id }
        }
      });

      if (existingAdmin) {
        return res.status(400).json({
          success: false,
          message: 'Administrator z tym adresem email już istnieje.'
        });
      }
    }

    // Przygotuj dane do aktualizacji
    const updateData = {};
    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (email !== undefined) updateData.email = email.toLowerCase();

    // Zaktualizuj profil
    await req.admin.update(updateData);

    // Pobierz zaktualizowane dane
    const updatedAdmin = await Admin.findByPk(req.admin.id);

    res.json({
      success: true,
      message: 'Profil został zaktualizowany pomyślnie.',
      data: {
        admin: updatedAdmin.toJSON()
      }
    });

    console.log(`✅ Profil zaktualizowany dla administratora: ${updatedAdmin.email}`);

  } catch (error) {
    console.error('Błąd podczas aktualizacji profilu:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas aktualizacji profilu.'
    });
  }
};

/**
 * Odświeżenie tokenu
 */
const refreshToken = async (req, res) => {
  try {
    if (!req.admin) {
      return res.status(401).json({
        success: false,
        message: 'Brak autoryzacji.'
      });
    }

    // Wygeneruj nowy token
    const newToken = generateToken(req.admin);

    // Ustaw nowe ciasteczko
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000 // 24 godziny
    };

    res.cookie('adminToken', newToken, cookieOptions);

    res.json({
      success: true,
      message: 'Token został odświeżony.',
      data: {
        token: newToken,
        expiresIn: config.jwt.expiresIn
      }
    });

  } catch (error) {
    console.error('Błąd podczas odświeżania tokenu:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas odświeżania tokenu.'
    });
  }
};

/**
 * Żądanie resetu hasła
 */
const requestPasswordReset = async (req, res) => {
  try {
    const { email } = req.body;

    // Walidacja danych wejściowych
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email jest wymagany.'
      });
    }

    // Znajdź administratora po emailu
    const admin = await Admin.findOne({ where: { email: email.toLowerCase() } });

    if (!admin) {
      // Nie ujawniaj czy email istnieje w systemie
      return res.json({
        success: true,
        message: 'Jeśli podany email istnieje w systemie, instrukcje resetu hasła zostały wysłane.'
      });
    }

    // Sprawdź czy konto jest aktywne
    if (!admin.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Konto zostało dezaktywowane.'
      });
    }

    // Wygeneruj token resetu hasła
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 godzina

    // Zapisz token w bazie danych
    await admin.update({
      resetPasswordToken: resetToken,
      resetPasswordExpires: resetTokenExpiry
    });

    // W rzeczywistej aplikacji tutaj wysłałbyś email z linkiem do resetu
    // Na potrzeby demonstracji zwracamy token w odpowiedzi
    console.log(`🔑 Token resetu hasła dla ${admin.email}: ${resetToken}`);

    res.json({
      success: true,
      message: 'Instrukcje resetu hasła zostały wysłane na podany adres email.',
      // W produkcji nie zwracaj tokenu w odpowiedzi!
      resetToken: process.env.NODE_ENV === 'development' ? resetToken : undefined
    });

  } catch (error) {
    console.error('Błąd podczas żądania resetu hasła:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas żądania resetu hasła.'
    });
  }
};

/**
 * Reset hasła z tokenem
 */
const resetPassword = async (req, res) => {
  try {
    const { token, newPassword, confirmPassword } = req.body;

    // Walidacja danych wejściowych
    if (!token || !newPassword || !confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'Token, nowe hasło i potwierdzenie hasła są wymagane.'
      });
    }

    if (newPassword !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'Nowe hasło i potwierdzenie hasła muszą być identyczne.'
      });
    }

    if (newPassword.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Nowe hasło musi mieć co najmniej 8 znaków.'
      });
    }

    // Znajdź administratora z ważnym tokenem resetu
    const admin = await Admin.findOne({
      where: {
        resetPasswordToken: token,
        resetPasswordExpires: {
          [require('sequelize').Op.gt]: new Date()
        }
      }
    });

    if (!admin) {
      return res.status(400).json({
        success: false,
        message: 'Token resetu hasła jest nieprawidłowy lub wygasł.'
      });
    }

    // Sprawdź czy konto jest aktywne
    if (!admin.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Konto zostało dezaktywowane.'
      });
    }

    // Zaktualizuj hasło i usuń token resetu
    await admin.update({
      password: newPassword,
      resetPasswordToken: null,
      resetPasswordExpires: null,
      loginAttempts: 0,
      lockedUntil: null
    });

    res.json({
      success: true,
      message: 'Hasło zostało zmienione pomyślnie. Możesz się teraz zalogować.'
    });

    console.log(`✅ Hasło zresetowane dla administratora: ${admin.email}`);

  } catch (error) {
    console.error('Błąd podczas resetu hasła:', error);
    res.status(500).json({
      success: false,
      message: 'Błąd serwera podczas resetu hasła.'
    });
  }
};

module.exports = {
  login,
  logout,
  checkAuth,
  changePassword,
  updateProfile,
  refreshToken,
  requestPasswordReset,
  resetPassword
};
