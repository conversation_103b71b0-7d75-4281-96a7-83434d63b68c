#!/usr/bin/env node

/**
 * Skrypt konfiguracji bazy danych MySQL dla Żyrardów.poleca.to
 * 
 * Ten skrypt:
 * 1. Tworzy bazę danych MySQL
 * 2. Tworzy użytkownika bazy danych
 * 3. Nadaje odpowiednie uprawnienia
 * 4. Inicjalizuje tabele
 * 5. Tworzy domyślne dane
 */

const mysql = require('mysql2/promise');
const config = require('../config/config');
require('dotenv').config();

// Konfiguracja połączenia z MySQL (bez bazy danych)
const rootConnection = {
  host: config.database.host,
  port: config.database.port,
  user: 'root', // Użytkownik root do tworzenia bazy
  password: process.env.MYSQL_ROOT_PASSWORD || '', // Hasło root
  charset: 'utf8mb4'
};

/**
 * Główna funkcja konfiguracji
 */
async function setupDatabase() {
  let connection;
  
  try {
    console.log('🔄 Rozpoczynanie konfiguracji bazy danych MySQL...');
    
    // Połącz się z MySQL jako root
    console.log('🔌 Łączenie z serwerem MySQL...');
    connection = await mysql.createConnection(rootConnection);
    console.log('✅ Połączono z serwerem MySQL.');
    
    // Utwórz bazę danych
    console.log(`🔄 Tworzenie bazy danych: ${config.database.name}`);
    await connection.execute(`
      CREATE DATABASE IF NOT EXISTS \`${config.database.name}\`
      CHARACTER SET utf8mb4
      COLLATE utf8mb4_unicode_ci
    `);
    console.log(`✅ Baza danych "${config.database.name}" została utworzona.`);
    
    // Utwórz użytkownika bazy danych
    console.log(`🔄 Tworzenie użytkownika: ${config.database.user}`);
    
    // Usuń użytkownika jeśli istnieje (dla bezpieczeństwa)
    try {
      await connection.execute(`DROP USER IF EXISTS '${config.database.user}'@'localhost'`);
      await connection.execute(`DROP USER IF EXISTS '${config.database.user}'@'%'`);
    } catch (error) {
      // Ignoruj błędy jeśli użytkownik nie istnieje
    }
    
    // Utwórz nowego użytkownika
    await connection.execute(`
      CREATE USER '${config.database.user}'@'localhost' 
      IDENTIFIED BY '${config.database.password}'
    `);
    
    await connection.execute(`
      CREATE USER '${config.database.user}'@'%' 
      IDENTIFIED BY '${config.database.password}'
    `);
    
    console.log(`✅ Użytkownik "${config.database.user}" został utworzony.`);
    
    // Nadaj uprawnienia
    console.log('🔄 Nadawanie uprawnień...');
    
    const privileges = [
      'SELECT', 'INSERT', 'UPDATE', 'DELETE', 
      'CREATE', 'DROP', 'INDEX', 'ALTER',
      'CREATE TEMPORARY TABLES', 'LOCK TABLES'
    ];
    
    const privilegesString = privileges.join(', ');
    
    await connection.execute(`
      GRANT ${privilegesString} ON \`${config.database.name}\`.* 
      TO '${config.database.user}'@'localhost'
    `);
    
    await connection.execute(`
      GRANT ${privilegesString} ON \`${config.database.name}\`.* 
      TO '${config.database.user}'@'%'
    `);
    
    // Odśwież uprawnienia
    await connection.execute('FLUSH PRIVILEGES');
    
    console.log('✅ Uprawnienia zostały nadane.');
    
    // Zamknij połączenie root
    await connection.end();
    
    // Teraz połącz się jako nowy użytkownik i zainicjalizuj tabele
    console.log('🔄 Inicjalizacja tabel...');
    
    const { initializeDatabase, createSampleData } = require('../models');
    
    // Inicjalizuj bazę danych (force = true dla czystej instalacji)
    await initializeDatabase(true);
    
    // Utwórz przykładowe dane jeśli jesteśmy w trybie development
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 Tworzenie przykładowych danych...');
      await createSampleData();
    }
    
    console.log('🎉 Konfiguracja bazy danych została zakończona pomyślnie!');
    console.log('');
    console.log('📋 Podsumowanie:');
    console.log(`   Baza danych: ${config.database.name}`);
    console.log(`   Użytkownik: ${config.database.user}`);
    console.log(`   Host: ${config.database.host}:${config.database.port}`);
    console.log('');
    console.log('🔐 Dane logowania do panelu admin:');
    console.log(`   Email: ${config.admin.email}`);
    console.log(`   Hasło: ${config.admin.password}`);
    console.log('');
    console.log('⚠️  WAŻNE: Zmień hasło administratora po pierwszym logowaniu!');
    
  } catch (error) {
    console.error('❌ Błąd podczas konfiguracji bazy danych:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('');
      console.error('💡 Sprawdź:');
      console.error('   - Czy MySQL jest uruchomiony');
      console.error('   - Czy hasło root jest prawidłowe');
      console.error('   - Czy użytkownik root ma uprawnienia do tworzenia baz danych');
      console.error('');
      console.error('🔧 Aby ustawić hasło root MySQL:');
      console.error('   sudo mysql -u root -p');
      console.error('   ALTER USER \'root\'@\'localhost\' IDENTIFIED BY \'twoje_haslo\';');
      console.error('   FLUSH PRIVILEGES;');
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.error('');
      console.error('💡 MySQL nie jest uruchomiony. Uruchom MySQL:');
      console.error('   Ubuntu/Debian: sudo systemctl start mysql');
      console.error('   CentOS/RHEL: sudo systemctl start mysqld');
      console.error('   macOS: brew services start mysql');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      try {
        await connection.end();
      } catch (error) {
        // Ignoruj błędy zamykania połączenia
      }
    }
  }
}

/**
 * Funkcja sprawdzenia połączenia z MySQL
 */
async function testConnection() {
  try {
    console.log('🔄 Testowanie połączenia z bazą danych...');
    
    const { checkDatabaseConnection } = require('../models');
    const isConnected = await checkDatabaseConnection();
    
    if (isConnected) {
      console.log('✅ Połączenie z bazą danych działa poprawnie.');
      
      // Sprawdź czy tabele istnieją
      const { sequelize } = require('../config/database');
      const [results] = await sequelize.query(`
        SELECT COUNT(*) as table_count 
        FROM information_schema.tables 
        WHERE table_schema = '${config.database.name}'
      `);
      
      const tableCount = results[0].table_count;
      console.log(`📊 Liczba tabel w bazie danych: ${tableCount}`);
      
      if (tableCount === 0) {
        console.log('⚠️  Baza danych jest pusta. Uruchom: npm run setup-db');
      }
      
    } else {
      console.log('❌ Nie można połączyć się z bazą danych.');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Błąd podczas testowania połączenia:', error.message);
    process.exit(1);
  }
}

// Obsługa argumentów wiersza poleceń
const args = process.argv.slice(2);

if (args.includes('--test')) {
  testConnection();
} else if (args.includes('--help') || args.includes('-h')) {
  console.log('');
  console.log('🛠️  Skrypt konfiguracji bazy danych - Żyrardów.poleca.to');
  console.log('');
  console.log('Użycie:');
  console.log('  node setup-database.js          - Konfiguracja bazy danych');
  console.log('  node setup-database.js --test   - Test połączenia z bazą');
  console.log('  node setup-database.js --help   - Wyświetl tę pomoc');
  console.log('');
  console.log('Zmienne środowiskowe:');
  console.log('  MYSQL_ROOT_PASSWORD - Hasło użytkownika root MySQL');
  console.log('  NODE_ENV           - Środowisko (development/production)');
  console.log('');
} else {
  setupDatabase();
}

module.exports = { setupDatabase, testConnection };
