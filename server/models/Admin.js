const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const { sequelize } = require('../config/database');
const config = require('../config/config');

const Admin = sequelize.define('Admin', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  password: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      len: [8, 255]
    }
  },
  firstName: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  lastName: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  role: {
    type: DataTypes.ENUM('super_admin', 'admin', 'moderator'),
    defaultValue: 'admin'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  lastLogin: {
    type: DataTypes.DATE,
    allowNull: true
  },
  loginAttempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  lockedUntil: {
    type: DataTypes.DATE,
    allowNull: true
  },
  resetPasswordToken: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  resetPasswordExpires: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'admins',
  timestamps: true,
  hooks: {
    beforeCreate: async (admin) => {
      if (admin.password) {
        admin.password = await bcrypt.hash(admin.password, config.security.bcryptRounds);
      }
    },
    beforeUpdate: async (admin) => {
      if (admin.changed('password')) {
        admin.password = await bcrypt.hash(admin.password, config.security.bcryptRounds);
      }
    }
  }
});

// Metody instancji
Admin.prototype.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

Admin.prototype.incrementLoginAttempts = async function() {
  // Jeśli konto jest już zablokowane i czas blokady minął
  if (this.lockedUntil && this.lockedUntil <= Date.now()) {
    return this.update({
      loginAttempts: 1,
      lockedUntil: null
    });
  }

  const updates = { loginAttempts: this.loginAttempts + 1 };

  // Jeśli osiągnięto maksymalną liczbę prób (5), zablokuj konto na 30 minut
  if (updates.loginAttempts >= 5) {
    updates.lockedUntil = Date.now() + (30 * 60 * 1000); // 30 minut
  }

  return this.update(updates);
};

Admin.prototype.resetLoginAttempts = async function() {
  return this.update({
    loginAttempts: 0,
    lockedUntil: null,
    lastLogin: new Date()
  });
};

Admin.prototype.isLocked = function() {
  return !!(this.lockedUntil && this.lockedUntil > Date.now());
};

Admin.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  delete values.password;
  delete values.loginAttempts;
  delete values.lockedUntil;
  delete values.resetPasswordToken;
  delete values.resetPasswordExpires;
  return values;
};

// Metody statyczne
Admin.createDefaultAdmin = async function() {
  try {
    const existingAdmin = await Admin.findOne({ where: { email: config.admin.email } });

    if (!existingAdmin) {
      const defaultAdmin = await Admin.create({
        email: config.admin.email,
        password: config.admin.password,
        firstName: 'Administrator',
        lastName: 'Systemu',
        role: 'super_admin',
        isActive: true
      });

      console.log('✅ Domyślny administrator został utworzony:', config.admin.email);
      return defaultAdmin;
    }

    console.log('ℹ️ Domyślny administrator już istnieje.');
    return existingAdmin;
  } catch (error) {
    console.error('❌ Błąd podczas tworzenia domyślnego administratora:', error.message);
    throw error;
  }
};

module.exports = Admin;
