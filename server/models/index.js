const { sequelize } = require('../config/database');

// Import wszystkich modeli
const Admin = require('./Admin');
const Category = require('./Category');
const Company = require('./Company');
const Offer = require('./Offer');
const Coupon = require('./Coupon');

// Definicja relacji między modelami

// Relacje Category
Category.hasMany(Category, {
  as: 'subcategories',
  foreignKey: 'parentId'
});

Category.belongsTo(Category, {
  as: 'parent',
  foreignKey: 'parentId'
});

Category.hasMany(Company, {
  as: 'companies',
  foreignKey: 'categoryId'
});

Category.hasMany(Company, {
  as: 'subcategoryCompanies',
  foreignKey: 'subcategoryId'
});

Category.hasMany(Offer, {
  as: 'offers',
  foreignKey: 'categoryId'
});

// Relacje Company
Company.belongsTo(Category, {
  as: 'category',
  foreignKey: 'categoryId'
});

Company.belongsTo(Category, {
  as: 'subcategory',
  foreignKey: 'subcategoryId'
});

Company.hasMany(Offer, {
  as: 'offers',
  foreignKey: 'companyId',
  onDelete: 'CASCADE'
});

Company.hasMany(Coupon, {
  as: 'coupons',
  foreignKey: 'companyId',
  onDelete: 'CASCADE'
});

// Relacje Offer
Offer.belongsTo(Company, {
  as: 'company',
  foreignKey: 'companyId'
});

Offer.belongsTo(Category, {
  as: 'category',
  foreignKey: 'categoryId'
});

// Relacje Coupon
Coupon.belongsTo(Company, {
  as: 'company',
  foreignKey: 'companyId'
});

// Eksport wszystkich modeli
const models = {
  Admin,
  Category,
  Company,
  Offer,
  Coupon,
  sequelize
};

// Funkcja inicjalizacji bazy danych
const initializeDatabase = async (force = false) => {
  try {
    console.log('🔄 Inicjalizacja bazy danych...');
    
    // Synchronizacja modeli z bazą danych
    await sequelize.sync({ force });
    console.log('✅ Modele zostały zsynchronizowane z bazą danych.');
    
    // Utworzenie domyślnych danych
    if (force || process.env.NODE_ENV === 'development') {
      console.log('🔄 Tworzenie domyślnych danych...');
      
      // Utwórz domyślnego administratora
      await Admin.createDefaultAdmin();
      
      // Utwórz domyślne kategorie
      await Category.createDefaultCategories();
      
      console.log('✅ Domyślne dane zostały utworzone.');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Błąd podczas inicjalizacji bazy danych:', error.message);
    throw error;
  }
};

// Funkcja sprawdzenia połączenia z bazą danych
const checkDatabaseConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('✅ Połączenie z bazą danych zostało nawiązane pomyślnie.');
    return true;
  } catch (error) {
    console.error('❌ Nie można połączyć się z bazą danych:', error.message);
    return false;
  }
};

// Funkcja zamknięcia połączenia z bazą danych
const closeDatabaseConnection = async () => {
  try {
    await sequelize.close();
    console.log('✅ Połączenie z bazą danych zostało zamknięte.');
  } catch (error) {
    console.error('❌ Błąd podczas zamykania połączenia z bazą danych:', error.message);
  }
};

// Funkcja czyszczenia wygasłych ofert i kuponów (cron job)
const cleanupExpiredItems = async () => {
  try {
    console.log('🔄 Czyszczenie wygasłych elementów...');
    
    // Wygaś stare oferty
    const expiredOffers = await Offer.expireOldOffers();
    console.log(`✅ Wygaszono ${expiredOffers[0]} ofert.`);
    
    // Wygaś stare kupony
    const expiredCoupons = await Coupon.expireOldCoupons();
    console.log(`✅ Wygaszono ${expiredCoupons[0]} kuponów.`);
    
    return { expiredOffers: expiredOffers[0], expiredCoupons: expiredCoupons[0] };
  } catch (error) {
    console.error('❌ Błąd podczas czyszczenia wygasłych elementów:', error.message);
    throw error;
  }
};

// Funkcja tworzenia przykładowych danych (tylko dla developmentu)
const createSampleData = async () => {
  if (process.env.NODE_ENV !== 'development') {
    console.log('ℹ️ Przykładowe dane są tworzone tylko w trybie development.');
    return;
  }
  
  try {
    console.log('🔄 Tworzenie przykładowych danych...');
    
    // Pobierz kategorie
    const categories = await Category.findAll({ where: { parentId: null } });
    const subcategories = await Category.findAll({ where: { parentId: { [require('sequelize').Op.not]: null } } });
    
    if (categories.length === 0) {
      console.log('⚠️ Brak kategorii. Najpierw utwórz kategorie.');
      return;
    }
    
    // Sprawdź czy już istnieją firmy
    const existingCompanies = await Company.count();
    if (existingCompanies > 0) {
      console.log('ℹ️ Przykładowe firmy już istnieją.');
      return;
    }
    
    // Utwórz przykładowe firmy
    const sampleCompanies = [
      {
        name: 'Restauracja Pod Akacjami',
        description: 'Najlepsza restauracja w Żyrardowie z tradycyjną polską kuchnią.',
        categoryId: categories.find(c => c.name.includes('Jedzenie'))?.id || categories[0].id,
        subcategoryId: subcategories.find(c => c.name === 'Restauracje')?.id,
        address: 'ul. Przykładowa 1',
        postalCode: '96-300',
        city: 'Żyrardów',
        phone: '+48 ***********',
        email: '<EMAIL>',
        website: 'https://podakacjami.pl',
        status: 'active',
        topPosition: 1,
        featured: true
      },
      {
        name: 'Salon Fryzjerski Bella',
        description: 'Profesjonalne usługi fryzjerskie i kosmetyczne.',
        categoryId: categories.find(c => c.name.includes('Uroda'))?.id || categories[1]?.id,
        subcategoryId: subcategories.find(c => c.name === 'Salony fryzjerskie')?.id,
        address: 'ul. Przykładowa 2',
        postalCode: '96-300',
        city: 'Żyrardów',
        phone: '+48 234 567 890',
        email: '<EMAIL>',
        status: 'active',
        topPosition: 2
      },
      {
        name: 'Sklep Sportowy Active',
        description: 'Szeroki wybór odzieży i sprzętu sportowego.',
        categoryId: categories.find(c => c.name.includes('Zakupy'))?.id || categories[2]?.id,
        subcategoryId: subcategories.find(c => c.name === 'Odzież i obuwie')?.id,
        address: 'ul. Przykładowa 3',
        postalCode: '96-300',
        city: 'Żyrardów',
        phone: '+48 ***********',
        email: '<EMAIL>',
        status: 'active',
        topPosition: 3
      }
    ];
    
    const createdCompanies = await Company.bulkCreate(sampleCompanies);
    console.log(`✅ Utworzono ${createdCompanies.length} przykładowych firm.`);
    
    // Utwórz przykładowe oferty
    const sampleOffers = [
      {
        title: 'Obiad biznesowy -20%',
        description: 'Specjalna oferta na obiady biznesowe w godzinach 12:00-15:00.',
        companyId: createdCompanies[0].id,
        regularPrice: 25.00,
        salePrice: 20.00,
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 dni
        status: 'active',
        featured: true
      },
      {
        title: 'Strzyżenie + stylizacja',
        description: 'Kompleksowa usługa fryzjerska w promocyjnej cenie.',
        companyId: createdCompanies[1].id,
        regularPrice: 80.00,
        salePrice: 60.00,
        startDate: new Date(),
        endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 dni
        status: 'active'
      }
    ];
    
    const createdOffers = await Offer.bulkCreate(sampleOffers);
    console.log(`✅ Utworzono ${createdOffers.length} przykładowych ofert.`);
    
    // Utwórz przykładowe kupony
    const sampleCoupons = [
      {
        code: 'ZYRARDOW20',
        title: 'Rabat 20% na pierwsze zamówienie',
        description: 'Specjalny rabat dla nowych klientów.',
        companyId: createdCompanies[0].id,
        discountType: 'percentage',
        discountValue: 20,
        minOrderValue: 50.00,
        maxUsage: 100,
        startDate: new Date(),
        endDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 dni
        status: 'active'
      }
    ];
    
    const createdCoupons = await Coupon.bulkCreate(sampleCoupons);
    console.log(`✅ Utworzono ${createdCoupons.length} przykładowych kuponów.`);
    
    console.log('✅ Wszystkie przykładowe dane zostały utworzone.');
    
  } catch (error) {
    console.error('❌ Błąd podczas tworzenia przykładowych danych:', error.message);
    throw error;
  }
};

module.exports = {
  ...models,
  initializeDatabase,
  checkDatabaseConnection,
  closeDatabaseConnection,
  cleanupExpiredItems,
  createSampleData
};
