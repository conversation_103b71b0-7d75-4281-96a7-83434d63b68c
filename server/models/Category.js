const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const slugify = require('slugify');

const Category = sequelize.define('Category', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 100]
    }
  },
  slug: {
    type: DataTypes.STRING(120),
    allowNull: false,
    unique: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  icon: {
    type: DataTypes.STRING(50),
    allowNull: true,
    defaultValue: 'fas fa-folder'
  },
  color: {
    type: DataTypes.STRING(7),
    allowNull: true,
    defaultValue: '#007bff',
    validate: {
      is: /^#[0-9A-F]{6}$/i
    }
  },
  parentId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'categories',
      key: 'id'
    }
  },
  sortOrder: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  metaTitle: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  metaDescription: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  metaKeywords: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'categories',
  timestamps: true,
  hooks: {
    beforeValidate: (category) => {
      if (category.name && !category.slug) {
        category.slug = slugify(category.name, {
          lower: true,
          strict: true,
          locale: 'pl'
        });
      }
    }
  }
});

// Metody instancji
Category.prototype.getFullPath = async function() {
  const path = [this.name];
  let current = this;
  
  while (current.parentId) {
    current = await Category.findByPk(current.parentId);
    if (current) {
      path.unshift(current.name);
    } else {
      break;
    }
  }
  
  return path.join(' > ');
};

Category.prototype.hasChildren = async function() {
  const count = await Category.count({
    where: { parentId: this.id }
  });
  return count > 0;
};

// Metody statyczne
Category.getMainCategories = async function() {
  return Category.findAll({
    where: {
      parentId: null,
      isActive: true
    },
    include: [{
      model: Category,
      as: 'subcategories',
      where: { isActive: true },
      required: false
    }],
    order: [
      ['sortOrder', 'ASC'],
      ['name', 'ASC'],
      [{ model: Category, as: 'subcategories' }, 'sortOrder', 'ASC'],
      [{ model: Category, as: 'subcategories' }, 'name', 'ASC']
    ]
  });
};

Category.getCategoryTree = async function() {
  const categories = await Category.findAll({
    where: { isActive: true },
    order: [['sortOrder', 'ASC'], ['name', 'ASC']]
  });
  
  const buildTree = (parentId = null) => {
    return categories
      .filter(cat => cat.parentId === parentId)
      .map(cat => ({
        ...cat.toJSON(),
        children: buildTree(cat.id)
      }));
  };
  
  return buildTree();
};

Category.createDefaultCategories = async function() {
  const config = require('../config/config');
  
  try {
    const existingCount = await Category.count();
    
    if (existingCount === 0) {
      console.log('🔄 Tworzenie domyślnych kategorii...');
      
      for (const [index, categoryData] of config.defaultCategories.entries()) {
        // Utwórz główną kategorię
        const mainCategory = await Category.create({
          name: categoryData.name,
          slug: categoryData.slug,
          sortOrder: index + 1,
          isActive: true
        });
        
        // Utwórz podkategorie
        for (const [subIndex, subcategoryName] of categoryData.subcategories.entries()) {
          await Category.create({
            name: subcategoryName,
            slug: slugify(`${categoryData.slug}-${subcategoryName}`, {
              lower: true,
              strict: true,
              locale: 'pl'
            }),
            parentId: mainCategory.id,
            sortOrder: subIndex + 1,
            isActive: true
          });
        }
      }
      
      console.log('✅ Domyślne kategorie zostały utworzone.');
    } else {
      console.log('ℹ️ Kategorie już istnieją w bazie danych.');
    }
  } catch (error) {
    console.error('❌ Błąd podczas tworzenia domyślnych kategorii:', error.message);
    throw error;
  }
};

module.exports = Category;
