const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const slugify = require('slugify');

const Offer = sequelize.define('Offer', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [5, 255]
    }
  },
  slug: {
    type: DataTypes.STRING(300),
    allowNull: false,
    unique: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  companyId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  categoryId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'categories',
      key: 'id'
    }
  },
  
  // Ceny i rabaty
  regularPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  salePrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  discountPercent: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
      max: 100
    }
  },
  savings: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  
  // Daty
  startDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  endDate: {
    type: DataTypes.DATE,
    allowNull: true,
    validate: {
      isAfterStartDate(value) {
        if (value && this.startDate && value <= this.startDate) {
          throw new Error('Data zakończenia musi być późniejsza niż data rozpoczęcia');
        }
      }
    }
  },
  
  // Warunki oferty
  terms: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  minOrderValue: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  maxUsage: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1
    }
  },
  currentUsage: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  
  // Zdjęcie oferty
  image: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  
  // Status i wyróżnienie
  status: {
    type: DataTypes.ENUM('draft', 'active', 'expired', 'suspended'),
    defaultValue: 'draft'
  },
  featured: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  
  // Statystyki
  views: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  clicks: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  
  // SEO
  metaTitle: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  metaDescription: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  // Daty publikacji
  publishedAt: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'offers',
  timestamps: true,
  hooks: {
    beforeValidate: (offer) => {
      if (offer.title && !offer.slug) {
        offer.slug = slugify(offer.title, {
          lower: true,
          strict: true,
          locale: 'pl'
        });
      }
      
      // Automatyczne obliczanie oszczędności i procentu rabatu
      if (offer.regularPrice && offer.salePrice) {
        offer.savings = offer.regularPrice - offer.salePrice;
        offer.discountPercent = Math.round((offer.savings / offer.regularPrice) * 100);
      }
    },
    beforeCreate: (offer) => {
      if (offer.status === 'active' && !offer.publishedAt) {
        offer.publishedAt = new Date();
      }
    },
    beforeUpdate: (offer) => {
      if (offer.changed('status') && offer.status === 'active' && !offer.publishedAt) {
        offer.publishedAt = new Date();
      }
      
      // Sprawdź czy oferta wygasła
      if (offer.endDate && offer.endDate < new Date() && offer.status === 'active') {
        offer.status = 'expired';
      }
    }
  }
});

// Metody instancji
Offer.prototype.incrementViews = async function() {
  return this.increment('views');
};

Offer.prototype.incrementClicks = async function() {
  return this.increment('clicks');
};

Offer.prototype.incrementUsage = async function() {
  if (this.maxUsage && this.currentUsage >= this.maxUsage) {
    throw new Error('Osiągnięto maksymalną liczbę wykorzystań oferty');
  }
  return this.increment('currentUsage');
};

Offer.prototype.isActive = function() {
  return this.status === 'active' && 
         (!this.endDate || this.endDate > new Date()) &&
         (!this.maxUsage || this.currentUsage < this.maxUsage);
};

Offer.prototype.isExpired = function() {
  return this.endDate && this.endDate < new Date();
};

Offer.prototype.hasDiscount = function() {
  return this.regularPrice && this.salePrice && this.salePrice < this.regularPrice;
};

Offer.prototype.getRemainingDays = function() {
  if (!this.endDate) return null;
  const now = new Date();
  const end = new Date(this.endDate);
  const diffTime = end - now;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// Metody statyczne
Offer.getActiveOffers = async function(options = {}) {
  const { companyId, categoryId, featured, limit, offset } = options;
  
  const where = {
    status: 'active'
  };
  
  // Dodaj warunek na datę zakończenia
  const now = new Date();
  where[require('sequelize').Op.or] = [
    { endDate: null },
    { endDate: { [require('sequelize').Op.gt]: now } }
  ];
  
  if (companyId) where.companyId = companyId;
  if (categoryId) where.categoryId = categoryId;
  if (featured !== undefined) where.featured = featured;
  
  return Offer.findAndCountAll({
    where,
    include: [
      {
        model: require('./Company'),
        as: 'company',
        attributes: ['id', 'name', 'slug', 'logo'],
        where: { status: 'active' }
      },
      {
        model: require('./Category'),
        as: 'category',
        attributes: ['id', 'name', 'slug'],
        required: false
      }
    ],
    order: [
      ['featured', 'DESC'],
      ['publishedAt', 'DESC'],
      ['createdAt', 'DESC']
    ],
    limit,
    offset
  });
};

Offer.getFeaturedOffers = async function(limit = 6) {
  return Offer.findAll({
    where: {
      status: 'active',
      featured: true
    },
    include: [
      {
        model: require('./Company'),
        as: 'company',
        attributes: ['id', 'name', 'slug', 'logo'],
        where: { status: 'active' }
      }
    ],
    order: [['publishedAt', 'DESC']],
    limit
  });
};

Offer.expireOldOffers = async function() {
  const now = new Date();
  return Offer.update(
    { status: 'expired' },
    {
      where: {
        status: 'active',
        endDate: {
          [require('sequelize').Op.lt]: now
        }
      }
    }
  );
};

module.exports = Offer;
