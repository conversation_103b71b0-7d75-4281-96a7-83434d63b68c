const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Coupon = sequelize.define('Coupon', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: true,
      len: [3, 50],
      isAlphanumeric: true
    }
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [5, 255]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  companyId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  
  // Typ i wartość rabatu
  discountType: {
    type: DataTypes.ENUM('percentage', 'fixed_amount', 'free_shipping'),
    allowNull: false,
    defaultValue: 'percentage'
  },
  discountValue: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0,
      customValidator(value) {
        if (this.discountType === 'percentage' && value > 100) {
          throw new Error('Rabat procentowy nie może być większy niż 100%');
        }
        if (this.discountType === 'fixed_amount' && value <= 0) {
          throw new Error('Kwota rabatu musi być większa niż 0');
        }
      }
    }
  },
  
  // Warunki użycia
  minOrderValue: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  maxUsage: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1
    }
  },
  maxUsagePerUser: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 1,
    validate: {
      min: 1
    }
  },
  currentUsage: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  
  // Daty ważności
  startDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  endDate: {
    type: DataTypes.DATE,
    allowNull: true,
    validate: {
      isAfterStartDate(value) {
        if (value && this.startDate && value <= this.startDate) {
          throw new Error('Data zakończenia musi być późniejsza niż data rozpoczęcia');
        }
      }
    }
  },
  
  // Status
  status: {
    type: DataTypes.ENUM('draft', 'active', 'expired', 'suspended'),
    defaultValue: 'draft'
  },
  
  // Statystyki
  views: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  clicks: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  
  // Daty publikacji
  publishedAt: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'coupons',
  timestamps: true,
  hooks: {
    beforeValidate: (coupon) => {
      // Konwertuj kod na wielkie litery
      if (coupon.code) {
        coupon.code = coupon.code.toUpperCase();
      }
    },
    beforeCreate: (coupon) => {
      if (coupon.status === 'active' && !coupon.publishedAt) {
        coupon.publishedAt = new Date();
      }
    },
    beforeUpdate: (coupon) => {
      if (coupon.changed('status') && coupon.status === 'active' && !coupon.publishedAt) {
        coupon.publishedAt = new Date();
      }
      
      // Sprawdź czy kupon wygasł
      if (coupon.endDate && coupon.endDate < new Date() && coupon.status === 'active') {
        coupon.status = 'expired';
      }
      
      // Sprawdź czy osiągnięto limit użyć
      if (coupon.maxUsage && coupon.currentUsage >= coupon.maxUsage && coupon.status === 'active') {
        coupon.status = 'expired';
      }
    }
  }
});

// Metody instancji
Coupon.prototype.incrementViews = async function() {
  return this.increment('views');
};

Coupon.prototype.incrementClicks = async function() {
  return this.increment('clicks');
};

Coupon.prototype.incrementUsage = async function() {
  if (this.maxUsage && this.currentUsage >= this.maxUsage) {
    throw new Error('Osiągnięto maksymalną liczbę wykorzystań kuponu');
  }
  
  const result = await this.increment('currentUsage');
  
  // Sprawdź czy kupon powinien zostać wygaszony
  if (this.maxUsage && this.currentUsage + 1 >= this.maxUsage) {
    await this.update({ status: 'expired' });
  }
  
  return result;
};

Coupon.prototype.isActive = function() {
  const now = new Date();
  return this.status === 'active' && 
         (!this.startDate || this.startDate <= now) &&
         (!this.endDate || this.endDate > now) &&
         (!this.maxUsage || this.currentUsage < this.maxUsage);
};

Coupon.prototype.isExpired = function() {
  const now = new Date();
  return this.endDate && this.endDate < now;
};

Coupon.prototype.isUsageLimitReached = function() {
  return this.maxUsage && this.currentUsage >= this.maxUsage;
};

Coupon.prototype.getRemainingDays = function() {
  if (!this.endDate) return null;
  const now = new Date();
  const end = new Date(this.endDate);
  const diffTime = end - now;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

Coupon.prototype.getRemainingUsage = function() {
  if (!this.maxUsage) return null;
  return this.maxUsage - this.currentUsage;
};

Coupon.prototype.getDiscountText = function() {
  switch (this.discountType) {
    case 'percentage':
      return `${this.discountValue}%`;
    case 'fixed_amount':
      return `${this.discountValue} zł`;
    case 'free_shipping':
      return 'Darmowa dostawa';
    default:
      return '';
  }
};

// Metody statyczne
Coupon.getActiveCoupons = async function(options = {}) {
  const { companyId, limit, offset } = options;
  
  const now = new Date();
  const where = {
    status: 'active'
  };
  
  // Dodaj warunki na daty
  where[require('sequelize').Op.and] = [
    {
      [require('sequelize').Op.or]: [
        { startDate: null },
        { startDate: { [require('sequelize').Op.lte]: now } }
      ]
    },
    {
      [require('sequelize').Op.or]: [
        { endDate: null },
        { endDate: { [require('sequelize').Op.gt]: now } }
      ]
    }
  ];
  
  if (companyId) where.companyId = companyId;
  
  return Coupon.findAndCountAll({
    where,
    include: [
      {
        model: require('./Company'),
        as: 'company',
        attributes: ['id', 'name', 'slug', 'logo'],
        where: { status: 'active' }
      }
    ],
    order: [
      ['publishedAt', 'DESC'],
      ['createdAt', 'DESC']
    ],
    limit,
    offset
  });
};

Coupon.findByCode = async function(code) {
  return Coupon.findOne({
    where: {
      code: code.toUpperCase(),
      status: 'active'
    },
    include: [
      {
        model: require('./Company'),
        as: 'company',
        attributes: ['id', 'name', 'slug'],
        where: { status: 'active' }
      }
    ]
  });
};

Coupon.expireOldCoupons = async function() {
  const now = new Date();
  return Coupon.update(
    { status: 'expired' },
    {
      where: {
        status: 'active',
        [require('sequelize').Op.or]: [
          {
            endDate: {
              [require('sequelize').Op.lt]: now
            }
          },
          {
            [require('sequelize').Op.and]: [
              { maxUsage: { [require('sequelize').Op.not]: null } },
              sequelize.where(
                sequelize.col('currentUsage'),
                require('sequelize').Op.gte,
                sequelize.col('maxUsage')
              )
            ]
          }
        ]
      }
    }
  );
};

module.exports = Coupon;
