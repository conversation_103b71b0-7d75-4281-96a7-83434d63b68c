const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const slugify = require('slugify');

const Company = sequelize.define('Company', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 255]
    }
  },
  slug: {
    type: DataTypes.STRING(300),
    allowNull: false,
    unique: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  categoryId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'categories',
      key: 'id'
    }
  },
  subcategoryId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'categories',
      key: 'id'
    }
  },

  // <PERSON>
  address: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  postalCode: {
    type: DataTypes.STRING(10),
    allowNull: true
  },
  city: {
    type: DataTypes.STRING(100),
    allowNull: true,
    defaultValue: 'Żyrardów'
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: true,
    validate: {
      isEmail: true
    }
  },
  website: {
    type: DataTypes.STRING(500),
    allowNull: true,
    validate: {
      isUrl: true
    }
  },

  // Social media
  facebook: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  instagram: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  twitter: {
    type: DataTypes.STRING(500),
    allowNull: true
  },

  // Godziny otwarcia (JSON)
  openingHours: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {
      monday: { closed: false, open: '09:00', close: '17:00' },
      tuesday: { closed: false, open: '09:00', close: '17:00' },
      wednesday: { closed: false, open: '09:00', close: '17:00' },
      thursday: { closed: false, open: '09:00', close: '17:00' },
      friday: { closed: false, open: '09:00', close: '17:00' },
      saturday: { closed: false, open: '09:00', close: '15:00' },
      sunday: { closed: true, open: null, close: null }
    }
  },

  // Logo i zdjęcia
  logo: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },

  // Status i pozycja
  status: {
    type: DataTypes.ENUM('active', 'pending', 'inactive', 'suspended'),
    defaultValue: 'pending'
  },
  topPosition: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 3
    }
  },
  featured: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },

  // Statystyki
  views: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  clicks: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },

  // SEO
  metaTitle: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  metaDescription: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  metaKeywords: {
    type: DataTypes.TEXT,
    allowNull: true
  },

  // Daty
  publishedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'companies',
  timestamps: true,
  hooks: {
    beforeValidate: (company) => {
      if (company.name && !company.slug) {
        company.slug = slugify(company.name, {
          lower: true,
          strict: true,
          locale: 'pl'
        });
      }
    },
    beforeCreate: (company) => {
      if (company.status === 'active' && !company.publishedAt) {
        company.publishedAt = new Date();
      }
    },
    beforeUpdate: (company) => {
      if (company.changed('status') && company.status === 'active' && !company.publishedAt) {
        company.publishedAt = new Date();
      }
    }
  }
});

// Metody instancji
Company.prototype.incrementViews = async function() {
  return this.increment('views');
};

Company.prototype.incrementClicks = async function() {
  return this.increment('clicks');
};

Company.prototype.isActive = function() {
  return this.status === 'active';
};

Company.prototype.isExpired = function() {
  return this.expiresAt && this.expiresAt < new Date();
};

Company.prototype.getFullAddress = function() {
  const parts = [];
  if (this.address) parts.push(this.address);
  if (this.postalCode && this.city) parts.push(`${this.postalCode} ${this.city}`);
  else if (this.city) parts.push(this.city);
  return parts.join(', ');
};

// Metody statyczne
Company.getActiveCompanies = async function(options = {}) {
  const { categoryId, subcategoryId, featured, topPosition, limit, offset } = options;

  const where = {
    status: 'active'
  };

  if (categoryId) where.categoryId = categoryId;
  if (subcategoryId) where.subcategoryId = subcategoryId;
  if (featured !== undefined) where.featured = featured;
  if (topPosition !== undefined) where.topPosition = topPosition;

  return Company.findAndCountAll({
    where,
    include: [
      {
        model: require('./Category'),
        as: 'category',
        attributes: ['id', 'name', 'slug']
      },
      {
        model: require('./Category'),
        as: 'subcategory',
        attributes: ['id', 'name', 'slug'],
        required: false
      }
    ],
    order: [
      ['topPosition', 'ASC NULLS LAST'],
      ['featured', 'DESC'],
      ['views', 'DESC'],
      ['createdAt', 'DESC']
    ],
    limit,
    offset
  });
};

Company.getTopCompanies = async function() {
  return Company.findAll({
    where: {
      status: 'active',
      topPosition: {
        [require('sequelize').Op.not]: null
      }
    },
    include: [
      {
        model: require('./Category'),
        as: 'category',
        attributes: ['id', 'name', 'slug']
      }
    ],
    order: [['topPosition', 'ASC']]
  });
};

// Relacje będą zdefiniowane w models/index.js
module.exports = Company;
