const { Sequelize } = require('sequelize');
require('dotenv').config();

// Konfiguracja połączenia z bazą danych MySQL
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'mysql',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    define: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      timestamps: true,
      underscored: false
    },
    dialectOptions: {
      charset: 'utf8mb4',
      supportBigNumbers: true,
      bigNumberStrings: true,
      dateStrings: true,
      typeCast: true
    },
    timezone: '+01:00' // Strefa czasowa dla Polski
  }
);

// Test połączenia z bazą danych
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('✅ Połączenie z bazą danych MySQL zostało nawiązane pomyślnie.');
    return true;
  } catch (error) {
    console.error('❌ Nie można połączyć się z bazą danych:', error.message);
    return false;
  }
};

// Synchronizacja modeli z bazą danych
const syncDatabase = async (force = false) => {
  try {
    await sequelize.sync({ force });
    console.log(`✅ Baza danych została zsynchronizowana ${force ? '(FORCE MODE)' : ''}.`);
    return true;
  } catch (error) {
    console.error('❌ Błąd podczas synchronizacji bazy danych:', error.message);
    return false;
  }
};

// Zamknięcie połączenia z bazą danych
const closeConnection = async () => {
  try {
    await sequelize.close();
    console.log('✅ Połączenie z bazą danych zostało zamknięte.');
  } catch (error) {
    console.error('❌ Błąd podczas zamykania połączenia z bazą danych:', error.message);
  }
};

module.exports = {
  sequelize,
  testConnection,
  syncDatabase,
  closeConnection
};
