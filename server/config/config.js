require('dotenv').config();

const config = {
  // Konfiguracja serwera
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || '0.0.0.0',
    nodeEnv: process.env.NODE_ENV || 'development'
  },

  // Konfiguracja bazy danych
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    name: process.env.DB_NAME || 'zyrardow_poleca_db',
    user: process.env.DB_USER || 'zyrardow_admin',
    password: process.env.DB_PASSWORD || 'ZyrardowPoleca2024!@#'
  },

  // Konfiguracja JWT
  jwt: {
    secret: process.env.JWT_SECRET || 'super_secret_jwt_key_zyrardow_poleca_2024_very_long_and_secure',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  },

  // Konfiguracja admina
  admin: {
    email: process.env.ADMIN_EMAIL || '<EMAIL>',
    password: process.env.ADMIN_PASSWORD || 'AdminZyrardow2024!'
  },

  // Konfiguracja uploadów
  upload: {
    maxSize: parseInt(process.env.UPLOAD_MAX_SIZE) || 5242880, // 5MB
    allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,gif,webp').split(','),
    destination: './server/uploads/',
    publicPath: '/uploads/'
  },

  // Konfiguracja bezpieczeństwa
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12,
    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW) || 15, // minuty
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX) || 100 // żądania na okno
  },

  // Konfiguracja CORS
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    credentials: true
  },

  // Konfiguracja SSL (dla produkcji)
  ssl: {
    certPath: process.env.SSL_CERT_PATH,
    keyPath: process.env.SSL_KEY_PATH
  },

  // Konfiguracja aplikacji
  app: {
    name: 'Żyrardów.poleca.to',
    version: '1.0.0',
    description: 'Portal lokalny Żyrardowa z panelem administracyjnym',
    contactEmail: '<EMAIL>',
    contactPhone: '570 888 999'
  },

  // Konfiguracja kategorii (domyślne)
  defaultCategories: [
    {
      name: 'Jedzenie i Gastronomia',
      slug: 'jedzenie-gastronomia',
      subcategories: ['Restauracje', 'Kawiarnie', 'Bary', 'Catering', 'Fast Food']
    },
    {
      name: 'Zdrowie i Uroda',
      slug: 'zdrowie-uroda',
      subcategories: ['Salony fryzjerskie', 'Kosmetyczki', 'Masaż', 'Fitness', 'Apteki']
    },
    {
      name: 'Zakupy i Handel',
      slug: 'zakupy-handel',
      subcategories: ['Odzież i obuwie', 'Elektronika', 'Dom i ogród', 'Spożywcze', 'Księgarnie']
    },
    {
      name: 'Usługi Biznesowe',
      slug: 'uslugi-biznesowe',
      subcategories: ['Doradztwo prawne', 'Księgowość', 'Marketing', 'IT', 'Ubezpieczenia']
    },
    {
      name: 'Motoryzacja',
      slug: 'motoryzacja',
      subcategories: ['Warsztaty', 'Myjnie', 'Części', 'Dealerzy', 'Pomoc drogowa']
    },
    {
      name: 'Edukacja i Kultura',
      slug: 'edukacja-kultura',
      subcategories: ['Szkoły', 'Kursy', 'Biblioteki', 'Muzea', 'Teatry']
    }
  ]
};

module.exports = config;
