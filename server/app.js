const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const compression = require('compression');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Import konfiguracji i middleware
const config = require('./config/config');
const { initializeDatabase, cleanupExpiredItems } = require('./models');
const { 
  helmetConfig, 
  apiLimiter, 
  sanitizeInput, 
  securityLogger, 
  corsConfig 
} = require('./middleware/security');

// Import routes
const authRoutes = require('./routes/auth');
const companiesRoutes = require('./routes/companies');
const offersRoutes = require('./routes/offers');
const couponsRoutes = require('./routes/coupons');
const categoriesRoutes = require('./routes/categories');
const publicRoutes = require('./routes/public');

// Utworzenie aplikacji Express
const app = express();

// Trust proxy (ważne dla VPS za reverse proxy)
app.set('trust proxy', 1);

// Middleware bezpieczeństwa
app.use(helmetConfig);
app.use(cors(corsConfig));

// Middleware logowania
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Middleware kompresji
app.use(compression());

// Middleware parsowania
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Middleware bezpieczeństwa
app.use(securityLogger);
app.use(sanitizeInput);

// Statyczne pliki
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use('/admin', express.static(path.join(__dirname, '../admin')));
app.use('/', express.static(path.join(__dirname, '../public')));

// Rate limiting dla API
app.use('/api', apiLimiter);

// Routes API
app.use('/api/auth', authRoutes);
app.use('/api/companies', companiesRoutes);
app.use('/api/offers', offersRoutes);
app.use('/api/coupons', couponsRoutes);
app.use('/api/categories', categoriesRoutes);
app.use('/api/public', publicRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Serwer działa poprawnie',
    timestamp: new Date().toISOString(),
    version: config.app.version,
    environment: process.env.NODE_ENV
  });
});

// Endpoint dla informacji o aplikacji
app.get('/api/info', (req, res) => {
  res.json({
    success: true,
    data: {
      name: config.app.name,
      version: config.app.version,
      description: config.app.description,
      contact: {
        email: config.app.contactEmail,
        phone: config.app.contactPhone
      }
    }
  });
});

// Obsługa routingu SPA dla panelu admin
app.get('/admin/*', (req, res) => {
  const filePath = path.join(__dirname, '../admin', req.path);
  
  // Sprawdź czy plik istnieje
  if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
    res.sendFile(filePath);
  } else {
    // Przekieruj do głównej strony admin
    res.sendFile(path.join(__dirname, '../admin/index.html'));
  }
});

// Obsługa routingu SPA dla strony głównej
app.get('*', (req, res) => {
  // Sprawdź czy to żądanie API
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({
      success: false,
      message: 'Endpoint nie został znaleziony'
    });
  }
  
  const filePath = path.join(__dirname, '../public', req.path);
  
  // Sprawdź czy plik istnieje
  if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
    res.sendFile(filePath);
  } else {
    // Przekieruj do głównej strony
    res.sendFile(path.join(__dirname, '../public/index.html'));
  }
});

// Middleware obsługi błędów
app.use((err, req, res, next) => {
  console.error('Błąd aplikacji:', err);
  
  // Błędy Multer (upload plików)
  if (err.code === 'LIMIT_FILE_SIZE') {
    return res.status(413).json({
      success: false,
      message: 'Plik jest zbyt duży',
      maxSize: config.upload.maxSize
    });
  }
  
  if (err.code === 'INVALID_FILE_TYPE') {
    return res.status(400).json({
      success: false,
      message: err.message
    });
  }
  
  // Błędy walidacji Sequelize
  if (err.name === 'SequelizeValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Błąd walidacji danych',
      errors: err.errors.map(e => ({
        field: e.path,
        message: e.message
      }))
    });
  }
  
  // Błędy unikalności Sequelize
  if (err.name === 'SequelizeUniqueConstraintError') {
    return res.status(409).json({
      success: false,
      message: 'Rekord z takimi danymi już istnieje',
      field: err.errors[0]?.path
    });
  }
  
  // Błędy CORS
  if (err.message === 'Nie dozwolone przez CORS') {
    return res.status(403).json({
      success: false,
      message: 'Żądanie zablokowane przez CORS'
    });
  }
  
  // Domyślny błąd serwera
  res.status(500).json({
    success: false,
    message: process.env.NODE_ENV === 'development' ? err.message : 'Wewnętrzny błąd serwera'
  });
});

// Funkcja uruchomienia serwera
const startServer = async () => {
  try {
    console.log('🚀 Uruchamianie serwera Żyrardów.poleca.to...');
    
    // Inicjalizacja bazy danych
    console.log('🔄 Inicjalizacja bazy danych...');
    await initializeDatabase();
    
    // Uruchomienie serwera
    const PORT = config.server.port;
    const HOST = config.server.host;
    
    const server = app.listen(PORT, HOST, () => {
      console.log(`✅ Serwer uruchomiony na http://${HOST}:${PORT}`);
      console.log(`📊 Panel administracyjny: http://${HOST}:${PORT}/admin`);
      console.log(`🌐 Strona główna: http://${HOST}:${PORT}`);
      console.log(`🔧 Środowisko: ${process.env.NODE_ENV}`);
    });
    
    // Graceful shutdown
    const gracefulShutdown = (signal) => {
      console.log(`\n🛑 Otrzymano sygnał ${signal}. Zamykanie serwera...`);
      
      server.close(async () => {
        console.log('🔄 Zamykanie połączeń z bazą danych...');
        
        try {
          const { closeDatabaseConnection } = require('./models');
          await closeDatabaseConnection();
          console.log('✅ Serwer został zamknięty pomyślnie.');
          process.exit(0);
        } catch (error) {
          console.error('❌ Błąd podczas zamykania serwera:', error);
          process.exit(1);
        }
      });
      
      // Wymuś zamknięcie po 10 sekundach
      setTimeout(() => {
        console.error('❌ Wymuszenie zamknięcia serwera...');
        process.exit(1);
      }, 10000);
    };
    
    // Obsługa sygnałów zamknięcia
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
    // Obsługa nieobsłużonych błędów
    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ Nieobsłużone odrzucenie Promise:', reason);
    });
    
    process.on('uncaughtException', (error) => {
      console.error('❌ Nieobsłużony wyjątek:', error);
      process.exit(1);
    });
    
    // Cron job do czyszczenia wygasłych elementów (co godzinę)
    setInterval(async () => {
      try {
        await cleanupExpiredItems();
      } catch (error) {
        console.error('❌ Błąd podczas czyszczenia wygasłych elementów:', error);
      }
    }, 60 * 60 * 1000); // 1 godzina
    
    return server;
    
  } catch (error) {
    console.error('❌ Błąd podczas uruchamiania serwera:', error);
    process.exit(1);
  }
};

// Uruchom serwer tylko jeśli plik jest uruchamiany bezpośrednio
if (require.main === module) {
  startServer();
}

module.exports = { app, startServer };
