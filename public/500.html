<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Meta tagi bezpieczeństwa -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <title>500 - Błąd serwera | Żyrardów Poleca</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/error-pages.css">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
    <div class="error-container">
        <div class="error-content">
            <div class="error-logo">
                <img src="images/logo-zyrardow-poleca.png" alt="Żyrardów Poleca" class="logo">
            </div>
            
            <div class="error-code">500</div>
            
            <h1 class="error-title">Błąd serwera</h1>
            
            <p class="error-description">
                Wystąpił nieoczekiwany błąd serwera. Pracujemy nad rozwiązaniem problemu. 
                Spróbuj ponownie za kilka minut.
            </p>
            
            <div class="error-actions">
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-home"></i> Strona główna
                </a>
                <a href="javascript:location.reload()" class="btn btn-secondary">
                    <i class="fas fa-refresh"></i> Odśwież
                </a>
                <a href="/kontakt.html" class="btn btn-outline">
                    <i class="fas fa-envelope"></i> Zgłoś problem
                </a>
            </div>
            
            <div class="error-info">
                <p><small>Kod błędu: <span id="error-id"></span></small></p>
            </div>
        </div>
    </div>
    
    <script>
        // Generuj unikalny ID błędu
        document.getElementById('error-id').textContent = 
            'ERR-' + Date.now().toString(36).toUpperCase();
        
        // Logowanie błędu 500
        console.error('500 Internal Server Error:', {
            url: window.location.href,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
        });
    </script>
</body>
</html>
