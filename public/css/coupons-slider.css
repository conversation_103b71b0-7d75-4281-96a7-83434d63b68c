/* Style dla slidera kuponów */

.coupons-slider-container {
    position: relative;
    margin-top: 40px;
    padding: 0 50px;
}

.coupons-slider {
    display: flex;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    gap: 20px;
    padding: 10px 0;
}

.coupons-slider::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.coupon-card {
    flex: 0 0 calc(25% - 15px);
    min-width: 280px;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.coupon-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md);
}

.slider-controls {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
    z-index: 1;
}

.slider-arrow {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--white);
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    pointer-events: auto;
    transition: background-color 0.3s, transform 0.3s;
}

.slider-arrow:hover {
    background-color: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

.slider-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
}

.slider-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ccc;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.3s;
}

.slider-dot.active {
    background-color: var(--primary-color);
    transform: scale(1.2);
}

/* Styl dla kuponów */
.coupon-header {
    position: relative;
    padding: 15px;
    background-color: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.coupon-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.coupon-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.coupon-discount {
    background-color: var(--primary-color);
    color: white;
    font-weight: 700;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 1rem;
}

.coupon-content {
    padding: 20px;
}

.coupon-content h3 {
    margin-bottom: 10px;
    color: var(--text-dark);
    font-size: 1.1rem;
}

.coupon-title {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.coupon-description {
    color: var(--text-medium);
    margin-bottom: 15px;
    font-size: 0.9rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.coupon-validity {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.coupon-validity i {
    margin-right: 5px;
    color: var(--primary-color);
}

.coupon-code-container {
    position: relative;
}

.coupon-code-btn {
    width: 100%;
    padding: 10px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
}

.coupon-code-btn:hover {
    background-color: #e55c00;
}

.coupon-code {
    display: none;
    background-color: #f8f8f8;
    padding: 10px;
    border-radius: var(--border-radius-sm);
    border: 2px dashed var(--primary-color);
    text-align: center;
    font-weight: 600;
    margin-top: 10px;
    color: var(--primary-color);
}

.coupon-copy-btn {
    display: block;
    width: 100%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: 8px 15px;
    margin-top: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
}

.coupon-copy-btn:hover {
    background-color: #e55c00;
}

.coupon-copy-btn:disabled {
    background-color: #4CAF50;
    cursor: default;
}

.coupon-code-message {
    background-color: #4CAF50;
    color: white;
    padding: 5px 10px;
    border-radius: var(--border-radius-sm);
    text-align: center;
    margin-top: 10px;
    font-size: 0.9rem;
}

@media (max-width: 1200px) {
    .coupon-card {
        flex: 0 0 calc(33.333% - 15px);
    }
}

@media (max-width: 992px) {
    .coupon-card {
        flex: 0 0 calc(50% - 15px);
    }
}

@media (max-width: 576px) {
    .coupon-card {
        flex: 0 0 100%;
    }
    
    .coupons-slider-container {
        padding: 0 20px;
    }
}
