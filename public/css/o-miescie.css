/* Style dla strony O mi<PERSON> */

/* Hero section */
.page-hero {
    height: 500px;
    background-size: cover;
    background-position: center;
    position: relative;
    margin-top: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.5) 0%, rgba(0,0,0,0.7) 100%);
}

.page-hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.page-hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: var(--white);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.page-hero-content .lead {
    font-size: 1.5rem;
    margin-bottom: 0;
    color: var(--white);
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Breadcrumbs */
.breadcrumbs {
    background-color: var(--light-gray);
    padding: 15px 0;
    border-bottom: 1px solid var(--medium-gray);
}

.breadcrumbs-list {
    display: flex;
    list-style: none;
    flex-wrap: wrap;
}

.breadcrumbs-list li {
    display: flex;
    align-items: center;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.breadcrumbs-list li:not(:last-child)::after {
    content: '/';
    margin: 0 10px;
    color: var(--text-light);
}

.breadcrumbs-list a {
    color: var(--text-medium);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumbs-list a:hover {
    color: var(--primary-color);
}

/* About City Section */
.about-city {
    padding: 80px 0;
}

.about-grid {
    display: grid;
    grid-template-columns: 3fr 2fr;
    gap: 50px;
    margin-top: 40px;
    align-items: center;
}

.about-content .lead {
    font-size: 1.2rem;
    color: var(--text-dark);
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.about-image {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.about-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.5s ease;
}

.about-image:hover img {
    transform: scale(1.05);
}

/* City Stats */
.city-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 60px;
}

.stat-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 30px;
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 70px;
    height: 70px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.stat-icon i {
    font-size: 28px;
    color: var(--primary-color);
}

.stat-content h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--text-medium);
}

.stat-content p {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: var(--text-dark);
}

.stat-info {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* City Features */
.city-features {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.feature-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 30px;
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.feature-icon i {
    font-size: 24px;
    color: var(--primary-color);
}

.feature-content {
    flex: 1;
}

.feature-content h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.feature-content p {
    color: var(--text-medium);
    margin-bottom: 0;
    line-height: 1.6;
}

/* City Map */
.city-map {
    padding: 80px 0;
}

.map-container {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    margin-top: 40px;
}

.map-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.map-info-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.map-info-icon {
    width: 50px;
    height: 50px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.map-info-icon i {
    font-size: 20px;
    color: var(--primary-color);
}

.map-info-content h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.map-info-content p {
    color: var(--text-medium);
    margin-bottom: 0;
    line-height: 1.6;
}

/* Facebook Promo */
.facebook-promo {
    padding: 80px 0;
    background-color: #3b5998;
    color: var(--white);
}

.facebook-promo-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.facebook-icon {
    width: 80px;
    height: 80px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.facebook-icon i {
    font-size: 40px;
    color: var(--white);
}

.facebook-promo h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--white);
}

.facebook-promo p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: var(--white);
    opacity: 0.9;
}

.facebook-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.btn-facebook {
    background-color: var(--white);
    color: #3b5998;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.btn-facebook:hover {
    background-color: #f0f2f5;
    color: #3b5998;
}

/* City SEO */
.city-seo {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.city-seo-content {
    max-width: 900px;
    margin: 0 auto;
}

.city-seo-content h2 {
    font-size: 2rem;
    margin-bottom: 30px;
    color: var(--text-dark);
    text-align: center;
}

.city-seo-content p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.8;
}

/* Responsive */
@media (max-width: 1200px) {
    .about-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .city-stats,
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .map-info {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .page-hero {
        height: 400px;
    }
    
    .page-hero-content h1 {
        font-size: 2.5rem;
    }
    
    .page-hero-content .lead {
        font-size: 1.2rem;
    }
    
    .city-stats,
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .facebook-promo h2 {
        font-size: 2rem;
    }
    
    .facebook-buttons {
        flex-direction: column;
    }
}
