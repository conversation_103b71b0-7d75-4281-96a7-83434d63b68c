/* Style dla stron szczegółowych atrakcji */

/* Hero section */
.page-hero {
    height: 500px;
    background-size: cover;
    background-position: center;
    position: relative;
    margin-top: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
}

.page-hero-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
}

.page-hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.5) 0%, rgba(0,0,0,0.7) 100%);
}

.page-hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.page-hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: var(--white);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.page-hero-content p {
    font-size: 1.5rem;
    margin-bottom: 0;
    color: var(--white);
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Attraction Details */
.attraction-details {
    padding: 80px 0;
}

.attraction-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 50px;
}

/* Attraction Description */
.attraction-description {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 40px;
}

.attraction-description h2 {
    font-size: 2rem;
    margin-bottom: 25px;
    color: var(--text-dark);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 15px;
}

.attraction-description h3 {
    font-size: 1.5rem;
    margin: 30px 0 20px;
    color: var(--text-dark);
}

.attraction-description p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.8;
}

.attraction-description ul,
.attraction-description ol {
    margin-bottom: 25px;
    padding-left: 20px;
}

.attraction-description li {
    color: var(--text-medium);
    margin-bottom: 10px;
    line-height: 1.6;
}

.attraction-description strong {
    color: var(--text-dark);
    font-weight: 600;
}

/* Attraction Sidebar */
.attraction-sidebar {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.attraction-info-box {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 30px;
}

.attraction-info-box h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: var(--text-dark);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.attraction-info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.attraction-info-list li {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 15px;
    color: var(--text-medium);
}

.attraction-info-list li:last-child {
    margin-bottom: 0;
}

.attraction-info-list li i {
    color: var(--primary-color);
    margin-top: 4px;
}

.attraction-info-list li strong {
    color: var(--text-dark);
    font-weight: 600;
    margin-right: 5px;
}

/* Attraction Gallery */
.attraction-gallery {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 30px;
}

.attraction-gallery h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: var(--text-dark);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.gallery-item {
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    cursor: pointer;
}

.gallery-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

/* Attraction Map */
.attraction-map {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 30px;
}

.attraction-map h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: var(--text-dark);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.map-container {
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.map-container iframe {
    display: block;
}

/* Related Attractions */
.related-attractions {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.related-attractions-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.related-attraction-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.related-attraction-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.related-attraction-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.related-attraction-card:hover img {
    transform: scale(1.1);
}

.related-attraction-content {
    padding: 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.related-attraction-content h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.related-attraction-content p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.6;
    flex: 1;
}

/* Lightbox */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.lightbox-image {
    max-width: 100%;
    max-height: 90vh;
    border: 5px solid var(--white);
    border-radius: var(--border-radius-md);
}

.lightbox-close {
    position: absolute;
    top: -40px;
    right: 0;
    width: 30px;
    height: 30px;
    background: none;
    border: none;
    color: var(--white);
    font-size: 24px;
    cursor: pointer;
}

.lightbox-caption {
    position: absolute;
    bottom: -40px;
    left: 0;
    width: 100%;
    text-align: center;
    color: var(--white);
    font-size: 1rem;
}

/* Responsive */
@media (max-width: 1200px) {
    .attraction-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .attraction-sidebar {
        order: -1;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 992px) {
    .related-attractions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .gallery-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .page-hero {
        height: 400px;
    }
    
    .page-hero-content h1 {
        font-size: 2.5rem;
    }
    
    .page-hero-content p {
        font-size: 1.2rem;
    }
    
    .attraction-description {
        padding: 25px;
    }
    
    .attraction-description h2 {
        font-size: 1.8rem;
    }
    
    .related-attractions-grid {
        grid-template-columns: 1fr;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .gallery-grid {
        grid-template-columns: 1fr;
    }
}
