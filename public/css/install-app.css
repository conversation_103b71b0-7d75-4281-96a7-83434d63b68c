/* Style dla przycisku instalacji aplikacji */

.install-app-button {
    display: none; /* <PERSON><PERSON><PERSON><PERSON><PERSON> uk<PERSON>, pokazywany przez JavaScript */
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: 8px 15px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
    margin-left: 15px;
}

.install-app-button:hover {
    background-color: var(--primary-color-dark);
}

.install-app-button i {
    margin-right: 5px;
}

/* Style dla lightboxa z instrukcjami instalacji */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    background-color: white;
    border-radius: var(--border-radius-md);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.lightbox-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-dark);
    z-index: 1;
}

.lightbox-header {
    padding: 20px;
    border-bottom: 1px solid var(--light-gray);
}

.lightbox-header h3 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-dark);
}

.lightbox-body {
    padding: 20px;
}

/* Style dla instrukcji instalacji */
.install-instructions {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.install-platform {
    flex: 1;
    min-width: 300px;
}

.install-platform h4 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.install-platform ol {
    padding-left: 20px;
    margin-bottom: 20px;
}

.install-platform li {
    margin-bottom: 10px;
    color: var(--text-medium);
}

.install-image {
    border: 1px solid var(--light-gray);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.install-image img {
    width: 100%;
    height: auto;
    display: block;
}

@media (max-width: 768px) {
    .install-app-button {
        padding: 6px 10px;
        font-size: 0.8rem;
    }
    
    .install-app-button i {
        margin-right: 0;
    }
    
    .install-app-button span {
        display: none;
    }
}
