/* Style dla sekc<PERSON> wydarzeń i statystyk */

/* Sek<PERSON>ja nadchodzących wydarzeń */
.upcoming-events {
    padding: 60px 0;
    background-color: var(--white);
}

.events-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.event-card {
    display: flex;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.event-date {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    padding: 15px;
    background-color: var(--primary-color);
    color: var(--white);
    text-align: center;
}

.event-day {
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1;
}

.event-month {
    font-size: 1rem;
    text-transform: uppercase;
    margin-top: 5px;
}

.event-content {
    padding: 20px;
    flex-grow: 1;
}

.event-content h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.event-location {
    font-size: 0.9rem;
    color: var(--text-medium);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.event-location i {
    color: var(--primary-color);
    margin-right: 5px;
}

.event-description {
    font-size: 0.9rem;
    color: var(--text-medium);
    margin-bottom: 15px;
    line-height: 1.5;
}

/* Sekcja statystyk miasta */
.city-stats {
    padding: 80px 0;
    background-color: var(--primary-color);
    color: var(--white);
}

.city-stats .section-header h2,
.city-stats .section-subtitle {
    color: var(--white);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 50px;
}

.stat-item {
    text-align: center;
    padding: 30px 20px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-md);
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-10px);
    background-color: rgba(255, 255, 255, 0.2);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--white);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--white);
}

.stat-label {
    font-size: 1.1rem;
    color: var(--white);
    opacity: 0.9;
}

/* Sekcja newslettera */
.newsletter-section {
    padding: 60px 0;
    background-color: var(--light-gray);
}

.newsletter-container {
    display: flex;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.newsletter-content {
    flex: 1;
    padding: 40px;
}

.newsletter-content h2 {
    font-size: 2rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.newsletter-content p {
    font-size: 1rem;
    color: var(--text-medium);
    margin-bottom: 25px;
    line-height: 1.6;
}

.newsletter-form .form-group {
    display: flex;
    margin-bottom: 15px;
}

.newsletter-form input[type="email"] {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 1rem;
    margin-right: 10px;
}

.newsletter-form .form-check {
    display: flex;
    align-items: flex-start;
    font-size: 0.9rem;
    color: var(--text-medium);
}

.newsletter-form .form-check input {
    margin-right: 10px;
    margin-top: 3px;
}

.newsletter-image {
    flex: 1;
    min-height: 400px;
}

.newsletter-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Animacja licznika */
@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-number.animate {
    animation: countUp 1s ease-out forwards;
}

/* Responsywność */
@media (max-width: 1200px) {
    .events-grid,
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .newsletter-container {
        flex-direction: column;
    }
    
    .newsletter-image {
        min-height: 300px;
    }
}

@media (max-width: 768px) {
    .events-grid,
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .event-card {
        max-width: 500px;
        margin: 0 auto;
    }
    
    .stat-item {
        max-width: 300px;
        margin: 0 auto;
    }
}

@media (max-width: 576px) {
    .newsletter-content {
        padding: 30px 20px;
    }
    
    .newsletter-form .form-group {
        flex-direction: column;
    }
    
    .newsletter-form input[type="email"] {
        margin-right: 0;
        margin-bottom: 10px;
    }
}
