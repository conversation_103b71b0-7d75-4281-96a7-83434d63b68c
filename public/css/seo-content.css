/**
 * SEO Content Styles - Żyrardów Poleca
 * Wersja 2.0 z Local SEO
 */

/* Stare style dla kompatybilności */
.seo-content {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.seo-content-sections {
    margin-top: 40px;
}

.seo-content-section {
    display: flex;
    align-items: center;
    margin-bottom: 60px;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.seo-content-section:last-child {
    margin-bottom: 0;
}

.seo-content-section.reverse {
    flex-direction: row-reverse;
}

.seo-content-image {
    width: 40%;
    flex-shrink: 0;
    height: 300px;
    overflow: hidden;
}

.seo-content-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: transform 0.5s;
}

.seo-content-section:hover .seo-content-image img {
    transform: scale(1.05);
}

.seo-content-text {
    width: 60%;
    padding: 40px;
}

.seo-content-text h2 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.seo-content-text p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.6;
}

.seo-content-text p:last-of-type {
    margin-bottom: 30px;
}

.seo-content-text .btn {
    margin-top: 10px;
}

/* Nowe style dla Local SEO */
.local-seo {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 80px 0;
    margin: 40px 0;
    position: relative;
    overflow: hidden;
}

.local-seo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.local-seo h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--text-dark);
    font-size: 2.5rem;
    font-weight: 700;
    position: relative;
}

.local-seo h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
}

.seo-content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
}

.seo-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 4rem;
    background: white;
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.seo-grid:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-5px);
}

.seo-grid.reverse {
    direction: rtl;
}

.seo-grid.reverse > * {
    direction: ltr;
}

.seo-text h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.seo-text h4 {
    color: var(--text-dark);
    margin: 2rem 0 1rem 0;
    font-size: 1.4rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.seo-text p {
    color: var(--text-medium);
    line-height: 1.8;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.seo-text strong {
    color: var(--primary-color);
    font-weight: 600;
}

.seo-text ul {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
}

.seo-text li {
    color: var(--text-medium);
    margin-bottom: 0.8rem;
    padding-left: 2rem;
    position: relative;
    line-height: 1.6;
    font-size: 1.05rem;
}

.seo-image {
    text-align: center;
}

.seo-image img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.seo-image img:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* Local Keywords Section */
.local-keywords {
    background: white;
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: 3rem;
    text-align: center;
}

.local-keywords h4 {
    color: var(--text-dark);
    margin-bottom: 2rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.keywords-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    max-width: 800px;
    margin: 0 auto;
}

.keyword-tag {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition);
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.keyword-tag:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.cta-section h3 {
    margin-bottom: 1rem;
    font-size: 1.8rem;
    font-weight: 600;
}

.cta-section p {
    margin-bottom: 2rem;
    font-size: 1.1rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-buttons .btn {
    min-width: 150px;
}

.cta-buttons .btn-primary {
    background: white;
    color: var(--primary-color);
    border: 2px solid white;
}

.cta-buttons .btn-primary:hover {
    background: transparent;
    color: white;
    border-color: white;
}

.cta-buttons .btn-outline {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.cta-buttons .btn-outline:hover {
    background: white;
    color: var(--primary-color);
}

/* Responsywność */
@media (max-width: 992px) {
    .seo-content-section,
    .seo-content-section.reverse {
        flex-direction: column;
    }

    .seo-content-image,
    .seo-content-text {
        width: 100%;
    }

    .seo-content-image {
        height: 300px;
    }
}

@media (max-width: 768px) {
    .local-seo {
        padding: 60px 0;
        margin: 20px 0;
    }

    .local-seo h2 {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .seo-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        margin-bottom: 3rem;
        padding: 2rem;
    }

    .seo-text h3 {
        font-size: 1.5rem;
    }

    .seo-text h4 {
        font-size: 1.2rem;
    }

    .seo-text p {
        font-size: 1rem;
    }

    .local-keywords {
        padding: 2rem;
    }

    .keywords-grid {
        gap: 0.5rem;
    }

    .keyword-tag {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    .cta-section {
        padding: 2rem;
    }

    .cta-section h3 {
        font-size: 1.5rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons .btn {
        width: 100%;
        max-width: 250px;
    }
}
