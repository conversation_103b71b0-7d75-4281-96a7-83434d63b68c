/* ===== WIDGET POGODY ===== */
.weather-widget {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    padding: 20px 0;
    margin: 20px 0;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.weather-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="70" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    pointer-events: none;
}

.weather-content {
    position: relative;
    z-index: 1;
}

.weather-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.weather-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.weather-title i {
    font-size: 1.4rem;
    color: #ffeaa7;
}

.weather-location {
    font-size: 0.9rem;
    opacity: 0.9;
    display: flex;
    align-items: center;
    gap: 5px;
}

.weather-forecast {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.weather-day {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.weather-day:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.weather-day.today {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.day-name {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 10px;
    opacity: 0.9;
}

.weather-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto 10px auto;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.weather-temp {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.weather-temp-range {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-bottom: 8px;
}

.weather-description {
    font-size: 0.8rem;
    opacity: 0.9;
    text-transform: capitalize;
    margin-bottom: 10px;
}

.weather-details {
    display: flex;
    justify-content: space-around;
    font-size: 0.7rem;
    opacity: 0.8;
}

.weather-detail {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.weather-detail i {
    font-size: 0.8rem;
    margin-bottom: 2px;
}

.weather-loading {
    text-align: center;
    padding: 40px 20px;
    color: rgba(255, 255, 255, 0.8);
}

.weather-loading i {
    font-size: 2rem;
    margin-bottom: 10px;
    animation: spin 2s linear infinite;
}

.weather-error {
    text-align: center;
    padding: 20px;
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.3);
    border-radius: 8px;
    color: #e74c3c;
}

.weather-error i {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.weather-updated {
    text-align: center;
    font-size: 0.7rem;
    opacity: 0.7;
    margin-top: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsywność */
@media (max-width: 768px) {
    .weather-widget {
        margin: 15px 0;
        padding: 15px 0;
    }
    
    .weather-forecast {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .weather-day {
        padding: 15px;
    }
    
    .weather-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        margin-bottom: 15px;
    }
    
    .weather-temp {
        font-size: 1.5rem;
    }
    
    .weather-icon {
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 480px) {
    .weather-forecast {
        gap: 10px;
    }
    
    .weather-day {
        padding: 12px;
    }
    
    .weather-details {
        flex-direction: column;
        gap: 5px;
    }
    
    .weather-detail {
        flex-direction: row;
        justify-content: center;
        gap: 5px;
    }
}

/* Ikony pogody */
.weather-icon[data-icon="01d"], .weather-icon[data-icon="01n"] {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffeaa7"><circle cx="12" cy="12" r="5"/><path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/></svg>');
}

.weather-icon[data-icon="02d"], .weather-icon[data-icon="02n"],
.weather-icon[data-icon="03d"], .weather-icon[data-icon="03n"],
.weather-icon[data-icon="04d"], .weather-icon[data-icon="04n"] {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"/></svg>');
}

.weather-icon[data-icon="09d"], .weather-icon[data-icon="09n"],
.weather-icon[data-icon="10d"], .weather-icon[data-icon="10n"] {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"/><path d="M8 19v2M8 13v2M16 19v2M16 13v2M12 21v2M12 15v2" stroke="%23ffffff" stroke-width="2"/></svg>');
}

.weather-icon[data-icon="11d"], .weather-icon[data-icon="11n"] {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"/><path d="M13 16l-4 6 6-6-4-6z" fill="%23ffeaa7"/></svg>');
}

.weather-icon[data-icon="13d"], .weather-icon[data-icon="13n"] {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"/><circle cx="8" cy="16" r="1"/><circle cx="12" cy="18" r="1"/><circle cx="16" cy="16" r="1"/><circle cx="10" cy="20" r="1"/><circle cx="14" cy="20" r="1"/></svg>');
}

.weather-icon[data-icon="50d"], .weather-icon[data-icon="50n"] {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M3 15h18M3 9h18M3 21h18M3 3h18" stroke="%23ffffff" stroke-width="2" opacity="0.6"/></svg>');
}
