/**
 * Aplik<PERSON><PERSON> kole<PERSON>ć sekcji na stronie głównej
 */

class SectionOrderApplier {
    constructor() {
        this.sectionSelectors = {
            'hero': '.hero',
            'city-info': '.city-info-section',
            'categories': '.categories-section',
            'coupons': '.featured-coupons',
            'girard': '.filip-girard-year',
            'heritage': '.heritage-monument',
            'about': '.about-section',
            'seo': '.seo-content',
            'offers': '.featured-offers',
            'news': '.news-section',
            'places': '.places-section',
            'stats': '.stats-section',
            'weather': '.weather-section'
        };

        this.init();
    }

    init() {
        // Sprawdź czy jesteśmy na stronie głównej
        if (this.isHomePage()) {
            this.applySavedOrder();
        }
    }

    isHomePage() {
        // Sprawdź czy to strona główna (index.html lub root)
        const path = window.location.pathname;
        return path === '/' ||
               path === '/index.html' ||
               path.endsWith('/') ||
               path.endsWith('/index.html') ||
               path.includes('zyrardow/') && !path.includes('admin/');
    }

    applySavedOrder() {
        try {
            const savedOrder = localStorage.getItem('section_order');
            if (!savedOrder) {
                console.log('Brak zapisanej kolejności sekcji');
                return;
            }

            const order = JSON.parse(savedOrder);
            console.log('Aplikowanie kolejności sekcji:', order);

            this.reorderSections(order);
        } catch (error) {
            console.error('Błąd aplikowania kolejności sekcji:', error);
        }
    }

    reorderSections(order) {
        const main = document.querySelector('main');
        if (!main) {
            console.warn('Nie znaleziono elementu main');
            return;
        }

        const sections = {};

        // Znajdź wszystkie sekcje na stronie (pomijając hero/slider)
        order.forEach(sectionId => {
            // Slider (hero) ma być zawsze pierwszy - nie przenosimy go
            if (sectionId === 'hero') {
                console.log('Pomijam slider - ma być zawsze pierwszy');
                return;
            }

            const selector = this.sectionSelectors[sectionId];
            if (selector) {
                const element = document.querySelector(selector);
                if (element) {
                    sections[sectionId] = element;
                    console.log(`Znaleziono sekcję ${sectionId}:`, element);
                }
            }
        });

        // Znajdź slider jako punkt odniesienia
        const heroSection = document.querySelector('.hero');
        if (!heroSection) {
            console.warn('Nie znaleziono sekcji hero');
            return;
        }

        // Przenieś sekcje w nowej kolejności (po sliderze)
        let previousElement = heroSection;

        order.forEach(sectionId => {
            // Pomijamy hero - ma być zawsze pierwszy
            if (sectionId === 'hero') return;

            if (sections[sectionId]) {
                // Wstaw sekcję po poprzednim elemencie
                if (previousElement.nextSibling) {
                    main.insertBefore(sections[sectionId], previousElement.nextSibling);
                } else {
                    main.appendChild(sections[sectionId]);
                }
                previousElement = sections[sectionId];
                console.log(`Przeniesiono sekcję ${sectionId}`);
            }
        });

        console.log('Kolejność sekcji została zastosowana (slider pozostał na pierwszej pozycji)');
    }

    findInsertionPoint() {
        // Znajdź header lub nav jako punkt odniesienia
        const selectors = [
            'header',
            'nav',
            '.header',
            '.navigation',
            '#header',
            '.hero-section',
            '.slider-section'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                console.log('Punkt wstawienia:', selector);
                return element;
            }
        }

        // Fallback - pierwszy element w body
        return document.body.firstElementChild;
    }

    // Metoda do debugowania - pokazuje wszystkie sekcje
    debugSections() {
        console.log('=== DEBUG SEKCJI ===');
        Object.entries(this.sectionSelectors).forEach(([id, selector]) => {
            const element = document.querySelector(selector);
            console.log(`${id}: ${selector} ->`, element ? 'ZNALEZIONO' : 'BRAK');
        });
    }
}

// Inicjalizacja po załadowaniu DOM
document.addEventListener('DOMContentLoaded', function() {
    // Małe opóźnienie, żeby wszystkie elementy się załadowały
    setTimeout(() => {
        window.sectionOrderApplier = new SectionOrderApplier();

        // Debug w konsoli (można usunąć w produkcji)
        if (window.location.search.includes('debug=sections')) {
            window.sectionOrderApplier.debugSections();
        }
    }, 100);
});
