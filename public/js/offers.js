/**
 * Oferty - Żyrardów Poleca
 * JavaScript dla ofert
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja filtrów ofert
    initOffersFilters();

    // Inicjalizacja wyszukiwania
    initOffersSearch();

    // Inicjalizacja lightbox dla szczegółów ofert
    initOfferDetails();

    // Inicjalizacja lightbox dla kontaktu z firmą
    initCompanyContact();
});

/**
 * Inicjalizacja filtrów ofert
 */
function initOffersFilters() {
    const categoryFilter = document.getElementById('categoryFilter');
    const sortFilter = document.getElementById('sortFilter');

    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            filterOffers();
        });
    }

    if (sortFilter) {
        sortFilter.addEventListener('change', function() {
            sortOffers();
        });
    }
}

/**
 * Inicjalizacja wyszukiwania
 */
function initOffersSearch() {
    const searchInput = document.getElementById('offersSearch');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterOffers();
        });
    }
}

/**
 * Filtrowanie ofert na podstawie kategorii i wyszukiwania
 */
function filterOffers() {
    const searchInput = document.getElementById('offersSearch');
    const categoryFilter = document.getElementById('categoryFilter');
    const offerCards = document.querySelectorAll('.offer-card');

    if (searchInput && categoryFilter && offerCards.length) {
        const searchTerm = searchInput.value.toLowerCase();
        const categoryValue = categoryFilter.value;

        offerCards.forEach(card => {
            // Pobranie danych oferty
            const offerTitle = card.querySelector('h3').textContent.toLowerCase();
            const offerPlace = card.querySelector('.offer-place').textContent.toLowerCase();
            const offerDescription = card.querySelector('p:not(.offer-place)').textContent.toLowerCase();

            // Pobranie kategorii oferty (z ikony lub atrybutu data)
            let offerCategory = '';
            const iconElement = card.querySelector('.offer-placeholder-icon i');
            if (iconElement) {
                if (iconElement.classList.contains('fa-utensils')) offerCategory = 'gastronomy';
                else if (iconElement.classList.contains('fa-spa')) offerCategory = 'beauty';
                else if (iconElement.classList.contains('fa-briefcase')) offerCategory = 'services';
                else if (iconElement.classList.contains('fa-shopping-cart')) offerCategory = 'shopping';
                else if (iconElement.classList.contains('fa-ticket-alt')) offerCategory = 'entertainment';
                else if (iconElement.classList.contains('fa-dumbbell')) offerCategory = 'sport';
            }

            // Sprawdzenie czy oferta pasuje do wyszukiwania
            const matchesSearch = searchTerm === '' ||
                                 offerTitle.includes(searchTerm) ||
                                 offerPlace.includes(searchTerm) ||
                                 offerDescription.includes(searchTerm);

            // Sprawdzenie czy oferta pasuje do wybranej kategorii
            const matchesCategory = categoryValue === '' || offerCategory.includes(categoryValue);

            // Pokazanie lub ukrycie oferty
            if (matchesSearch && matchesCategory) {
                card.style.display = '';
            } else {
                card.style.display = 'none';
            }
        });

        // Sprawdzenie czy są widoczne oferty
        const visibleOffers = document.querySelectorAll('.offer-card[style="display: ;"]');
        const noResultsMessage = document.getElementById('noResultsMessage');

        if (visibleOffers.length === 0) {
            // Jeśli nie ma widocznych ofert, pokaż komunikat
            if (!noResultsMessage) {
                const offersContainer = document.getElementById('offersContainer');
                const message = document.createElement('div');
                message.id = 'noResultsMessage';
                message.className = 'no-results-message';
                message.innerHTML = `
                    <i class="fas fa-search"></i>
                    <h3>Brak wyników</h3>
                    <p>Nie znaleziono ofert spełniających kryteria wyszukiwania.</p>
                `;
                offersContainer.parentNode.insertBefore(message, offersContainer.nextSibling);
            } else {
                noResultsMessage.style.display = '';
            }
        } else if (noResultsMessage) {
            // Jeśli są widoczne oferty, ukryj komunikat
            noResultsMessage.style.display = 'none';
        }
    }
}

/**
 * Sortowanie ofert
 */
function sortOffers() {
    const sortFilter = document.getElementById('sortFilter');
    const offersContainer = document.getElementById('offersContainer');

    if (sortFilter && offersContainer) {
        const sortValue = sortFilter.value;
        const offerCards = Array.from(document.querySelectorAll('.offer-card'));

        // Sortowanie ofert
        offerCards.sort((a, b) => {
            if (sortValue === 'discount') {
                // Sortowanie według zniżki
                const discountA = getDiscountPercentage(a);
                const discountB = getDiscountPercentage(b);
                return discountB - discountA; // Od największej do najmniejszej
            } else if (sortValue === 'price') {
                // Sortowanie według ceny
                const priceA = getNewPrice(a);
                const priceB = getNewPrice(b);
                return priceA - priceB; // Od najmniejszej do największej
            } else if (sortValue === 'expiry') {
                // Sortowanie według daty ważności
                const expiryA = getExpiryDate(a);
                const expiryB = getExpiryDate(b);
                return expiryA - expiryB; // Od najbliższej do najdalszej
            }

            return 0;
        });

        // Usunięcie wszystkich ofert z kontenera
        while (offersContainer.firstChild) {
            offersContainer.removeChild(offersContainer.firstChild);
        }

        // Dodanie posortowanych ofert do kontenera
        offerCards.forEach(card => {
            offersContainer.appendChild(card);
        });
    }
}

/**
 * Pobranie procentu zniżki z oferty
 * @param {Element} offerCard - Element karty oferty
 * @returns {number} - Procent zniżki
 */
function getDiscountPercentage(offerCard) {
    const badgeText = offerCard.querySelector('.offer-badge').textContent;
    const discountMatch = badgeText.match(/(\d+)%/);

    if (discountMatch) {
        return parseInt(discountMatch[1]);
    }

    // Jeśli nie ma procentu zniżki, spróbuj obliczyć na podstawie cen
    const oldPriceElement = offerCard.querySelector('.old-price');
    const newPriceElement = offerCard.querySelector('.new-price');

    if (oldPriceElement && newPriceElement) {
        const oldPrice = parseFloat(oldPriceElement.textContent.replace(/[^\d.,]/g, '').replace(',', '.'));
        const newPrice = parseFloat(newPriceElement.textContent.replace(/[^\d.,]/g, '').replace(',', '.'));

        if (!isNaN(oldPrice) && !isNaN(newPrice) && oldPrice > 0) {
            return Math.round((1 - newPrice / oldPrice) * 100);
        }
    }

    return 0;
}

/**
 * Pobranie nowej ceny z oferty
 * @param {Element} offerCard - Element karty oferty
 * @returns {number} - Nowa cena
 */
function getNewPrice(offerCard) {
    const newPriceElement = offerCard.querySelector('.new-price');

    if (newPriceElement) {
        const priceText = newPriceElement.textContent;
        const priceMatch = priceText.match(/(\d+(?:[.,]\d+)?)/);

        if (priceMatch) {
            return parseFloat(priceMatch[1].replace(',', '.'));
        }
    }

    return Infinity;
}

/**
 * Pobranie daty ważności z oferty
 * @param {Element} offerCard - Element karty oferty
 * @returns {Date} - Data ważności
 */
function getExpiryDate(offerCard) {
    const validityElement = offerCard.querySelector('.offer-validity');

    if (validityElement) {
        const validityText = validityElement.textContent;
        const dateMatch = validityText.match(/(\d{1,2})\.(\d{1,2})\.(\d{4})/);

        if (dateMatch) {
            const day = parseInt(dateMatch[1]);
            const month = parseInt(dateMatch[2]) - 1; // Miesiące w JS są od 0 do 11
            const year = parseInt(dateMatch[3]);

            return new Date(year, month, day);
        }
    }

    return new Date(9999, 11, 31); // Bardzo odległa data
}

/**
 * Inicjalizacja lightbox dla szczegółów ofert
 */
function initOfferDetails() {
    const offerButtons = document.querySelectorAll('.js-offer-details');
    const lightbox = document.getElementById('offerLightbox');
    const lightboxClose = document.querySelector('.lightbox-close');
    const lightboxBody = document.getElementById('offerDetails');

    if (offerButtons.length && lightbox && lightboxClose && lightboxBody) {
        // Obsługa kliknięcia w przycisk "Zobacz szczegóły"
        offerButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                // Pobranie ID oferty
                const offerId = this.getAttribute('data-offer');

                // Pobranie danych oferty
                const offerCard = this.closest('.offer-card');
                const offerTitle = offerCard.querySelector('h3').textContent;
                const offerPlace = offerCard.querySelector('.offer-place').textContent;
                const offerDescription = offerCard.querySelector('p:not(.offer-place)').textContent;
                const offerOldPrice = offerCard.querySelector('.old-price').textContent;
                const offerNewPrice = offerCard.querySelector('.new-price').textContent;
                const offerValidity = offerCard.querySelector('.offer-validity').textContent.replace('Ważne do:', '').trim();
                const offerBadge = offerCard.querySelector('.offer-badge').textContent;

                // Wygenerowanie zawartości lightbox
                lightboxBody.innerHTML = `
                    <div class="offer-details-header">
                        <div class="offer-details-logo">
                            <img src="images/business-logo${offerId.slice(-1)}.jpg" alt="${offerPlace}">
                        </div>
                        <div class="offer-details-title">
                            <h2>${offerTitle}</h2>
                            <p>${offerPlace}</p>
                        </div>
                    </div>
                    <div class="offer-details-content">
                        <p>${offerDescription}</p>
                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
                    </div>
                    <div class="offer-details-info">
                        <div class="offer-details-info-item">
                            <div class="offer-details-info-label">Cena regularna:</div>
                            <div>${offerOldPrice}</div>
                        </div>
                        <div class="offer-details-info-item">
                            <div class="offer-details-info-label">Cena promocyjna:</div>
                            <div>${offerNewPrice}</div>
                        </div>
                        <div class="offer-details-info-item">
                            <div class="offer-details-info-label">Zniżka:</div>
                            <div>${offerBadge}</div>
                        </div>
                        <div class="offer-details-info-item">
                            <div class="offer-details-info-label">Ważność:</div>
                            <div>${offerValidity}</div>
                        </div>
                    </div>
                    <div class="offer-details-actions">
                        <a href="tel:+48123456789" class="btn btn-primary">Zadzwoń i zarezerwuj</a>
                        <a href="https://maps.google.com" target="_blank" class="btn btn-secondary">Zobacz na mapie</a>
                    </div>
                `;

                // Otwarcie lightbox
                lightbox.classList.add('active');
                document.body.style.overflow = 'hidden';
            });
        });

        // Obsługa zamknięcia lightbox
        lightboxClose.addEventListener('click', function() {
            lightbox.classList.remove('active');
            document.body.style.overflow = '';
        });

        // Zamknięcie lightbox po kliknięciu poza zawartością
        lightbox.addEventListener('click', function(e) {
            if (e.target === lightbox) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        });

        // Obsługa klawisza Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && lightbox.classList.contains('active')) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }
}

/**
 * Inicjalizacja lightbox dla kontaktu z firmą
 */
function initCompanyContact() {
    const contactButtons = document.querySelectorAll('.js-contact-company');
    const lightbox = document.getElementById('contactLightbox');
    const lightboxClose = lightbox ? lightbox.querySelector('.lightbox-close') : null;
    const lightboxBody = document.getElementById('contactDetails');

    // Dane firm (w prawdziwej aplikacji byłyby pobierane z API)
    const companiesData = {
        'pizzeria-bella': {
            name: 'Pizzeria Bella',
            address: 'ul. Główna 15, 96-300 Żyrardów',
            phone: '+48 46 855 12 34',
            email: '<EMAIL>',
            website: 'www.pizzeriabella.pl',
            facebook: 'https://facebook.com/pizzeriabella',
            instagram: 'https://instagram.com/pizzeriabella',
            hours: 'Pon-Pt: 11:00-22:00, Sob-Nie: 12:00-23:00',
            description: 'Najlepsza pizza w Żyrardowie! Świeże składniki, tradycyjne przepisy i szybka dostawa.'
        },
        'salon-urody-venus': {
            name: 'Salon Urody Venus',
            address: 'ul. Piękna 8, 96-300 Żyrardów',
            phone: '+48 46 855 56 78',
            email: '<EMAIL>',
            website: 'www.salonvenus.pl',
            facebook: 'https://facebook.com/salonvenus',
            instagram: 'https://instagram.com/salonvenus',
            hours: 'Pon-Pt: 9:00-18:00, Sob: 9:00-15:00',
            description: 'Profesjonalne usługi kosmetyczne i fryzjerskie. Doświadczony zespół i najwyższa jakość.'
        },
        'fitness-club-energy': {
            name: 'Fitness Club Energy',
            address: 'ul. Sportowa 22, 96-300 Żyrardów',
            phone: '+48 46 855 90 12',
            email: '<EMAIL>',
            website: 'www.fitnessenergy.pl',
            facebook: 'https://facebook.com/fitnessenergy',
            instagram: 'https://instagram.com/fitnessenergy',
            hours: 'Pon-Pt: 6:00-22:00, Sob-Nie: 8:00-20:00',
            description: 'Nowoczesny klub fitness z pełnym wyposażeniem. Zajęcia grupowe i treningi personalne.'
        },
        'sklep-odziezowy-moda': {
            name: 'Sklep Odzieżowy Moda',
            address: 'ul. Handlowa 5, 96-300 Żyrardów',
            phone: '+48 46 855 34 56',
            email: '<EMAIL>',
            website: 'www.moda-zyrardow.pl',
            facebook: 'https://facebook.com/modazyrardow',
            instagram: 'https://instagram.com/modazyrardow',
            hours: 'Pon-Pt: 10:00-19:00, Sob: 10:00-16:00',
            description: 'Najnowsze trendy w modzie damskiej i męskiej. Szeroki wybór odzieży i akcesoriów.'
        },
        'warsztat-samochodowy-auto': {
            name: 'Warsztat Samochodowy Auto-Serwis',
            address: 'ul. Mechaniczna 12, 96-300 Żyrardów',
            phone: '+48 46 855 78 90',
            email: '<EMAIL>',
            website: 'www.autoserwis-zyrardow.pl',
            facebook: 'https://facebook.com/autoserwiszyrardow',
            hours: 'Pon-Pt: 8:00-17:00, Sob: 8:00-14:00',
            description: 'Kompleksowe usługi serwisowe dla wszystkich marek samochodów. Doświadczeni mechanicy.'
        },
        'szkola-jezykowa-poliglota': {
            name: 'Szkoła Językowa Poliglota',
            address: 'ul. Edukacyjna 3, 96-300 Żyrardów',
            phone: '+48 46 855 23 45',
            email: '<EMAIL>',
            website: 'www.poliglota-zyrardow.pl',
            facebook: 'https://facebook.com/poliglotazyrardow',
            instagram: 'https://instagram.com/poliglotazyrardow',
            hours: 'Pon-Pt: 15:00-21:00, Sob: 9:00-15:00',
            description: 'Kursy językowe dla dzieci i dorosłych. Angielski, niemiecki, francuski i inne języki.'
        }
    };

    if (contactButtons.length && lightbox && lightboxClose && lightboxBody) {
        // Obsługa kliknięcia w przycisk "Skontaktuj się z firmą"
        contactButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                // Pobranie ID firmy
                const companyId = this.getAttribute('data-company');
                const companyData = companiesData[companyId];

                if (companyData) {
                    // Wygenerowanie zawartości lightbox
                    lightboxBody.innerHTML = `
                        <div class="company-contact-header">
                            <div class="company-contact-logo">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="company-contact-title">
                                <h2>${companyData.name}</h2>
                                <p>${companyData.address}</p>
                            </div>
                        </div>
                        <div class="company-contact-content">
                            <p>${companyData.description}</p>
                        </div>
                        <div class="company-contact-info">
                            <div class="contact-info-grid">
                                <div class="contact-info-item">
                                    <div class="contact-info-icon">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    <div class="contact-info-content">
                                        <div class="contact-info-label">Telefon</div>
                                        <div class="contact-info-value">
                                            <a href="tel:${companyData.phone}">${companyData.phone}</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="contact-info-item">
                                    <div class="contact-info-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="contact-info-content">
                                        <div class="contact-info-label">Email</div>
                                        <div class="contact-info-value">
                                            <a href="mailto:${companyData.email}">${companyData.email}</a>
                                        </div>
                                    </div>
                                </div>
                                ${companyData.website ? `
                                <div class="contact-info-item">
                                    <div class="contact-info-icon">
                                        <i class="fas fa-globe"></i>
                                    </div>
                                    <div class="contact-info-content">
                                        <div class="contact-info-label">Strona internetowa</div>
                                        <div class="contact-info-value">
                                            <a href="https://${companyData.website}" target="_blank">${companyData.website}</a>
                                        </div>
                                    </div>
                                </div>
                                ` : ''}
                                <div class="contact-info-item">
                                    <div class="contact-info-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="contact-info-content">
                                        <div class="contact-info-label">Godziny otwarcia</div>
                                        <div class="contact-info-value">${companyData.hours}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="company-social-media">
                            <h3>Znajdź nas w mediach społecznościowych</h3>
                            <div class="social-media-links">
                                ${companyData.facebook ? `
                                <a href="${companyData.facebook}" target="_blank" class="social-link facebook">
                                    <i class="fab fa-facebook-f"></i>
                                    <span>Facebook</span>
                                </a>
                                ` : ''}
                                ${companyData.instagram ? `
                                <a href="${companyData.instagram}" target="_blank" class="social-link instagram">
                                    <i class="fab fa-instagram"></i>
                                    <span>Instagram</span>
                                </a>
                                ` : ''}
                            </div>
                        </div>
                        <div class="company-contact-actions">
                            <a href="tel:${companyData.phone}" class="btn btn-primary">
                                <i class="fas fa-phone"></i> Zadzwoń teraz
                            </a>
                            <a href="mailto:${companyData.email}" class="btn btn-secondary">
                                <i class="fas fa-envelope"></i> Wyślij email
                            </a>
                        </div>
                    `;

                    // Otwarcie lightbox
                    lightbox.classList.add('active');
                    document.body.style.overflow = 'hidden';
                }
            });
        });

        // Obsługa zamknięcia lightbox
        lightboxClose.addEventListener('click', function() {
            lightbox.classList.remove('active');
            document.body.style.overflow = '';
        });

        // Zamknięcie lightbox po kliknięciu poza zawartością
        lightbox.addEventListener('click', function(e) {
            if (e.target === lightbox) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        });

        // Obsługa klawisza Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && lightbox.classList.contains('active')) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }
}
