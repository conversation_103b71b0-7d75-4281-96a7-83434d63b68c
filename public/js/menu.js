// Skrypt obsługujący menu z rozwijanymi listami

document.addEventListener('DOMContentLoaded', function() {
    // Elementy menu
    const searchToggle = document.querySelector('.search-toggle');
    const searchContainer = document.querySelector('.search-container');
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navLinks = document.querySelector('.nav-links');
    const hasSubmenuItems = document.querySelectorAll('.has-submenu');
    const installAppButton = document.getElementById('installAppButton');
    
    // Obsługa wyszukiwarki
    if (searchToggle && searchContainer) {
        searchToggle.addEventListener('click', function() {
            searchContainer.classList.toggle('active');
            if (searchContainer.classList.contains('active')) {
                searchContainer.querySelector('input').focus();
            }
        });
        
        // Zamknij wyszukiwarkę po kliknięciu poza nią
        document.addEventListener('click', function(event) {
            if (!searchContainer.contains(event.target) && !searchToggle.contains(event.target)) {
                searchContainer.classList.remove('active');
            }
        });
    }
    
    // Obsługa menu mobilnego
    if (mobileMenuBtn && navLinks) {
        mobileMenuBtn.addEventListener('click', function() {
            navLinks.classList.toggle('active');
            document.body.classList.toggle('menu-open');
            
            // Animacja przycisku menu
            const spans = mobileMenuBtn.querySelectorAll('span');
            if (navLinks.classList.contains('active')) {
                spans[0].style.transform = 'translateY(9px) rotate(45deg)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'translateY(-9px) rotate(-45deg)';
            } else {
                spans[0].style.transform = 'none';
                spans[1].style.opacity = '1';
                spans[2].style.transform = 'none';
            }
        });
    }
    
    // Obsługa rozwijanych list w menu mobilnym
    if (hasSubmenuItems.length > 0) {
        hasSubmenuItems.forEach(function(item) {
            const link = item.querySelector('a');
            
            // Tylko w widoku mobilnym
            if (window.innerWidth <= 992) {
                link.addEventListener('click', function(event) {
                    event.preventDefault();
                    item.classList.toggle('active');
                });
            }
        });
        
        // Aktualizacja obsługi po zmianie rozmiaru okna
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 992) {
                hasSubmenuItems.forEach(function(item) {
                    const link = item.querySelector('a');
                    
                    // Usuń poprzednie listenery
                    const newLink = link.cloneNode(true);
                    link.parentNode.replaceChild(newLink, link);
                    
                    // Dodaj nowy listener
                    newLink.addEventListener('click', function(event) {
                        event.preventDefault();
                        item.classList.toggle('active');
                    });
                });
            } else {
                hasSubmenuItems.forEach(function(item) {
                    const link = item.querySelector('a');
                    
                    // Usuń poprzednie listenery
                    const newLink = link.cloneNode(true);
                    link.parentNode.replaceChild(newLink, link);
                    
                    // Dodaj nowy listener tylko dla href="#"
                    if (newLink.getAttribute('href') === '#') {
                        newLink.addEventListener('click', function(event) {
                            event.preventDefault();
                        });
                    }
                });
            }
        });
    }
    
    // Obsługa przycisku instalacji aplikacji
    if (installAppButton) {
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            // Zapobiegaj automatycznemu pokazywaniu promptu
            e.preventDefault();
            // Zapisz zdarzenie, aby móc je wywołać później
            deferredPrompt = e;
            // Pokaż przycisk instalacji
            installAppButton.style.display = 'flex';
            
            // Obsługa kliknięcia przycisku instalacji
            installAppButton.addEventListener('click', () => {
                // Pokaż prompt instalacji
                deferredPrompt.prompt();
                // Czekaj na wybór użytkownika
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('Użytkownik zaakceptował instalację');
                    } else {
                        console.log('Użytkownik odrzucił instalację');
                    }
                    // Wyczyść zapisane zdarzenie
                    deferredPrompt = null;
                });
            });
        });
        
        // Ukryj przycisk, jeśli aplikacja jest już zainstalowana
        window.addEventListener('appinstalled', () => {
            installAppButton.style.display = 'none';
            console.log('Aplikacja została zainstalowana');
        });
        
        // Domyślnie ukryj przycisk
        installAppButton.style.display = 'none';
    }
    
    // Obsługa aktywnej strony w menu
    const currentPath = window.location.pathname;
    const currentPage = currentPath.substring(currentPath.lastIndexOf('/') + 1);
    
    if (currentPage) {
        const navLinks = document.querySelectorAll('.nav-links a');
        
        navLinks.forEach(link => {
            const linkHref = link.getAttribute('href');
            
            if (linkHref === currentPage) {
                link.classList.add('active');
                
                // Jeśli link jest w submenu, aktywuj również rodzica
                const parentLi = link.closest('.submenu')?.parentElement;
                if (parentLi && parentLi.classList.contains('has-submenu')) {
                    parentLi.querySelector('a').classList.add('active');
                }
            }
        });
    }
});
