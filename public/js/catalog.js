/**
 * Katalog firm - Żyrardów Poleca
 * JavaScript dla katalogu firm
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja filtrów katalogu
    initCatalogFilters();
    
    // Inicjalizacja przełącznika widoku (grid/list)
    initViewToggle();
    
    // Inicjalizacja wyszukiwania
    initCatalogSearch();
});

/**
 * Inicjalizacja filtrów katalogu
 */
function initCatalogFilters() {
    const categoryFilter = document.getElementById('categoryFilter');
    const sortFilter = document.getElementById('sortFilter');
    
    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            filterCompanies();
        });
    }
    
    if (sortFilter) {
        sortFilter.addEventListener('change', function() {
            sortCompanies();
        });
    }
}

/**
 * Inicjalizacja przełącznika widoku (grid/list)
 */
function initViewToggle() {
    const viewButtons = document.querySelectorAll('.view-btn');
    const companiesContainer = document.getElementById('companiesContainer');
    
    if (viewButtons.length && companiesContainer) {
        viewButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Usunięcie klasy active ze wszystkich przycisków
                viewButtons.forEach(btn => btn.classList.remove('active'));
                
                // Dodanie klasy active do klikniętego przycisku
                this.classList.add('active');
                
                // Zmiana widoku na podstawie atrybutu data-view
                const viewType = this.getAttribute('data-view');
                
                if (viewType === 'grid') {
                    companiesContainer.classList.remove('companies-list');
                    companiesContainer.classList.add('companies-grid');
                } else if (viewType === 'list') {
                    companiesContainer.classList.remove('companies-grid');
                    companiesContainer.classList.add('companies-list');
                }
                
                // Zapisanie preferencji widoku w localStorage
                localStorage.setItem('catalogViewType', viewType);
            });
        });
        
        // Sprawdzenie zapisanej preferencji widoku
        const savedViewType = localStorage.getItem('catalogViewType');
        if (savedViewType) {
            const viewButton = document.querySelector(`.view-btn[data-view="${savedViewType}"]`);
            if (viewButton) {
                viewButton.click();
            }
        }
    }
}

/**
 * Inicjalizacja wyszukiwania
 */
function initCatalogSearch() {
    const searchInput = document.getElementById('catalogSearch');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterCompanies();
        });
    }
}

/**
 * Filtrowanie firm na podstawie kategorii i wyszukiwania
 */
function filterCompanies() {
    const searchInput = document.getElementById('catalogSearch');
    const categoryFilter = document.getElementById('categoryFilter');
    const companyCards = document.querySelectorAll('.company-card');
    
    if (searchInput && categoryFilter && companyCards.length) {
        const searchTerm = searchInput.value.toLowerCase();
        const categoryValue = categoryFilter.value;
        
        companyCards.forEach(card => {
            // Pobranie danych firmy
            const companyName = card.querySelector('.company-title h3').textContent.toLowerCase();
            const companyDescription = card.querySelector('.company-description').textContent.toLowerCase();
            const companyTags = Array.from(card.querySelectorAll('.company-tag')).map(tag => tag.textContent.toLowerCase());
            
            // Sprawdzenie czy firma pasuje do wyszukiwania
            const matchesSearch = searchTerm === '' || 
                                 companyName.includes(searchTerm) || 
                                 companyDescription.includes(searchTerm) ||
                                 companyTags.some(tag => tag.includes(searchTerm));
            
            // Sprawdzenie czy firma pasuje do wybranej kategorii
            const matchesCategory = categoryValue === '' || companyTags.some(tag => tag.includes(categoryValue));
            
            // Pokazanie lub ukrycie firmy
            if (matchesSearch && matchesCategory) {
                card.style.display = '';
            } else {
                card.style.display = 'none';
            }
        });
        
        // Sprawdzenie czy są widoczne firmy
        const visibleCompanies = document.querySelectorAll('.company-card[style="display: ;"]');
        const noResultsMessage = document.getElementById('noResultsMessage');
        
        if (visibleCompanies.length === 0) {
            // Jeśli nie ma widocznych firm, pokaż komunikat
            if (!noResultsMessage) {
                const companiesContainer = document.getElementById('companiesContainer');
                const message = document.createElement('div');
                message.id = 'noResultsMessage';
                message.className = 'no-results-message';
                message.innerHTML = `
                    <i class="fas fa-search"></i>
                    <h3>Brak wyników</h3>
                    <p>Nie znaleziono firm spełniających kryteria wyszukiwania.</p>
                `;
                companiesContainer.parentNode.insertBefore(message, companiesContainer.nextSibling);
            } else {
                noResultsMessage.style.display = '';
            }
        } else if (noResultsMessage) {
            // Jeśli są widoczne firmy, ukryj komunikat
            noResultsMessage.style.display = 'none';
        }
    }
}

/**
 * Sortowanie firm
 */
function sortCompanies() {
    const sortFilter = document.getElementById('sortFilter');
    const companiesContainer = document.getElementById('companiesContainer');
    
    if (sortFilter && companiesContainer) {
        const sortValue = sortFilter.value;
        const companyCards = Array.from(document.querySelectorAll('.company-card'));
        
        // Sortowanie firm
        companyCards.sort((a, b) => {
            if (sortValue === 'name') {
                // Sortowanie według nazwy
                const nameA = a.querySelector('.company-title h3').textContent.toLowerCase();
                const nameB = b.querySelector('.company-title h3').textContent.toLowerCase();
                return nameA.localeCompare(nameB);
            } else if (sortValue === 'rating') {
                // Sortowanie według oceny (jeśli istnieje)
                const ratingA = a.querySelector('.company-rating') ? 
                    parseFloat(a.querySelector('.company-rating').getAttribute('data-rating')) : 0;
                const ratingB = b.querySelector('.company-rating') ? 
                    parseFloat(b.querySelector('.company-rating').getAttribute('data-rating')) : 0;
                return ratingB - ratingA; // Od najwyższej do najniższej
            } else if (sortValue === 'newest') {
                // Sortowanie według daty dodania (jeśli istnieje)
                const dateA = a.getAttribute('data-date') ? new Date(a.getAttribute('data-date')) : new Date(0);
                const dateB = b.getAttribute('data-date') ? new Date(b.getAttribute('data-date')) : new Date(0);
                return dateB - dateA; // Od najnowszej do najstarszej
            }
            
            return 0;
        });
        
        // Usunięcie wszystkich firm z kontenera
        while (companiesContainer.firstChild) {
            companiesContainer.removeChild(companiesContainer.firstChild);
        }
        
        // Dodanie posortowanych firm do kontenera
        companyCards.forEach(card => {
            companiesContainer.appendChild(card);
        });
    }
}
