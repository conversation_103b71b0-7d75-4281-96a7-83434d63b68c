/**
 * Strona wiadomości - JavaScript
 * Żyrardów Poleca
 */

document.addEventListener('DOMContentLoaded', function() {
    initNewsPage();
});

// Globalne zmienne
let allNews = [];
let filteredNews = [];
let currentCategory = 'all';
let currentPage = 1;
let itemsPerPage = 9;
let currentSort = 'newest';

/**
 * Inicjalizacja strony wiadomości
 */
function initNewsPage() {
    loadNews();
    initEventListeners();
    updateStats();

    // Sprawdź czy jest parametr ID w URL
    const urlParams = new URLSearchParams(window.location.search);
    const newsId = urlParams.get('id');
    if (newsId) {
        // Opóźnij otwarcie modalu, żeby strona się załadowała
        setTimeout(() => {
            openNewsDetail(newsId);
        }, 500);
    }
}

/**
 * Ładowanie wiadomości
 */
function loadNews() {
    showLoadingState();

    // Pobierz wiadomości z localStorage
    const savedNews = localStorage.getItem('zyrardow_news');
    if (savedNews) {
        try {
            const newsData = JSON.parse(savedNews);
            allNews = newsData.filter(news => news.status === 'published');
        } catch (error) {
            console.error('Błąd podczas ładowania wiadomości:', error);
            allNews = getExampleNews();
        }
    } else {
        allNews = getExampleNews();
    }

    // Sortuj wiadomości
    sortNews();

    // Filtruj i wyświetl
    filterNews();
    hideLoadingState();
}

/**
 * Przykładowe wiadomości
 */
function getExampleNews() {
    return [
        {
            id: 1,
            title: "Nowe inwestycje w centrum Żyrardowa",
            category: "local",
            excerpt: "Miasto planuje modernizację centrum miasta z nowymi chodnikami i oświetleniem LED. Prace rozpoczną się już w przyszłym miesiącu.",
            content: "Miasto Żyrardów ogłosiło plan kompleksowej modernizacji centrum miasta. Inwestycja obejmuje wymianę chodników, instalację nowoczesnego oświetlenia LED oraz nasadzenia zieleni. Prace rozpoczną się w marcu i potrwają około 6 miesięcy. Koszt całej inwestycji wynosi 2,5 miliona złotych.",
            image: "images/news/inwestycje.jpg",
            imageAlt: "Centrum Żyrardowa",
            status: "published",
            publishDate: "2024-01-15T10:00:00",
            slug: "nowe-inwestycje-centrum-zyrardowa",
            metaDescription: "Miasto Żyrardów planuje nowe inwestycje w centrum miasta",
            tags: "żyrardów, inwestycje, centrum, modernizacja"
        },
        {
            id: 2,
            title: "Festiwal Kultury Żyrardowskiej 2024",
            category: "culture",
            excerpt: "Już w czerwcu odbędzie się coroczny Festiwal Kultury z bogatym programem artystycznym i występami lokalnych zespołów.",
            content: "Festiwal Kultury Żyrardowskiej to największe wydarzenie kulturalne w naszym mieście. W tym roku organizatorzy przygotowali wyjątkowy program z koncertami, spektaklami teatralnymi i wystawami. Wydarzenie odbędzie się w dniach 15-17 czerwca w Parku Miejskim.",
            image: null,
            imageAlt: "",
            status: "published",
            publishDate: "2024-01-10T18:00:00",
            slug: "festiwal-kultury-zyrardowskiej-2024",
            metaDescription: "Festiwal Kultury Żyrardowskiej 2024 - program i informacje",
            tags: "żyrardów, kultura, festiwal, wydarzenia"
        },
        {
            id: 3,
            title: "Nowa restauracja w centrum miasta",
            category: "business",
            excerpt: "W centrum Żyrardowa otwarto nową restaurację serwującą kuchnię regionalną. Lokal oferuje dania z lokalnych produktów.",
            content: "Restauracja 'Żyrardowskie Smaki' to nowy punkt na kulinarnej mapie miasta. Właściciele stawiają na kuchnię regionalną przygotowywaną z lokalnych produktów. Menu zmienia się sezonowo, a w ofercie znajdziemy tradycyjne polskie potrawy w nowoczesnym wydaniu.",
            image: "images/news/restauracja.jpg",
            imageAlt: "Nowa restauracja w Żyrardowie",
            status: "published",
            publishDate: "2024-01-05T12:00:00",
            slug: "nowa-restauracja-centrum-miasta",
            metaDescription: "Nowa restauracja w centrum Żyrardowa - kuchnia regionalna",
            tags: "żyrardów, restauracja, gastronomia, biznes"
        },
        {
            id: 4,
            title: "Turniej piłki nożnej młodzików",
            category: "sport",
            excerpt: "W weekend odbędzie się turniej piłki nożnej dla młodzików. Udział wezmą drużyny z całego powiatu żyrardowskiego.",
            content: "Klub Sportowy Żyrardów organizuje turniej piłki nożnej dla młodzików. Wydarzenie odbędzie się na stadionie miejskim w sobotę 20 stycznia. Udział wezmą drużyny z Żyrardowa, Mszczonowa i okolicznych miejscowości. Początek turnieju o godzinie 10:00.",
            image: "images/news/turniej.jpg",
            imageAlt: "Turniej piłki nożnej",
            status: "published",
            publishDate: "2024-01-18T14:00:00",
            slug: "turniej-pilki-noznej-mlodzikow",
            metaDescription: "Turniej piłki nożnej młodzików w Żyrardowie",
            tags: "żyrardów, sport, piłka nożna, młodzież"
        },
        {
            id: 5,
            title: "Spotkanie z przedsiębiorcami",
            category: "events",
            excerpt: "Urząd Miasta zaprasza na spotkanie z lokalnymi przedsiębiorcami. Tematem będzie rozwój gospodarczy miasta.",
            content: "Burmistrz Żyrardowa zaprasza wszystkich przedsiębiorców na spotkanie poświęcone rozwojowi gospodarczemu miasta. Podczas spotkania omówione zostaną planowane inwestycje, możliwości wsparcia dla firm oraz nowe regulacje. Spotkanie odbędzie się w Urzędzie Miasta 25 stycznia o godzinie 17:00.",
            image: null,
            imageAlt: "",
            status: "published",
            publishDate: "2024-01-20T09:00:00",
            slug: "spotkanie-z-przedsiebiorcami",
            metaDescription: "Spotkanie z przedsiębiorcami w Żyrardowie",
            tags: "żyrardów, biznes, przedsiębiorcy, urząd miasta"
        },
        {
            id: 6,
            title: "Remont ulicy Głównej",
            category: "local",
            excerpt: "Rozpoczął się remont ulicy Głównej. Prace potrwają około miesiąca i mogą powodować utrudnienia w ruchu.",
            content: "Zarząd Dróg Powiatowych rozpoczął remont ulicy Głównej na odcinku od ronda do skrzyżowania z ulicą Parkową. Prace obejmują wymianę nawierzchni, remont chodników i oznakowania. Kierowcy muszą liczyć się z utrudnieniami w ruchu. Objazd prowadzi ulicami Kolejową i Sportową.",
            image: "images/news/remont.jpg",
            imageAlt: "Remont ulicy Głównej",
            status: "published",
            publishDate: "2024-01-22T08:00:00",
            slug: "remont-ulicy-glownej",
            metaDescription: "Remont ulicy Głównej w Żyrardowie - utrudnienia w ruchu",
            tags: "żyrardów, remont, ulica główna, komunikacja"
        }
    ];
}

/**
 * Inicjalizacja event listenerów
 */
function initEventListeners() {
    // Filtry kategorii
    const categoryBtns = document.querySelectorAll('.category-btn');
    categoryBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const category = this.dataset.category;
            setActiveCategory(category);
            filterNews();
        });
    });

    // Wyszukiwarka
    const searchInput = document.getElementById('newsSearch');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterNews();
        });
    }

    // Sortowanie
    const sortSelect = document.getElementById('sortSelect');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            currentSort = this.value;
            sortNews();
            filterNews();
        });
    }

    // Newsletter
    const newsletterForm = document.getElementById('newsletterForm');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleNewsletterSubmit();
        });
    }

    // Modal
    const modal = document.getElementById('newsModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal || e.target.classList.contains('modal-close')) {
                closeModal();
            }
        });
    }
}

/**
 * Ustawienie aktywnej kategorii
 */
function setActiveCategory(category) {
    currentCategory = category;
    currentPage = 1;

    // Aktualizuj przyciski
    const categoryBtns = document.querySelectorAll('.category-btn');
    categoryBtns.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.category === category) {
            btn.classList.add('active');
        }
    });
}

/**
 * Filtrowanie wiadomości
 */
function filterNews() {
    const searchTerm = document.getElementById('newsSearch').value.toLowerCase();

    filteredNews = allNews.filter(news => {
        // Filtr kategorii
        const categoryMatch = currentCategory === 'all' || news.category === currentCategory;

        // Filtr wyszukiwania
        const searchMatch = !searchTerm ||
            news.title.toLowerCase().includes(searchTerm) ||
            news.excerpt.toLowerCase().includes(searchTerm) ||
            news.tags.toLowerCase().includes(searchTerm);

        return categoryMatch && searchMatch;
    });

    updateResultsInfo();
    renderNews();
    renderPagination();
}

/**
 * Sortowanie wiadomości
 */
function sortNews() {
    allNews.sort((a, b) => {
        switch (currentSort) {
            case 'newest':
                return new Date(b.publishDate) - new Date(a.publishDate);
            case 'oldest':
                return new Date(a.publishDate) - new Date(b.publishDate);
            case 'title':
                return a.title.localeCompare(b.title);
            default:
                return 0;
        }
    });
}

/**
 * Renderowanie wiadomości
 */
function renderNews() {
    const newsGrid = document.getElementById('newsGrid');
    const emptyState = document.getElementById('emptyState');

    if (filteredNews.length === 0) {
        newsGrid.style.display = 'none';
        emptyState.style.display = 'block';
        return;
    }

    newsGrid.style.display = 'grid';
    emptyState.style.display = 'none';

    // Paginacja
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageNews = filteredNews.slice(startIndex, endIndex);

    const newsHTML = pageNews.map(news => createNewsCardHTML(news)).join('');
    newsGrid.innerHTML = newsHTML;

    // Dodaj event listenery
    addNewsClickHandlers();
}

/**
 * Tworzenie HTML dla karty wiadomości
 */
function createNewsCardHTML(news) {
    const categoryName = getCategoryName(news.category);
    const categoryClass = `news-category-${news.category}`;
    const formattedDate = formatDate(news.publishDate);

    return `
        <article class="news-card" data-id="${news.id}">
            <div class="news-card-image ${news.image ? '' : 'no-image'}">
                ${news.image ?
                    `<img src="${news.image}" alt="${news.imageAlt || news.title}" loading="lazy">` :
                    '<i class="fas fa-newspaper"></i>'
                }
                <span class="news-category-badge ${categoryClass}">${categoryName}</span>
            </div>
            <div class="news-card-content">
                <h3 class="news-card-title">${news.title}</h3>
                <p class="news-card-excerpt">${news.excerpt}</p>
                <div class="news-card-meta">
                    <span class="news-card-date">
                        <i class="fas fa-calendar-alt"></i>
                        ${formattedDate}
                    </span>
                    <a href="#" class="news-card-read-more" data-id="${news.id}">
                        Czytaj więcej <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </article>
    `;
}

/**
 * Dodawanie obsługi kliknięć na wiadomości
 */
function addNewsClickHandlers() {
    const newsCards = document.querySelectorAll('.news-card');
    const readMoreLinks = document.querySelectorAll('.news-card-read-more');

    // Obsługa kliknięcia na kartę
    newsCards.forEach(card => {
        card.addEventListener('click', function(e) {
            if (e.target.closest('.news-card-read-more')) return;

            const newsId = this.dataset.id;
            openNewsDetail(newsId);
        });
    });

    // Obsługa kliknięcia na "Czytaj więcej"
    readMoreLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const newsId = this.dataset.id;
            openNewsDetail(newsId);
        });
    });
}

/**
 * Otwieranie szczegółów wiadomości
 */
function openNewsDetail(newsId) {
    const news = allNews.find(n => n.id == newsId);

    if (!news) {
        console.error('Nie znaleziono wiadomości o ID:', newsId);
        return;
    }

    // Przekieruj do osobnej strony HTML artykułu
    const articleUrl = `news/${news.slug}.html`;
    window.location.href = articleUrl;
}

/**
 * Wyświetlanie wiadomości w modalu
 */
function showNewsModal(news) {
    const modal = document.getElementById('newsModal');
    const modalBody = document.getElementById('newsModalBody');

    const categoryName = getCategoryName(news.category);
    const categoryClass = `news-category-${news.category}`;
    const formattedDate = formatDate(news.publishDate);

    modalBody.innerHTML = `
        <div class="news-details">
            ${news.image ? `
                <div class="news-details-image">
                    <img src="${news.image}" alt="${news.imageAlt || news.title}">
                </div>
            ` : ''}
            <div class="news-details-header">
                <span class="news-category-badge ${categoryClass}">${categoryName}</span>
                <h2>${news.title}</h2>
                <div class="news-details-meta">
                    <span class="news-details-date">
                        <i class="fas fa-calendar-alt"></i>
                        ${formattedDate}
                    </span>
                </div>
            </div>
            <div class="news-details-content">
                ${news.content.split('\n').map(p => p.trim() ? `<p>${p}</p>` : '').join('')}
            </div>
            ${news.tags ? `
                <div class="news-details-tags">
                    <strong>Tagi:</strong> ${news.tags}
                </div>
            ` : ''}
        </div>
    `;

    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

/**
 * Zamykanie modalu
 */
function closeModal() {
    const modal = document.getElementById('newsModal');
    modal.classList.remove('active');
    document.body.style.overflow = '';
}

/**
 * Aktualizacja informacji o wynikach
 */
function updateResultsInfo() {
    const resultsCount = document.getElementById('resultsCount');
    const total = filteredNews.length;

    if (total === 0) {
        resultsCount.textContent = 'Brak wyników';
    } else if (total === 1) {
        resultsCount.textContent = 'Znaleziono 1 wiadomość';
    } else {
        resultsCount.textContent = `Znaleziono ${total} wiadomości`;
    }
}

/**
 * Renderowanie paginacji
 */
function renderPagination() {
    const pagination = document.getElementById('pagination');
    const totalPages = Math.ceil(filteredNews.length / itemsPerPage);

    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHTML = '';

    // Przycisk poprzedni
    paginationHTML += `
        <button class="pagination-btn" ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">
            <i class="fas fa-chevron-left"></i>
        </button>
    `;

    // Numery stron
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += '<span class="pagination-dots">...</span>';
        }
    }

    // Przycisk następny
    paginationHTML += `
        <button class="pagination-btn" ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">
            <i class="fas fa-chevron-right"></i>
        </button>
    `;

    pagination.innerHTML = paginationHTML;
}

/**
 * Zmiana strony
 */
function changePage(page) {
    const totalPages = Math.ceil(filteredNews.length / itemsPerPage);

    if (page < 1 || page > totalPages) return;

    currentPage = page;
    renderNews();
    renderPagination();

    // Przewiń do góry
    document.querySelector('.news-content').scrollIntoView({ behavior: 'smooth' });
}

/**
 * Aktualizacja statystyk
 */
function updateStats() {
    const totalNews = document.getElementById('totalNews');
    const thisMonth = document.getElementById('thisMonth');

    if (totalNews) {
        totalNews.textContent = allNews.length;
    }

    if (thisMonth) {
        const currentDate = new Date();
        const currentMonth = currentDate.getMonth();
        const currentYear = currentDate.getFullYear();

        const monthlyCount = allNews.filter(news => {
            const newsDate = new Date(news.publishDate);
            return newsDate.getMonth() === currentMonth && newsDate.getFullYear() === currentYear;
        }).length;

        thisMonth.textContent = monthlyCount;
    }

    // Aktualizuj liczniki kategorii
    updateCategoryCounts();
}

/**
 * Aktualizacja liczników kategorii
 */
function updateCategoryCounts() {
    const categories = ['all', 'local', 'events', 'business', 'culture', 'sport'];

    categories.forEach(category => {
        const countElement = document.getElementById(`count${category.charAt(0).toUpperCase() + category.slice(1)}`);
        if (countElement) {
            if (category === 'all') {
                countElement.textContent = allNews.length;
            } else {
                const count = allNews.filter(news => news.category === category).length;
                countElement.textContent = count;
            }
        }
    });
}

/**
 * Obsługa newslettera
 */
function handleNewsletterSubmit() {
    const email = document.querySelector('#newsletterForm input[type="email"]').value;

    // Tutaj byłaby integracja z systemem newslettera
    console.log('Newsletter signup:', email);

    // Pokaż komunikat
    alert('Dziękujemy za zapisanie się do newslettera!');

    // Wyczyść formularz
    document.querySelector('#newsletterForm input[type="email"]').value = '';
}

/**
 * Resetowanie filtrów
 */
function resetFilters() {
    document.getElementById('newsSearch').value = '';
    setActiveCategory('all');
    filterNews();
}

/**
 * Pokazywanie stanu ładowania
 */
function showLoadingState() {
    const loadingState = document.getElementById('loadingState');
    const newsGrid = document.getElementById('newsGrid');

    if (loadingState) loadingState.style.display = 'block';
    if (newsGrid) newsGrid.style.display = 'none';
}

/**
 * Ukrywanie stanu ładowania
 */
function hideLoadingState() {
    const loadingState = document.getElementById('loadingState');

    if (loadingState) loadingState.style.display = 'none';
}

/**
 * Pobieranie nazwy kategorii
 */
function getCategoryName(category) {
    const categories = {
        'local': 'Lokalne',
        'events': 'Wydarzenia',
        'business': 'Biznes',
        'culture': 'Kultura',
        'sport': 'Sport'
    };
    return categories[category] || category;
}

/**
 * Formatowanie daty
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('pl-PL', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Udostępnij funkcje globalnie
window.changePage = changePage;
window.resetFilters = resetFilters;
