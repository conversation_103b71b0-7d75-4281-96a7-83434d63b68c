/**
 * Obsługa ekranu powitalnego - Żyrardów Poleca
 * JavaScript dla obsługi ekranu powitalnego w katalogu firm
 */

document.addEventListener('DOMContentLoaded', function() {
    // Pokaż ekran powitalny
    showWelcomeScreen();
});

/**
 * Pokazanie ekranu powitalnego
 */
function showWelcomeScreen() {
    const categoryContent = document.getElementById('categoryContent');
    if (!categoryContent) return;

    categoryContent.innerHTML = `
        <div class="welcome-screen">
            <div class="welcome-icon">
                <i class="fas fa-search"></i>
            </div>
            <h3>Znajdź najlepsze firmy w powiecie</h3>
            <p>Wyszukaj lub wybierz kategorię, a przedstawimy Ci TOP 3 firmy w powiecie żyrardowskim.</p>
            <div class="welcome-categories">
                <div class="welcome-category-row">
                    <a href="#" class="welcome-category-item" data-category="food" data-subcategory="restaurants">
                        <i class="fas fa-utensils"></i>
                        <span>Jedzenie i Gastronomia</span>
                        <small>Restauracje</small>
                    </a>
                    <a href="#" class="welcome-category-item" data-category="food" data-subcategory="pizzerias">
                        <i class="fas fa-pizza-slice"></i>
                        <span>Jedzenie i Gastronomia</span>
                        <small>Pizzerie</small>
                    </a>
                    <a href="#" class="welcome-category-item" data-category="health" data-subcategory="beauty-salons">
                        <i class="fas fa-cut"></i>
                        <span>Zdrowie i Uroda</span>
                        <small>Salony piękności</small>
                    </a>
                </div>
                <div class="welcome-category-row">
                    <a href="#" class="welcome-category-item" data-category="health" data-subcategory="pharmacies">
                        <i class="fas fa-pills"></i>
                        <span>Zdrowie i Uroda</span>
                        <small>Apteki</small>
                    </a>
                    <a href="#" class="welcome-category-item" data-category="automotive" data-subcategory="car-services">
                        <i class="fas fa-wrench"></i>
                        <span>Motoryzacja</span>
                        <small>Serwisy samochodowe</small>
                    </a>
                    <a href="#" class="welcome-category-item" data-category="home" data-subcategory="construction">
                        <i class="fas fa-hammer"></i>
                        <span>Dom i Ogród</span>
                        <small>Budownictwo</small>
                    </a>
                </div>
                <div class="welcome-category-row">
                    <a href="#" class="welcome-category-item" data-category="business" data-subcategory="legal">
                        <i class="fas fa-balance-scale"></i>
                        <span>Usługi Biznesowe</span>
                        <small>Usługi prawne</small>
                    </a>
                    <a href="#" class="welcome-category-item" data-category="education" data-subcategory="schools">
                        <i class="fas fa-school"></i>
                        <span>Edukacja i Nauka</span>
                        <small>Szkoły i kursy</small>
                    </a>
                    <a href="#" class="welcome-category-item" data-category="shopping" data-subcategory="clothing">
                        <i class="fas fa-tshirt"></i>
                        <span>Zakupy i Handel</span>
                        <small>Odzież i obuwie</small>
                    </a>
                </div>
            </div>
        </div>
    `;

    // Dodaj obsługę kliknięcia w podkategorię
    const welcomeCategoryItems = categoryContent.querySelectorAll('.welcome-category-item');
    welcomeCategoryItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();

            const category = this.getAttribute('data-category');
            const subcategory = this.getAttribute('data-subcategory');

            // Aktywuj kategorię w sidebarze
            const categoryItem = document.querySelector(`.category-item[data-category="${category}"]`);
            if (categoryItem) {
                // Usuń aktywne klasy ze wszystkich kategorii
                document.querySelectorAll('.category-item').forEach(item => {
                    item.classList.remove('active');
                });

                // Aktywuj wybraną kategorię
                categoryItem.classList.add('active');

                // Znajdź i aktywuj odpowiednią podkategorię
                const subcategoryLink = categoryItem.querySelector(`a[data-subcategory="${subcategory}"]`);
                if (subcategoryLink) {
                    // Usuń aktywne klasy ze wszystkich podkategorii
                    document.querySelectorAll('.subcategories-list a').forEach(link => {
                        link.classList.remove('active');
                    });

                    // Aktywuj wybraną podkategorię
                    subcategoryLink.classList.add('active');

                    // Pokaż firmy dla wybranej podkategorii
                    if (typeof showCompaniesForCategory === 'function') {
                        showCompaniesForCategory(category, subcategory);
                    }
                }
            }

            // Przewiń do sekcji kategorii
            const categoriesSection = document.getElementById('categories');
            if (categoriesSection) {
                categoriesSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}
