/**
 * Obsługa slidera ofert - Żyrardów Poleca
 * JavaScript dla obsługi slidera ofert specjalnych
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicjalizacja slidera ofert
    initOffersSlider();

    // Inicjalizacja lightboxów ofert
    initOfferLightboxes();
});

/**
 * Inicjalizacja slidera ofert
 */
function initOffersSlider() {
    // Pobierz elementy slidera
    const offersGrid = document.querySelector('.offers-grid');
    if (!offersGrid) return;

    // Sprawdź czy to sekcja featured-offers (strona główna)
    const featuredOffersSection = offersGrid.closest('.featured-offers');
    if (featuredOffersSection) {
        // Na stronie głównej nie dodajemy slidera - pokazujemy tylko 2 oferty
        console.log('Sekcja featured-offers: slider wyłączony, pokazujemy tylko 2 oferty');
        return;
    }

    // Dodaj klasę slider
    offersGrid.classList.add('offers-slider');
    offersGrid.id = 'featuredOffersSlider';

    // Pobierz rodzica
    const parent = offersGrid.parentNode;

    // Utwórz kontener slidera
    const sliderContainer = document.createElement('div');
    sliderContainer.className = 'offers-slider-container';

    // Przenieś slider do kontenera
    parent.insertBefore(sliderContainer, offersGrid);
    sliderContainer.appendChild(offersGrid);

    // Dodaj kontrolki slidera
    const sliderControls = document.createElement('div');
    sliderControls.className = 'slider-controls';
    sliderControls.innerHTML = `
        <button class="slider-arrow prev" id="offersPrev" aria-label="Poprzednia oferta">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="slider-arrow next" id="offersNext" aria-label="Następna oferta">
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    sliderContainer.appendChild(sliderControls);

    // Dodaj kropki
    const sliderDots = document.createElement('div');
    sliderDots.className = 'slider-dots';
    sliderDots.id = 'offersDots';
    sliderContainer.appendChild(sliderDots);

    // Pobierz karty ofert
    const cards = offersGrid.querySelectorAll('.offer-card');
    if (cards.length === 0) return;

    // Ustaw szerokość karty
    const cardWidth = cards[0].offsetWidth + parseInt(window.getComputedStyle(cards[0]).marginRight);

    // Dodaj kropki nawigacyjne
    createDots(sliderDots, cards.length, 'featuredOffersSlider');

    // Obsługa przycisku "Poprzedni"
    const prevButton = document.getElementById('offersPrev');
    prevButton.addEventListener('click', () => {
        offersGrid.scrollBy({
            left: -cardWidth * 4,
            behavior: 'smooth'
        });

        // Aktualizuj aktywną kropkę
        updateActiveDot(offersGrid, sliderDots, cardWidth);
    });

    // Obsługa przycisku "Następny"
    const nextButton = document.getElementById('offersNext');
    nextButton.addEventListener('click', () => {
        offersGrid.scrollBy({
            left: cardWidth * 4,
            behavior: 'smooth'
        });

        // Aktualizuj aktywną kropkę
        updateActiveDot(offersGrid, sliderDots, cardWidth);
    });

    // Obsługa przewijania
    offersGrid.addEventListener('scroll', () => {
        // Aktualizuj aktywną kropkę
        updateActiveDot(offersGrid, sliderDots, cardWidth);
    });
}

/**
 * Tworzenie kropek nawigacyjnych
 */
function createDots(dotsContainer, count, sliderId) {
    if (!dotsContainer) return;

    // Wyczyść kontener kropek
    dotsContainer.innerHTML = '';

    // Dodaj kropki
    for (let i = 0; i < Math.ceil(count / 4); i++) {
        const dot = document.createElement('button');
        dot.classList.add('slider-dot');
        dot.setAttribute('aria-label', `Przejdź do slajdu ${i + 1}`);
        dot.setAttribute('data-index', i);
        dot.setAttribute('data-slider', sliderId);

        // Obsługa kliknięcia w kropkę
        dot.addEventListener('click', () => {
            const slider = document.getElementById(sliderId);
            if (!slider) return;

            const cards = slider.querySelectorAll('.offer-card');
            if (cards.length === 0) return;

            const cardWidth = cards[0].offsetWidth + parseInt(window.getComputedStyle(cards[0]).marginRight);

            // Przewiń do wybranej karty
            slider.scrollTo({
                left: i * cardWidth * 4,
                behavior: 'smooth'
            });

            // Aktualizuj aktywną kropkę
            updateActiveDot(slider, dotsContainer, cardWidth);
        });

        // Dodaj kropkę do kontenera
        dotsContainer.appendChild(dot);
    }

    // Ustaw pierwszą kropkę jako aktywną
    if (dotsContainer.firstChild) {
        dotsContainer.firstChild.classList.add('active');
    }
}

/**
 * Aktualizacja aktywnej kropki
 */
function updateActiveDot(slider, dotsContainer, cardWidth) {
    if (!dotsContainer) return;

    // Pobierz aktualną pozycję przewijania
    const scrollPosition = slider.scrollLeft;

    // Oblicz indeks aktywnej kropki
    const activeIndex = Math.round(scrollPosition / (cardWidth * 4));

    // Pobierz wszystkie kropki
    const dots = dotsContainer.querySelectorAll('.slider-dot');

    // Usuń klasę aktywną ze wszystkich kropek
    dots.forEach(dot => dot.classList.remove('active'));

    // Dodaj klasę aktywną do aktywnej kropki
    if (dots[activeIndex]) {
        dots[activeIndex].classList.add('active');
    }
}

/**
 * Inicjalizacja lightboxów ofert
 */
function initOfferLightboxes() {
    // Pobierz wszystkie przyciski "Zobacz szczegóły"
    const detailButtons = document.querySelectorAll('.offer-card .btn');

    detailButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Pobierz dane oferty
            const offerCard = this.closest('.offer-card');
            const offerTitle = offerCard.querySelector('h3').textContent;
            const offerCompany = offerCard.querySelector('.offer-company span').textContent;
            const offerDescription = offerCard.querySelector('.offer-description').textContent;
            const offerImage = offerCard.querySelector('.offer-image img').src;
            const offerLogo = offerCard.querySelector('.offer-company img').src;
            const offerPriceOld = offerCard.querySelector('.price-old').textContent;
            const offerPriceNew = offerCard.querySelector('.price-new').textContent;

            // Utwórz lightbox
            const lightboxHTML = `
                <div id="offerLightbox" class="lightbox">
                    <div class="lightbox-content">
                        <button class="lightbox-close" aria-label="Zamknij">&times;</button>
                        <div class="offer-lightbox">
                            <div class="offer-lightbox-image">
                                <img src="${offerImage}" alt="${offerTitle}">
                            </div>
                            <div class="offer-lightbox-content">
                                <div class="offer-lightbox-header">
                                    <img src="${offerLogo}" alt="${offerCompany}" class="offer-lightbox-logo">
                                    <div>
                                        <h3>${offerTitle}</h3>
                                        <p class="offer-lightbox-company">${offerCompany}</p>
                                    </div>
                                </div>
                                <div class="offer-lightbox-description">
                                    <p>${offerDescription}</p>
                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
                                </div>
                                <div class="offer-lightbox-price">
                                    <span class="price-old">${offerPriceOld}</span>
                                    <span class="price-new">${offerPriceNew}</span>
                                </div>
                                <div class="offer-lightbox-actions">
                                    <a href="tel:+48123456789" class="btn btn-primary"><i class="fas fa-phone"></i> Zadzwoń i zamów</a>
                                    <a href="mailto:<EMAIL>" class="btn btn-outline"><i class="fas fa-envelope"></i> Wyślij zapytanie</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Dodaj lightbox do body
            document.body.insertAdjacentHTML('beforeend', lightboxHTML);

            // Pokaż lightbox
            const lightbox = document.getElementById('offerLightbox');
            lightbox.classList.add('active');

            // Obsługa zamykania lightboxa
            const closeButton = lightbox.querySelector('.lightbox-close');
            closeButton.addEventListener('click', function() {
                lightbox.classList.remove('active');
                setTimeout(() => {
                    lightbox.remove();
                }, 300);
            });

            // Zamykanie lightboxa po kliknięciu w tło
            lightbox.addEventListener('click', function(e) {
                if (e.target === lightbox) {
                    lightbox.classList.remove('active');
                    setTimeout(() => {
                        lightbox.remove();
                    }, 300);
                }
            });
        });
    });
}
