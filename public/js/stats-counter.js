/**
 * Skrypt obsługujący animację licznika statystyk
 */
document.addEventListener('DOMContentLoaded', function() {
    initStatsCounter();
});

/**
 * Inicjalizacja licznika statystyk
 */
function initStatsCounter() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    if (statNumbers.length === 0) return;
    
    // Funkcja sprawdzająca, czy element jest widoczny w oknie przeglądarki
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
    
    // Funkcja animująca licznik
    function animateCounter(el) {
        const target = parseInt(el.getAttribute('data-count'));
        const duration = 2000; // Czas trwania animacji w milisekundach
        const step = target / (duration / 16); // 60 FPS
        let current = 0;
        
        el.classList.add('animate');
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                clearInterval(timer);
                el.textContent = target;
            } else {
                el.textContent = Math.floor(current);
            }
        }, 16);
    }
    
    // Obsługa zdarzenia przewijania strony
    let animated = false;
    
    function checkScroll() {
        if (animated) return;
        
        const statsSection = document.querySelector('.city-stats');
        if (!statsSection) return;
        
        if (isElementInViewport(statsSection)) {
            statNumbers.forEach(statNumber => {
                animateCounter(statNumber);
            });
            animated = true;
            window.removeEventListener('scroll', checkScroll);
        }
    }
    
    window.addEventListener('scroll', checkScroll);
    checkScroll(); // Sprawdź przy załadowaniu strony
}
