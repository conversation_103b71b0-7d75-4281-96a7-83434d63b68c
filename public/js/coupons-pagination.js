/**
 * Pagin<PERSON>ja kuponów - Żyrardów Poleca
 * JavaScript dla obsługi paginacji kuponów
 */

document.addEventListener('DOMContentLoaded', function() {
    initCouponsPagination();
});

/**
 * Inicjalizacja paginacji kuponów
 */
function initCouponsPagination() {
    const couponsContainer = document.getElementById('couponsContainer');
    const pagination = document.querySelector('.pagination');
    
    if (!couponsContainer || !pagination) {
        console.log('Brak kontenera kuponów lub paginacji');
        return;
    }
    
    // Konfiguracja paginacji
    const itemsPerPage = 6; // 6 kuponów na stronę
    let currentPage = 1;
    
    // Pobierz wszystkie kupony
    const allCoupons = Array.from(couponsContainer.querySelectorAll('.coupon-card'));
    const totalCoupons = allCoupons.length;
    const totalPages = Math.ceil(totalCoupons / itemsPerPage);
    
    console.log(`Znaleziono ${totalCoupons} kuponów, ${totalPages} stron`);
    
    // Jeśli jest tylko jedna strona lub mniej, ukryj paginację
    if (totalPages <= 1) {
        pagination.style.display = 'none';
        console.log('Paginacja ukryta - tylko jedna strona');
        return;
    } else {
        pagination.style.display = 'flex';
    }
    
    // Generuj paginację
    generatePagination(totalPages, currentPage);
    
    // Pokaż pierwszą stronę
    showPage(currentPage, allCoupons, itemsPerPage);
    
    // Obsługa kliknięć w paginację
    pagination.addEventListener('click', function(e) {
        e.preventDefault();
        
        const target = e.target.closest('a');
        if (!target) return;
        
        if (target.classList.contains('prev') && currentPage > 1) {
            currentPage--;
        } else if (target.classList.contains('next') && currentPage < totalPages) {
            currentPage++;
        } else if (target.textContent && !isNaN(parseInt(target.textContent))) {
            currentPage = parseInt(target.textContent);
        }
        
        // Aktualizuj widok
        showPage(currentPage, allCoupons, itemsPerPage);
        updatePaginationButtons(currentPage, totalPages);
        
        // Przewiń do góry sekcji kuponów
        couponsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
    });
}

/**
 * Generowanie przycisków paginacji
 */
function generatePagination(totalPages, currentPage) {
    const pagination = document.querySelector('.pagination');
    if (!pagination) return;
    
    let paginationHTML = '';
    
    // Przycisk "Poprzednia"
    if (currentPage > 1) {
        paginationHTML += `<a href="#" class="prev"><i class="fas fa-chevron-left"></i> Poprzednia</a>`;
    }
    
    // Numery stron
    for (let i = 1; i <= totalPages; i++) {
        const activeClass = i === currentPage ? 'active' : '';
        paginationHTML += `<a href="#" class="${activeClass}">${i}</a>`;
    }
    
    // Przycisk "Następna"
    if (currentPage < totalPages) {
        paginationHTML += `<a href="#" class="next">Następna <i class="fas fa-chevron-right"></i></a>`;
    }
    
    pagination.innerHTML = paginationHTML;
}

/**
 * Pokazanie określonej strony kuponów
 */
function showPage(page, allCoupons, itemsPerPage) {
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    
    // Ukryj wszystkie kupony
    allCoupons.forEach(coupon => {
        coupon.style.display = 'none';
    });
    
    // Pokaż kupony dla aktualnej strony
    for (let i = startIndex; i < endIndex && i < allCoupons.length; i++) {
        allCoupons[i].style.display = 'block';
    }
    
    console.log(`Pokazano stronę ${page}, kupony ${startIndex + 1}-${Math.min(endIndex, allCoupons.length)}`);
}

/**
 * Aktualizacja przycisków paginacji
 */
function updatePaginationButtons(currentPage, totalPages) {
    const pagination = document.querySelector('.pagination');
    if (!pagination) return;
    
    // Usuń aktywną klasę ze wszystkich przycisków
    pagination.querySelectorAll('a').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Dodaj aktywną klasę do aktualnej strony
    const currentPageBtn = pagination.querySelector(`a:nth-child(${currentPage + (currentPage > 1 ? 1 : 0)})`);
    if (currentPageBtn && !currentPageBtn.classList.contains('prev') && !currentPageBtn.classList.contains('next')) {
        currentPageBtn.classList.add('active');
    }
    
    // Regeneruj paginację z aktualnymi przyciskami
    generatePagination(totalPages, currentPage);
}

/**
 * Aktualizacja paginacji po filtrowaniu
 */
function updatePaginationAfterFilter() {
    const couponsContainer = document.getElementById('couponsContainer');
    const pagination = document.querySelector('.pagination');
    
    if (!couponsContainer || !pagination) return;
    
    // Pobierz widoczne kupony
    const visibleCoupons = Array.from(couponsContainer.querySelectorAll('.coupon-card')).filter(coupon => {
        return coupon.style.display !== 'none';
    });
    
    const itemsPerPage = 6;
    const totalPages = Math.ceil(visibleCoupons.length / itemsPerPage);
    
    // Jeśli jest tylko jedna strona lub mniej, ukryj paginację
    if (totalPages <= 1) {
        pagination.style.display = 'none';
    } else {
        pagination.style.display = 'flex';
        generatePagination(totalPages, 1);
        showPage(1, visibleCoupons, itemsPerPage);
    }
}

// Eksportuj funkcję dla innych skryptów
window.updatePaginationAfterFilter = updatePaginationAfterFilter;
