/**
 * Obsługa ukrywania/odkrywania sekcji na stronie głównej
 */
document.addEventListener('DOMContentLoaded', function() {
    // Pobierz ustawienia z localStorage
    const siteSettings = localStorage.getItem('siteSettings');
    
    if (siteSettings) {
        const settings = JSON.parse(siteSettings);
        
        // Sprawdź, czy istnieją ustawienia dla sekcji strony głównej
        if (settings.homepage && settings.homepage.sections) {
            const sections = settings.homepage.sections;
            
            // Katalog firm
            if (sections.companies === false) {
                hideSection('companies-section');
            }
            
            // Kupony rabatowe
            if (sections.coupons === false) {
                hideSection('coupons-section');
            }
            
            // O Żyrardowie
            if (sections.about === false) {
                hideSection('about-section');
            }
            
            // Odkryj Żyrardów
            if (sections.discover === false) {
                hideSection('discover-section');
            }
            
            // Polecane oferty
            if (sections.offers === false) {
                hideSection('offers-section');
            }
            
            // Najciekawsze miejsca
            if (sections.places === false) {
                hideSection('places-section');
            }
            
            // Żyrardów w liczbach
            if (sections.stats === false) {
                hideSection('stats-section');
            }
        }
    }
    
    /**
     * Funkcja ukrywająca sekcję na stronie głównej
     * @param {string} sectionId - ID sekcji do ukrycia
     */
    function hideSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = 'none';
        }
    }
});
