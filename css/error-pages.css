/**
 * Strony błędów - Style CSS
 * Żyrardów Poleca
 */

.error-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    padding: 2rem;
    font-family: var(--font-body);
}

.error-content {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 3rem;
    text-align: center;
    box-shadow: var(--shadow-xl);
    max-width: 600px;
    width: 100%;
}

.error-logo {
    margin-bottom: 2rem;
}

.error-logo .logo {
    max-height: 80px;
    width: auto;
}

.error-code {
    font-size: 6rem;
    font-weight: 900;
    color: var(--primary-color);
    line-height: 1;
    margin-bottom: 1rem;
    font-family: var(--font-heading);
}

.error-title {
    font-size: 2rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-family: var(--font-heading);
}

.error-description {
    font-size: 1.1rem;
    color: var(--text-medium);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.error-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.error-actions .btn {
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-md);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.error-actions .btn-primary {
    background: var(--primary-color);
    color: white;
    border: 2px solid var(--primary-color);
}

.error-actions .btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
}

.error-actions .btn-secondary {
    background: var(--secondary-color);
    color: white;
    border: 2px solid var(--secondary-color);
}

.error-actions .btn-secondary:hover {
    background: var(--secondary-dark);
    border-color: var(--secondary-dark);
    transform: translateY(-2px);
}

.error-actions .btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.error-actions .btn-outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.error-suggestions {
    text-align: left;
    background: var(--light-gray);
    padding: 1.5rem;
    border-radius: var(--border-radius-md);
    margin-top: 2rem;
}

.error-suggestions h3 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.error-suggestions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.error-suggestions li {
    margin-bottom: 0.5rem;
}

.error-suggestions a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.error-suggestions a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.error-info {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--medium-gray);
}

.error-info small {
    color: var(--text-light);
}

/* Responsywność */
@media (max-width: 768px) {
    .error-container {
        padding: 1rem;
    }
    
    .error-content {
        padding: 2rem;
    }
    
    .error-code {
        font-size: 4rem;
    }
    
    .error-title {
        font-size: 1.5rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .error-actions .btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
    
    .error-suggestions {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .error-code {
        font-size: 3rem;
    }
    
    .error-title {
        font-size: 1.3rem;
    }
    
    .error-description {
        font-size: 1rem;
    }
}

/* Animacje */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.error-content {
    animation: fadeInUp 0.6s ease-out;
}

.error-code {
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.error-title {
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.error-description {
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

.error-actions {
    animation: fadeInUp 0.8s ease-out 0.8s both;
}
