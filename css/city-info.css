/* Style dla boxów informacyjnych o mieście */

.categories {
    padding-top: 80px; /* Zwiększony odstęp od góry */
}

.city-info-boxes {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* Dokładnie 3 kolumny */
    gap: 30px;
    margin: 40px 0;
}

.city-info-box {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 30px;
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
}

.city-info-box:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.city-info-icon {
    width: 80px;
    height: 80px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.city-info-icon i {
    font-size: 32px;
    color: var(--primary-color);
}

.city-info-box h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.city-info-box p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.6;
}

@media (max-width: 1200px) {
    .city-info-boxes {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .city-info-boxes {
        grid-template-columns: 1fr;
    }

    .categories {
        padding-top: 60px;
    }
}
