/* Wiadomości - Style CSS */

/* Hero Section */
.news-hero {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
    margin-top: 80px;
    display: flex;
    align-items: center;
    min-height: 400px;
}

.news-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../images/zyrardow-bg.jpg') center/cover;
    opacity: 0.1;
    z-index: 1;
}

.news-hero .container {
    position: relative;
    z-index: 2;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-content {
    text-align: center;
}

.hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin: 0 0 20px 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-content p {
    font-size: 1.3rem;
    margin: 0 0 40px 0;
    opacity: 0.9;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-top: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Main Content */
.news-main {
    padding: 60px 0;
    background-color: var(--light-gray);
}

/* Filtry */
.news-filters {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    margin-bottom: 40px;
    box-shadow: var(--shadow-sm);
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.filters-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
}

.search-box {
    position: relative;
    width: 300px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.search-box input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.1);
}

/* Category Filters */
.category-filters {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.category-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    background-color: var(--light-gray);
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    color: var(--text-medium);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    cursor: pointer;
    font-size: 1rem;
}

.category-btn:hover {
    background-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.category-btn.active {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.category-btn .count {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 700;
}

.category-btn.active .count {
    background-color: rgba(255, 255, 255, 0.3);
}

/* Content Header */
.news-content {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    box-shadow: var(--shadow-sm);
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--border-color);
}

.results-info {
    font-size: 1.1rem;
    color: var(--text-medium);
    font-weight: 600;
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-options label {
    font-weight: 600;
    color: var(--text-medium);
}

.sort-options select {
    padding: 8px 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--white);
    color: var(--text-dark);
    font-size: 1rem;
    cursor: pointer;
}

/* News Grid */
.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.news-card {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    cursor: pointer;
    border: 2px solid transparent;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.news-card-image {
    width: 100%;
    height: 220px;
    overflow: hidden;
    position: relative;
}

.news-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.news-card:hover .news-card-image img {
    transform: scale(1.05);
}

.news-card-image.no-image {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 3rem;
}

.news-category-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 6px 12px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    color: var(--white);
}

.news-category-local { background-color: #28a745; }
.news-category-events { background-color: #17a2b8; }
.news-category-business { background-color: #ffc107; color: var(--text-dark) !important; }
.news-category-culture { background-color: #6f42c1; }
.news-category-sport { background-color: #fd7e14; }

.news-card-content {
    padding: 25px;
}

.news-card-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 15px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-card-excerpt {
    color: var(--text-medium);
    line-height: 1.6;
    margin: 0 0 20px 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-light);
}

.news-card-date {
    display: flex;
    align-items: center;
    gap: 5px;
}

.news-card-read-more {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
}

.news-card-read-more:hover {
    color: var(--secondary-color);
}

/* States */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-medium);
}

.empty-icon {
    font-size: 4rem;
    color: var(--text-light);
    margin-bottom: 20px;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin: 0 0 10px 0;
    color: var(--text-dark);
}

.loading-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-medium);
}

.loading-spinner {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 40px;
}

.pagination-btn {
    padding: 10px 15px;
    border: 2px solid var(--border-color);
    background-color: var(--white);
    color: var(--text-medium);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
}

.pagination-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.pagination-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}



/* Responsive */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.2rem;
    }

    .hero-stats {
        gap: 30px;
    }

    .stat-number {
        font-size: 2rem;
    }

    .filters-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }

    .search-box {
        width: 100%;
    }

    .category-filters {
        justify-content: center;
    }

    .content-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .news-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .newsletter-content {
        flex-direction: column;
        text-align: center;
    }

    .input-group {
        flex-direction: column;
    }

    .input-group input {
        min-width: auto;
    }
}

/* Modal dla szczegółów wiadomości */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    animation: slideIn 0.3s ease;
}

.modal-content.large {
    max-width: 1000px;
}

.modal-header {
    position: sticky;
    top: 0;
    background-color: var(--white);
    padding: 20px 30px;
    border-bottom: 2px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    z-index: 10;
}

.modal-close {
    background: none;
    border: none;
    font-size: 2rem;
    color: var(--text-light);
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.modal-close:hover {
    background-color: var(--light-gray);
    color: var(--text-dark);
}

.modal-body {
    padding: 30px;
}

.news-details-image {
    width: 100%;
    height: 300px;
    overflow: hidden;
    border-radius: var(--border-radius);
    margin-bottom: 30px;
}

.news-details-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.news-details-header {
    margin-bottom: 30px;
}

.news-details-header h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 15px 0 20px 0;
    line-height: 1.3;
}

.news-details-meta {
    display: flex;
    align-items: center;
    gap: 20px;
    color: var(--text-light);
    font-size: 0.9rem;
}

.news-details-date {
    display: flex;
    align-items: center;
    gap: 5px;
}

.news-details-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-medium);
    margin-bottom: 30px;
}

.news-details-content p {
    margin-bottom: 20px;
}

.news-details-tags {
    padding-top: 20px;
    border-top: 2px solid var(--border-color);
    color: var(--text-light);
    font-size: 0.9rem;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
