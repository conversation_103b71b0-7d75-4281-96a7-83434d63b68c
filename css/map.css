/* Style dla mapy interaktywnej */

.map-button {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    z-index: 99;
    transition: transform 0.3s, background-color 0.3s;
}

.map-button:hover {
    transform: scale(1.1);
    background-color: var(--primary-color-dark);
}

.map-button i {
    font-size: 24px;
}

.map-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    z-index: 9999;
    display: none;
    opacity: 0;
    transition: opacity 0.3s;
    display: flex;
    flex-direction: column;
}

.map-container.active {
    opacity: 1;
}

.map-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background-color: var(--primary-color);
    color: white;
}

.map-header h3 {
    margin: 0;
    font-size: 1.5rem;
}

.close-map-button {
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    transition: transform 0.3s;
}

.close-map-button:hover {
    transform: scale(1.2);
}

.map-filters {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--light-gray);
    border-bottom: 1px solid var(--medium-gray);
}

.map-filter {
    margin-right: 20px;
    display: flex;
    align-items: center;
}

.map-filter label {
    margin-right: 10px;
    font-weight: 500;
}

.map-filter select {
    padding: 8px 12px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-sm);
    background-color: white;
}

.map-search {
    flex: 1;
    display: flex;
    align-items: center;
}

.map-search input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
}

.map-search button {
    padding: 10px 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
    cursor: pointer;
}

.map {
    flex: 1;
    width: 100%;
}

.map-info {
    padding: 10px 20px;
    background-color: var(--light-gray);
    border-top: 1px solid var(--medium-gray);
    font-size: 0.9rem;
    color: var(--text-medium);
}

/* Style dla okna informacyjnego na mapie */
.map-info-window {
    padding: 5px;
    max-width: 250px;
}

.info-window-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.info-window-header img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 50%;
    margin-right: 10px;
}

.info-window-header h4 {
    margin: 0;
    font-size: 1rem;
    color: var(--text-dark);
}

.info-window-content p {
    margin: 0 0 10px;
    font-size: 0.9rem;
    color: var(--text-medium);
}

.info-window-content i {
    color: var(--primary-color);
    margin-right: 5px;
}

.info-window-content .btn {
    display: inline-block;
    padding: 5px 10px;
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius-sm);
    text-decoration: none;
    font-size: 0.8rem;
    transition: background-color 0.3s;
}

.info-window-content .btn:hover {
    background-color: var(--primary-color-dark);
}

@media (max-width: 768px) {
    .map-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .map-filter {
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .map-button {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
    }
}
