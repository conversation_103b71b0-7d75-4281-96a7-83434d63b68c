/* Strona artykułu - Style CSS */

/* Article Layout */
.news-article {
    background-color: var(--light-gray);
    min-height: 100vh;
}

/* Breadcrumbs */
.breadcrumbs {
    background-color: var(--white);
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
}

.breadcrumbs-list {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
    padding: 0;
    list-style: none;
}

.breadcrumbs-list li {
    display: flex;
    align-items: center;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.breadcrumbs-list li:not(:last-child)::after {
    content: '>';
    margin-left: 10px;
    color: var(--text-light);
}

.breadcrumbs-list a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumbs-list a:hover {
    color: var(--primary-dark);
}

/* Article Content */
.article-content {
    background-color: var(--white);
    margin: 40px 0;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.article-content .container {
    max-width: 800px;
    padding: 60px 40px;
}

/* Article Header */
.article-header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 2px solid var(--border-color);
}

.article-meta {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.article-category {
    padding: 6px 12px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    color: var(--white);
}

.category-local { background-color: #28a745; }
.category-events { background-color: #17a2b8; }
.category-business { background-color: #ffc107; color: var(--text-dark) !important; }
.category-culture { background-color: #6f42c1; }
.category-sport { background-color: #fd7e14; }

.article-date {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-light);
    font-size: 0.9rem;
}

.article-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 20px 0;
    line-height: 1.3;
}

.article-excerpt {
    font-size: 1.2rem;
    color: var(--text-medium);
    line-height: 1.6;
    margin: 0;
    font-style: italic;
}

/* Article Image */
.article-image {
    margin: 40px 0;
    text-align: center;
}

.article-image img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
}

/* Article Body */
.article-body {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-dark);
    margin: 40px 0;
}

.article-body p {
    margin: 0 0 20px 0;
}

.article-body p:last-child {
    margin-bottom: 0;
}

.article-body h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 40px 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-color);
}

.article-body h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 30px 0 15px 0;
}

.article-body ul,
.article-body ol {
    margin: 20px 0;
    padding-left: 30px;
}

.article-body li {
    margin-bottom: 10px;
}

.article-body blockquote {
    background-color: var(--light-gray);
    border-left: 4px solid var(--primary-color);
    padding: 20px;
    margin: 30px 0;
    font-style: italic;
    border-radius: var(--border-radius-md);
}

/* Article Tags */
.article-tags {
    margin: 40px 0;
    padding: 20px 0;
    border-top: 1px solid var(--border-color);
}

.article-tags h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 15px 0;
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.tag {
    background-color: var(--light-gray);
    color: var(--text-medium);
    padding: 5px 12px;
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    transition: var(--transition);
}

.tag:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* Article Share */
.article-share {
    margin: 40px 0;
    padding: 20px 0;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.article-share h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 20px 0;
}

.share-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.share-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    font-size: 1.2rem;
    transition: var(--transition);
}

.share-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.share-btn.facebook {
    background-color: #1877f2;
}

.share-btn.twitter {
    background-color: #1da1f2;
}

.share-btn.email {
    background-color: var(--text-medium);
}

/* Article Navigation */
.article-navigation {
    margin: 40px 0 0 0;
    padding: 20px 0 0 0;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

/* Related Articles */
.related-articles {
    padding: 60px 0;
    background-color: var(--light-gray);
}

.related-articles h2 {
    text-align: center;
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 40px 0;
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.related-item {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    cursor: pointer;
}

.related-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.related-item-image {
    width: 100%;
    height: 180px;
    overflow: hidden;
    position: relative;
}

.related-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.related-item:hover .related-item-image img {
    transform: scale(1.05);
}

.related-item-image.no-image {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 2rem;
}

.related-item-content {
    padding: 20px;
}

.related-item-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 10px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-item-excerpt {
    color: var(--text-medium);
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Responsive */
@media (max-width: 768px) {
    .article-content .container {
        padding: 40px 20px;
    }
    
    .article-title {
        font-size: 2rem;
    }
    
    .article-excerpt {
        font-size: 1.1rem;
    }
    
    .article-body {
        font-size: 1rem;
    }
    
    .article-meta {
        flex-direction: column;
        gap: 10px;
    }
    
    .share-buttons {
        gap: 10px;
    }
    
    .share-btn {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
    
    .related-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}
