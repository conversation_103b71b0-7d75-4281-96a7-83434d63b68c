/* Style dla strony <PERSON> */

/* Hero section */
.page-hero {
    height: 500px;
    background-size: cover;
    background-position: center;
    position: relative;
    margin-top: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.5) 0%, rgba(0,0,0,0.7) 100%);
}

.page-hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.page-hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: var(--white);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.page-hero-content .lead {
    font-size: 1.5rem;
    margin-bottom: 0;
    color: var(--white);
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Breadcrumbs */
.breadcrumbs {
    background-color: var(--light-gray);
    padding: 15px 0;
    border-bottom: 1px solid var(--medium-gray);
}

.breadcrumbs-list {
    display: flex;
    list-style: none;
    flex-wrap: wrap;
}

.breadcrumbs-list li {
    display: flex;
    align-items: center;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.breadcrumbs-list li:not(:last-child)::after {
    content: '/';
    margin: 0 10px;
    color: var(--text-light);
}

.breadcrumbs-list a {
    color: var(--text-medium);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumbs-list a:hover {
    color: var(--primary-color);
}

/* Attractions Intro */
.attractions-intro {
    padding: 80px 0;
}

.intro-content {
    max-width: 900px;
    margin: 0 auto 60px;
    text-align: center;
}

.intro-content .lead {
    font-size: 1.2rem;
    color: var(--text-dark);
    font-weight: 500;
    margin-bottom: 1.5rem;
}

/* Attractions Categories */
.attractions-categories {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 50px;
}

.category-filter {
    padding: 10px 20px;
    background-color: var(--white);
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    color: var(--text-medium);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.category-filter:hover,
.category-filter.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

/* Attractions Grid */
.attractions-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.attraction-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.attraction-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.attraction-image {
    height: 250px;
    overflow: hidden;
    position: relative;
}

.attraction-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.attraction-card:hover .attraction-image img {
    transform: scale(1.1);
}

.attraction-category {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 5px 15px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
}

.attraction-content {
    padding: 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.attraction-content h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.attraction-info {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 15px;
}

.attraction-info-item {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.attraction-info-item i {
    color: var(--primary-color);
}

.attraction-content p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.6;
    flex: 1;
}

.attraction-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.attraction-rating {
    display: flex;
    align-items: center;
    gap: 5px;
}

.attraction-rating i {
    color: #FFD700;
}

.attraction-rating span {
    font-weight: 600;
    color: var(--text-dark);
}

/* Featured Attractions */
.featured-attractions {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.featured-slider {
    margin-top: 40px;
    position: relative;
}

.featured-slide {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.featured-image {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.featured-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.5s ease;
}

.featured-image:hover img {
    transform: scale(1.05);
}

.featured-content h3 {
    font-size: 2rem;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.featured-content .featured-category {
    display: inline-block;
    padding: 5px 15px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.featured-info {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
}

.featured-info-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-medium);
}

.featured-info-item i {
    color: var(--primary-color);
}

.featured-content p {
    color: var(--text-medium);
    margin-bottom: 30px;
    line-height: 1.8;
}

.featured-controls {
    position: absolute;
    bottom: -20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.featured-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--medium-gray);
    cursor: pointer;
    transition: var(--transition);
}

.featured-dot.active {
    background-color: var(--primary-color);
}

/* Attractions Map */
.attractions-map {
    padding: 80px 0;
}

.map-container {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    margin-top: 40px;
    height: 500px;
}

/* Responsive */
@media (max-width: 1200px) {
    .attractions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .featured-slide {
        grid-template-columns: 1fr;
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .page-hero {
        height: 400px;
    }
    
    .page-hero-content h1 {
        font-size: 2.5rem;
    }
    
    .page-hero-content .lead {
        font-size: 1.2rem;
    }
    
    .attractions-grid {
        grid-template-columns: 1fr;
    }
    
    .map-container {
        height: 400px;
    }
}

@media (max-width: 576px) {
    .attractions-categories {
        flex-direction: column;
        align-items: center;
    }
    
    .category-filter {
        width: 100%;
        text-align: center;
    }
}
