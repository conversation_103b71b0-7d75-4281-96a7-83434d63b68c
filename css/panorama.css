/* Style dla sekcji panoramy miasta */

.city-panorama {
    position: relative;
    margin: 40px 0;
    overflow: hidden;
}

.panorama-container {
    position: relative;
    width: 100%;
    height: 400px;
    overflow: hidden;
}

.panorama-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.panorama-container:hover .panorama-image {
    transform: scale(1.05);
}

.panorama-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.1) 100%);
    display: flex;
    align-items: center;
}

.panorama-content {
    color: var(--white);
    max-width: 600px;
    padding: 30px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius-md);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: fadeIn 1s ease-in-out;
}

.panorama-content h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--white);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.panorama-content p {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 0;
    color: var(--white);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Animacja */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsywność */
@media (max-width: 768px) {
    .panorama-container {
        height: 300px;
    }
    
    .panorama-content {
        max-width: 100%;
        padding: 20px;
    }
    
    .panorama-content h2 {
        font-size: 1.8rem;
    }
    
    .panorama-content p {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .panorama-container {
        height: 250px;
    }
    
    .panorama-overlay {
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.7) 100%);
        align-items: flex-end;
    }
    
    .panorama-content {
        padding: 15px;
    }
    
    .panorama-content h2 {
        font-size: 1.5rem;
        margin-bottom: 10px;
    }
}
