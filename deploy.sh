#!/bin/bash

# 🚀 Automatyczny skrypt wdrożenia - Żyrardów.poleca.to
# Autor: System wdrożeniowy
# Data: $(date)

set -e  # Zatrzymaj przy błędzie

# Kolory dla lepszej czytelności
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funkcje pomocnicze
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Sprawdzenie czy skrypt jest uruchamiany jako root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "Ten skrypt nie powinien być uruchamiany jako root!"
        log_info "Uruchom jako zwykły użytkownik z uprawnieniami sudo"
        exit 1
    fi
}

# Sprawdzenie systemu operacyjnego
check_os() {
    if [[ ! -f /etc/os-release ]]; then
        log_error "Nie można określić systemu operacyjnego"
        exit 1
    fi
    
    . /etc/os-release
    if [[ "$ID" != "ubuntu" ]]; then
        log_error "Ten skrypt jest przeznaczony dla Ubuntu"
        exit 1
    fi
    
    log_success "System: $PRETTY_NAME"
}

# Konfiguracja zmiennych
setup_variables() {
    log_info "Konfiguracja zmiennych środowiskowych..."
    
    # Domyślne wartości
    DOMAIN=${DOMAIN:-"zyrardow.poleca.to"}
    DB_NAME=${DB_NAME:-"zyrardow_poleca_db"}
    DB_USER=${DB_USER:-"zyrardow_admin"}
    DB_PASS=${DB_PASS:-"ZyrardowPoleca2024!@#"}
    ADMIN_EMAIL=${ADMIN_EMAIL:-"<EMAIL>"}
    
    # Generowanie bezpiecznego JWT secret
    JWT_SECRET=$(openssl rand -base64 64 | tr -d '\n')
    
    # Generowanie bezpiecznego hasła admina
    ADMIN_PASSWORD=$(openssl rand -base64 32 | tr -d '\n')
    
    log_success "Zmienne skonfigurowane"
}

# Aktualizacja systemu
update_system() {
    log_info "Aktualizacja systemu..."
    sudo apt update && sudo apt upgrade -y
    
    log_info "Instalacja podstawowych narzędzi..."
    sudo apt install -y curl wget git unzip software-properties-common \
        build-essential python3-pip ufw fail2ban htop nano vim jq
    
    log_success "System zaktualizowany"
}

# Konfiguracja firewall
setup_firewall() {
    log_info "Konfiguracja firewall..."
    
    sudo ufw --force enable
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    sudo ufw allow ssh
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    
    log_success "Firewall skonfigurowany"
}

# Instalacja Node.js
install_nodejs() {
    log_info "Instalacja Node.js 18.x LTS..."
    
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
    
    # Weryfikacja
    NODE_VERSION=$(node --version)
    NPM_VERSION=$(npm --version)
    
    log_success "Node.js $NODE_VERSION zainstalowany"
    log_success "npm $NPM_VERSION zainstalowany"
    
    # Instalacja PM2
    log_info "Instalacja PM2..."
    sudo npm install -g pm2
    
    log_success "PM2 zainstalowany"
}

# Instalacja MySQL
install_mysql() {
    log_info "Instalacja MySQL Server..."
    
    sudo apt install -y mysql-server
    sudo systemctl start mysql
    sudo systemctl enable mysql
    
    log_success "MySQL zainstalowany i uruchomiony"
}

# Konfiguracja MySQL
setup_mysql() {
    log_info "Konfiguracja MySQL..."
    
    # Zabezpieczenie MySQL (automatyczne odpowiedzi)
    sudo mysql -e "DELETE FROM mysql.user WHERE User='';"
    sudo mysql -e "DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');"
    sudo mysql -e "DROP DATABASE IF EXISTS test;"
    sudo mysql -e "DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';"
    sudo mysql -e "FLUSH PRIVILEGES;"
    
    # Utworzenie bazy danych i użytkownika
    sudo mysql -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    sudo mysql -e "DROP USER IF EXISTS '$DB_USER'@'localhost';"
    sudo mysql -e "CREATE USER '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';"
    sudo mysql -e "GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER, CREATE TEMPORARY TABLES, LOCK TABLES ON $DB_NAME.* TO '$DB_USER'@'localhost';"
    sudo mysql -e "FLUSH PRIVILEGES;"
    
    log_success "MySQL skonfigurowany"
}

# Instalacja Nginx
install_nginx() {
    log_info "Instalacja Nginx..."
    
    sudo apt install -y nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
    
    log_success "Nginx zainstalowany i uruchomiony"
}

# Utworzenie użytkownika aplikacji
create_app_user() {
    log_info "Tworzenie użytkownika aplikacji..."
    
    sudo adduser --system --group --home /var/www/zyrardow-poleca --shell /bin/bash zyrardow
    sudo mkdir -p /var/www/zyrardow-poleca/app
    sudo chown -R zyrardow:zyrardow /var/www/zyrardow-poleca
    
    log_success "Użytkownik 'zyrardow' utworzony"
}

# Wdrożenie aplikacji
deploy_application() {
    log_info "Wdrażanie aplikacji..."
    
    # Kopiowanie plików aplikacji
    sudo -u zyrardow cp -r . /var/www/zyrardow-poleca/app/
    
    # Przejście do katalogu aplikacji
    cd /var/www/zyrardow-poleca/app
    
    # Instalacja zależności
    log_info "Instalacja zależności npm..."
    sudo -u zyrardow npm install --only=production
    
    # Konfiguracja .env
    log_info "Konfiguracja zmiennych środowiskowych..."
    sudo -u zyrardow cp .env.example .env
    
    # Aktualizacja pliku .env
    sudo -u zyrardow sed -i "s/DB_HOST=.*/DB_HOST=localhost/" .env
    sudo -u zyrardow sed -i "s/DB_NAME=.*/DB_NAME=$DB_NAME/" .env
    sudo -u zyrardow sed -i "s/DB_USER=.*/DB_USER=$DB_USER/" .env
    sudo -u zyrardow sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASS/" .env
    sudo -u zyrardow sed -i "s/JWT_SECRET=.*/JWT_SECRET=$JWT_SECRET/" .env
    sudo -u zyrardow sed -i "s/NODE_ENV=.*/NODE_ENV=production/" .env
    sudo -u zyrardow sed -i "s/ADMIN_EMAIL=.*/ADMIN_EMAIL=$ADMIN_EMAIL/" .env
    sudo -u zyrardow sed -i "s/ADMIN_PASSWORD=.*/ADMIN_PASSWORD=$ADMIN_PASSWORD/" .env
    sudo -u zyrardow sed -i "s|CORS_ORIGIN=.*|CORS_ORIGIN=https://$DOMAIN|" .env
    
    # Utworzenie katalogów
    sudo -u zyrardow mkdir -p server/uploads
    sudo mkdir -p /var/log/zyrardow-poleca
    sudo chown -R zyrardow:zyrardow /var/log/zyrardow-poleca
    
    log_success "Aplikacja wdrożona"
}

# Inicjalizacja bazy danych
init_database() {
    log_info "Inicjalizacja bazy danych..."
    
    cd /var/www/zyrardow-poleca/app
    
    # Ustawienie hasła root MySQL dla skryptu
    echo "MYSQL_ROOT_PASSWORD=" | sudo -u zyrardow tee -a .env > /dev/null
    
    # Uruchomienie skryptu inicjalizacji
    sudo -u zyrardow npm run setup-db
    
    log_success "Baza danych zainicjalizowana"
}

# Konfiguracja PM2
setup_pm2() {
    log_info "Konfiguracja PM2..."
    
    cd /var/www/zyrardow-poleca/app
    
    # Utworzenie pliku konfiguracyjnego PM2
    sudo -u zyrardow cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'zyrardow-poleca',
    script: 'server/app.js',
    cwd: '/var/www/zyrardow-poleca/app',
    user: 'zyrardow',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/zyrardow-poleca/error.log',
    out_file: '/var/log/zyrardow-poleca/out.log',
    log_file: '/var/log/zyrardow-poleca/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    watch: false,
    max_restarts: 10,
    min_uptime: '10s',
    kill_timeout: 5000,
    listen_timeout: 8000,
    cron_restart: '0 2 * * *'
  }]
};
EOF
    
    # Uruchomienie aplikacji
    sudo -u zyrardow pm2 start ecosystem.config.js --env production
    sudo -u zyrardow pm2 save
    
    # Konfiguracja autostartu
    sudo pm2 startup systemd -u zyrardow --hp /var/www/zyrardow-poleca
    
    log_success "PM2 skonfigurowany i aplikacja uruchomiona"
}

# Konfiguracja Nginx
setup_nginx() {
    log_info "Konfiguracja Nginx..."
    
    # Utworzenie konfiguracji strony
    sudo cat > /etc/nginx/sites-available/zyrardow-poleca << EOF
upstream zyrardow_backend {
    least_conn;
    server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

server {
    listen 80;
    listen [::]:80;
    server_name $DOMAIN www.$DOMAIN;
    
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;
    
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/json application/javascript application/xml+rss;
    
    client_max_body_size 10M;
    
    location /uploads/ {
        alias /var/www/zyrardow-poleca/app/server/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /admin/ {
        alias /var/www/zyrardow-poleca/app/admin/;
        try_files \$uri \$uri/ /admin/index.html;
        expires 1h;
    }
    
    location /api/ {
        proxy_pass http://zyrardow_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    location / {
        root /var/www/zyrardow-poleca/app;
        try_files \$uri \$uri/ @proxy;
        expires 1h;
    }
    
    location @proxy {
        proxy_pass http://zyrardow_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    location ~ /\. {
        deny all;
    }
}
EOF
    
    # Aktywacja konfiguracji
    sudo ln -sf /etc/nginx/sites-available/zyrardow-poleca /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Test konfiguracji
    sudo nginx -t
    sudo systemctl reload nginx
    
    log_success "Nginx skonfigurowany"
}

# Instalacja SSL
install_ssl() {
    log_info "Instalacja certyfikatu SSL..."
    
    # Instalacja Certbot
    sudo apt install -y certbot python3-certbot-nginx
    
    # Zatrzymanie Nginx tymczasowo
    sudo systemctl stop nginx
    
    # Uzyskanie certyfikatu
    sudo certbot certonly --standalone -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email $ADMIN_EMAIL
    
    # Uruchomienie Nginx
    sudo systemctl start nginx
    
    # Konfiguracja automatycznego odnawiania
    (sudo crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet --post-hook 'systemctl reload nginx'") | sudo crontab -
    
    log_success "SSL skonfigurowany"
}

# Konfiguracja fail2ban
setup_fail2ban() {
    log_info "Konfiguracja fail2ban..."
    
    sudo cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10
findtime = 600
bantime = 7200
EOF
    
    sudo systemctl enable fail2ban
    sudo systemctl start fail2ban
    
    log_success "fail2ban skonfigurowany"
}

# Konfiguracja backupów
setup_backups() {
    log_info "Konfiguracja systemu backupów..."
    
    # Skrypt backup
    sudo cat > /usr/local/bin/backup-zyrardow.sh << EOF
#!/bin/bash
BACKUP_DIR="/var/backups/zyrardow-poleca"
DATE=\$(date +%Y%m%d_%H%M%S)
DB_NAME="$DB_NAME"
DB_USER="$DB_USER"
DB_PASS="$DB_PASS"
APP_DIR="/var/www/zyrardow-poleca/app"

mkdir -p \$BACKUP_DIR

echo "\$(date): Rozpoczynanie backup..."
mysqldump -u \$DB_USER -p\$DB_PASS --single-transaction \$DB_NAME | gzip > \$BACKUP_DIR/db_backup_\$DATE.sql.gz

if [ -d "\$APP_DIR/server/uploads" ]; then
    tar -czf \$BACKUP_DIR/uploads_backup_\$DATE.tar.gz -C \$APP_DIR/server uploads/
fi

find \$BACKUP_DIR -name "*.gz" -mtime +7 -delete
echo "\$(date): Backup zakończony"
EOF
    
    sudo chmod +x /usr/local/bin/backup-zyrardow.sh
    
    # Dodanie do cron
    (sudo crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/backup-zyrardow.sh >> /var/log/backup-zyrardow.log 2>&1") | sudo crontab -
    
    log_success "System backupów skonfigurowany"
}

# Finalne testy
run_tests() {
    log_info "Uruchamianie testów..."
    
    # Test health check
    sleep 10
    if curl -f -s http://localhost:3000/api/health > /dev/null; then
        log_success "Health check: OK"
    else
        log_error "Health check: FAILED"
        return 1
    fi
    
    # Test PM2
    if sudo -u zyrardow pm2 list | grep -q "zyrardow-poleca.*online"; then
        log_success "PM2: aplikacja działa"
    else
        log_error "PM2: aplikacja nie działa"
        return 1
    fi
    
    # Test Nginx
    if sudo nginx -t > /dev/null 2>&1; then
        log_success "Nginx: konfiguracja poprawna"
    else
        log_error "Nginx: błąd konfiguracji"
        return 1
    fi
    
    log_success "Wszystkie testy przeszły pomyślnie"
}

# Wyświetlenie podsumowania
show_summary() {
    echo ""
    echo "🎉 WDROŻENIE ZAKOŃCZONE POMYŚLNIE!"
    echo "=================================="
    echo ""
    echo "🌐 Dostępy:"
    echo "   Strona główna: https://$DOMAIN"
    echo "   Panel admin:   https://$DOMAIN/admin"
    echo "   API:          https://$DOMAIN/api/"
    echo ""
    echo "🔐 Dane logowania do panelu admin:"
    echo "   Email:  $ADMIN_EMAIL"
    echo "   Hasło:  $ADMIN_PASSWORD"
    echo ""
    echo "📊 Przydatne komendy:"
    echo "   Status aplikacji:    sudo -u zyrardow pm2 status"
    echo "   Logi aplikacji:      sudo -u zyrardow pm2 logs"
    echo "   Restart aplikacji:   sudo -u zyrardow pm2 restart zyrardow-poleca"
    echo "   Backup ręczny:       sudo /usr/local/bin/backup-zyrardow.sh"
    echo ""
    echo "⚠️  WAŻNE:"
    echo "   1. Zapisz hasło administratora w bezpiecznym miejscu"
    echo "   2. Skonfiguruj DNS dla domeny $DOMAIN"
    echo "   3. Zmień hasła po pierwszym logowaniu"
    echo ""
    echo "📋 Logi wdrożenia zapisane w: /tmp/deploy-$(date +%Y%m%d_%H%M%S).log"
}

# Główna funkcja
main() {
    echo "🚀 Automatyczne wdrożenie - Żyrardów.poleca.to"
    echo "=============================================="
    echo ""
    
    check_root
    check_os
    setup_variables
    
    log_info "Rozpoczynanie wdrożenia..."
    
    update_system
    setup_firewall
    install_nodejs
    install_mysql
    setup_mysql
    install_nginx
    create_app_user
    deploy_application
    init_database
    setup_pm2
    setup_nginx
    install_ssl
    setup_fail2ban
    setup_backups
    run_tests
    
    show_summary
}

# Uruchomienie z logowaniem
main 2>&1 | tee /tmp/deploy-$(date +%Y%m%d_%H%M%S).log
