# 🚀 Szczegółowy Plan Wdrożenia - Żyrardów.poleca.to

## Przegląd Projektu

**Żyrardów.poleca.to** to portal lokalny z panelem administracyjnym oparty na:
- **Frontend**: HTML, CSS, JavaScript (statyczne pliki)
- **Backend**: Node.js + Express.js
- **Baza danych**: MySQL
- **Serwer WWW**: Nginx (reverse proxy)
- **Process Manager**: PM2
- **SSL**: Let's Encrypt

## Wymagania Systemowe

### Minimalne wymagania VPS:
- **System**: Ubuntu 20.04 LTS lub nowszy
- **RAM**: 2GB (zalecane 4GB)
- **CPU**: 2 vCPU
- **Dysk**: 20GB SSD
- **Przepustowość**: Nielimitowana

### Wymagane porty:
- **22** - SSH
- **80** - HTTP (przekierowanie na HTTPS)
- **443** - HTTPS
- **3000** - Node.js (we<PERSON><PERSON>trz<PERSON>, za Nginx)

## Faza 1: Przygotowanie Serwera VPS

### 1.1 Aktualizacja systemu i instalacja podstawowych narzędzi

```bash
# Aktualizacja systemu
sudo apt update && sudo apt upgrade -y

# Instalacja podstawowych narzędzi
sudo apt install -y curl wget git unzip software-properties-common \
  build-essential python3-pip ufw fail2ban htop nano vim

# Konfiguracja strefy czasowej
sudo timedatectl set-timezone Europe/Warsaw
```

### 1.2 Konfiguracja firewall

```bash
# Włączenie UFW
sudo ufw --force enable

# Podstawowe reguły
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Dozwolone porty
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Sprawdzenie statusu
sudo ufw status verbose
```

### 1.3 Konfiguracja bezpieczeństwa SSH

```bash
# Backup oryginalnej konfiguracji
sudo cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

# Edycja konfiguracji SSH
sudo nano /etc/ssh/sshd_config
```

**Zalecane ustawienia SSH:**
```
Port 22
PermitRootLogin no
PasswordAuthentication yes  # Zmień na 'no' po skonfigurowaniu kluczy SSH
PubkeyAuthentication yes
MaxAuthTries 3
ClientAliveInterval 300
ClientAliveCountMax 2
```

```bash
# Restart SSH
sudo systemctl restart sshd
```

## Faza 2: Instalacja Node.js i PM2

### 2.1 Instalacja Node.js 18.x LTS

```bash
# Dodanie repozytorium NodeSource
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

# Instalacja Node.js
sudo apt-get install -y nodejs

# Weryfikacja instalacji
node --version  # Powinno pokazać v18.x.x
npm --version   # Powinno pokazać 9.x.x lub nowszy
```

### 2.2 Instalacja PM2

```bash
# Instalacja PM2 globalnie
sudo npm install -g pm2

# Weryfikacja instalacji
pm2 --version

# Konfiguracja autostartu PM2
sudo pm2 startup
```

## Faza 3: Instalacja i Konfiguracja MySQL

### 3.1 Instalacja MySQL Server

```bash
# Instalacja MySQL
sudo apt install -y mysql-server

# Uruchomienie i włączenie autostartu
sudo systemctl start mysql
sudo systemctl enable mysql

# Sprawdzenie statusu
sudo systemctl status mysql
```

### 3.2 Zabezpieczenie MySQL

```bash
# Uruchomienie skryptu zabezpieczającego
sudo mysql_secure_installation
```

**Odpowiedzi na pytania:**
- Validate Password Plugin? **N** (lub Y jeśli chcesz silne hasła)
- Remove anonymous users? **Y**
- Disallow root login remotely? **Y**
- Remove test database? **Y**
- Reload privilege tables? **Y**

### 3.3 Konfiguracja bazy danych

```bash
# Logowanie do MySQL
sudo mysql -u root -p
```

```sql
-- Utworzenie bazy danych
CREATE DATABASE zyrardow_poleca_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Utworzenie użytkownika aplikacji
CREATE USER 'zyrardow_admin'@'localhost' IDENTIFIED BY 'ZyrardowPoleca2024!@#';

-- Nadanie uprawnień
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER, 
      CREATE TEMPORARY TABLES, LOCK TABLES 
ON zyrardow_poleca_db.* TO 'zyrardow_admin'@'localhost';

-- Odświeżenie uprawnień
FLUSH PRIVILEGES;

-- Sprawdzenie utworzonych baz
SHOW DATABASES;

-- Sprawdzenie użytkowników
SELECT User, Host FROM mysql.user WHERE User = 'zyrardow_admin';

-- Wyjście
EXIT;
```

### 3.4 Konfiguracja MySQL dla produkcji

```bash
# Edycja konfiguracji MySQL
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

**Zalecane ustawienia:**
```ini
[mysqld]
# Podstawowe ustawienia
bind-address = 127.0.0.1
port = 3306

# Optymalizacja wydajności
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# Bezpieczeństwo
local-infile = 0
skip-show-database

# Kodowanie
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Logi
log-error = /var/log/mysql/error.log
slow-query-log = 1
slow-query-log-file = /var/log/mysql/slow.log
long_query_time = 2
```

```bash
# Restart MySQL
sudo systemctl restart mysql
```

## Faza 4: Instalacja Nginx

### 4.1 Instalacja Nginx

```bash
# Instalacja Nginx
sudo apt install -y nginx

# Uruchomienie i włączenie autostartu
sudo systemctl start nginx
sudo systemctl enable nginx

# Sprawdzenie statusu
sudo systemctl status nginx
```

### 4.2 Podstawowa konfiguracja Nginx

```bash
# Backup domyślnej konfiguracji
sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup

# Edycja głównej konfiguracji
sudo nano /etc/nginx/nginx.conf
```

**Zalecane ustawienia w nginx.conf:**
```nginx
user www-data;
worker_processes auto;
pid /run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # Podstawowe ustawienia
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    
    # MIME types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logi
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
    
    # Kompresja
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Limity
    client_max_body_size 10M;
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    
    # Bezpieczeństwo
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # Konfiguracje stron
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
```

## Faza 5: Wdrożenie Aplikacji

### 5.1 Utworzenie użytkownika aplikacji

```bash
# Utworzenie użytkownika systemowego
sudo adduser --system --group --home /var/www/zyrardow-poleca --shell /bin/bash zyrardow

# Utworzenie katalogu aplikacji
sudo mkdir -p /var/www/zyrardow-poleca/app
sudo chown -R zyrardow:zyrardow /var/www/zyrardow-poleca
```

### 5.2 Wdrożenie kodu aplikacji

```bash
# Przejście do katalogu domowego użytkownika zyrardow
sudo -u zyrardow -i

# Klonowanie repozytorium (zastąp URL swoim repozytorium)
git clone https://github.com/twoje-repo/zyrardow-poleca.git /var/www/zyrardow-poleca/app

# Przejście do katalogu aplikacji
cd /var/www/zyrardow-poleca/app

# Instalacja zależności produkcyjnych
npm install --only=production

# Wyjście z sesji użytkownika zyrardow
exit
```

### 5.3 Konfiguracja zmiennych środowiskowych

```bash
# Kopiowanie przykładowego pliku .env
sudo -u zyrardow cp /var/www/zyrardow-poleca/app/.env.example /var/www/zyrardow-poleca/app/.env

# Edycja pliku .env
sudo -u zyrardow nano /var/www/zyrardow-poleca/app/.env
```

**Konfiguracja produkcyjna .env:**
```env
# Konfiguracja bazy danych MySQL
DB_HOST=localhost
DB_PORT=3306
DB_NAME=zyrardow_poleca_db
DB_USER=zyrardow_admin
DB_PASSWORD=ZyrardowPoleca2024!@#

# Konfiguracja JWT (WYGENERUJ NOWY KLUCZ!)
JWT_SECRET=WYGENERUJ_BARDZO_DŁUGI_I_BEZPIECZNY_KLUCZ_JWT_MINIMUM_64_ZNAKI_1234567890
JWT_EXPIRES_IN=24h

# Konfiguracja serwera
PORT=3000
NODE_ENV=production

# Konfiguracja admina
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=ZMIEŃ_TO_HASŁO_NA_BARDZO_BEZPIECZNE

# Konfiguracja uploadów
UPLOAD_MAX_SIZE=5242880
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp

# Konfiguracja bezpieczeństwa
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# Konfiguracja CORS
CORS_ORIGIN=https://zyrardow.poleca.to

# Hasło root MySQL (dla skryptu setup)
MYSQL_ROOT_PASSWORD=TWOJE_HASŁO_ROOT_MYSQL

# Konfiguracja SSL (będzie skonfigurowane później)
SSL_CERT_PATH=/etc/letsencrypt/live/zyrardow.poleca.to/fullchain.pem
SSL_KEY_PATH=/etc/letsencrypt/live/zyrardow.poleca.to/privkey.pem
```

### 5.4 Inicjalizacja bazy danych

```bash
# Przejście do katalogu aplikacji
cd /var/www/zyrardow-poleca/app

# Uruchomienie skryptu inicjalizacji bazy danych
sudo -u zyrardow npm run setup-db

# Test połączenia z bazą danych
sudo -u zyrardow node server/scripts/setup-database.js --test
```

### 5.5 Utworzenie katalogów i uprawnień

```bash
# Utworzenie katalogów dla uploadów i logów
sudo -u zyrardow mkdir -p /var/www/zyrardow-poleca/app/server/uploads
sudo -u zyrardow mkdir -p /var/log/zyrardow-poleca

# Ustawienie uprawnień
sudo chown -R zyrardow:zyrardow /var/www/zyrardow-poleca
sudo chown -R zyrardow:zyrardow /var/log/zyrardow-poleca
```

## Faza 6: Konfiguracja PM2

### 6.1 Utworzenie pliku konfiguracyjnego PM2

```bash
# Utworzenie pliku ecosystem.config.js
sudo -u zyrardow nano /var/www/zyrardow-poleca/app/ecosystem.config.js
```

```javascript
module.exports = {
  apps: [{
    name: 'zyrardow-poleca',
    script: 'server/app.js',
    cwd: '/var/www/zyrardow-poleca/app',
    user: 'zyrardow',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/zyrardow-poleca/error.log',
    out_file: '/var/log/zyrardow-poleca/out.log',
    log_file: '/var/log/zyrardow-poleca/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    watch: false,
    ignore_watch: ['node_modules', 'uploads', 'logs'],
    max_restarts: 10,
    min_uptime: '10s',
    kill_timeout: 5000,
    listen_timeout: 8000,
    cron_restart: '0 2 * * *' // Restart codziennie o 2:00
  }]
};
```

### 6.2 Uruchomienie aplikacji przez PM2

```bash
# Przejście do katalogu aplikacji
cd /var/www/zyrardow-poleca/app

# Uruchomienie aplikacji
sudo -u zyrardow pm2 start ecosystem.config.js --env production

# Zapisanie konfiguracji PM2
sudo -u zyrardow pm2 save

# Konfiguracja autostartu PM2
sudo pm2 startup systemd -u zyrardow --hp /var/www/zyrardow-poleca

# Sprawdzenie statusu
sudo -u zyrardow pm2 status
sudo -u zyrardow pm2 logs --lines 50
```

## Faza 7: Konfiguracja Nginx jako Reverse Proxy

### 7.1 Utworzenie konfiguracji strony

```bash
# Utworzenie pliku konfiguracyjnego
sudo nano /etc/nginx/sites-available/zyrardow-poleca
```

```nginx
# Konfiguracja upstream dla load balancing
upstream zyrardow_backend {
    least_conn;
    server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# Przekierowanie HTTP na HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name zyrardow.poleca.to www.zyrardow.poleca.to;
    
    # Przekierowanie na HTTPS
    return 301 https://$server_name$request_uri;
}

# Główna konfiguracja HTTPS
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name zyrardow.poleca.to www.zyrardow.poleca.to;
    
    # Ścieżka do certyfikatów SSL (będą skonfigurowane przez Certbot)
    ssl_certificate /etc/letsencrypt/live/zyrardow.poleca.to/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/zyrardow.poleca.to/privkey.pem;
    
    # Konfiguracja SSL
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # Nagłówki bezpieczeństwa
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'; media-src 'self'; object-src 'none'; child-src 'none'; worker-src 'none'; frame-ancestors 'none'; form-action 'self'; base-uri 'self';" always;
    
    # Kompresja
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Limity
    client_max_body_size 10M;
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    
    # Logi
    access_log /var/log/nginx/zyrardow-poleca.access.log;
    error_log /var/log/nginx/zyrardow-poleca.error.log;
    
    # Obsługa plików statycznych - uploads
    location /uploads/ {
        alias /var/www/zyrardow-poleca/app/server/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
        
        # Bezpieczeństwo - blokowanie wykonywania skryptów
        location ~* \.(php|pl|py|jsp|asp|sh|cgi)$ {
            deny all;
        }
    }
    
    # Panel administracyjny
    location /admin/ {
        alias /var/www/zyrardow-poleca/app/admin/;
        try_files $uri $uri/ /admin/index.html;
        expires 1h;
        add_header Cache-Control "no-cache, must-revalidate";
        
        # Dodatkowe zabezpieczenia dla panelu admin
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
    }
    
    # Pliki statyczne głównej strony
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot)$ {
        root /var/www/zyrardow-poleca/app;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
    }
    
    # API endpoints - proxy do Node.js
    location /api/ {
        proxy_pass http://zyrardow_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        proxy_send_timeout 300s;
        
        # Bufory
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
    
    # Główna strona - try_files z fallback do proxy
    location / {
        root /var/www/zyrardow-poleca/app;
        try_files $uri $uri/ @proxy;
        expires 1h;
        add_header Cache-Control "no-cache, must-revalidate";
    }
    
    # Fallback proxy dla SPA routing
    location @proxy {
        proxy_pass http://zyrardow_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        proxy_send_timeout 300s;
    }
    
    # Blokowanie dostępu do wrażliwych plików
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|config|log)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://zyrardow_backend/api/health;
        access_log off;
    }
}
```

### 7.2 Aktywacja konfiguracji Nginx

```bash
# Sprawdzenie składni konfiguracji
sudo nginx -t

# Aktywacja strony
sudo ln -s /etc/nginx/sites-available/zyrardow-poleca /etc/nginx/sites-enabled/

# Usunięcie domyślnej strony
sudo rm -f /etc/nginx/sites-enabled/default

# Ponowne sprawdzenie składni
sudo nginx -t

# Przeładowanie Nginx
sudo systemctl reload nginx
```

## Faza 8: Konfiguracja SSL (Let's Encrypt)

### 8.1 Instalacja Certbot

```bash
# Instalacja Certbot
sudo apt install -y certbot python3-certbot-nginx

# Sprawdzenie wersji
certbot --version
```

### 8.2 Uzyskanie certyfikatu SSL

```bash
# Zatrzymanie Nginx tymczasowo
sudo systemctl stop nginx

# Uzyskanie certyfikatu (standalone mode)
sudo certbot certonly --standalone -d zyrardow.poleca.to -d www.zyrardow.poleca.to

# Uruchomienie Nginx
sudo systemctl start nginx

# Alternatywnie - użycie pluginu nginx (jeśli Nginx działa)
# sudo certbot --nginx -d zyrardow.poleca.to -d www.zyrardow.poleca.to
```

### 8.3 Konfiguracja automatycznego odnawiania

```bash
# Test odnawiania
sudo certbot renew --dry-run

# Dodanie zadania cron dla automatycznego odnawiania
sudo crontab -e
```

**Dodaj linię do crontab:**
```
0 12 * * * /usr/bin/certbot renew --quiet --post-hook "systemctl reload nginx"
```

### 8.4 Sprawdzenie certyfikatu

```bash
# Sprawdzenie certyfikatu
sudo certbot certificates

# Test HTTPS
curl -I https://zyrardow.poleca.to
```

## Faza 9: Konfiguracja Monitorowania i Logów

### 9.1 Konfiguracja logrotate

```bash
# Konfiguracja logrotate dla aplikacji
sudo nano /etc/logrotate.d/zyrardow-poleca
```

```
/var/log/zyrardow-poleca/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 zyrardow zyrardow
    postrotate
        sudo -u zyrardow pm2 reloadLogs
    endscript
}

/var/log/nginx/zyrardow-poleca.*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
```

### 9.2 Konfiguracja fail2ban

```bash
# Instalacja fail2ban
sudo apt install -y fail2ban

# Konfiguracja fail2ban
sudo nano /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5
backend = systemd

[sshd]
enabled = true
port = ssh
logpath = %(sshd_log)s
backend = %(sshd_backend)s

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10
findtime = 600
bantime = 7200

[nginx-botsearch]
enabled = true
filter = nginx-botsearch
port = http,https
logpath = /var/log/nginx/access.log
maxretry = 2
```

```bash
# Uruchomienie fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Sprawdzenie statusu
sudo fail2ban-client status
```

## Faza 10: Konfiguracja Backupów

### 10.1 Skrypt backup bazy danych

```bash
# Utworzenie skryptu backup
sudo nano /usr/local/bin/backup-zyrardow.sh
```

```bash
#!/bin/bash

# Konfiguracja
BACKUP_DIR="/var/backups/zyrardow-poleca"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="zyrardow_poleca_db"
DB_USER="zyrardow_admin"
DB_PASS="ZyrardowPoleca2024!@#"
APP_DIR="/var/www/zyrardow-poleca/app"
RETENTION_DAYS=7

# Utworzenie katalogu backup
mkdir -p $BACKUP_DIR

echo "$(date): Rozpoczynanie backup..."

# Backup bazy danych
echo "$(date): Backup bazy danych..."
mysqldump -u $DB_USER -p$DB_PASS --single-transaction --routines --triggers $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

if [ $? -eq 0 ]; then
    echo "$(date): Backup bazy danych zakończony pomyślnie"
else
    echo "$(date): BŁĄD: Backup bazy danych nieudany"
    exit 1
fi

# Backup plików uploads
echo "$(date): Backup plików uploads..."
if [ -d "$APP_DIR/server/uploads" ]; then
    tar -czf $BACKUP_DIR/uploads_backup_$DATE.tar.gz -C $APP_DIR/server uploads/
    echo "$(date): Backup uploads zakończony pomyślnie"
else
    echo "$(date): OSTRZEŻENIE: Katalog uploads nie istnieje"
fi

# Backup konfiguracji
echo "$(date): Backup konfiguracji..."
tar -czf $BACKUP_DIR/config_backup_$DATE.tar.gz \
    $APP_DIR/.env \
    $APP_DIR/ecosystem.config.js \
    /etc/nginx/sites-available/zyrardow-poleca \
    /etc/letsencrypt/live/zyrardow.poleca.to/ 2>/dev/null

# Usunięcie starych backupów
echo "$(date): Usuwanie starych backupów (starszych niż $RETENTION_DAYS dni)..."
find $BACKUP_DIR -name "*.gz" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete

# Sprawdzenie miejsca na dysku
DISK_USAGE=$(df $BACKUP_DIR | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): OSTRZEŻENIE: Wykorzystanie dysku: $DISK_USAGE%"
fi

echo "$(date): Backup zakończony pomyślnie"

# Opcjonalnie - wysłanie powiadomienia email
# mail -s "Backup Żyrardów.poleca.to - $(date)" <EMAIL> < /tmp/backup_log.txt
```

```bash
# Nadanie uprawnień wykonywania
sudo chmod +x /usr/local/bin/backup-zyrardow.sh

# Test skryptu backup
sudo /usr/local/bin/backup-zyrardow.sh
```

### 10.2 Automatyczne backupy

```bash
# Dodanie zadania cron dla automatycznych backupów
sudo crontab -e
```

**Dodaj linię do crontab:**
```
0 2 * * * /usr/local/bin/backup-zyrardow.sh >> /var/log/backup-zyrardow.log 2>&1
```

### 10.3 Skrypt przywracania z backup

```bash
# Utworzenie skryptu restore
sudo nano /usr/local/bin/restore-zyrardow.sh
```

```bash
#!/bin/bash

# Konfiguracja
BACKUP_DIR="/var/backups/zyrardow-poleca"
DB_NAME="zyrardow_poleca_db"
DB_USER="zyrardow_admin"
DB_PASS="ZyrardowPoleca2024!@#"
APP_DIR="/var/www/zyrardow-poleca/app"

if [ $# -eq 0 ]; then
    echo "Użycie: $0 <data_backup_YYYYMMDD_HHMMSS>"
    echo "Dostępne backupy:"
    ls -la $BACKUP_DIR/db_backup_*.sql.gz | awk '{print $9}' | sed 's/.*db_backup_//' | sed 's/.sql.gz//'
    exit 1
fi

BACKUP_DATE=$1

echo "$(date): Rozpoczynanie przywracania z backup $BACKUP_DATE..."

# Sprawdzenie czy pliki backup istnieją
if [ ! -f "$BACKUP_DIR/db_backup_$BACKUP_DATE.sql.gz" ]; then
    echo "BŁĄD: Plik backup bazy danych nie istnieje: $BACKUP_DIR/db_backup_$BACKUP_DATE.sql.gz"
    exit 1
fi

# Zatrzymanie aplikacji
echo "$(date): Zatrzymywanie aplikacji..."
sudo -u zyrardow pm2 stop zyrardow-poleca

# Przywracanie bazy danych
echo "$(date): Przywracanie bazy danych..."
gunzip < $BACKUP_DIR/db_backup_$BACKUP_DATE.sql.gz | mysql -u $DB_USER -p$DB_PASS $DB_NAME

if [ $? -eq 0 ]; then
    echo "$(date): Baza danych przywrócona pomyślnie"
else
    echo "$(date): BŁĄD: Przywracanie bazy danych nieudane"
    exit 1
fi

# Przywracanie plików uploads (jeśli istnieją)
if [ -f "$BACKUP_DIR/uploads_backup_$BACKUP_DATE.tar.gz" ]; then
    echo "$(date): Przywracanie plików uploads..."
    rm -rf $APP_DIR/server/uploads/*
    tar -xzf $BACKUP_DIR/uploads_backup_$BACKUP_DATE.tar.gz -C $APP_DIR/server/
    chown -R zyrardow:zyrardow $APP_DIR/server/uploads
    echo "$(date): Pliki uploads przywrócone pomyślnie"
fi

# Uruchomienie aplikacji
echo "$(date): Uruchamianie aplikacji..."
sudo -u zyrardow pm2 start zyrardow-poleca

echo "$(date): Przywracanie zakończone pomyślnie"
```

```bash
# Nadanie uprawnień wykonywania
sudo chmod +x /usr/local/bin/restore-zyrardow.sh
```

## Faza 11: Skrypty Zarządzania

### 11.1 Skrypt aktualizacji aplikacji

```bash
# Utworzenie skryptu aktualizacji
sudo nano /usr/local/bin/update-zyrardow.sh
```

```bash
#!/bin/bash

APP_DIR="/var/www/zyrardow-poleca/app"
BACKUP_DIR="/var/backups/zyrardow-poleca"
DATE=$(date +%Y%m%d_%H%M%S)

echo "$(date): Rozpoczynanie aktualizacji aplikacji..."

# Przejście do katalogu aplikacji
cd $APP_DIR

# Backup przed aktualizacją
echo "$(date): Tworzenie backup przed aktualizacją..."
/usr/local/bin/backup-zyrardow.sh

# Pobieranie najnowszych zmian
echo "$(date): Pobieranie najnowszych zmian z repozytorium..."
sudo -u zyrardow git fetch origin
sudo -u zyrardow git pull origin main

if [ $? -ne 0 ]; then
    echo "$(date): BŁĄD: Nie udało się pobrać zmian z repozytorium"
    exit 1
fi

# Instalacja/aktualizacja zależności
echo "$(date): Aktualizacja zależności..."
sudo -u zyrardow npm install --only=production

if [ $? -ne 0 ]; then
    echo "$(date): BŁĄD: Nie udało się zainstalować zależności"
    exit 1
fi

# Sprawdzenie czy są zmiany w bazie danych
if [ -f "server/scripts/migrate-database.js" ]; then
    echo "$(date): Uruchamianie migracji bazy danych..."
    sudo -u zyrardow node server/scripts/migrate-database.js
fi

# Restart aplikacji
echo "$(date): Restart aplikacji..."
sudo -u zyrardow pm2 restart zyrardow-poleca

# Sprawdzenie statusu
sleep 5
if sudo -u zyrardow pm2 list | grep -q "online"; then
    echo "$(date): ✅ Aktualizacja zakończona pomyślnie!"
    echo "$(date): Aplikacja działa poprawnie"
else
    echo "$(date): ❌ BŁĄD: Aplikacja nie uruchomiła się poprawnie"
    echo "$(date): Sprawdź logi: sudo -u zyrardow pm2 logs"
    exit 1
fi

# Test health check
echo "$(date): Sprawdzanie health check..."
sleep 10
if curl -f -s http://localhost:3000/api/health > /dev/null; then
    echo "$(date): ✅ Health check OK"
else
    echo "$(date): ⚠️  OSTRZEŻENIE: Health check nieudany"
fi

echo "$(date): Aktualizacja zakończona!"
```

```bash
# Nadanie uprawnień wykonywania
sudo chmod +x /usr/local/bin/update-zyrardow.sh
```

### 11.2 Skrypt monitorowania

```bash
# Utworzenie skryptu monitorowania
sudo nano /usr/local/bin/monitor-zyrardow.sh
```

```bash
#!/bin/bash

# Konfiguracja
APP_NAME="zyrardow-poleca"
HEALTH_URL="http://localhost:3000/api/health"
LOG_FILE="/var/log/zyrardow-poleca/monitor.log"
EMAIL_ALERT="<EMAIL>"

# Funkcja logowania
log_message() {
    echo "$(date): $1" | tee -a $LOG_FILE
}

# Sprawdzenie PM2
check_pm2() {
    if ! sudo -u zyrardow pm2 list | grep -q "$APP_NAME.*online"; then
        log_message "❌ BŁĄD: Aplikacja $APP_NAME nie działa w PM2"
        return 1
    fi
    return 0
}

# Sprawdzenie health check
check_health() {
    if ! curl -f -s $HEALTH_URL > /dev/null; then
        log_message "❌ BŁĄD: Health check nieudany ($HEALTH_URL)"
        return 1
    fi
    return 0
}

# Sprawdzenie MySQL
check_mysql() {
    if ! systemctl is-active --quiet mysql; then
        log_message "❌ BŁĄD: MySQL nie działa"
        return 1
    fi
    return 0
}

# Sprawdzenie Nginx
check_nginx() {
    if ! systemctl is-active --quiet nginx; then
        log_message "❌ BŁĄD: Nginx nie działa"
        return 1
    fi
    return 0
}

# Sprawdzenie miejsca na dysku
check_disk_space() {
    DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ $DISK_USAGE -gt 85 ]; then
        log_message "⚠️  OSTRZEŻENIE: Wykorzystanie dysku: $DISK_USAGE%"
        return 1
    fi
    return 0
}

# Sprawdzenie pamięci
check_memory() {
    MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    if [ $MEMORY_USAGE -gt 90 ]; then
        log_message "⚠️  OSTRZEŻENIE: Wykorzystanie pamięci: $MEMORY_USAGE%"
        return 1
    fi
    return 0
}

# Główna funkcja monitorowania
main() {
    log_message "🔍 Rozpoczynanie monitorowania systemu..."
    
    ERRORS=0
    
    # Sprawdzenia
    check_pm2 || ((ERRORS++))
    check_health || ((ERRORS++))
    check_mysql || ((ERRORS++))
    check_nginx || ((ERRORS++))
    check_disk_space || ((ERRORS++))
    check_memory || ((ERRORS++))
    
    if [ $ERRORS -eq 0 ]; then
        log_message "✅ Wszystkie sprawdzenia przeszły pomyślnie"
    else
        log_message "❌ Znaleziono $ERRORS problemów"
        
        # Opcjonalnie - wysłanie alertu email
        # echo "Znaleziono problemy w systemie Żyrardów.poleca.to" | mail -s "Alert: Problemy w systemie" $EMAIL_ALERT
        
        # Próba automatycznego naprawienia
        if check_pm2; then
            log_message "🔄 Próba restartu aplikacji..."
            sudo -u zyrardow pm2 restart $APP_NAME
            sleep 10
            if check_health; then
                log_message "✅ Aplikacja została naprawiona"
            fi
        fi
    fi
    
    log_message "🏁 Monitorowanie zakończone"
}

# Uruchomienie
main
```

```bash
# Nadanie uprawnień wykonywania
sudo chmod +x /usr/local/bin/monitor-zyrardow.sh

# Dodanie do cron (sprawdzanie co 5 minut)
sudo crontab -e
```

**Dodaj linię do crontab:**
```
*/5 * * * * /usr/local/bin/monitor-zyrardow.sh
```

## Faza 12: Finalne Testy i Weryfikacja

### 12.1 Lista kontrolna przed uruchomieniem

```bash
# Skrypt sprawdzenia gotowości do produkcji
sudo nano /usr/local/bin/production-check.sh
```

```bash
#!/bin/bash

echo "🔍 Sprawdzanie gotowości do produkcji - Żyrardów.poleca.to"
echo "============================================================"

ERRORS=0

# Sprawdzenie usług systemowych
echo "📋 Sprawdzanie usług systemowych..."
services=("mysql" "nginx" "fail2ban")
for service in "${services[@]}"; do
    if systemctl is-active --quiet $service; then
        echo "✅ $service: działa"
    else
        echo "❌ $service: nie działa"
        ((ERRORS++))
    fi
done

# Sprawdzenie PM2
echo -e "\n📋 Sprawdzanie PM2..."
if sudo -u zyrardow pm2 list | grep -q "zyrardow-poleca.*online"; then
    echo "✅ PM2: aplikacja działa"
else
    echo "❌ PM2: aplikacja nie działa"
    ((ERRORS++))
fi

# Sprawdzenie bazy danych
echo -e "\n📋 Sprawdzanie bazy danych..."
if sudo -u zyrardow node /var/www/zyrardow-poleca/app/server/scripts/setup-database.js --test > /dev/null 2>&1; then
    echo "✅ MySQL: połączenie działa"
else
    echo "❌ MySQL: problem z połączeniem"
    ((ERRORS++))
fi

# Sprawdzenie certyfikatu SSL
echo -e "\n📋 Sprawdzanie SSL..."
if [ -f "/etc/letsencrypt/live/zyrardow.poleca.to/fullchain.pem" ]; then
    CERT_EXPIRY=$(openssl x509 -enddate -noout -in /etc/letsencrypt/live/zyrardow.poleca.to/fullchain.pem | cut -d= -f2)
    echo "✅ SSL: certyfikat istnieje (wygasa: $CERT_EXPIRY)"
else
    echo "❌ SSL: brak certyfikatu"
    ((ERRORS++))
fi

# Sprawdzenie konfiguracji Nginx
echo -e "\n📋 Sprawdzanie konfiguracji Nginx..."
if nginx -t > /dev/null 2>&1; then
    echo "✅ Nginx: konfiguracja poprawna"
else
    echo "❌ Nginx: błąd w konfiguracji"
    ((ERRORS++))
fi

# Sprawdzenie health check
echo -e "\n📋 Sprawdzanie health check..."
if curl -f -s http://localhost:3000/api/health > /dev/null; then
    echo "✅ Health check: OK"
else
    echo "❌ Health check: nieudany"
    ((ERRORS++))
fi

# Sprawdzenie backupów
echo -e "\n📋 Sprawdzanie systemu backupów..."
if [ -x "/usr/local/bin/backup-zyrardow.sh" ]; then
    echo "✅ Backup: skrypt istnieje"
else
    echo "❌ Backup: brak skryptu"
    ((ERRORS++))
fi

# Sprawdzenie logów
echo -e "\n📋 Sprawdzanie logów..."
if [ -d "/var/log/zyrardow-poleca" ]; then
    echo "✅ Logi: katalog istnieje"
else
    echo "❌ Logi: brak katalogu"
    ((ERRORS++))
fi

# Sprawdzenie uprawnień
echo -e "\n📋 Sprawdzanie uprawnień..."
if [ "$(stat -c %U /var/www/zyrardow-poleca/app)" = "zyrardow" ]; then
    echo "✅ Uprawnienia: poprawne"
else
    echo "❌ Uprawnienia: niepoprawne"
    ((ERRORS++))
fi

# Sprawdzenie firewall
echo -e "\n📋 Sprawdzanie firewall..."
if ufw status | grep -q "Status: active"; then
    echo "✅ UFW: aktywny"
else
    echo "❌ UFW: nieaktywny"
    ((ERRORS++))
fi

# Podsumowanie
echo -e "\n============================================================"
if [ $ERRORS -eq 0 ]; then
    echo "🎉 GOTOWE! System jest gotowy do produkcji."
    echo "🌐 Strona: https://zyrardow.poleca.to"
    echo "🔧 Panel admin: https://zyrardow.poleca.to/admin"
else
    echo "❌ Znaleziono $ERRORS problemów. Napraw je przed uruchomieniem produkcji."
    exit 1
fi
```

```bash
# Nadanie uprawnień wykonywania
sudo chmod +x /usr/local/bin/production-check.sh

# Uruchomienie sprawdzenia
sudo /usr/local/bin/production-check.sh
```

### 12.2 Testy funkcjonalne

```bash
# Test wszystkich endpointów
echo "🧪 Testowanie endpointów API..."

# Health check
curl -s http://localhost:3000/api/health | jq .

# Info endpoint
curl -s http://localhost:3000/api/info | jq .

# Test HTTPS
curl -I https://zyrardow.poleca.to

# Test przekierowania HTTP -> HTTPS
curl -I http://zyrardow.poleca.to
```

## Faza 13: Dokumentacja Operacyjna

### 13.1 Przydatne komendy

```bash
# === ZARZĄDZANIE APLIKACJĄ ===

# Status aplikacji
sudo -u zyrardow pm2 status
sudo -u zyrardow pm2 monit

# Logi aplikacji
sudo -u zyrardow pm2 logs
sudo -u zyrardow pm2 logs --lines 100

# Restart aplikacji
sudo -u zyrardow pm2 restart zyrardow-poleca

# Reload aplikacji (zero-downtime)
sudo -u zyrardow pm2 reload zyrardow-poleca

# === ZARZĄDZANIE BAZĄ DANYCH ===

# Połączenie z bazą danych
mysql -u zyrardow_admin -p zyrardow_poleca_db

# Backup ręczny
sudo /usr/local/bin/backup-zyrardow.sh

# Przywracanie z backup
sudo /usr/local/bin/restore-zyrardow.sh YYYYMMDD_HHMMSS

# === ZARZĄDZANIE NGINX ===

# Test konfiguracji
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx

# Reload Nginx (bez przerwy w działaniu)
sudo systemctl reload nginx

# Logi Nginx
sudo tail -f /var/log/nginx/zyrardow-poleca.access.log
sudo tail -f /var/log/nginx/zyrardow-poleca.error.log

# === ZARZĄDZANIE SSL ===

# Status certyfikatów
sudo certbot certificates

# Ręczne odnowienie
sudo certbot renew

# Test odnowienia
sudo certbot renew --dry-run

# === MONITOROWANIE ===

# Status wszystkich usług
sudo systemctl status mysql nginx fail2ban

# Wykorzystanie zasobów
htop
df -h
free -h

# Logi systemowe
sudo journalctl -u nginx -f
sudo journalctl -u mysql -f

# === AKTUALIZACJE ===

# Aktualizacja aplikacji
sudo /usr/local/bin/update-zyrardow.sh

# Sprawdzenie gotowości
sudo /usr/local/bin/production-check.sh

# Monitorowanie systemu
sudo /usr/local/bin/monitor-zyrardow.sh
```

### 13.2 Rozwiązywanie problemów

```bash
# === TYPOWE PROBLEMY ===

# Problem: Aplikacja nie startuje
# Rozwiązanie:
sudo -u zyrardow pm2 logs  # Sprawdź logi
sudo -u zyrardow pm2 restart zyrardow-poleca

# Problem: Błąd połączenia z bazą danych
# Rozwiązanie:
sudo systemctl status mysql
mysql -u zyrardow_admin -p zyrardow_poleca_db  # Test połączenia

# Problem: Nginx zwraca 502 Bad Gateway
# Rozwiązanie:
sudo -u zyrardow pm2 status  # Sprawdź czy aplikacja działa
sudo nginx -t  # Sprawdź konfigurację
sudo systemctl restart nginx

# Problem: SSL nie działa
# Rozwiązanie:
sudo certbot certificates  # Sprawdź certyfikaty
sudo certbot renew  # Odnów certyfikaty
sudo systemctl reload nginx

# Problem: Brak miejsca na dysku
# Rozwiązanie:
df -h  # Sprawdź wykorzystanie
sudo find /var/log -name "*.log" -mtime +30 -delete  # Usuń stare logi
sudo apt autoremove  # Usuń niepotrzebne pakiety
```

## 🎯 Podsumowanie Wdrożenia

### Struktura Systemu

```
/var/www/zyrardow-poleca/
├── app/                          # Aplikacja
│   ├── server/                   # Backend Node.js
│   ├── admin/                    # Panel administracyjny
│   ├── public/                   # Strona główna
│   ├── .env                      # Konfiguracja
│   └── ecosystem.config.js       # Konfiguracja PM2

/var/log/zyrardow-poleca/         # Logi aplikacji
/var/backups/zyrardow-poleca/     # Backupy
/usr/local/bin/                   # Skrypty zarządzania
├── backup-zyrardow.sh
├── restore-zyrardow.sh
├── update-zyrardow.sh
├── monitor-zyrardow.sh
└── production-check.sh
```

### Dostępy

- **Strona główna**: https://zyrardow.poleca.to
- **Panel admin**: https://zyrardow.poleca.to/admin
- **API**: https://zyrardow.poleca.to/api/
- **Health check**: https://zyrardow.poleca.to/api/health

### Dane logowania

- **Email**: <EMAIL>
- **Hasło**: (ustawione w .env)

### ⚠️ WAŻNE - Po wdrożeniu

1. **Zmień hasła**:
   - Hasło administratora aplikacji
   - Hasło użytkownika MySQL
   - JWT Secret

2. **Skonfiguruj DNS**:
   - Ustaw rekord A dla zyrardow.poleca.to
   - Ustaw rekord CNAME dla www.zyrardow.poleca.to

3. **Sprawdź backupy**:
   - Uruchom test backup
   - Sprawdź czy cron działa

4. **Monitorowanie**:
   - Sprawdź czy wszystkie usługi działają
   - Skonfiguruj alerty email (opcjonalnie)

### 🚀 Gotowe do produkcji!

System jest teraz w pełni skonfigurowany i gotowy do użycia w środowisku produkcyjnym.
