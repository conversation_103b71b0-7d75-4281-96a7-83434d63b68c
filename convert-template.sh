#!/bin/bash

# Funkcja do konwersji pliku HTML, aby korzystał z szablonów
convert_file() {
    local file=$1
    echo "Konwersja pliku: $file"
    
    # Tworzenie kopii zapasowej
    cp "$file" "${file}.bak"
    
    # Zamiana nagłówka na kontener
    sed -i '' '/<header class="site-header">/,/<\/header>/c\
    <!-- Kontener na nagłówek - zostanie wypełniony przez templates.js -->\
    <div id="header"></div>' "$file"
    
    # Zamiana stopki na kontener
    sed -i '' '/<footer class="site-footer">/,/<\/footer>/c\
    <!-- Kontener na stopkę - zostanie wypełniony przez templates.js -->\
    <div id="footer"></div>' "$file"
    
    # Usunięcie powiadomienia o cookies
    sed -i '' '/<div class="cookie-notice"/,/<\/div>/d' "$file"
    
    # Usunięcie przycisku "powrót do góry"
    sed -i '' '/<a href="#" class="back-to-top"/,/<\/a>/d' "$file"
    
    # Dodanie odniesienia do templates.js
    if ! grep -q 'templates.js' "$file"; then
        sed -i '' '/<script src="js\//i\
    <script src="js/templates.js"></script>' "$file"
    fi
    
    echo "Plik $file został przekonwertowany"
}

# Konwersja pliku kupony.html
convert_file "kupony.html"

echo "Konwersja zakończona pomyślnie!"
