#!/bin/bash

# Tworzenie kopii zapasowej
cp historia.html historia.html.bak

# Zamiana nagłówka na kontener
sed -i '' '/<header class="site-header">/,/<\/header>/c\
    <!-- Kontener na nagłówek - zostanie wypełniony przez templates.js -->\
    <div id="header"></div>' historia.html

# Zamiana stopki na kontener
sed -i '' '/<footer class="site-footer">/,/<\/footer>/c\
    <!-- Kontener na stopkę - zostanie wypełniony przez templates.js -->\
    <div id="footer"></div>' historia.html

# Usunięcie powiadomienia o cookies
sed -i '' '/<div class="cookie-notice"/,/<\/div>/d' historia.html

# Usunięcie przycisku "powrót do góry"
sed -i '' '/<a href="#" class="back-to-top"/,/<\/a>/d' historia.html

# Dodanie odniesienia do templates.js
sed -i '' '/<script src="https:\/\/cdnjs.cloudflare.com\/ajax\/libs\/jquery\/3.6.0\/jquery.min.js"><\/script>/a\
    <script src="js/templates.js"></script>' historia.html

echo "Plik historia.html został przekonwertowany"
