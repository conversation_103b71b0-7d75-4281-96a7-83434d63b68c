module.exports = {
  apps: [{
    name: 'zyrardow-poleca',
    script: './server/app.js',
    instances: 2,
    exec_mode: 'cluster',
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/zyrardow-poleca/error.log',
    out_file: '/var/log/zyrardow-poleca/out.log',
    log_file: '/var/log/zyrardow-poleca/combined.log',
    time: true,
    cron_restart: '0 2 * * *',
    max_restarts: 10,
    min_uptime: '10s'
  }]
};