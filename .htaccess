# W<PERSON><PERSON><PERSON> moduł rewrite
RewriteEngine On

# Przekieruj z HTTP na HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Przekieruj z www na bez www
RewriteCond %{HTTP_HOST} ^www\.(.+)$ [NC]
RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# Włącz kompresję GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Włącz cache dla statycznych plików
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# Zabezpieczenia
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Frame-Options "DENY"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" env=HTTPS

    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://unpkg.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.openweathermap.org; frame-src 'none'; object-src 'none';"
</IfModule>

# Blokuj dostęp do ukrytych plików
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# Blokuj dostęp do plików systemowych
<FilesMatch "^(\.htaccess|\.htpasswd|\.git|\.env|composer\.json|composer\.lock|package\.json|package-lock\.json)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Dodatkowe zabezpieczenia plików
<FilesMatch "\.(ini|log|sh|sql|conf|bak|backup|old|tmp)$">
    Require all denied
</FilesMatch>

# Chroń katalogi admin (odkomentuj dla produkcji)
# <Directory "admin">
#     AuthType Basic
#     AuthName "Admin Panel - Żyrardów Poleca"
#     AuthUserFile /path/to/.htpasswd
#     Require valid-user
# </Directory>

# Zapobiegaj wykonywaniu PHP w katalogach uploads
<Directory "images">
    <FilesMatch "\.php$">
        Require all denied
    </FilesMatch>
</Directory>

# Wyłącz autoindex
Options -Indexes

# Domyślny charset
AddDefaultCharset UTF-8

# Strony błędów
ErrorDocument 404 /404.html
ErrorDocument 403 /403.html
ErrorDocument 500 /500.html

# Rate limiting (wymaga mod_evasive)
# <IfModule mod_evasive24.c>
#     DOSHashTableSize    2048
#     DOSPageCount        10
#     DOSPageInterval     1
#     DOSSiteCount        50
#     DOSSiteInterval     1
#     DOSBlockingPeriod   600
# </IfModule>