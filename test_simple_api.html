<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prosty Test API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Prosty Test API</h1>
    
    <div class="test">
        <h2>Test 1: TOP Firmy</h2>
        <button onclick="testTop()">Test TOP</button>
        <div id="topResult" class="result"></div>
    </div>
    
    <div class="test">
        <h2>Test 2: Wszystkie Firmy</h2>
        <button onclick="testAll()">Test ALL</button>
        <div id="allResult" class="result"></div>
    </div>
    
    <div class="test">
        <h2>Test 3: Kategorie</h2>
        <button onclick="testCategories()">Test Categories</button>
        <div id="categoriesResult" class="result"></div>
    </div>
    
    <div class="test">
        <h2>Test 4: Logowanie</h2>
        <button onclick="testLogin()">Test Login</button>
        <div id="loginResult" class="result"></div>
    </div>

    <script>
        async function testTop() {
            const result = document.getElementById('topResult');
            try {
                const response = await fetch('admin/api/companies.php?path=top');
                const text = await response.text();
                
                result.className = 'result success';
                result.innerHTML = `
                    <strong>Status:</strong> ${response.status}<br>
                    <strong>Content-Type:</strong> ${response.headers.get('content-type')}<br>
                    <strong>Odpowiedź:</strong><br>
                    <pre>${text}</pre>
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<strong>Błąd:</strong> ${error.message}`;
            }
        }
        
        async function testAll() {
            const result = document.getElementById('allResult');
            try {
                const response = await fetch('admin/api/companies.php?path=all');
                const text = await response.text();
                
                result.className = 'result success';
                result.innerHTML = `
                    <strong>Status:</strong> ${response.status}<br>
                    <strong>Content-Type:</strong> ${response.headers.get('content-type')}<br>
                    <strong>Odpowiedź:</strong><br>
                    <pre>${text}</pre>
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<strong>Błąd:</strong> ${error.message}`;
            }
        }
        
        async function testCategories() {
            const result = document.getElementById('categoriesResult');
            try {
                const response = await fetch('admin/api/companies.php?path=categories');
                const text = await response.text();
                
                result.className = 'result success';
                result.innerHTML = `
                    <strong>Status:</strong> ${response.status}<br>
                    <strong>Content-Type:</strong> ${response.headers.get('content-type')}<br>
                    <strong>Odpowiedź:</strong><br>
                    <pre>${text}</pre>
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<strong>Błąd:</strong> ${error.message}`;
            }
        }
        
        async function testLogin() {
            const result = document.getElementById('loginResult');
            try {
                const response = await fetch('admin/api/auth.php?path=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                const text = await response.text();
                
                result.className = 'result success';
                result.innerHTML = `
                    <strong>Status:</strong> ${response.status}<br>
                    <strong>Content-Type:</strong> ${response.headers.get('content-type')}<br>
                    <strong>Odpowiedź:</strong><br>
                    <pre>${text}</pre>
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<strong>Błąd:</strong> ${error.message}`;
            }
        }
    </script>
</body>
</html>
