<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class Company extends Model
{
    use HasFactory, HasSlug;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'short_description',
        'category_id',
        'address',
        'phone',
        'email',
        'website',
        'facebook',
        'instagram',
        'opening_hours',
        'logo',
        'images',
        'is_top',
        'top_position',
        'is_active',
        'views_count',
        'rating',
        'reviews_count'
    ];

    protected $casts = [
        'is_top' => 'boolean',
        'is_active' => 'boolean',
        'views_count' => 'integer',
        'reviews_count' => 'integer',
        'rating' => 'decimal:1',
        'top_position' => 'integer',
        'images' => 'array',
        'opening_hours' => 'array'
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Relacja z kategorią
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Relacja z kuponami
     */
    public function coupons()
    {
        return $this->hasMany(Coupon::class);
    }

    /**
     * Scope dla aktywnych firm
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope dla firm TOP
     */
    public function scopeTop($query)
    {
        return $query->where('is_top', true)->orderBy('top_position');
    }

    /**
     * Scope dla wyszukiwania
     */
    public function scopeSearch($query, $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('name', 'LIKE', "%{$term}%")
              ->orWhere('description', 'LIKE', "%{$term}%")
              ->orWhere('short_description', 'LIKE', "%{$term}%");
        });
    }

    /**
     * Scope dla sortowania według popularności
     */
    public function scopePopular($query)
    {
        return $query->orderBy('views_count', 'desc');
    }

    /**
     * Scope dla sortowania według oceny
     */
    public function scopeByRating($query)
    {
        return $query->orderBy('rating', 'desc');
    }

    /**
     * Pobierz aktywne kupony
     */
    public function getActiveCoupons()
    {
        return $this->coupons()
            ->where('is_active', true)
            ->where('valid_from', '<=', now())
            ->where('valid_to', '>=', now())
            ->get();
    }

    /**
     * Zwiększ licznik wyświetleń
     */
    public function incrementViews()
    {
        $this->increment('views_count');
    }

    /**
     * Sprawdź czy firma ma logo
     */
    public function hasLogo(): bool
    {
        return !empty($this->logo);
    }

    /**
     * Pobierz URL logo
     */
    public function getLogoUrl(): ?string
    {
        return $this->logo ? asset('storage/' . $this->logo) : null;
    }

    /**
     * Pobierz pierwsze zdjęcie
     */
    public function getFirstImage(): ?string
    {
        if (empty($this->images) || !is_array($this->images)) {
            return null;
        }

        return asset('storage/' . $this->images[0]);
    }

    /**
     * Pobierz wszystkie zdjęcia jako URLs
     */
    public function getImageUrls(): array
    {
        if (empty($this->images) || !is_array($this->images)) {
            return [];
        }

        return array_map(function ($image) {
            return asset('storage/' . $image);
        }, $this->images);
    }

    /**
     * Formatuj godziny otwarcia
     */
    public function getFormattedOpeningHours(): array
    {
        if (empty($this->opening_hours) || !is_array($this->opening_hours)) {
            return [];
        }

        $days = [
            'monday' => 'Poniedziałek',
            'tuesday' => 'Wtorek', 
            'wednesday' => 'Środa',
            'thursday' => 'Czwartek',
            'friday' => 'Piątek',
            'saturday' => 'Sobota',
            'sunday' => 'Niedziela'
        ];

        $formatted = [];
        foreach ($days as $key => $name) {
            if (isset($this->opening_hours[$key])) {
                $formatted[] = [
                    'day' => $name,
                    'hours' => $this->opening_hours[$key]
                ];
            }
        }

        return $formatted;
    }

    /**
     * Sprawdź czy firma jest obecnie otwarta
     */
    public function isOpenNow(): bool
    {
        if (empty($this->opening_hours) || !is_array($this->opening_hours)) {
            return false;
        }

        $currentDay = strtolower(now()->format('l'));
        $currentTime = now()->format('H:i');

        if (!isset($this->opening_hours[$currentDay])) {
            return false;
        }

        $hours = $this->opening_hours[$currentDay];
        if ($hours === 'closed' || empty($hours)) {
            return false;
        }

        // Format: "09:00-17:00"
        if (preg_match('/(\d{2}:\d{2})-(\d{2}:\d{2})/', $hours, $matches)) {
            $openTime = $matches[1];
            $closeTime = $matches[2];
            
            return $currentTime >= $openTime && $currentTime <= $closeTime;
        }

        return false;
    }
}
