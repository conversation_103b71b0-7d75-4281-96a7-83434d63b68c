<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Coupon extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'title',
        'description',
        'code',
        'discount_type',
        'discount_value',
        'min_order_value',
        'max_uses',
        'used_count',
        'valid_from',
        'valid_to',
        'is_active',
        'terms_conditions'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'discount_value' => 'decimal:2',
        'min_order_value' => 'decimal:2',
        'max_uses' => 'integer',
        'used_count' => 'integer',
        'valid_from' => 'datetime',
        'valid_to' => 'datetime'
    ];

    /**
     * Typy rabatów
     */
    const DISCOUNT_TYPES = [
        'percentage' => 'Procent',
        'fixed' => 'Kwota stała',
        'free_shipping' => 'Darmowa dostawa'
    ];

    /**
     * Relacja z firmą
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Scope dla aktywnych kuponów
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope dla ważnych kuponów (w terminie)
     */
    public function scopeValid($query)
    {
        return $query->where('valid_from', '<=', now())
                    ->where('valid_to', '>=', now());
    }

    /**
     * Scope dla dostępnych kuponów (nie wyczerpane)
     */
    public function scopeAvailable($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('max_uses')
              ->orWhereRaw('used_count < max_uses');
        });
    }

    /**
     * Scope dla kuponów według typu rabatu
     */
    public function scopeByDiscountType($query, $type)
    {
        return $query->where('discount_type', $type);
    }

    /**
     * Sprawdź czy kupon jest ważny
     */
    public function isValid(): bool
    {
        return $this->is_active 
            && $this->valid_from <= now() 
            && $this->valid_to >= now()
            && $this->isAvailable();
    }

    /**
     * Sprawdź czy kupon jest dostępny (nie wyczerpany)
     */
    public function isAvailable(): bool
    {
        if (is_null($this->max_uses)) {
            return true;
        }

        return $this->used_count < $this->max_uses;
    }

    /**
     * Sprawdź czy kupon wygasł
     */
    public function isExpired(): bool
    {
        return $this->valid_to < now();
    }

    /**
     * Sprawdź czy kupon jeszcze nie jest aktywny
     */
    public function isNotYetActive(): bool
    {
        return $this->valid_from > now();
    }

    /**
     * Użyj kuponu (zwiększ licznik)
     */
    public function use(): bool
    {
        if (!$this->isValid()) {
            return false;
        }

        $this->increment('used_count');
        return true;
    }

    /**
     * Pobierz sformatowaną wartość rabatu
     */
    public function getFormattedDiscount(): string
    {
        switch ($this->discount_type) {
            case 'percentage':
                return $this->discount_value . '%';
            case 'fixed':
                return number_format($this->discount_value, 2) . ' zł';
            case 'free_shipping':
                return 'Darmowa dostawa';
            default:
                return '';
        }
    }

    /**
     * Pobierz nazwę typu rabatu
     */
    public function getDiscountTypeName(): string
    {
        return self::DISCOUNT_TYPES[$this->discount_type] ?? '';
    }

    /**
     * Pobierz status kuponu
     */
    public function getStatus(): string
    {
        if (!$this->is_active) {
            return 'Nieaktywny';
        }

        if ($this->isNotYetActive()) {
            return 'Oczekuje na aktywację';
        }

        if ($this->isExpired()) {
            return 'Wygasł';
        }

        if (!$this->isAvailable()) {
            return 'Wyczerpany';
        }

        return 'Aktywny';
    }

    /**
     * Pobierz kolor statusu dla UI
     */
    public function getStatusColor(): string
    {
        $status = $this->getStatus();
        
        switch ($status) {
            case 'Aktywny':
                return 'green';
            case 'Oczekuje na aktywację':
                return 'yellow';
            case 'Wygasł':
            case 'Wyczerpany':
            case 'Nieaktywny':
                return 'red';
            default:
                return 'gray';
        }
    }

    /**
     * Generuj unikalny kod kuponu
     */
    public static function generateUniqueCode($length = 8): string
    {
        do {
            $code = strtoupper(Str::random($length));
        } while (self::where('code', $code)->exists());

        return $code;
    }

    /**
     * Pobierz pozostałe użycia
     */
    public function getRemainingUses(): ?int
    {
        if (is_null($this->max_uses)) {
            return null;
        }

        return max(0, $this->max_uses - $this->used_count);
    }

    /**
     * Pobierz procent wykorzystania
     */
    public function getUsagePercentage(): float
    {
        if (is_null($this->max_uses) || $this->max_uses == 0) {
            return 0;
        }

        return ($this->used_count / $this->max_uses) * 100;
    }

    /**
     * Sprawdź czy kupon można edytować
     */
    public function canBeEdited(): bool
    {
        return $this->used_count == 0;
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($coupon) {
            if (empty($coupon->code)) {
                $coupon->code = self::generateUniqueCode();
            }
        });
    }
}
