# 🚀 Wdrożenie Portalu Żyrardów.poleca.to

## Przegląd

Ten dokument zawiera kompletne instrukcje wdrożenia portalu **Żyrardów.poleca.to** na serwerze VPS z systemem Ubuntu. Portal składa się z:

- **Frontend**: Statyczne pliki HTML/CSS/JS
- **Backend**: Node.js + Express.js
- **Baza danych**: MySQL
- **Serwer WWW**: Nginx (reverse proxy)
- **Process Manager**: PM2
- **SSL**: Let's Encrypt

## 🎯 Szybkie Wdrożenie (Automatyczne)

### Wymagania

- **VPS** z Ubuntu 20.04 LTS lub nowszym
- **RAM**: minimum 2GB (zalecane 4GB)
- **CPU**: 2 vCPU
- **Dysk**: 20GB SSD
- **Dostęp**: SSH z uprawnieniami sudo
- **Domena**: skonfigurowana i wskazująca na serwer

### Kroki wdrożenia

1. **Połącz się z serwerem VPS**:
   ```bash
   ssh user@your-server-ip
   ```

2. **Sklonuj repozytorium**:
   ```bash
   git clone https://github.com/your-repo/zyrardow-poleca.git
   cd zyrardow-poleca
   ```

3. **Uruchom automatyczny skrypt wdrożenia**:
   ```bash
   ./deploy.sh
   ```

4. **Skonfiguruj DNS** (jeśli jeszcze nie zrobiłeś):
   - Ustaw rekord A dla `zyrardow.poleca.to` wskazujący na IP serwera
   - Ustaw rekord CNAME dla `www.zyrardow.poleca.to` wskazujący na `zyrardow.poleca.to`

5. **Gotowe!** 🎉
   - Strona: https://zyrardow.poleca.to
   - Panel admin: https://zyrardow.poleca.to/admin

### Konfiguracja zmiennych środowiskowych (opcjonalna)

Przed uruchomieniem skryptu możesz ustawić własne wartości:

```bash
export DOMAIN="twoja-domena.pl"
export ADMIN_EMAIL="<EMAIL>"
export DB_NAME="nazwa_bazy"
export DB_USER="uzytkownik_bazy"
export DB_PASS="haslo_bazy"
./deploy.sh
```

## 📋 Wdrożenie Manualne (Krok po kroku)

Jeśli wolisz kontrolować każdy krok wdrożenia, skorzystaj z szczegółowego planu:

👉 **[DEPLOYMENT_PLAN.md](./DEPLOYMENT_PLAN.md)** - Kompletny plan wdrożenia

## 🔧 Zarządzanie Aplikacją

### Podstawowe komendy

```bash
# Status aplikacji
sudo -u zyrardow pm2 status

# Logi aplikacji
sudo -u zyrardow pm2 logs

# Restart aplikacji
sudo -u zyrardow pm2 restart zyrardow-poleca

# Reload aplikacji (zero-downtime)
sudo -u zyrardow pm2 reload zyrardow-poleca

# Monitorowanie zasobów
sudo -u zyrardow pm2 monit
```

### Zarządzanie bazą danych

```bash
# Połączenie z bazą danych
mysql -u zyrardow_admin -p zyrardow_poleca_db

# Backup ręczny
sudo /usr/local/bin/backup-zyrardow.sh

# Przywracanie z backup
sudo /usr/local/bin/restore-zyrardow.sh YYYYMMDD_HHMMSS
```

### Zarządzanie Nginx

```bash
# Test konfiguracji
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx

# Reload Nginx (bez przerwy)
sudo systemctl reload nginx

# Logi Nginx
sudo tail -f /var/log/nginx/zyrardow-poleca.access.log
sudo tail -f /var/log/nginx/zyrardow-poleca.error.log
```

### Zarządzanie SSL

```bash
# Status certyfikatów
sudo certbot certificates

# Ręczne odnowienie
sudo certbot renew

# Test odnowienia
sudo certbot renew --dry-run
```

## 🔍 Monitorowanie

### Sprawdzenie statusu systemu

```bash
# Sprawdzenie wszystkich usług
sudo systemctl status mysql nginx fail2ban

# Wykorzystanie zasobów
htop
df -h
free -h

# Sprawdzenie gotowości do produkcji
sudo /usr/local/bin/production-check.sh

# Monitorowanie systemu
sudo /usr/local/bin/monitor-zyrardow.sh
```

### Logi systemowe

```bash
# Logi aplikacji
sudo tail -f /var/log/zyrardow-poleca/combined.log

# Logi systemowe
sudo journalctl -u nginx -f
sudo journalctl -u mysql -f

# Logi fail2ban
sudo fail2ban-client status
```

## 🔄 Aktualizacje

### Automatyczna aktualizacja

```bash
# Aktualizacja aplikacji z repozytorium
sudo /usr/local/bin/update-zyrardow.sh
```

### Manualna aktualizacja

```bash
cd /var/www/zyrardow-poleca/app

# Backup przed aktualizacją
sudo /usr/local/bin/backup-zyrardow.sh

# Pobranie zmian
sudo -u zyrardow git pull origin main

# Aktualizacja zależności
sudo -u zyrardow npm install --only=production

# Restart aplikacji
sudo -u zyrardow pm2 restart zyrardow-poleca
```

## 🛡️ Bezpieczeństwo

### Zalecenia po wdrożeniu

1. **Zmień hasła**:
   - Hasło administratora aplikacji
   - Hasło użytkownika MySQL
   - Wygeneruj nowy JWT Secret

2. **Konfiguracja SSH**:
   ```bash
   # Wyłącz logowanie hasłem (po skonfigurowaniu kluczy SSH)
   sudo nano /etc/ssh/sshd_config
   # PasswordAuthentication no
   sudo systemctl restart sshd
   ```

3. **Regularne aktualizacje**:
   ```bash
   # Aktualizacja systemu
   sudo apt update && sudo apt upgrade -y
   
   # Aktualizacja aplikacji
   sudo /usr/local/bin/update-zyrardow.sh
   ```

4. **Monitorowanie logów**:
   ```bash
   # Sprawdzanie prób włamania
   sudo fail2ban-client status
   
   # Analiza logów dostępu
   sudo tail -f /var/log/nginx/zyrardow-poleca.access.log
   ```

## 📦 Backupy

### Automatyczne backupy

Backupy są tworzone automatycznie codziennie o 2:00:
- Baza danych (MySQL dump)
- Pliki uploads
- Konfiguracja

### Ręczne backupy

```bash
# Utworzenie backup
sudo /usr/local/bin/backup-zyrardow.sh

# Lista dostępnych backupów
ls -la /var/backups/zyrardow-poleca/

# Przywracanie z backup
sudo /usr/local/bin/restore-zyrardow.sh 20241201_020000
```

## 🚨 Rozwiązywanie Problemów

### Aplikacja nie działa

```bash
# Sprawdź status PM2
sudo -u zyrardow pm2 status

# Sprawdź logi
sudo -u zyrardow pm2 logs

# Restart aplikacji
sudo -u zyrardow pm2 restart zyrardow-poleca
```

### Błąd 502 Bad Gateway

```bash
# Sprawdź czy aplikacja działa
sudo -u zyrardow pm2 status

# Sprawdź konfigurację Nginx
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

### Problemy z bazą danych

```bash
# Sprawdź status MySQL
sudo systemctl status mysql

# Test połączenia
mysql -u zyrardow_admin -p zyrardow_poleca_db

# Restart MySQL
sudo systemctl restart mysql
```

### Problemy z SSL

```bash
# Sprawdź certyfikaty
sudo certbot certificates

# Odnów certyfikaty
sudo certbot renew

# Restart Nginx po odnowieniu
sudo systemctl reload nginx
```

## 📁 Struktura Plików

```
/var/www/zyrardow-poleca/
├── app/                          # Aplikacja
│   ├── server/                   # Backend Node.js
│   │   ├── app.js               # Główny plik aplikacji
│   │   ├── config/              # Konfiguracja
│   │   ├── controllers/         # Kontrolery API
│   │   ├── models/              # Modele bazy danych
│   │   ├── routes/              # Routing API
│   │   ├── middleware/          # Middleware
│   │   ├── scripts/             # Skrypty pomocnicze
│   │   └── uploads/             # Pliki przesłane przez użytkowników
│   ├── admin/                   # Panel administracyjny
│   ├── public/                  # Strona główna (statyczne pliki)
│   ├── .env                     # Konfiguracja środowiskowa
│   ├── package.json             # Zależności npm
│   └── ecosystem.config.js      # Konfiguracja PM2

/var/log/zyrardow-poleca/         # Logi aplikacji
├── error.log                    # Logi błędów
├── out.log                      # Logi wyjścia
├── combined.log                 # Wszystkie logi
└── monitor.log                  # Logi monitorowania

/var/backups/zyrardow-poleca/     # Backupy
├── db_backup_*.sql.gz           # Backupy bazy danych
├── uploads_backup_*.tar.gz      # Backupy plików
└── config_backup_*.tar.gz       # Backupy konfiguracji

/usr/local/bin/                   # Skrypty zarządzania
├── backup-zyrardow.sh           # Skrypt backup
├── restore-zyrardow.sh          # Skrypt przywracania
├── update-zyrardow.sh           # Skrypt aktualizacji
├── monitor-zyrardow.sh          # Skrypt monitorowania
└── production-check.sh          # Sprawdzenie gotowości
```

## 🌐 Dostępy

Po pomyślnym wdrożeniu:

- **Strona główna**: https://zyrardow.poleca.to
- **Panel administracyjny**: https://zyrardow.poleca.to/admin
- **API**: https://zyrardow.poleca.to/api/
- **Health check**: https://zyrardow.poleca.to/api/health

## 📞 Wsparcie

W przypadku problemów:

1. Sprawdź logi aplikacji: `sudo -u zyrardow pm2 logs`
2. Uruchom diagnostykę: `sudo /usr/local/bin/production-check.sh`
3. Sprawdź dokumentację: [DEPLOYMENT_PLAN.md](./DEPLOYMENT_PLAN.md)

## 📝 Changelog

### v1.0.0 (2024-12-01)
- Pierwsza wersja systemu wdrożeniowego
- Automatyczny skrypt wdrożenia
- Kompletna dokumentacja
- System backupów i monitorowania

---

**🎉 Powodzenia z wdrożeniem!**

Jeśli wszystko przebiegło pomyślnie, Twój portal Żyrardów.poleca.to jest teraz gotowy do użycia w środowisku produkcyjnym.
