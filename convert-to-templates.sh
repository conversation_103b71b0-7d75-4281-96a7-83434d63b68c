#!/bin/bash

# Skrypt do konwersji wszystkich plików HTML, aby korzystały z szablonów

# Lista plików HTML do przetworzenia (z wyjątkiem plików w folderze admin)
HTML_FILES=$(find . -name "*.html" -not -path "./admin/*" -not -path "./templates/*")

# Funkcja do dodawania kontenera nagłówka
add_header_container() {
    local file=$1
    # Szukaj znacznika <body> i dodaj kontener nagłówka po nim
    sed -i '/<body>/a \    <!-- Kontener na nagłówek - zostanie wypełniony przez templates.js -->\n    <div id="header"></div>' "$file"
}

# Funkcja do dodawania kontenera stopki
add_footer_container() {
    local file=$1
    # Szukaj znacznika </main> i dodaj kontener stopki po nim
    sed -i '/<\/main>/a \    <!-- <PERSON>nte<PERSON> na stopkę - zostanie wypełniony przez templates.js -->\n    <div id="footer"></div>' "$file"
}

# Funkcja do usuwania istniejącego nagłówka
remove_header() {
    local file=$1
    # Usuń istniejący nagłówek (od <header class="site-header"> do </header>)
    sed -i '/<header class="site-header">/,/<\/header>/d' "$file"
}

# Funkcja do usuwania istniejącej stopki
remove_footer() {
    local file=$1
    # Usuń istniejącą stopkę (od <footer class="site-footer"> do </footer>)
    sed -i '/<footer class="site-footer">/,/<\/footer>/d' "$file"
    
    # Usuń również powiadomienie o cookies i przycisk "powrót do góry"
    sed -i '/<div class="cookie-notice"/,/<\/div>/d' "$file"
    sed -i '/<a href="#" class="back-to-top"/,/<\/a>/d' "$file"
}

# Funkcja do dodawania odniesienia do pliku templates.js
add_templates_js() {
    local file=$1
    # Sprawdź, czy odniesienie do templates.js już istnieje
    if ! grep -q 'templates.js' "$file"; then
        # Dodaj odniesienie do templates.js przed pierwszym skryptem
        sed -i '/<script src="js\//i \    <script src="js/templates.js"></script>' "$file"
    fi
}

# Przetwórz każdy plik HTML
for file in $HTML_FILES; do
    echo "Przetwarzanie pliku: $file"
    
    # Usuń istniejący nagłówek i stopkę
    remove_header "$file"
    remove_footer "$file"
    
    # Dodaj kontenery nagłówka i stopki
    add_header_container "$file"
    add_footer_container "$file"
    
    # Dodaj odniesienie do pliku templates.js
    add_templates_js "$file"
    
    echo "Plik $file został przetworzony"
done

echo "Konwersja zakończona pomyślnie!"
