<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API - TOP Firmy</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>Test API - System TOP Firm</h1>

    <div class="test-section">
        <h2>Test 1: Pobieranie TOP firm (powinno być puste)</h2>
        <button onclick="testTopCompanies()">Testuj TOP firmy</button>
        <div id="topResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Pobieranie wszystkich firm (powinno być puste)</h2>
        <button onclick="testAllCompanies()">Testuj wszystkie firmy</button>
        <div id="allResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Pobieranie kategorii</h2>
        <button onclick="testCategories()">Testuj kategorie</button>
        <div id="categoriesResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 4: Dodawanie nowej firmy</h2>
        <button onclick="testCreateCompany()">Dodaj przykładową firmę</button>
        <div id="createResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 5: Ustawianie pozycji TOP</h2>
        <button onclick="testSetTopPosition()">Ustaw pozycję TOP 1</button>
        <div id="topPositionResult" class="result"></div>
    </div>

    <script>
        async function testTopCompanies() {
            const result = document.getElementById('topResult');
            try {
                const response = await fetch('admin/api/companies.php?path=top');

                // Sprawdź czy odpowiedź jest OK
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                // Sprawdź content-type
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const text = await response.text();
                    throw new Error(`Oczekiwano JSON, otrzymano: ${contentType}. Odpowiedź: ${text.substring(0, 500)}...`);
                }

                const data = await response.json();

                result.className = 'result ' + (data.success ? 'success' : 'error');
                result.innerHTML = `
                    <strong>Status:</strong> ${data.success ? 'Sukces' : 'Błąd'}<br>
                    <strong>Liczba firm TOP:</strong> ${data.data ? data.data.length : 0}<br>
                    <strong>Odpowiedź:</strong> <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<strong>Błąd:</strong> ${error.message}`;
            }
        }

        async function testAllCompanies() {
            const result = document.getElementById('allResult');
            try {
                const response = await fetch('admin/api/companies.php?path=all');
                const data = await response.json();

                result.className = 'result ' + (data.success ? 'success' : 'error');
                result.innerHTML = `
                    <strong>Status:</strong> ${data.success ? 'Sukces' : 'Błąd'}<br>
                    <strong>Liczba firm:</strong> ${data.data && data.data.companies ? data.data.companies.length : 0}<br>
                    <strong>Odpowiedź:</strong> <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<strong>Błąd:</strong> ${error.message}`;
            }
        }

        async function testCategories() {
            const result = document.getElementById('categoriesResult');
            try {
                const response = await fetch('admin/api/companies.php?path=categories');
                const data = await response.json();

                result.className = 'result ' + (data.success ? 'success' : 'error');
                result.innerHTML = `
                    <strong>Status:</strong> ${data.success ? 'Sukces' : 'Błąd'}<br>
                    <strong>Liczba kategorii:</strong> ${data.data ? data.data.length : 0}<br>
                    <strong>Odpowiedź:</strong> <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<strong>Błąd:</strong> ${error.message}`;
            }
        }

        async function testCreateCompany() {
            const result = document.getElementById('createResult');
            try {
                const companyData = {
                    name: 'Testowa Firma ' + Date.now(),
                    categoryId: 1,
                    description: 'Opis testowej firmy',
                    address: 'ul. Testowa 1, Żyrardów',
                    phone: '+48 ***********',
                    email: '<EMAIL>',
                    status: 'active'
                };

                const response = await fetch('admin/api/companies.php?path=create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(companyData)
                });
                const data = await response.json();

                result.className = 'result ' + (data.success ? 'success' : 'error');
                result.innerHTML = `
                    <strong>Status:</strong> ${data.success ? 'Sukces' : 'Błąd'}<br>
                    <strong>Wiadomość:</strong> ${data.message}<br>
                    <strong>ID firmy:</strong> ${data.data ? data.data.id : 'brak'}<br>
                    <strong>Odpowiedź:</strong> <pre>${JSON.stringify(data, null, 2)}</pre>
                `;

                // Zapisz ID dla testu pozycji TOP
                if (data.success && data.data && data.data.id) {
                    window.testCompanyId = data.data.id;
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<strong>Błąd:</strong> ${error.message}`;
            }
        }

        async function testSetTopPosition() {
            const result = document.getElementById('topPositionResult');

            if (!window.testCompanyId) {
                result.className = 'result error';
                result.innerHTML = '<strong>Błąd:</strong> Najpierw dodaj firmę (Test 4)';
                return;
            }

            try {
                const response = await fetch('admin/api/companies.php?path=set-top-position', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        companyId: window.testCompanyId,
                        topPosition: 1
                    })
                });
                const data = await response.json();

                result.className = 'result ' + (data.success ? 'success' : 'error');
                result.innerHTML = `
                    <strong>Status:</strong> ${data.success ? 'Sukces' : 'Błąd'}<br>
                    <strong>Wiadomość:</strong> ${data.message}<br>
                    <strong>Odpowiedź:</strong> <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<strong>Błąd:</strong> ${error.message}`;
            }
        }
    </script>
</body>
</html>
