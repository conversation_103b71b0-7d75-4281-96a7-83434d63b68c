#!/bin/bash

# 🚀 Setup script dla VPS Aruba - Laravel + Next.js
# Żyrardów Poleca

set -e

echo "🚀 Rozpoczynam setup na VPS Aruba..."

# Kolory
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Sprawdź czy jesteś root
if [[ $EUID -eq 0 ]]; then
   print_error "Nie uruchamiaj tego skryptu jako root!"
   exit 1
fi

# 1. Aktualizacja systemu
print_status "Aktualizacja systemu..."
sudo apt update && sudo apt upgrade -y

# 2. Instalacja PHP 8.2+
print_status "Instalacja PHP 8.2..."
sudo apt install -y software-properties-common
sudo add-apt-repository ppa:ondrej/php -y
sudo apt update
sudo apt install -y php8.2 php8.2-fpm php8.2-mysql php8.2-sqlite3 php8.2-xml php8.2-curl php8.2-mbstring php8.2-zip php8.2-gd php8.2-intl php8.2-bcmath

# 3. Instalacja Composer
print_status "Instalacja Composer..."
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
sudo chmod +x /usr/local/bin/composer

# 4. Instalacja Node.js 18+
print_status "Instalacja Node.js 18..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# 5. Instalacja pnpm
print_status "Instalacja pnpm..."
sudo npm install -g pnpm

# 6. Instalacja PM2
print_status "Instalacja PM2..."
sudo npm install -g pm2

# 7. Instalacja Nginx
print_status "Instalacja Nginx..."
sudo apt install -y nginx

# 8. Instalacja Certbot (SSL)
print_status "Instalacja Certbot..."
sudo apt install -y certbot python3-certbot-nginx

# 9. Utwórz strukturę katalogów
print_status "Tworzenie struktury katalogów..."
sudo mkdir -p /var/www/zyrardow-poleca/{backend,frontend,admin}
sudo chown -R $USER:$USER /var/www/zyrardow-poleca

# 10. Sprawdź wersje
print_status "Sprawdzanie wersji..."
php --version
composer --version
node --version
pnpm --version
pm2 --version
nginx -v

print_success "🎉 Środowisko VPS zostało przygotowane!"
echo ""
echo "📋 Następne kroki:"
echo ""
echo "1. Skopiuj pliki projektu do /var/www/zyrardow-poleca/"
echo "2. Uruchom setup-backend-vps.sh"
echo "3. Uruchom setup-frontend-vps.sh"
echo "4. Skonfiguruj Nginx"
echo "5. Uzyskaj certyfikat SSL"
echo ""
echo "💡 Przykładowe komendy:"
echo "   rsync -av laravel-nextjs/ user@your-server:/var/www/zyrardow-poleca/"
echo "   ssh user@your-server 'cd /var/www/zyrardow-poleca && ./setup-backend-vps.sh'"
