#!/bin/bash

# 🚀 Setup frontendu Next.js na VPS
# Żyrardów Poleca

set -e

echo "🎨 Konfiguracja frontendu Next.js na VPS..."

# Kolory
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Sprawdź czy jesteśmy w odpowiednim katalogu
if [[ ! -d "frontend" || ! -d "admin" ]]; then
    echo "❌ Uruchom skrypt z katalogu /var/www/zyrardow-poleca/"
    exit 1
fi

# 1. Setup frontendu
print_status "Konfiguracja frontendu Next.js..."
cd frontend

# Zainstaluj zależności
print_status "Instalacja zależności pnpm..."
pnpm install

# Konfiguracja .env.local dla produkcji
print_status "Konfiguracja środowiska produkcyjnego..."
cat > .env.local << EOF
NEXT_PUBLIC_API_URL=https://zyrardow.poleca.to/api/v1
NEXT_PUBLIC_SITE_URL=https://zyrardow.poleca.to
NODE_ENV=production
EOF

# Build aplikacji
print_status "Budowanie aplikacji Next.js..."
pnpm build

print_success "✅ Frontend Next.js zbudowany!"

# 2. Setup admin panelu
print_status "Konfiguracja panelu admin..."
cd ../admin

# Zainstaluj zależności
print_status "Instalacja zależności admin..."
pnpm install

# Konfiguracja .env.local dla admin
cat > .env.local << EOF
NEXT_PUBLIC_API_URL=https://zyrardow.poleca.to/api/admin
NEXT_PUBLIC_FRONTEND_URL=https://zyrardow.poleca.to
NODE_ENV=production
EOF

# Build admin panelu
print_status "Budowanie panelu admin..."
pnpm build

print_success "✅ Panel admin zbudowany!"

# 3. Konfiguracja PM2
print_status "Konfiguracja PM2..."
cd ..

# Utwórz plik konfiguracyjny PM2
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: 'zyrardow-frontend',
      cwd: '/var/www/zyrardow-poleca/frontend',
      script: 'npm',
      args: 'start',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      error_file: '/var/log/pm2/zyrardow-frontend-error.log',
      out_file: '/var/log/pm2/zyrardow-frontend-out.log',
      log_file: '/var/log/pm2/zyrardow-frontend.log'
    },
    {
      name: 'zyrardow-admin',
      cwd: '/var/www/zyrardow-poleca/admin',
      script: 'npm',
      args: 'start',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      error_file: '/var/log/pm2/zyrardow-admin-error.log',
      out_file: '/var/log/pm2/zyrardow-admin-out.log',
      log_file: '/var/log/pm2/zyrardow-admin.log'
    }
  ]
};
EOF

# Utwórz katalog logów PM2
sudo mkdir -p /var/log/pm2
sudo chown -R $USER:$USER /var/log/pm2

# Uruchom aplikacje przez PM2
print_status "Uruchamianie aplikacji przez PM2..."
pm2 start ecosystem.config.js

# Zapisz konfigurację PM2
pm2 save

# Ustaw PM2 do automatycznego startu
pm2 startup
print_warning "⚠️  Wykonaj komendę którą wyświetlił PM2 powyżej (sudo env PATH=...)"

print_success "✅ Frontend i Admin uruchomione przez PM2!"

# 4. Sprawdź status
print_status "Status aplikacji:"
pm2 status

print_success "🎉 Frontend i Admin gotowe!"
echo ""
echo "📋 Następne kroki:"
echo "1. Skonfiguruj Nginx: sudo cp nginx-config.conf /etc/nginx/sites-available/zyrardow.poleca.to"
echo "2. Aktywuj konfigurację: sudo ln -s /etc/nginx/sites-available/zyrardow.poleca.to /etc/nginx/sites-enabled/"
echo "3. Testuj Nginx: sudo nginx -t"
echo "4. Restart Nginx: sudo systemctl restart nginx"
echo "5. Uzyskaj SSL: sudo certbot --nginx -d zyrardow.poleca.to -d www.zyrardow.poleca.to"
echo ""
echo "🌐 Aplikacje działają na:"
echo "   Frontend: http://localhost:3000"
echo "   Admin:    http://localhost:3001"
