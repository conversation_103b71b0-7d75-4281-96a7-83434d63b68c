#!/bin/bash

# 🚀 Setup script dla MacBook - <PERSON><PERSON> + Next.js
# Żyrardów Poleca

set -e  # Zatrzymaj przy błędzie

echo "🚀 Rozpoczynam setup <PERSON>vel + Next.js dla Żyrardów Poleca..."

# Kolory dla outputu
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funkcja do wyświetlania kolorowych komunikatów
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Sprawdź czy jesteśmy w odpowiednim katalogu
if [[ ! -d "backend" || ! -d "frontend" ]]; then
    print_error "Uruchom skrypt z katalogu laravel-nextjs/"
    exit 1
fi

# 1. Setup Backend Laravel
print_status "Konfiguracja backendu Laravel..."
cd backend

# Sprawdź czy Laravel jest zainstalowany
if [[ ! -f "artisan" ]]; then
    print_status "Instalacja Laravel..."
    composer create-project laravel/laravel . "10.*"
fi

# Zainstaluj pakiety
print_status "Instalacja pakietów Laravel..."
composer require laravel/sanctum spatie/laravel-sluggable

# Konfiguracja .env
if [[ ! -f ".env" ]]; then
    cp .env.example .env
    php artisan key:generate
fi

# Aktualizuj .env dla SQLite
print_status "Konfiguracja bazy danych SQLite..."
sed -i '' 's/DB_CONNECTION=mysql/DB_CONNECTION=sqlite/' .env
sed -i '' 's/DB_HOST=127.0.0.1/#DB_HOST=127.0.0.1/' .env
sed -i '' 's/DB_PORT=3306/#DB_PORT=3306/' .env
sed -i '' 's/DB_DATABASE=laravel/DB_DATABASE=/' .env
sed -i '' 's/DB_USERNAME=root/#DB_USERNAME=root/' .env
sed -i '' 's/DB_PASSWORD=/#DB_PASSWORD=/' .env

# Dodaj konfigurację CORS
echo "" >> .env
echo "# CORS dla Next.js" >> .env
echo "FRONTEND_URL=http://localhost:3000" >> .env
echo "ADMIN_URL=http://localhost:3001" >> .env
echo "" >> .env
echo "# API" >> .env
echo "SANCTUM_STATEFUL_DOMAINS=localhost:3000,localhost:3001" >> .env
echo "SESSION_DOMAIN=localhost" >> .env

# Utwórz bazę danych SQLite
touch database/database.sqlite

# Uruchom migracje
print_status "Uruchamianie migracji..."
php artisan migrate

# Uruchom seeders
print_status "Dodawanie przykładowych danych..."
php artisan db:seed

# Publikuj konfigurację Sanctum
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"

# Cache konfiguracji
php artisan config:cache

print_success "Backend Laravel skonfigurowany!"

# 2. Setup Frontend Next.js
print_status "Konfiguracja frontendu Next.js..."
cd ../frontend

# Sprawdź czy Next.js jest zainstalowany
if [[ ! -f "package.json" ]]; then
    print_status "Instalacja Next.js..."
    npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir --import-alias "@/*" --yes
fi

# Zainstaluj dodatkowe pakiety
print_status "Instalacja pakietów Next.js..."
pnpm add @headlessui/react @heroicons/react swr axios clsx tailwind-merge
pnpm add -D @types/node

# Utwórz .env.local
print_status "Konfiguracja środowiska Next.js..."
echo "NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1" > .env.local

print_success "Frontend Next.js skonfigurowany!"

# 3. Setup Admin Panel
print_status "Konfiguracja panelu admin..."
cd ../admin

# Sprawdź czy admin panel jest zainstalowany
if [[ ! -f "package.json" ]]; then
    print_status "Instalacja admin panelu..."
    npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir --import-alias "@/*" --yes
fi

# Zainstaluj pakiety dla admin
print_status "Instalacja pakietów admin..."
pnpm add @headlessui/react @heroicons/react swr axios clsx tailwind-merge react-hook-form @hookform/resolvers yup
pnpm add -D @types/node

# Utwórz .env.local dla admin
echo "NEXT_PUBLIC_API_URL=http://localhost:8000/api/admin" > .env.local
echo "NEXT_PUBLIC_FRONTEND_URL=http://localhost:3000" >> .env.local

print_success "Panel admin skonfigurowany!"

# 4. Podsumowanie
cd ..
print_success "🎉 Setup zakończony pomyślnie!"
echo ""
echo "📋 Następne kroki:"
echo ""
echo "1. Uruchom backend Laravel:"
echo "   cd backend && php artisan serve --host=0.0.0.0 --port=8000"
echo ""
echo "2. Uruchom frontend Next.js (nowy terminal):"
echo "   cd frontend && pnpm dev"
echo ""
echo "3. Uruchom admin panel (nowy terminal):"
echo "   cd admin && pnpm dev --port 3001"
echo ""
echo "🌐 URLs:"
echo "   - Backend API: http://localhost:8000/api/v1"
echo "   - Frontend: http://localhost:3000"
echo "   - Admin Panel: http://localhost:3001"
echo ""
echo "📊 Przykładowe dane zostały dodane do bazy!"
echo "🎯 Możesz teraz testować aplikację!"
