#!/bin/bash

# 🚀 Setup backendu Laravel na VPS
# Żyrardów Poleca

set -e

echo "🔧 Konfiguracja backendu Laravel na VPS..."

# Kolory
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Sprawdź czy jesteśmy w odpowiednim katalogu
if [[ ! -d "backend" ]]; then
    echo "❌ Uruchom skrypt z katalogu /var/www/zyrardow-poleca/"
    exit 1
fi

# 1. Setup backendu
print_status "Konfiguracja backendu Laravel..."
cd backend

# Zainstaluj zależności
print_status "Instalacja zależności Composer..."
composer install --optimize-autoloader --no-dev

# Konfiguracja .env dla produkcji
print_status "Konfiguracja środowiska produkcyjnego..."
if [[ ! -f ".env" ]]; then
    cp .env.example .env
fi

# Generuj klucz aplikacji
php artisan key:generate --force

# Aktualizuj .env dla produkcji
cat > .env << EOF
APP_NAME="Żyrardów Poleca"
APP_ENV=production
APP_KEY=$(php artisan --no-ansi key:generate --show)
APP_DEBUG=false
APP_URL=https://zyrardow.poleca.to

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=sqlite
DB_DATABASE=/var/www/zyrardow-poleca/backend/database/database.sqlite

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Żyrardów Poleca"

# CORS dla Next.js
FRONTEND_URL=https://zyrardow.poleca.to
ADMIN_URL=https://zyrardow.poleca.to/admin

# API
SANCTUM_STATEFUL_DOMAINS=zyrardow.poleca.to,www.zyrardow.poleca.to
SESSION_DOMAIN=.poleca.to
EOF

# Utwórz bazę danych SQLite
print_status "Tworzenie bazy danych SQLite..."
touch database/database.sqlite

# Uruchom migracje
print_status "Uruchamianie migracji..."
php artisan migrate --force

# Uruchom seeders
print_status "Dodawanie przykładowych danych..."
php artisan db:seed --force

# Cache konfiguracji
print_status "Optymalizacja dla produkcji..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Ustaw uprawnienia
print_status "Ustawianie uprawnień..."
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 775 storage bootstrap/cache

# Utwórz link do storage
php artisan storage:link

print_success "✅ Backend Laravel skonfigurowany!"

# 2. Konfiguracja PHP-FPM
print_status "Konfiguracja PHP-FPM..."
sudo tee /etc/php/8.2/fpm/pool.d/zyrardow.conf > /dev/null << EOF
[zyrardow]
user = www-data
group = www-data
listen = /run/php/php8.2-fpm-zyrardow.sock
listen.owner = www-data
listen.group = www-data
pm = dynamic
pm.max_children = 5
pm.start_servers = 2
pm.min_spare_servers = 1
pm.max_spare_servers = 3
EOF

# Restart PHP-FPM
sudo systemctl restart php8.2-fpm

print_success "✅ PHP-FPM skonfigurowany!"

cd ..

print_success "🎉 Backend gotowy do użycia!"
echo ""
echo "📋 Następne kroki:"
echo "1. Skonfiguruj frontend: ./setup-frontend-vps.sh"
echo "2. Skonfiguruj Nginx"
echo "3. Uzyskaj certyfikat SSL"
