# 🚀 Żyrardów Poleca - <PERSON><PERSON> + Next.js

Nowoczesna aplikacja webowa dla katalogu firm w Żyrardowie, zbudowana w architekturze Laravel (backend) + Next.js (frontend + admin).

## 📋 Spis treści

- [Architektura](#architektura)
- [Wymagania](#wymagania)
- [Instalacja na MacBook](#instalacja-na-macbook)
- [Wdrożenie na VPS](#wdrożenie-na-vps)
- [Użytkowanie](#użytkowanie)
- [API Documentation](#api-documentation)

## 🏗️ Architektura

```
📁 laravel-nextjs/
├── 📁 backend/          # Laravel API (port 8000)
│   ├── app/Models/      # Company, Category, Coupon
│   ├── app/Http/Controllers/Api/
│   ├── database/migrations/
│   └── routes/api.php
│
├── 📁 frontend/         # Next.js Frontend (port 3000)
│   ├── src/app/         # App Router
│   ├── src/components/  # React Components
│   └── src/lib/api.ts   # API Client
│
├── 📁 admin/           # Next.js Admin Panel (port 3001)
│   ├── src/app/        # Admin Dashboard
│   └── src/components/ # Admin Components
│
└── 📁 scripts/         # Setup Scripts
```

## 💻 Wymagania

### MacBook:
- macOS 10.15+
- Homebrew
- Terminal

### VPS Aruba:
- Ubuntu 20.04+ / Debian 11+
- Root access
- Domena wskazująca na serwer

## 🚀 Instalacja na MacBook

### 1. Przygotowanie środowiska

```bash
# Sprawdź czy masz Homebrew
brew --version

# Jeśli nie masz, zainstaluj:
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Zainstaluj wymagane narzędzia
brew install php@8.2 composer node@18
npm install -g pnpm

# Dodaj do PATH
echo 'export PATH="/opt/homebrew/opt/php@8.2/bin:$PATH"' >> ~/.zshrc
echo 'export PATH="/opt/homebrew/opt/node@18/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
```

### 2. Uruchomienie projektu

```bash
# Przejdź do katalogu projektu
cd /Users/<USER>/zyrardow.poleca.to/laravel-nextjs

# Nadaj uprawnienia skryptom
chmod +x *.sh

# Uruchom setup (jednorazowo)
./setup-macbook.sh

# Uruchom wszystkie serwisy
./start-dev.sh
```

### 3. Dostępne URLs

- **Frontend**: http://localhost:3000
- **Admin Panel**: http://localhost:3001  
- **Backend API**: http://localhost:8000/api/v1
- **API Docs**: http://localhost:8000/api/v1/stats

## 🌐 Wdrożenie na VPS

### 1. Przygotowanie VPS

```bash
# Połącz się z VPS
ssh user@your-server-ip

# Pobierz i uruchom skrypt przygotowania
wget https://raw.githubusercontent.com/your-repo/setup-vps.sh
chmod +x setup-vps.sh
./setup-vps.sh
```

### 2. Przesłanie plików

```bash
# Z MacBooka - prześlij pliki na VPS
rsync -av --exclude node_modules --exclude vendor laravel-nextjs/ user@your-server:/var/www/zyrardow-poleca/

# Prześlij również skrypty
scp laravel-nextjs/*.sh user@your-server:/var/www/zyrardow-poleca/
```

### 3. Konfiguracja backendu

```bash
# Na VPS
ssh user@your-server
cd /var/www/zyrardow-poleca

# Nadaj uprawnienia
chmod +x *.sh

# Skonfiguruj backend
./setup-backend-vps.sh
```

### 4. Konfiguracja frontendu

```bash
# Skonfiguruj frontend i admin
./setup-frontend-vps.sh

# Wykonaj komendę PM2 startup (zostanie wyświetlona)
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $USER --hp $HOME
```

### 5. Konfiguracja Nginx

```bash
# Skopiuj konfigurację Nginx
sudo cp nginx-config.conf /etc/nginx/sites-available/zyrardow.poleca.to

# Aktywuj konfigurację
sudo ln -s /etc/nginx/sites-available/zyrardow.poleca.to /etc/nginx/sites-enabled/

# Usuń domyślną konfigurację
sudo rm /etc/nginx/sites-enabled/default

# Testuj konfigurację
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

### 6. Certyfikat SSL

```bash
# Uzyskaj certyfikat SSL
sudo certbot --nginx -d zyrardow.poleca.to -d www.zyrardow.poleca.to

# Automatyczne odnowienie
sudo crontab -e
# Dodaj linię:
0 12 * * * /usr/bin/certbot renew --quiet
```

## 🎯 Użytkowanie

### Frontend (Strona publiczna)
- **Strona główna**: Lista TOP firm, kategorie
- **Katalog firm**: Przeglądanie i wyszukiwanie firm
- **Strona firmy**: Szczegóły, kontakt, kupony
- **Kategorie**: Firmy pogrupowane tematycznie

### Admin Panel
- **Dashboard**: Statystyki i przegląd
- **Firmy**: Zarządzanie firmami (CRUD)
- **Kategorie**: Zarządzanie kategoriami
- **Kupony**: Zarządzanie kuponami rabatowymi
- **Pozycje TOP**: Ustawianie wyróżnionych firm

### API Endpoints

```bash
# Publiczne API
GET /api/v1/companies          # Lista firm
GET /api/v1/companies/top      # TOP firmy
GET /api/v1/categories         # Kategorie
GET /api/v1/coupons/active     # Aktywne kupony

# Admin API (wymagana autoryzacja)
POST /api/admin/companies      # Dodaj firmę
PUT /api/admin/companies/{id}  # Edytuj firmę
DELETE /api/admin/companies/{id} # Usuń firmę
```

## 🔧 Przydatne komendy

### MacBook - Development

```bash
# Restart wszystkich serwisów
./start-dev.sh

# Tylko backend
cd backend && php artisan serve --port=8000

# Tylko frontend
cd frontend && pnpm dev

# Tylko admin
cd admin && pnpm dev --port=3001

# Reset bazy danych
cd backend && php artisan migrate:fresh --seed
```

### VPS - Production

```bash
# Status aplikacji
pm2 status

# Restart aplikacji
pm2 restart all

# Logi aplikacji
pm2 logs

# Status Nginx
sudo systemctl status nginx

# Restart Nginx
sudo systemctl restart nginx

# Sprawdź certyfikat SSL
sudo certbot certificates
```

## 🐛 Rozwiązywanie problemów

### Port zajęty
```bash
# Sprawdź co używa portu
lsof -i :3000
lsof -i :8000

# Zabij proces
kill -9 PID
```

### Błędy uprawnień
```bash
# Backend
sudo chown -R www-data:www-data /var/www/zyrardow-poleca/backend/storage
sudo chmod -R 775 /var/www/zyrardow-poleca/backend/storage

# Frontend
sudo chown -R $USER:$USER /var/www/zyrardow-poleca/frontend
```

### Błędy bazy danych
```bash
cd backend
php artisan migrate:fresh --seed
```

## 📞 Wsparcie

W przypadku problemów:
1. Sprawdź logi: `pm2 logs` (VPS) lub terminal (MacBook)
2. Sprawdź status serwisów: `pm2 status`
3. Sprawdź konfigurację Nginx: `sudo nginx -t`

---

**Żyrardów Poleca** - Nowoczesny katalog firm 🚀
