import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Types
export interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  color: string;
  companies_count: number;
  children?: Category[];
}

export interface Company {
  id: number;
  name: string;
  slug: string;
  description?: string;
  short_description?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  facebook?: string;
  instagram?: string;
  opening_hours?: Array<{
    day: string;
    hours: string;
  }>;
  logo?: string;
  images?: string[];
  rating: number;
  reviews_count: number;
  views_count: number;
  is_top: boolean;
  is_open_now: boolean;
  category?: {
    id: number;
    name: string;
    slug: string;
  };
  active_coupons?: Coupon[];
}

export interface Coupon {
  id: number;
  title: string;
  description: string;
  code: string;
  discount: string;
  valid_to: string;
  company?: {
    id: number;
    name: string;
    slug: string;
    logo?: string;
  };
}

export interface Stats {
  companies: {
    total: number;
    active: number;
    top: number;
  };
  categories: {
    total: number;
    active: number;
    main: number;
  };
  coupons: {
    total: number;
    active: number;
    expired: number;
  };
  popular_categories: Array<{
    name: string;
    companies_count: number;
  }>;
  top_companies: Array<{
    name: string;
    views: number;
    rating: string;
  }>;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

// API functions
export const apiClient = {
  // Stats
  getStats: async (): Promise<Stats> => {
    const response = await api.get<ApiResponse<Stats>>('/stats');
    return response.data.data;
  },

  // Categories
  getCategories: async (): Promise<Category[]> => {
    const response = await api.get<ApiResponse<Category[]>>('/categories');
    return response.data.data;
  },

  getCategory: async (slug: string): Promise<Category> => {
    const response = await api.get<ApiResponse<Category>>(`/categories/${slug}`);
    return response.data.data;
  },

  getCategoryCompanies: async (
    slug: string,
    params?: {
      sort?: 'name' | 'popular' | 'rating' | 'top';
      search?: string;
      page?: number;
    }
  ): Promise<PaginatedResponse<Company>> => {
    const response = await api.get<PaginatedResponse<Company>>(
      `/categories/${slug}/companies`,
      { params }
    );
    return response.data;
  },

  // Companies
  getCompanies: async (params?: {
    category_id?: number;
    search?: string;
    sort?: 'name' | 'popular' | 'rating' | 'top';
    page?: number;
  }): Promise<PaginatedResponse<Company>> => {
    const response = await api.get<PaginatedResponse<Company>>('/companies', { params });
    return response.data;
  },

  getTopCompanies: async (): Promise<Company[]> => {
    const response = await api.get<ApiResponse<Company[]>>('/companies/top');
    return response.data.data;
  },

  getCompany: async (slug: string): Promise<Company> => {
    const response = await api.get<ApiResponse<Company>>(`/companies/${slug}`);
    return response.data.data;
  },

  searchCompanies: async (query: string): Promise<Company[]> => {
    const response = await api.get<ApiResponse<Company[]>>('/companies/search', {
      params: { q: query }
    });
    return response.data.data;
  },

  // Coupons
  getCoupons: async (params?: {
    company_id?: number;
    discount_type?: string;
    sort?: 'discount' | 'newest' | 'valid_to';
    page?: number;
  }): Promise<PaginatedResponse<Coupon>> => {
    const response = await api.get<PaginatedResponse<Coupon>>('/coupons', { params });
    return response.data;
  },

  getActiveCoupons: async (): Promise<Coupon[]> => {
    const response = await api.get<ApiResponse<Coupon[]>>('/coupons/active');
    return response.data.data;
  },

  getCoupon: async (id: number): Promise<Coupon> => {
    const response = await api.get<ApiResponse<Coupon>>(`/coupons/${id}`);
    return response.data.data;
  },

  validateCoupon: async (code: string): Promise<ApiResponse<{ valid: boolean; coupon?: Coupon }>> => {
    const response = await api.post<ApiResponse<{ valid: boolean; coupon?: Coupon }>>('/coupons/validate', { code });
    return response.data;
  },

  useCoupon: async (id: number): Promise<ApiResponse<{ success: boolean; message: string }>> => {
    const response = await api.post<ApiResponse<{ success: boolean; message: string }>>(`/coupons/${id}/use`);
    return response.data;
  },
};

export default api;
