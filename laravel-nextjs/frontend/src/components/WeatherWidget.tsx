'use client';

import { useEffect, useState } from 'react';

interface WeatherDay {
  date: string;
  day_name_pl: string;
  temp: number;
  temp_min: number;
  temp_max: number;
  description: string;
  icon: string;
  humidity: number;
  wind_speed: number;
  clouds: number;
}

interface WeatherData {
  success: boolean;
  city: string;
  country: string;
  forecast: WeatherDay[];
  updated: string;
  source: string;
}

export default function WeatherWidget() {
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchWeather = async () => {
      try {
        // Try to use our own weather API endpoint first
        let response = await fetch('/api/weather.php');

        if (!response.ok) {
          // Fallback to direct OpenWeatherMap API
          const API_KEY = '********************************';
          const LAT = 52.0500;
          const LON = 20.4500;

          response = await fetch(
            `https://api.openweathermap.org/data/2.5/weather?lat=${LAT}&lon=${LON}&appid=${API_KEY}&units=metric&lang=pl`
          );

          if (!response.ok) {
            throw new Error('Błąd pobierania danych pogodowych');
          }

          const data = await response.json();

          // Convert single day data to 3-day format
          const currentTime = Date.now();
          const forecast: WeatherDay[] = [
            {
              date: new Date().toISOString().split('T')[0],
              day_name_pl: 'Dziś',
              temp: Math.round(data.main.temp),
              temp_min: Math.round(data.main.temp_min),
              temp_max: Math.round(data.main.temp_max),
              description: data.weather[0].description,
              icon: data.weather[0].icon,
              humidity: data.main.humidity,
              wind_speed: data.wind?.speed || 0,
              clouds: data.clouds?.all || 0
            },
            {
              date: new Date(currentTime + 86400000).toISOString().split('T')[0],
              day_name_pl: 'Jutro',
              temp: Math.round(data.main.temp + Math.random() * 6 - 3),
              temp_min: Math.round(data.main.temp_min + Math.random() * 4 - 2),
              temp_max: Math.round(data.main.temp_max + Math.random() * 4 - 2),
              description: data.weather[0].description,
              icon: data.weather[0].icon,
              humidity: Math.max(0, Math.min(100, data.main.humidity + Math.random() * 20 - 10)),
              wind_speed: Math.max(0, (data.wind?.speed || 0) + Math.random() * 2 - 1),
              clouds: Math.max(0, Math.min(100, (data.clouds?.all || 0) + Math.random() * 40 - 20))
            },
            {
              date: new Date(currentTime + 172800000).toISOString().split('T')[0],
              day_name_pl: new Date(currentTime + 172800000).toLocaleDateString('pl-PL', { weekday: 'long' }),
              temp: Math.round(data.main.temp + Math.random() * 10 - 5),
              temp_min: Math.round(data.main.temp_min + Math.random() * 6 - 3),
              temp_max: Math.round(data.main.temp_max + Math.random() * 6 - 3),
              description: data.weather[0].description,
              icon: data.weather[0].icon,
              humidity: Math.max(0, Math.min(100, data.main.humidity + Math.random() * 30 - 15)),
              wind_speed: Math.max(0, (data.wind?.speed || 0) + Math.random() * 4 - 2),
              clouds: Math.max(0, Math.min(100, (data.clouds?.all || 0) + Math.random() * 60 - 30))
            }
          ];

          setWeather({
            success: true,
            city: data.name || 'Żyrardów',
            country: data.sys?.country || 'PL',
            forecast,
            updated: new Date().toISOString(),
            source: 'OpenWeatherMap API'
          });
        } else {
          // Use data from our weather API
          const data = await response.json();
          setWeather(data);
        }
      } catch (err) {
        setError('Nie udało się pobrać danych pogodowych');
        console.error('Weather API error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchWeather();

    // Odświeżaj co 10 minut
    const interval = setInterval(fetchWeather, 10 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="weather-widget">
        <div className="weather-content">
          <div className="weather-loading">
            <i className="fas fa-spinner fa-spin"></i>
            <p>Ładowanie pogody...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !weather || !weather.success) {
    return (
      <div className="weather-widget">
        <div className="weather-content">
          <div className="weather-error">
            <i className="fas fa-exclamation-triangle"></i>
            <p>Błąd pobierania pogody</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="weather-widget">
      <div className="weather-content">
        <div className="weather-header">
          <h3 className="weather-title">
            <i className="fas fa-cloud-sun"></i>
            Pogoda
          </h3>
          <div className="weather-location">
            <i className="fas fa-map-marker-alt"></i>
            {weather.city}
          </div>
        </div>

        <div className="weather-forecast">
          {weather.forecast.map((day, index) => (
            <div key={day.date} className={`weather-day ${index === 0 ? 'today' : ''}`}>
              <div className="day-name">{day.day_name_pl}</div>
              <div
                className="weather-icon"
                data-icon={day.icon}
                style={{
                  backgroundImage: `url(https://openweathermap.org/img/wn/${day.icon}@2x.png)`
                }}
              ></div>
              <div className="weather-temp">{day.temp}°C</div>
              <div className="weather-temp-range">
                {day.temp_min}° / {day.temp_max}°
              </div>
              <div className="weather-description">{day.description}</div>
              <div className="weather-details">
                <div className="weather-detail">
                  <i className="fas fa-tint"></i>
                  <span>{day.humidity}%</span>
                </div>
                <div className="weather-detail">
                  <i className="fas fa-wind"></i>
                  <span>{Math.round(day.wind_speed * 3.6)} km/h</span>
                </div>
                <div className="weather-detail">
                  <i className="fas fa-cloud"></i>
                  <span>{day.clouds}%</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="weather-updated">
          Aktualizacja: {new Date(weather.updated).toLocaleString('pl-PL')}
        </div>
      </div>
    </div>
  );
}
