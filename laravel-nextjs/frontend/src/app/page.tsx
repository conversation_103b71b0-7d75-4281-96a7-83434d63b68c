'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import WeatherWidget from '../components/WeatherWidget';

// Temporary types until we fix imports
interface Company {
  id: number;
  name: string;
  slug: string;
  short_description?: string;
  rating: number;
  category?: string;
  is_open_now: boolean;
  logo?: string;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  icon: string;
  color: string;
  companies_count: number;
}

interface Coupon {
  id: number;
  title: string;
  description: string;
  code: string;
  discount: string;
  valid_to: string;
  company?: {
    name: string;
  };
}

export default function Home() {
  const [topCompanies, setTopCompanies] = useState<Company[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [activeCoupons, setActiveCoupons] = useState<Coupon[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch data from Laravel API
        const [categoriesResponse, companiesResponse, couponsResponse] = await Promise.all([
          fetch('http://127.0.0.1:8000/api/categories'),
          fetch('http://127.0.0.1:8000/api/companies/top'),
          fetch('http://127.0.0.1:8000/api/coupons/active')
        ]);

        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          setCategories(categoriesData.data || []);
        }

        if (companiesResponse.ok) {
          const companiesData = await companiesResponse.json();
          setTopCompanies(companiesData.data || []);
        }

        if (couponsResponse.ok) {
          const couponsData = await couponsResponse.json();
          setActiveCoupons(couponsData.data || []);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        // Fallback to mock data if API fails
        const mockCategories: Category[] = [
          { id: 1, name: 'Gastronomia', slug: 'gastronomia', icon: 'fas fa-utensils', color: '#e74c3c', companies_count: 12 },
          { id: 2, name: 'Zdrowie i uroda', slug: 'zdrowie-uroda', icon: 'fas fa-heartbeat', color: '#27ae60', companies_count: 8 },
          { id: 3, name: 'Uroda', slug: 'uroda', icon: 'fas fa-cut', color: '#9b59b6', companies_count: 6 },
          { id: 4, name: 'Sport i rekreacja', slug: 'sport-rekreacja', icon: 'fas fa-dumbbell', color: '#3498db', companies_count: 4 },
          { id: 5, name: 'Motoryzacja', slug: 'motoryzacja', icon: 'fas fa-car', color: '#f39c12', companies_count: 7 },
          { id: 6, name: 'Usługi', slug: 'uslugi', icon: 'fas fa-tools', color: '#34495e', companies_count: 15 },
          { id: 7, name: 'Zakupy', slug: 'zakupy', icon: 'fas fa-shopping-bag', color: '#e67e22', companies_count: 9 },
          { id: 8, name: 'Edukacja', slug: 'edukacja', icon: 'fas fa-graduation-cap', color: '#2c3e50', companies_count: 3 },
        ];

        const mockCompanies: Company[] = [
          { id: 1, name: 'Restauracja Pod Lipami', slug: 'restauracja-pod-lipami', short_description: 'Tradycyjna kuchnia polska w sercu Żyrardowa', rating: 4.8, category: 'Gastronomia', is_open_now: true },
          { id: 2, name: 'Salon Piękności Venus', slug: 'salon-pieknosci-venus', short_description: 'Profesjonalne usługi kosmetyczne i fryzjerskie', rating: 4.6, category: 'Uroda', is_open_now: false },
          { id: 3, name: 'Warsztat Samochodowy Auto-Serwis', slug: 'warsztat-auto-serwis', short_description: 'Kompleksowe naprawy i serwis pojazdów', rating: 4.7, category: 'Motoryzacja', is_open_now: true },
        ];

        const mockCoupons: Coupon[] = [
          { id: 1, title: '20% zniżki na obiad', description: 'Rabat na wszystkie dania główne', code: 'OBIAD20', discount: '20%', valid_to: '2025-12-31', company: { name: 'Restauracja Pod Lipami' } },
          { id: 2, title: 'Darmowa konsultacja', description: 'Bezpłatna konsultacja kosmetyczna', code: 'KONSULT', discount: '100%', valid_to: '2025-06-30', company: { name: 'Salon Piękności Venus' } },
        ];

        setCategories(mockCategories);
        setTopCompanies(mockCompanies);
        setActiveCoupons(mockCoupons);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Auto-slide functionality
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % 3);
    }, 5000);
    return () => clearInterval(timer);
  }, []);

  const slides = [
    {
      bg: '/images/zyrardow-slider1.jpg',
      title: 'Odkryj TOP firmy w Żyrardowie 2025',
      subtitle: '🏆 Portal lokalny #1 w Żyrardowie - najlepsze firmy, kupony rabatowe i usługi w mieście Filip de Girard',
      primaryBtn: { text: 'Katalog firm Żyrardów', href: '#categories' },
      secondaryBtn: { text: 'Dla przedsiębiorców', href: '/dla-biznesu' }
    },
    {
      bg: '/images/zyrardow-slider2.jpg',
      title: 'Historia Żyrardowa - Filip de Girard i Pomnik Historii',
      subtitle: '🏛️ Odkryj unikalne dziedzictwo przemysłowe - osada fabryczna, Muzeum Lniarstwa i zabytki XIX wieku',
      primaryBtn: { text: 'Historia Żyrardowa', href: '/historia' },
      secondaryBtn: { text: 'Zabytki przemysłowe', href: '/zabytki' }
    },
    {
      bg: '/images/zyrardow-slider3.jpg',
      title: 'Kupony rabatowe Żyrardów - oszczędzaj lokalnie!',
      subtitle: '💰 Ekskluzywne rabaty w restauracjach, salonach i sklepach Żyrardowa - tylko dla mieszkańców!',
      primaryBtn: { text: 'Kupony Żyrardów', href: '/kupony' },
      secondaryBtn: { text: 'Promocje lokalne', href: '/oferty' }
    }
  ];

  const toggleCategory = (categorySlug: string) => {
    setActiveCategory(activeCategory === categorySlug ? null : categorySlug);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <header className="site-header">
        <div className="container">
          <nav className="main-nav">
            <div className="logo">
              <Link href="/">
                <Image
                  src="/images/logo-zyrardow-poleca.png"
                  alt="Żyrardów.poleca.to"
                  width={200}
                  height={60}
                />
              </Link>
            </div>
            <ul className="nav-links">
              <li><Link href="#categories-section">Polecane firmy i miejsca</Link></li>
              <li className="has-submenu">
                <Link href="/o-miescie">O mieście</Link>
                <ul className="submenu">
                  <li><Link href="/o-miescie">O Żyrardowie</Link></li>
                  <li><Link href="/historia">Historia</Link></li>
                  <li><Link href="/zabytki">Zabytki</Link></li>
                  <li><Link href="/powiat">Powiat żyrardowski</Link></li>
                </ul>
              </li>
              <li className="has-submenu">
                <Link href="/atrakcje">Atrakcje w Żyrardowie</Link>
                <ul className="submenu">
                  <li><Link href="/atrakcje">Miejsca warte odwiedzenia</Link></li>
                  <li><Link href="/atrakcje/osada-fabryczna">Osada fabryczna</Link></li>
                  <li><Link href="/atrakcje/park-dittricha">Park Dittricha</Link></li>
                  <li><Link href="/atrakcje/muzeum-lniarstwa">Muzeum Lniarstwa</Link></li>
                  <li><Link href="/atrakcje/kosciol-mb-pocieszenia">Kościół MB Pocieszenia</Link></li>
                  <li><Link href="/atrakcje/kultura-sport">Centrum Kultury</Link></li>
                  <li><Link href="/atrakcje/aquapark">Aquapark</Link></li>
                  <li><Link href="/atrakcje/kultura-sport">Kultura i sport</Link></li>
                </ul>
              </li>
              <li><Link href="/kontakt">Kontakt</Link></li>
            </ul>
            <div className="nav-actions">
              <Link href="https://facebook.com/zyrardow.poleca.to" className="social-icon" target="_blank" rel="noopener noreferrer">
                <i className="fab fa-facebook-f"></i>
              </Link>
              <button className="mobile-menu-btn">
                <span></span>
                <span></span>
                <span></span>
              </button>
            </div>
          </nav>
          <div className="search-container">
            <form className="search-form">
              <input type="text" placeholder="Szukaj w serwisie..." />
              <button type="submit"><i className="fas fa-search"></i></button>
            </form>
          </div>
        </div>
      </header>

      <main>
        {/* Hero Section */}
        <section className="hero">
          <div className="hero-slider">
            {slides.map((slide, index) => (
              <div
                key={index}
                className={`hero-slide ${index === currentSlide ? 'active' : ''}`}
              >
                <div
                  className="hero-slide-bg"
                  style={{ backgroundImage: `url('${slide.bg}')` }}
                ></div>
                <div className="hero-overlay"></div>
                <div className="container">
                  <div className="hero-content">
                    <h1>{slide.title}</h1>
                    <p className="lead">{slide.subtitle}</p>
                    <div className="hero-buttons">
                      <Link href={slide.primaryBtn.href} className="btn btn-primary">
                        {slide.primaryBtn.text}
                      </Link>
                      <Link href={slide.secondaryBtn.href} className="btn btn-outline">
                        {slide.secondaryBtn.text}
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="hero-controls">
            <button
              className="hero-control prev"
              onClick={() => setCurrentSlide((prev) => (prev - 1 + 3) % 3)}
            >
              <i className="fas fa-chevron-left"></i>
            </button>
            <div className="hero-dots">
              {slides.map((_, index) => (
                <span
                  key={index}
                  className={`hero-dot ${index === currentSlide ? 'active' : ''}`}
                  onClick={() => setCurrentSlide(index)}
                ></span>
              ))}
            </div>
            <button
              className="hero-control next"
              onClick={() => setCurrentSlide((prev) => (prev + 1) % 3)}
            >
              <i className="fas fa-chevron-right"></i>
            </button>
          </div>
        </section>

        {/* Sekcja informacyjna o mieście */}
        <section className="city-info-section" id="city-info-section">
          <div className="container">
            <div className="city-info-boxes">
              <div className="city-info-box">
                <div className="city-info-icon">
                  <i className="fas fa-city"></i>
                </div>
                <h3>O Żyrardowie</h3>
                <p>Żyrardów to unikalne miasto z bogatą historią przemysłową, położone zaledwie 45 km na zachód od Warszawy. Poznaj jego wyjątkowy charakter i atrakcje.</p>
                <Link href="/o-miescie" className="btn btn-sm btn-outline">Czytaj więcej</Link>
              </div>

              <div className="city-info-box">
                <div className="city-info-icon">
                  <i className="fas fa-landmark"></i>
                </div>
                <h3>Historia miasta</h3>
                <p>Historia Żyrardowa nierozerwalnie związana jest z przemysłem lniarskim. Nazwa miasta pochodzi od nazwiska francuskiego inżyniera Philippe'a de Girarda.</p>
                <Link href="/historia" className="btn btn-sm btn-outline">Czytaj więcej</Link>
              </div>

              <div className="city-info-box">
                <div className="city-info-icon">
                  <i className="fas fa-map-marked-alt"></i>
                </div>
                <h3>Atrakcje turystyczne</h3>
                <p>Zabytkowa osada fabryczna, Muzeum Lniarstwa, Park Dittricha - to tylko niektóre z atrakcji, które warto zobaczyć podczas wizyty w Żyrardowie.</p>
                <Link href="/atrakcje" className="btn btn-sm btn-outline">Czytaj więcej</Link>
              </div>
            </div>
          </div>
        </section>

        {/* Sekcja katalogu firm */}
        <section className="categories categories-section" id="categories-section">
          <div className="container">
            <div className="section-header">
              <h2>Ranking 🏆 TOP 3 firmy z wybranej kategorii w Żyrardowie</h2>
              <p className="section-subtitle">Wybierz kategorię i sprawdź do jakiego miejsca np. restauracji czy salonu kosmetycznego w Żyrardowie i powiecie żyrardowskim warto się udać! TOP 3 zweryfikowane, najlepsze firmy z najwyższymi ocenami klientów.</p>
            </div>

            <div className="catalog-search main-search">
              <form className="catalog-search-form">
                <div className="search-input">
                  <i className="fas fa-search"></i>
                  <input type="text" placeholder="Szukaj firm w Żyrardowie: restauracje, fryzjerzy, sklepy..." />
                </div>
                <button type="submit" className="btn btn-primary">Szukaj w Żyrardowie</button>
              </form>
            </div>

            <div className="catalog-layout">
              <div className="categories-sidebar">
                <h3>Kategorie firm</h3>
                <ul className="categories-list">
                  {categories.map((category) => (
                    <li
                      key={category.id}
                      className={`category-item ${activeCategory === category.slug ? 'active' : ''}`}
                      data-category={category.slug}
                    >
                      <div
                        className="category-header"
                        onClick={() => toggleCategory(category.slug)}
                        style={{ cursor: 'pointer' }}
                      >
                        <i className={category.icon}></i>
                        <span>{category.name}</span>
                        <i className={`fas fa-chevron-down ${activeCategory === category.slug ? 'rotated' : ''}`}></i>
                      </div>
                      {activeCategory === category.slug && (
                        <ul className="subcategories-list">
                          <li><Link href={`/kategorie/${category.slug}`}>Wszystkie firmy</Link></li>
                          <li><Link href={`/kategorie/${category.slug}#top`}>TOP firmy</Link></li>
                          <li><Link href={`/kategorie/${category.slug}#promocje`}>Promocje</Link></li>
                        </ul>
                      )}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="category-content" id="categoryContent">
                <div className="top-companies-section">
                  <h3>TOP 3 polecane firmy</h3>
                  <div className="top-companies-list">
                    {topCompanies.length > 0 ? (
                      topCompanies.map((company) => (
                        <div key={company.id} className="top-company-card">
                          <div className="company-badge">TOP</div>

                          <div className="company-logo">
                            {company.logo ? (
                              <Image src={company.logo} alt={company.name} width={90} height={90} />
                            ) : (
                              <div className="company-logo-placeholder">
                                <i className="fas fa-building"></i>
                              </div>
                            )}
                          </div>

                          <div className="company-info">
                            <h4>{company.name}</h4>
                            <div className="company-tags">
                              <span className="company-tag">{company.category}</span>
                            </div>
                            <p className="company-description">{company.short_description}</p>

                            <div className="company-contact">
                              <p><i className="fas fa-star"></i> Ocena: {company.rating}/5</p>
                              <p><i className="fas fa-clock"></i> {company.is_open_now ? 'Otwarte' : 'Zamknięte'}</p>
                            </div>

                            <Link href={`/firmy/${company.slug}`} className="btn btn-primary btn-sm">
                              Zobacz szczegóły
                            </Link>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="loading-placeholder">
                        <i className="fas fa-spinner fa-spin"></i>
                        <p>Ładowanie firm...</p>
                      </div>
                    )}
                  </div>

                  <div className="add-company-promo">
                    <div className="promo-icon">
                      <i className="fas fa-building"></i>
                    </div>
                    <div className="promo-content">
                      <h4>Dodaj swoją firmę do katalogu</h4>
                      <p>Zwiększ widoczność swojej firmy w internecie i pozyskaj nowych klientów. Zweryfikujemy Twoją firmę/miejsce, ponieważ współpracujemy tylko z najlepszymi z powiatu.</p>
                      <Link href="/dodaj-firme" className="btn btn-primary">Dodaj firmę</Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Sekcja kuponów */}
        <section className="featured-coupons" id="coupons-section">
          <div className="container">
            <div className="section-header">
              <h2>Najnowsze kupony rabatowe</h2>
              <p className="section-subtitle">Skorzystaj z aktualnych promocji i rabatów w Żyrardowie</p>
            </div>

            <div className="coupons-grid">
              {activeCoupons.length > 0 ? (
                activeCoupons.slice(0, 6).map((coupon) => (
                  <div key={coupon.id} className="coupon-card">
                    <div className="coupon-header">
                      <div className="coupon-discount">{coupon.discount}</div>
                      <div className="coupon-company">{coupon.company?.name}</div>
                    </div>

                    <h3 className="coupon-title">{coupon.title}</h3>
                    <p className="coupon-description">{coupon.description}</p>

                    <div className="coupon-footer">
                      <div className="coupon-code">
                        <span>Kod:</span>
                        <strong>{coupon.code}</strong>
                      </div>
                      <div className="coupon-validity">
                        Ważny do: {new Date(coupon.valid_to).toLocaleDateString('pl-PL')}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="loading-placeholder">
                  <i className="fas fa-spinner fa-spin"></i>
                  <p>Ładowanie kuponów...</p>
                </div>
              )}
            </div>

            <div className="text-center mt-40">
              <Link href="/kupony" className="btn btn-primary">Zobacz wszystkie kupony</Link>
            </div>
          </div>
        </section>

        {/* Sekcja Rok Filipa de Girarda 2025 */}
        <section className="filip-girard-year girard-light-theme">
          <div className="container">
            <div className="girard-header">
              <h2 className="section-title">2025 - Rok Filipa de Girarda</h2>
              <p className="section-subtitle">250. rocznica urodzin patrona naszego miasta</p>
            </div>
            <div className="girard-content">
              <div className="girard-info">
                <p>Filip de Girard (1775-1845) - francuski inżynier i wynalazca, twórca mechanicznej przędzarki lnu, pierwszy dyrektor techniczny żyrardowskiej fabryki. Od jego nazwiska pochodzi nazwa naszego miasta.</p>
                <div className="girard-highlights">
                  <div className="highlight-item">
                    <div className="highlight-icon">
                      <i className="fas fa-cog"></i>
                    </div>
                    <h3>Wynalazca</h3>
                    <p>Twórca mechanicznej przędzarki lnu, która zrewolucjonizowała przemysł</p>
                  </div>
                  <div className="highlight-item">
                    <div className="highlight-icon">
                      <i className="fas fa-industry"></i>
                    </div>
                    <h3>Dyrektor fabryki</h3>
                    <p>Pierwszy dyrektor techniczny żyrardowskiej fabryki od 1833 roku</p>
                  </div>
                  <div className="highlight-item">
                    <div className="highlight-icon">
                      <i className="fas fa-flag"></i>
                    </div>
                    <h3>Współpraca z Francją</h3>
                    <p>Partnerstwo z francuskim miastem Lourmarin - miejscem urodzenia Girarda</p>
                  </div>
                </div>
              </div>
              <div className="girard-image">
                <Image src="/images/filip-de-girard.jpg" alt="Filip de Girard - patron Żyrardowa" width={400} height={300} />
              </div>
            </div>
            <div className="girard-cta">
              <Link href="/historia#filip-de-girard" className="btn btn-primary">Poznaj historię Filipa de Girarda</Link>
            </div>
          </div>
        </section>

        {/* Sekcja Pomnik Historii */}
        <section className="heritage-monument">
          <div className="container">
            <div className="heritage-content">
              <div className="heritage-text">
                <h2 className="section-title">Żyrardów - Pomnik Historii</h2>
                <p className="section-subtitle">Zabytkowa osada fabryczna wpisana na listę Pomników Historii</p>
                <p>Żyrardowska osada fabryczna to jeden z najlepiej zachowanych przykładów XIX-wiecznej urbanistyki przemysłowej w Europie. Charakterystyczna zabudowa z nietynkowanej czerwonej cegły, obsadzone drzewami ulice i unikatowy charakter przemysłowy sprawiają, że Żyrardów jest wyjątkowym miejscem na mapie Polski.</p>
                <div className="heritage-stats">
                  <div className="stat">
                    <span className="number">2012</span>
                    <span className="label">Rok wpisu na listę</span>
                  </div>
                  <div className="stat">
                    <span className="number">70 ha</span>
                    <span className="label">Powierzchnia obszaru</span>
                  </div>
                  <div className="stat">
                    <span className="number">XIX w.</span>
                    <span className="label">Urbanistyka przemysłowa</span>
                  </div>
                </div>
                <Link href="/zabytki" className="btn btn-outline">Odkryj zabytki Żyrardowa</Link>
              </div>
              <div className="heritage-image">
                <Image src="/images/fabryka-lnu.jpg" alt="Zabytkowa osada fabryczna w Żyrardowie" width={500} height={400} />
              </div>
            </div>
          </div>
        </section>

        {/* Sekcja O Żyrardowie */}
        <section className="about-city about-section" id="about-section">
          <div className="container">
            <div className="section-header">
              <h2>O Żyrardowie</h2>
              <p className="section-subtitle">Poznaj nasze miasto i jego wyjątkową historię</p>
            </div>

            <div className="about-city-content">
              <div className="about-city-text">
                <p>Żyrardów to miasto położone w województwie mazowieckim, około 45 km na zachód od Warszawy. Miasto słynie z wyjątkowego dziedzictwa przemysłowego - XIX-wiecznej osady fabrycznej, która jest jednym z najlepiej zachowanych przykładów przemysłowej urbanistyki w Europie.</p>
                <p>Nazwa miasta pochodzi od nazwiska francuskiego inżyniera Philippe'a de Girarda, wynalazcy maszyny do mechanicznego przędzenia lnu, który w 1833 roku został dyrektorem technicznym powstającej tu fabryki.</p>
                <p>Dziś Żyrardów to nowoczesne miasto, które z dumą prezentuje swoje przemysłowe dziedzictwo. Zabytkowa osada fabryczna, wpisana na listę Pomników Historii, przyciąga turystów z całej Polski i zagranicy.</p>
                <div className="about-city-buttons">
                  <Link href="/historia" className="btn btn-outline">Historia miasta</Link>
                  <Link href="/zabytki" className="btn btn-outline">Zabytki</Link>
                  <Link href="/atrakcje" className="btn btn-outline">Atrakcje</Link>
                </div>
              </div>
              <div className="about-city-image">
                <Image src="/images/zyrardow-panorama.jpg" alt="Panorama Żyrardowa" width={500} height={400} />
              </div>
            </div>
          </div>
        </section>

        {/* Sekcja Miejsca warte odwiedzenia */}
        <section className="discover-city places-section" id="places-section">
          <div className="container">
            <div className="section-header">
              <h2>Jakie miejsca warto odwiedzić w Żyrardowie?</h2>
              <p className="section-subtitle">Poznaj historię miasta i najciekawsze miejsca</p>
            </div>

            <div className="discover-grid">
              <div className="discover-item">
                <div className="discover-image">
                  <Image src="/images/fabryka-lnu.jpg" alt="Zabytkowa osada fabryczna w Żyrardowie" width={300} height={200} />
                  <div className="discover-overlay">
                    <h3>Zabytkowa osada fabryczna</h3>
                    <p>Unikatowy kompleks urbanistyczny z XIX wieku</p>
                    <Link href="/zabytki" className="btn btn-outline btn-sm">Dowiedz się więcej</Link>
                  </div>
                </div>
              </div>

              <div className="discover-item">
                <div className="discover-image">
                  <Image src="/images/willa-dittricha.jpg" alt="Park Dittricha w Żyrardowie" width={300} height={200} />
                  <div className="discover-overlay">
                    <h3>Park Dittricha</h3>
                    <p>Zabytkowy park miejski z XIX wieku</p>
                    <Link href="/atrakcje" className="btn btn-outline btn-sm">Dowiedz się więcej</Link>
                  </div>
                </div>
              </div>

              <div className="discover-item">
                <div className="discover-image">
                  <Image src="/images/domy-robotnicze.jpg" alt="Muzeum Lniarstwa w Żyrardowie" width={300} height={200} />
                  <div className="discover-overlay">
                    <h3>Muzeum Lniarstwa</h3>
                    <p>Historia przemysłu lniarskiego w Żyrardowie</p>
                    <Link href="/atrakcje" className="btn btn-outline btn-sm">Dowiedz się więcej</Link>
                  </div>
                </div>
              </div>

              <div className="discover-item">
                <div className="discover-image">
                  <Image src="/images/zyrardow-panorama.jpg" alt="Kościół pw. Matki Bożej Pocieszenia w Żyrardowie" width={300} height={200} />
                  <div className="discover-overlay">
                    <h3>Kościół pw. Matki Bożej Pocieszenia</h3>
                    <p>Neogotycka świątynia z końca XIX wieku</p>
                    <Link href="/zabytki" className="btn btn-outline btn-sm">Dowiedz się więcej</Link>
                  </div>
                </div>
              </div>

              <div className="discover-item">
                <div className="discover-image">
                  <Image src="/images/zyrardow-miasto-hero.jpg" alt="Centrum Kultury w Żyrardowie" width={300} height={200} />
                  <div className="discover-overlay">
                    <h3>Centrum Kultury</h3>
                    <p>Miejsce wydarzeń kulturalnych i rozrywkowych</p>
                    <Link href="/kultura-sport" className="btn btn-outline btn-sm">Dowiedz się więcej</Link>
                  </div>
                </div>
              </div>

              <div className="discover-item">
                <div className="discover-image">
                  <Image src="/images/zyrardow-panorama.jpg" alt="Aquapark Żyrardów" width={300} height={200} />
                  <div className="discover-overlay">
                    <h3>Aquapark</h3>
                    <p>Nowoczesny kompleks rekreacyjno-sportowy</p>
                    <Link href="/kultura-sport" className="btn btn-outline btn-sm">Dowiedz się więcej</Link>
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center mt-40">
              <Link href="/atrakcje" className="btn btn-primary">Zobacz wszystkie atrakcje</Link>
            </div>
          </div>
        </section>

        {/* Sekcja statystyk miasta */}
        <section className="city-stats stats-section" id="stats-section">
          <div className="container">
            <div className="section-header">
              <h2>Żyrardów w liczbach</h2>
              <p className="section-subtitle">Poznaj najważniejsze statystyki naszego miasta</p>
            </div>

            <div className="stats-grid">
              <div className="stat-item">
                <div className="stat-icon">
                  <i className="fas fa-users"></i>
                </div>
                <div className="stat-number">40,000</div>
                <div className="stat-label">Mieszkańców</div>
              </div>

              <div className="stat-item">
                <div className="stat-icon">
                  <i className="fas fa-building"></i>
                </div>
                <div className="stat-number">1829</div>
                <div className="stat-label">Rok założenia</div>
              </div>

              <div className="stat-item">
                <div className="stat-icon">
                  <i className="fas fa-map-marked-alt"></i>
                </div>
                <div className="stat-number">14</div>
                <div className="stat-label">km² powierzchni</div>
              </div>

              <div className="stat-item">
                <div className="stat-icon">
                  <i className="fas fa-landmark"></i>
                </div>
                <div className="stat-number">200</div>
                <div className="stat-label">Zabytków</div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="site-footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <div className="footer-logo">
                <Image
                  src="/images/logo-zyrardow-poleca.png"
                  alt="Żyrardów.poleca.to"
                  width={200}
                  height={60}
                />
              </div>
              <p>Portal lokalny Żyrardowa - odkryj najlepsze firmy, usługi i atrakcje w mieście Filip de Girard.</p>
              <div className="social-links">
                <Link href="https://facebook.com/zyrardow.poleca.to" target="_blank" rel="noopener noreferrer">
                  <i className="fab fa-facebook-f"></i>
                </Link>
                <Link href="https://instagram.com/zyrardow.poleca.to" target="_blank" rel="noopener noreferrer">
                  <i className="fab fa-instagram"></i>
                </Link>
              </div>
            </div>

            <div className="footer-section">
              <h4>Dla firm</h4>
              <ul>
                <li><Link href="/dla-biznesu">Dodaj firmę</Link></li>
                <li><Link href="/cennik">Cennik</Link></li>
                <li><Link href="/kontakt">Kontakt</Link></li>
              </ul>
            </div>

            <div className="footer-section">
              <h4>O Żyrardowie</h4>
              <ul>
                <li><Link href="/o-miescie">O mieście</Link></li>
                <li><Link href="/historia">Historia</Link></li>
                <li><Link href="/atrakcje">Atrakcje</Link></li>
                <li><Link href="/powiat">Powiat</Link></li>
              </ul>
            </div>

            <div className="footer-section">
              <h4>Kontakt</h4>
              <ul>
                <li><i className="fas fa-envelope"></i> <EMAIL></li>
                <li><i className="fas fa-phone"></i> +48 123 456 789</li>
                <li><i className="fas fa-map-marker-alt"></i> Żyrardów, Polska</li>
              </ul>
            </div>

            <div className="footer-section">
              <h4>Pogoda w Żyrardowie</h4>
              <WeatherWidget />
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2025 Żyrardów.poleca.to - Wszystkie prawa zastrzeżone</p>
            <div className="footer-links">
              <Link href="/regulamin">Regulamin</Link>
              <Link href="/polityka-prywatnosci">Polityka prywatności</Link>
              <Link href="/cookies">Cookies</Link>
            </div>
          </div>
        </div>
      </footer>
    </>
  );
}
