'use client'

import Link from 'next/link'
import { useEffect } from 'react'

export default function OsadaFabrycznaPage() {
  useEffect(() => {
    // Menu functionality
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navLinks = document.querySelector('.nav-links');
    const hasSubmenuItems = document.querySelectorAll('.has-submenu');

    // Mobile menu toggle
    if (mobileMenuBtn && navLinks) {
      mobileMenuBtn.addEventListener('click', () => {
        navLinks.classList.toggle('active');
      });
    }

    // Submenu toggle for mobile
    hasSubmenuItems.forEach(item => {
      const link = item.querySelector('a');
      if (link) {
        link.addEventListener('click', (e) => {
          if (window.innerWidth <= 992) {
            e.preventDefault();
            item.classList.toggle('active');
          }
        });
      }
    });

    // Search toggle
    const searchToggle = document.querySelector('.search-toggle');
    const searchContainer = document.querySelector('.search-container');

    if (searchToggle && searchContainer) {
      searchToggle.addEventListener('click', () => {
        searchContainer.classList.toggle('active');
      });
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.main-nav') && navLinks?.classList.contains('active')) {
        navLinks.classList.remove('active');
      }
    });

    // Cleanup
    return () => {
      if (mobileMenuBtn && navLinks) {
        mobileMenuBtn.removeEventListener('click', () => {});
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="site-header">
        <div className="container">
          <nav className="main-nav">
            <div className="logo">
              <Link href="/">
                <img src="/images/logo.png" alt="Żyrardów.poleca.to" />
              </Link>
            </div>

            <ul className="nav-links">
              <li><Link href="/">Strona Główna</Link></li>
              <li className="has-submenu">
                <Link href="/o-miescie">O mieście</Link>
                <ul className="submenu">
                  <li><Link href="/o-miescie/historia">Historia</Link></li>
                  <li><Link href="/o-miescie/zabytki">Zabytki</Link></li>
                  <li><Link href="/o-miescie/kultura-sport">Kultura i Sport</Link></li>
                </ul>
              </li>
              <li className="has-submenu">
                <Link href="/atrakcje">Atrakcje</Link>
                <ul className="submenu">
                  <li><Link href="/atrakcje/muzeum-lniarstwa">Muzeum Lniarstwa</Link></li>
                  <li><Link href="/atrakcje/kosciol-mb-pocieszenia">Kościół MB Pocieszenia</Link></li>
                  <li><Link href="/atrakcje/aquapark">Aquapark</Link></li>
                  <li><Link href="/atrakcje/kultura-sport">Kultura i Sport</Link></li>
                  <li><Link href="/atrakcje/park-dittricha">Park Dittricha</Link></li>
                  <li><Link href="/atrakcje/osada-fabryczna">Osada Fabryczna</Link></li>
                </ul>
              </li>
              <li><Link href="/katalog-firm">Katalog Firm</Link></li>
              <li><Link href="/kontakt">Kontakt</Link></li>
            </ul>

            <div className="nav-actions">
              <button className="search-toggle">
                <i className="fas fa-search"></i>
              </button>
              <a href="https://facebook.com/zyrardow.poleca.to" target="_blank" rel="noopener noreferrer" className="social-icon">
                <i className="fab fa-facebook-f"></i>
              </a>
              <button className="mobile-menu-btn">
                <span></span>
                <span></span>
                <span></span>
              </button>
            </div>
          </nav>

          <div className="search-container">
            <form className="search-form">
              <input type="text" placeholder="Szukaj firm, usług..." />
              <button type="submit">
                <i className="fas fa-search"></i>
              </button>
            </form>
          </div>
        </div>
      </header>

      {/* Breadcrumbs */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-blue-600">Strona główna</Link>
            <span>/</span>
            <Link href="/atrakcje" className="hover:text-blue-600">Atrakcje</Link>
            <span>/</span>
            <span className="text-gray-900">Osada Fabryczna</span>
          </nav>
        </div>
      </section>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-amber-900 to-orange-700 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Osada Fabryczna
            </h1>
            <p className="text-xl md:text-2xl opacity-90">
              Unikalny zespół urbanistyczny z XIX wieku - przykład nowoczesnego osiedla robotniczego
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <div>
                <h2 className="text-3xl font-bold mb-6 text-gray-900">Historia osady</h2>
                <p className="text-gray-700 mb-6 leading-relaxed">
                  Osada Fabryczna w Żyrardowie została założona w 1833 roku przez Filipa de Girarda 
                  jako nowoczesne osiedle mieszkaniowe dla robotników Zakładów Lniarskich. 
                  To jeden z najlepiej zachowanych przykładów XIX-wiecznej architektury przemysłowej w Polsce.
                </p>
                
                <p className="text-gray-700 mb-6 leading-relaxed">
                  Osada została zaprojektowana według nowoczesnych jak na tamte czasy standardów urbanistycznych. 
                  Składa się z regularnie rozmieszczonych domów robotniczych, które zapewniały mieszkańcom 
                  lepsze warunki życia niż w innych ośrodkach przemysłowych.
                </p>

                <div className="bg-amber-50 border-l-4 border-amber-400 p-6 mb-6">
                  <h3 className="text-xl font-semibold mb-3 text-gray-900">Zabytkowy charakter</h3>
                  <div className="space-y-2 text-gray-700">
                    <p><strong>Status:</strong> Wpisana do rejestru zabytków</p>
                    <p><strong>Okres budowy:</strong> 1833-1855</p>
                    <p><strong>Architekt:</strong> Henryk Marconi</p>
                    <p><strong>Styl:</strong> Klasycyzm przemysłowy</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-amber-50 rounded-lg">
                    <i className="fas fa-home text-2xl text-amber-600 mb-2"></i>
                    <h4 className="font-semibold text-gray-900">Domy robotnicze</h4>
                    <p className="text-sm text-gray-600">Ponad 100 budynków</p>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <i className="fas fa-road text-2xl text-orange-600 mb-2"></i>
                    <h4 className="font-semibold text-gray-900">Układ urbanistyczny</h4>
                    <p className="text-sm text-gray-600">Regularna siatka ulic</p>
                  </div>
                  <div className="text-center p-4 bg-yellow-50 rounded-lg">
                    <i className="fas fa-landmark text-2xl text-yellow-600 mb-2"></i>
                    <h4 className="font-semibold text-gray-900">Zabytek</h4>
                    <p className="text-sm text-gray-600">Rejestr zabytków</p>
                  </div>
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <i className="fas fa-calendar text-2xl text-red-600 mb-2"></i>
                    <h4 className="font-semibold text-gray-900">XIX wiek</h4>
                    <p className="text-sm text-gray-600">Epoka przemysłowa</p>
                  </div>
                </div>
              </div>

              <div>
                <div className="bg-gray-200 rounded-lg h-64 mb-6 overflow-hidden">
                  <img
                    src="/images/osada-fabryczna.jpg"
                    alt="Osada Fabryczna w Żyrardowie"
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src = '/images/placeholder-buildings.jpg'
                    }}
                  />
                </div>

                <div className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="text-xl font-semibold mb-4 text-gray-900">Informacje praktyczne</h3>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <i className="fas fa-map-marker-alt text-amber-600 mt-1 mr-3"></i>
                      <div>
                        <strong>Lokalizacja:</strong><br />
                        Centrum Żyrardowa<br />
                        ul. Robotnicza, ul. Fabryczna
                      </div>
                    </div>
                    <div className="flex items-start">
                      <i className="fas fa-eye text-amber-600 mt-1 mr-3"></i>
                      <div>
                        <strong>Zwiedzanie:</strong><br />
                        Spacer po ulicach osady<br />
                        Bezpłatne zwiedzanie zewnętrzne
                      </div>
                    </div>
                    <div className="flex items-start">
                      <i className="fas fa-camera text-amber-600 mt-1 mr-3"></i>
                      <div>
                        <strong>Fotografia:</strong><br />
                        Dozwolona<br />
                        Piękne kadry architektoniczne
                      </div>
                    </div>
                    <div className="flex items-start">
                      <i className="fas fa-info-circle text-amber-600 mt-1 mr-3"></i>
                      <div>
                        <strong>Uwagi:</strong><br />
                        Budynki są zamieszkane<br />
                        Prosimy o szanowanie prywatności
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Architecture Section */}
            <div className="mt-16">
              <h2 className="text-3xl font-bold mb-8 text-center text-gray-900">Architektura osady</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <div className="text-center mb-4">
                    <i className="fas fa-building text-3xl text-amber-600 mb-3"></i>
                    <h3 className="text-xl font-semibold text-gray-900">Domy jednorodzinne</h3>
                  </div>
                  <p className="text-gray-600 text-center">
                    Dwupiętrowe budynki z czerwonej cegły, każdy z własnym ogródkiem i podwórkiem.
                  </p>
                </div>

                <div className="bg-white rounded-lg shadow-lg p-6">
                  <div className="text-center mb-4">
                    <i className="fas fa-users text-3xl text-orange-600 mb-3"></i>
                    <h3 className="text-xl font-semibold text-gray-900">Domy wielorodzinne</h3>
                  </div>
                  <p className="text-gray-600 text-center">
                    Większe budynki mieszkalne dla kilku rodzin robotniczych z wspólnymi przestrzeniami.
                  </p>
                </div>

                <div className="bg-white rounded-lg shadow-lg p-6">
                  <div className="text-center mb-4">
                    <i className="fas fa-store text-3xl text-yellow-600 mb-3"></i>
                    <h3 className="text-xl font-semibold text-gray-900">Budynki usługowe</h3>
                  </div>
                  <p className="text-gray-600 text-center">
                    Sklepy, szkoła, szpital i inne obiekty niezbędne do funkcjonowania społeczności.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="site-footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>Żyrardów.poleca.to</h3>
              <p>Portal lokalny miasta Żyrardów. Odkryj najlepsze firmy, usługi i atrakcje w naszym mieście.</p>
              <div className="social-links">
                <a href="https://facebook.com/zyrardow.poleca.to" target="_blank" rel="noopener noreferrer">
                  <i className="fab fa-facebook-f"></i>
                </a>
              </div>
            </div>

            <div className="footer-section">
              <h4>Nawigacja</h4>
              <ul>
                <li><Link href="/">Strona główna</Link></li>
                <li><Link href="/o-miescie">O mieście</Link></li>
                <li><Link href="/atrakcje">Atrakcje</Link></li>
                <li><Link href="/katalog-firm">Katalog firm</Link></li>
                <li><Link href="/kontakt">Kontakt</Link></li>
              </ul>
            </div>

            <div className="footer-section">
              <h4>Kontakt</h4>
              <div className="contact-info">
                <p><i className="fas fa-envelope"></i> <EMAIL></p>
                <p><i className="fas fa-map-marker-alt"></i> Żyrardów, Mazowieckie</p>
              </div>
            </div>

            <div className="footer-section">
              <h4>Informacje</h4>
              <ul>
                <li><Link href="/polityka-prywatnosci">Polityka prywatności</Link></li>
                <li><Link href="/regulamin">Regulamin</Link></li>
                <li><Link href="/dla-biznesu">Dla biznesu</Link></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 Żyrardów.poleca.to. Wszystkie prawa zastrzeżone.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
