'use client'

import Link from 'next/link'
import { useEffect } from 'react'

export default function KulturaSportPage() {
  useEffect(() => {
    // Menu functionality
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navLinks = document.querySelector('.nav-links');
    const hasSubmenuItems = document.querySelectorAll('.has-submenu');

    // Mobile menu toggle
    if (mobileMenuBtn && navLinks) {
      mobileMenuBtn.addEventListener('click', () => {
        navLinks.classList.toggle('active');
      });
    }

    // Submenu toggle for mobile
    hasSubmenuItems.forEach(item => {
      const link = item.querySelector('a');
      if (link) {
        link.addEventListener('click', (e) => {
          if (window.innerWidth <= 992) {
            e.preventDefault();
            item.classList.toggle('active');
          }
        });
      }
    });

    // Search toggle
    const searchToggle = document.querySelector('.search-toggle');
    const searchContainer = document.querySelector('.search-container');

    if (searchToggle && searchContainer) {
      searchToggle.addEventListener('click', () => {
        searchContainer.classList.toggle('active');
      });
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.main-nav') && navLinks?.classList.contains('active')) {
        navLinks.classList.remove('active');
      }
    });

    // Cleanup
    return () => {
      if (mobileMenuBtn && navLinks) {
        mobileMenuBtn.removeEventListener('click', () => {});
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="site-header">
        <div className="container">
          <nav className="main-nav">
            <div className="logo">
              <Link href="/">
                <img src="/images/logo.png" alt="Żyrardów.poleca.to" />
              </Link>
            </div>

            <ul className="nav-links">
              <li><Link href="/">Strona Główna</Link></li>
              <li className="has-submenu">
                <Link href="/o-miescie">O mieście</Link>
                <ul className="submenu">
                  <li><Link href="/o-miescie/historia">Historia</Link></li>
                  <li><Link href="/o-miescie/zabytki">Zabytki</Link></li>
                  <li><Link href="/o-miescie/kultura-sport">Kultura i Sport</Link></li>
                </ul>
              </li>
              <li className="has-submenu">
                <Link href="/atrakcje">Atrakcje</Link>
                <ul className="submenu">
                  <li><Link href="/atrakcje/muzeum-lniarstwa">Muzeum Lniarstwa</Link></li>
                  <li><Link href="/atrakcje/kosciol-mb-pocieszenia">Kościół MB Pocieszenia</Link></li>
                  <li><Link href="/atrakcje/aquapark">Aquapark</Link></li>
                  <li><Link href="/atrakcje/kultura-sport">Kultura i Sport</Link></li>
                  <li><Link href="/atrakcje/park-dittricha">Park Dittricha</Link></li>
                  <li><Link href="/atrakcje/osada-fabryczna">Osada Fabryczna</Link></li>
                </ul>
              </li>
              <li><Link href="/katalog-firm">Katalog Firm</Link></li>
              <li><Link href="/kontakt">Kontakt</Link></li>
            </ul>

            <div className="nav-actions">
              <button className="search-toggle">
                <i className="fas fa-search"></i>
              </button>
              <a href="https://facebook.com/zyrardow.poleca.to" target="_blank" rel="noopener noreferrer" className="social-icon">
                <i className="fab fa-facebook-f"></i>
              </a>
              <button className="mobile-menu-btn">
                <span></span>
                <span></span>
                <span></span>
              </button>
            </div>
          </nav>

          <div className="search-container">
            <form className="search-form">
              <input type="text" placeholder="Szukaj firm, usług..." />
              <button type="submit">
                <i className="fas fa-search"></i>
              </button>
            </form>
          </div>
        </div>
      </header>

      {/* Breadcrumbs */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-blue-600">Strona główna</Link>
            <span>/</span>
            <Link href="/atrakcje" className="hover:text-blue-600">Atrakcje</Link>
            <span>/</span>
            <span className="text-gray-900">Kultura i Sport</span>
          </nav>
        </div>
      </section>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-red-900 to-red-700 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Kultura i Sport w Żyrardowie
            </h1>
            <p className="text-xl md:text-2xl opacity-90">
              Centrum kulturalne, obiekty sportowe i bogata oferta wydarzeń dla mieszkańców
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <div>
                <h2 className="text-3xl font-bold mb-6 text-gray-900">Centrum Kultury</h2>
                <p className="text-gray-700 mb-6 leading-relaxed">
                  Żyrardowskie Centrum Kultury to główny ośrodek życia kulturalnego miasta. 
                  Organizuje spektakle teatralne, koncerty, wystawy oraz warsztaty artystyczne 
                  dla dzieci i dorosłych. Centrum posiada nowoczesną salę widowiskową oraz 
                  galerie wystawiennicze.
                </p>
                
                <p className="text-gray-700 mb-6 leading-relaxed">
                  W ofercie znajdują się również zajęcia taneczne, muzyczne i plastyczne. 
                  Centrum współpracuje z lokalnymi artystami i organizuje cykliczne wydarzenia 
                  kulturalne, takie jak Dni Żyrardowa czy Festiwal Lniarstwa.
                </p>

                <div className="bg-red-50 border-l-4 border-red-400 p-6 mb-6">
                  <h3 className="text-xl font-semibold mb-3 text-gray-900">Najbliższe wydarzenia</h3>
                  <div className="space-y-2 text-gray-700">
                    <p><strong>15 czerwca:</strong> Koncert muzyki klasycznej</p>
                    <p><strong>22 czerwca:</strong> Spektakl teatralny "Wesele"</p>
                    <p><strong>5 lipca:</strong> Wystawa malarstwa lokalnych artystów</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <i className="fas fa-theater-masks text-2xl text-red-600 mb-2"></i>
                    <h4 className="font-semibold text-gray-900">Teatr</h4>
                    <p className="text-sm text-gray-600">Spektakle i przedstawienia</p>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <i className="fas fa-music text-2xl text-blue-600 mb-2"></i>
                    <h4 className="font-semibold text-gray-900">Koncerty</h4>
                    <p className="text-sm text-gray-600">Muzyka na żywo</p>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <i className="fas fa-palette text-2xl text-green-600 mb-2"></i>
                    <h4 className="font-semibold text-gray-900">Wystawy</h4>
                    <p className="text-sm text-gray-600">Galerie sztuki</p>
                  </div>
                  <div className="text-center p-4 bg-yellow-50 rounded-lg">
                    <i className="fas fa-child text-2xl text-yellow-600 mb-2"></i>
                    <h4 className="font-semibold text-gray-900">Warsztaty</h4>
                    <p className="text-sm text-gray-600">Zajęcia dla dzieci</p>
                  </div>
                </div>
              </div>

              <div>
                <div className="bg-gray-200 rounded-lg h-64 mb-6 overflow-hidden">
                  <img
                    src="/images/centrum-kultury.jpg"
                    alt="Centrum Kultury w Żyrardowie"
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src = '/images/placeholder-culture.jpg'
                    }}
                  />
                </div>

                <div className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="text-xl font-semibold mb-4 text-gray-900">Informacje praktyczne</h3>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <i className="fas fa-map-marker-alt text-red-600 mt-1 mr-3"></i>
                      <div>
                        <strong>Adres:</strong><br />
                        ul. Kulturalna 5, Żyrardów
                      </div>
                    </div>
                    <div className="flex items-start">
                      <i className="fas fa-clock text-red-600 mt-1 mr-3"></i>
                      <div>
                        <strong>Godziny otwarcia:</strong><br />
                        Pon-Pt: 9:00-20:00<br />
                        Sobota: 10:00-18:00<br />
                        Niedziela: zamknięte
                      </div>
                    </div>
                    <div className="flex items-start">
                      <i className="fas fa-phone text-red-600 mt-1 mr-3"></i>
                      <div>
                        <strong>Telefon:</strong><br />
                        +48 46 855 25 30
                      </div>
                    </div>
                    <div className="flex items-start">
                      <i className="fas fa-envelope text-red-600 mt-1 mr-3"></i>
                      <div>
                        <strong>Email:</strong><br />
                        <EMAIL>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Sport Section */}
            <div className="mt-16">
              <h2 className="text-3xl font-bold mb-8 text-center text-gray-900">Obiekty sportowe</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <div className="text-center mb-4">
                    <i className="fas fa-futbol text-3xl text-green-600 mb-3"></i>
                    <h3 className="text-xl font-semibold text-gray-900">Stadion miejski</h3>
                  </div>
                  <p className="text-gray-600 text-center">
                    Nowoczesny stadion piłkarski z bieżnią lekkoatletyczną i trybunami na 2000 miejsc.
                  </p>
                </div>

                <div className="bg-white rounded-lg shadow-lg p-6">
                  <div className="text-center mb-4">
                    <i className="fas fa-basketball-ball text-3xl text-orange-600 mb-3"></i>
                    <h3 className="text-xl font-semibold text-gray-900">Hala sportowa</h3>
                  </div>
                  <p className="text-gray-600 text-center">
                    Wielofunkcyjna hala do koszykówki, siatkówki, piłki ręcznej i innych sportów halowych.
                  </p>
                </div>

                <div className="bg-white rounded-lg shadow-lg p-6">
                  <div className="text-center mb-4">
                    <i className="fas fa-tennis-ball text-3xl text-yellow-600 mb-3"></i>
                    <h3 className="text-xl font-semibold text-gray-900">Korty tenisowe</h3>
                  </div>
                  <p className="text-gray-600 text-center">
                    Dwa korty tenisowe z nawierzchnią ziemną, otwarte od kwietnia do października.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="site-footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>Żyrardów.poleca.to</h3>
              <p>Portal lokalny miasta Żyrardów. Odkryj najlepsze firmy, usługi i atrakcje w naszym mieście.</p>
              <div className="social-links">
                <a href="https://facebook.com/zyrardow.poleca.to" target="_blank" rel="noopener noreferrer">
                  <i className="fab fa-facebook-f"></i>
                </a>
              </div>
            </div>

            <div className="footer-section">
              <h4>Nawigacja</h4>
              <ul>
                <li><Link href="/">Strona główna</Link></li>
                <li><Link href="/o-miescie">O mieście</Link></li>
                <li><Link href="/atrakcje">Atrakcje</Link></li>
                <li><Link href="/katalog-firm">Katalog firm</Link></li>
                <li><Link href="/kontakt">Kontakt</Link></li>
              </ul>
            </div>

            <div className="footer-section">
              <h4>Kontakt</h4>
              <div className="contact-info">
                <p><i className="fas fa-envelope"></i> <EMAIL></p>
                <p><i className="fas fa-map-marker-alt"></i> Żyrardów, Mazowieckie</p>
              </div>
            </div>

            <div className="footer-section">
              <h4>Informacje</h4>
              <ul>
                <li><Link href="/polityka-prywatnosci">Polityka prywatności</Link></li>
                <li><Link href="/regulamin">Regulamin</Link></li>
                <li><Link href="/dla-biznesu">Dla biznesu</Link></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 Żyrardów.poleca.to. Wszystkie prawa zastrzeżone.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
