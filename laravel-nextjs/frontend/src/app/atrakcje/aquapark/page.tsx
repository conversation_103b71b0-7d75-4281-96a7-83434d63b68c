'use client'

import Link from 'next/link'
import { useEffect } from 'react'

export default function AquaparkPage() {
  useEffect(() => {
    // Menu functionality
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navLinks = document.querySelector('.nav-links');
    const hasSubmenuItems = document.querySelectorAll('.has-submenu');

    // Mobile menu toggle
    if (mobileMenuBtn && navLinks) {
      mobileMenuBtn.addEventListener('click', () => {
        navLinks.classList.toggle('active');
      });
    }

    // Submenu toggle for mobile
    hasSubmenuItems.forEach(item => {
      const link = item.querySelector('a');
      if (link) {
        link.addEventListener('click', (e) => {
          if (window.innerWidth <= 992) {
            e.preventDefault();
            item.classList.toggle('active');
          }
        });
      }
    });

    // Search toggle
    const searchToggle = document.querySelector('.search-toggle');
    const searchContainer = document.querySelector('.search-container');

    if (searchToggle && searchContainer) {
      searchToggle.addEventListener('click', () => {
        searchContainer.classList.toggle('active');
      });
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.main-nav') && navLinks?.classList.contains('active')) {
        navLinks.classList.remove('active');
      }
    });

    // Cleanup
    return () => {
      if (mobileMenuBtn && navLinks) {
        mobileMenuBtn.removeEventListener('click', () => {});
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="site-header">
        <div className="container">
          <nav className="main-nav">
            <div className="logo">
              <Link href="/">
                <img src="/images/logo.png" alt="Żyrardów.poleca.to" />
              </Link>
            </div>

            <ul className="nav-links">
              <li><Link href="/">Strona Główna</Link></li>
              <li className="has-submenu">
                <Link href="/o-miescie">O mieście</Link>
                <ul className="submenu">
                  <li><Link href="/o-miescie/historia">Historia</Link></li>
                  <li><Link href="/o-miescie/zabytki">Zabytki</Link></li>
                  <li><Link href="/o-miescie/kultura-sport">Kultura i Sport</Link></li>
                </ul>
              </li>
              <li className="has-submenu">
                <Link href="/atrakcje">Atrakcje</Link>
                <ul className="submenu">
                  <li><Link href="/atrakcje/muzeum-lniarstwa">Muzeum Lniarstwa</Link></li>
                  <li><Link href="/atrakcje/kosciol-mb-pocieszenia">Kościół MB Pocieszenia</Link></li>
                  <li><Link href="/atrakcje/aquapark">Aquapark</Link></li>
                  <li><Link href="/atrakcje/kultura-sport">Kultura i Sport</Link></li>
                  <li><Link href="/atrakcje/park-dittricha">Park Dittricha</Link></li>
                  <li><Link href="/atrakcje/osada-fabryczna">Osada Fabryczna</Link></li>
                </ul>
              </li>
              <li><Link href="/katalog-firm">Katalog Firm</Link></li>
              <li><Link href="/kontakt">Kontakt</Link></li>
            </ul>

            <div className="nav-actions">
              <button className="search-toggle">
                <i className="fas fa-search"></i>
              </button>
              <a href="https://facebook.com/zyrardow.poleca.to" target="_blank" rel="noopener noreferrer" className="social-icon">
                <i className="fab fa-facebook-f"></i>
              </a>
              <button className="mobile-menu-btn">
                <span></span>
                <span></span>
                <span></span>
              </button>
            </div>
          </nav>

          <div className="search-container">
            <form className="search-form">
              <input type="text" placeholder="Szukaj firm, usług..." />
              <button type="submit">
                <i className="fas fa-search"></i>
              </button>
            </form>
          </div>
        </div>
      </header>

      {/* Breadcrumbs */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-blue-600">Strona główna</Link>
            <span>/</span>
            <Link href="/atrakcje" className="hover:text-blue-600">Atrakcje</Link>
            <span>/</span>
            <span className="text-gray-900">Aquapark</span>
          </nav>
        </div>
      </section>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-cyan-900 to-blue-700 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Aquapark Żyrardów
            </h1>
            <p className="text-xl md:text-2xl opacity-90">
              Nowoczesny kompleks rekreacyjno-sportowy z basenami i atrakcjami wodnymi
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <div>
                <h2 className="text-3xl font-bold mb-6 text-gray-900">O aquaparku</h2>
                <p className="text-gray-700 mb-6 leading-relaxed">
                  Aquapark w Żyrardowie to nowoczesny kompleks rekreacyjno-sportowy, który oferuje 
                  doskonałe warunki do wypoczynku i aktywności fizycznej dla całej rodziny. 
                  Obiekt wyposażony jest w różnorodne baseny i atrakcje wodne.
                </p>
                
                <p className="text-gray-700 mb-6 leading-relaxed">
                  Kompleks składa się z basenu sportowego, rekreacyjnego, brodzika dla dzieci 
                  oraz strefy wellness z sauną i jacuzzi. Aquapark oferuje również zajęcia 
                  fitness w wodzie i kursy nauki pływania.
                </p>

                <div className="bg-cyan-50 border-l-4 border-cyan-400 p-6 mb-6">
                  <h3 className="text-xl font-semibold mb-3 text-gray-900">Godziny otwarcia</h3>
                  <div className="space-y-2 text-gray-700">
                    <p><strong>Poniedziałek-Piątek:</strong> 6:00-22:00</p>
                    <p><strong>Sobota-Niedziela:</strong> 8:00-22:00</p>
                    <p><strong>Święta:</strong> 10:00-20:00</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-cyan-50 rounded-lg">
                    <i className="fas fa-swimmer text-2xl text-cyan-600 mb-2"></i>
                    <h4 className="font-semibold text-gray-900">Basen sportowy</h4>
                    <p className="text-sm text-gray-600">25m, 6 torów</p>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <i className="fas fa-child text-2xl text-blue-600 mb-2"></i>
                    <h4 className="font-semibold text-gray-900">Brodzik dla dzieci</h4>
                    <p className="text-sm text-gray-600">Bezpieczna zabawa</p>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <i className="fas fa-hot-tub text-2xl text-green-600 mb-2"></i>
                    <h4 className="font-semibold text-gray-900">Jacuzzi</h4>
                    <p className="text-sm text-gray-600">Relaks i regeneracja</p>
                  </div>
                  <div className="text-center p-4 bg-yellow-50 rounded-lg">
                    <i className="fas fa-thermometer-half text-2xl text-yellow-600 mb-2"></i>
                    <h4 className="font-semibold text-gray-900">Sauna</h4>
                    <p className="text-sm text-gray-600">Strefa wellness</p>
                  </div>
                </div>
              </div>

              <div>
                <div className="bg-gray-200 rounded-lg h-64 mb-6 overflow-hidden">
                  <img
                    src="/images/aquapark.jpg"
                    alt="Aquapark w Żyrardowie"
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src = '/images/placeholder-pool.jpg'
                    }}
                  />
                </div>

                <div className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="text-xl font-semibold mb-4 text-gray-900">Informacje praktyczne</h3>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <i className="fas fa-map-marker-alt text-cyan-600 mt-1 mr-3"></i>
                      <div>
                        <strong>Adres:</strong><br />
                        ul. Sportowa 10, Żyrardów
                      </div>
                    </div>
                    <div className="flex items-start">
                      <i className="fas fa-ticket-alt text-cyan-600 mt-1 mr-3"></i>
                      <div>
                        <strong>Cennik:</strong><br />
                        Bilet normalny: 15 zł/h<br />
                        Bilet ulgowy: 12 zł/h<br />
                        Karnet miesięczny: 120 zł
                      </div>
                    </div>
                    <div className="flex items-start">
                      <i className="fas fa-phone text-cyan-600 mt-1 mr-3"></i>
                      <div>
                        <strong>Telefon:</strong><br />
                        +48 46 855 30 20
                      </div>
                    </div>
                    <div className="flex items-start">
                      <i className="fas fa-parking text-cyan-600 mt-1 mr-3"></i>
                      <div>
                        <strong>Parking:</strong><br />
                        Bezpłatny parking<br />
                        50 miejsc postojowych
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="site-footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>Żyrardów.poleca.to</h3>
              <p>Portal lokalny miasta Żyrardów. Odkryj najlepsze firmy, usługi i atrakcje w naszym mieście.</p>
              <div className="social-links">
                <a href="https://facebook.com/zyrardow.poleca.to" target="_blank" rel="noopener noreferrer">
                  <i className="fab fa-facebook-f"></i>
                </a>
              </div>
            </div>

            <div className="footer-section">
              <h4>Nawigacja</h4>
              <ul>
                <li><Link href="/">Strona główna</Link></li>
                <li><Link href="/o-miescie">O mieście</Link></li>
                <li><Link href="/atrakcje">Atrakcje</Link></li>
                <li><Link href="/katalog-firm">Katalog firm</Link></li>
                <li><Link href="/kontakt">Kontakt</Link></li>
              </ul>
            </div>

            <div className="footer-section">
              <h4>Kontakt</h4>
              <div className="contact-info">
                <p><i className="fas fa-envelope"></i> <EMAIL></p>
                <p><i className="fas fa-map-marker-alt"></i> Żyrardów, Mazowieckie</p>
              </div>
            </div>

            <div className="footer-section">
              <h4>Informacje</h4>
              <ul>
                <li><Link href="/polityka-prywatnosci">Polityka prywatności</Link></li>
                <li><Link href="/regulamin">Regulamin</Link></li>
                <li><Link href="/dla-biznesu">Dla biznesu</Link></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 Żyrardów.poleca.to. Wszystkie prawa zastrzeżone.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
