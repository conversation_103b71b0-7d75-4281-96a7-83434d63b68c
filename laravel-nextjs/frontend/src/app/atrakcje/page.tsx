'use client'

import Link from 'next/link'
import { useEffect } from 'react'

const attractionCategories = [
  {
    title: 'Zabytki',
    items: [
      'Osiedle robotnicze z XIX wieku',
      'Rezydencja Karola Dittricha',
      'Koś<PERSON>ół pw. <PERSON><PERSON>',
      'Zespół fabryczny Zakładów Lniarskich'
    ]
  },
  {
    title: '<PERSON>ze<PERSON>',
    items: [
      'Muzeum Lniarstwa',
      'Muzeum Mazowsza Zachodniego',
      'Galerie sztuki'
    ]
  },
  {
    title: 'Parki i rekreacja',
    items: [
      'Park im. Karola Augusta Dittricha',
      'Park im. Karola Dittricha',
      '<PERSON><PERSON><PERSON><PERSON> rowerowe',
      '<PERSON>ren<PERSON> rekreacyjne'
    ]
  }
]

const featuredAttractions = [
  {
    title: 'Osiedle robotnicze',
    description: 'Unikalny zespół urbanistyczny z XIX wieku, wpisany na listę zabytków. Przykład nowoczesnego osiedla robotniczego z tamtych czasów.',
    image: '/images/osiedle-robotnicze.jpg',
    link: '/atrakcje/osada-fabry<PERSON>'
  },
  {
    title: 'Rezyden<PERSON><PERSON> Karola Dittricha',
    description: '<PERSON><PERSON><PERSON>ła rezydencja przemysłowca, obecnie siedziba Muzeum Mazowsza Zachodniego. Warto zobaczyć wnętrza i ogród.',
    image: '/images/rezydencja.jpg',
    link: '/atrakcje/muzeum-mazowsza'
  },
  {
    title: 'Park im. Karola Augusta Dittricha',
    description: 'Największy park miejski z bogatą roślinnością, stawem i placem zabaw. Idealne miejsce na relaks i rekreację.',
    image: '/images/park.jpg',
    link: '/atrakcje/park-dittricha'
  },
  {
    title: 'Muzeum Lniarstwa',
    description: 'Jedyne w Polsce muzeum poświęcone historii przemysłu lniarskiego. Oryginalne maszyny i pokazy tkania.',
    image: '/images/muzeum-lniarstwa.jpg',
    link: '/atrakcje/muzeum-lniarstwa'
  },
  {
    title: 'Kościół MB Pocieszenia',
    description: 'Zabytkowy kościół z XIX wieku, jeden z najważniejszych zabytków sakralnych w Żyrardowie.',
    image: '/images/kosciol-mb-pocieszenia.jpg',
    link: '/atrakcje/kosciol-mb-pocieszenia'
  },
  {
    title: 'Centrum Kultury',
    description: 'Nowoczesne centrum kulturalne z bogatą ofertą wydarzeń, spektakli i wystaw.',
    image: '/images/centrum-kultury.jpg',
    link: '/atrakcje/centrum-kultury'
  }
]

const upcomingEvents = [
  {
    day: '15',
    month: 'Cze',
    title: 'Dni Żyrardowa',
    description: 'Doroczna impreza plenerowa z koncertami, atrakcjami dla dzieci i pokazami.'
  },
  {
    day: '22',
    month: 'Cze',
    title: 'Noc Muzeów',
    description: 'Specjalne zwiedzanie muzeów i zabytków w nocnej scenerii.'
  },
  {
    day: '05',
    month: 'Lip',
    title: 'Festiwal Lniarstwa',
    description: 'Pokazy tradycyjnych rzemiosł i warsztaty związane z przemysłem lniarskim.'
  }
]

export default function AtrakcjePage() {
  useEffect(() => {
    // Menu functionality
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navLinks = document.querySelector('.nav-links');
    const hasSubmenuItems = document.querySelectorAll('.has-submenu');

    // Mobile menu toggle
    if (mobileMenuBtn && navLinks) {
      mobileMenuBtn.addEventListener('click', () => {
        navLinks.classList.toggle('active');
      });
    }

    // Submenu toggle for mobile
    hasSubmenuItems.forEach(item => {
      const link = item.querySelector('a');
      if (link) {
        link.addEventListener('click', (e) => {
          if (window.innerWidth <= 992) {
            e.preventDefault();
            item.classList.toggle('active');
          }
        });
      }
    });

    // Search toggle
    const searchToggle = document.querySelector('.search-toggle');
    const searchContainer = document.querySelector('.search-container');

    if (searchToggle && searchContainer) {
      searchToggle.addEventListener('click', () => {
        searchContainer.classList.toggle('active');
      });
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.main-nav') && navLinks?.classList.contains('active')) {
        navLinks.classList.remove('active');
      }
    });

    // Cleanup
    return () => {
      if (mobileMenuBtn && navLinks) {
        mobileMenuBtn.removeEventListener('click', () => {});
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="site-header">
        <div className="container">
          <nav className="main-nav">
            <div className="logo">
              <Link href="/">
                <img src="/images/logo.png" alt="Żyrardów.poleca.to" />
              </Link>
            </div>

            <ul className="nav-links">
              <li><Link href="/">Strona Główna</Link></li>
              <li className="has-submenu">
                <Link href="/o-miescie">O mieście</Link>
                <ul className="submenu">
                  <li><Link href="/o-miescie/historia">Historia</Link></li>
                  <li><Link href="/o-miescie/zabytki">Zabytki</Link></li>
                  <li><Link href="/o-miescie/kultura-sport">Kultura i Sport</Link></li>
                </ul>
              </li>
              <li className="has-submenu">
                <Link href="/atrakcje">Atrakcje</Link>
                <ul className="submenu">
                  <li><Link href="/atrakcje/muzeum-lniarstwa">Muzeum Lniarstwa</Link></li>
                  <li><Link href="/atrakcje/kosciol-mb-pocieszenia">Kościół MB Pocieszenia</Link></li>
                  <li><Link href="/atrakcje/aquapark">Aquapark</Link></li>
                  <li><Link href="/atrakcje/kultura-sport">Kultura i Sport</Link></li>
                  <li><Link href="/atrakcje/park-dittricha">Park Dittricha</Link></li>
                  <li><Link href="/atrakcje/osada-fabryczna">Osada Fabryczna</Link></li>
                </ul>
              </li>
              <li><Link href="/katalog-firm">Katalog Firm</Link></li>
              <li><Link href="/kontakt">Kontakt</Link></li>
            </ul>

            <div className="nav-actions">
              <button className="search-toggle">
                <i className="fas fa-search"></i>
              </button>
              <a href="https://facebook.com/zyrardow.poleca.to" target="_blank" rel="noopener noreferrer" className="social-icon">
                <i className="fab fa-facebook-f"></i>
              </a>
              <button className="mobile-menu-btn">
                <span></span>
                <span></span>
                <span></span>
              </button>
            </div>
          </nav>

          <div className="search-container">
            <form className="search-form">
              <input type="text" placeholder="Szukaj firm, usług..." />
              <button type="submit">
                <i className="fas fa-search"></i>
              </button>
            </form>
          </div>
        </div>
      </header>

      {/* Breadcrumbs */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-blue-600">Strona główna</Link>
            <span>/</span>
            <span className="text-gray-900">Atrakcje</span>
          </nav>
        </div>
      </section>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-green-900 to-green-700 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Atrakcje Żyrardowa
            </h1>
            <p className="text-xl md:text-2xl opacity-90">
              Odkryj najciekawsze miejsca w mieście - zabytki przemysłowe, muzea, parki i miejsca rekreacji
            </p>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-900">
              Kategorie atrakcji
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {attractionCategories.map((category, index) => (
                <div key={index} className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="text-2xl font-semibold mb-4 text-gray-900">{category.title}</h3>
                  <ul className="space-y-2">
                    {category.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-center text-gray-700">
                        <i className="fas fa-check text-green-600 mr-3"></i>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Featured Attractions */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-900">
              Warto zobaczyć
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredAttractions.map((attraction, index) => (
                <div key={index} className="bg-gray-50 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                  <div className="h-48 bg-gray-200 overflow-hidden">
                    <img
                      src={attraction.image}
                      alt={attraction.title}
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        e.currentTarget.src = '/images/placeholder-attraction.jpg'
                      }}
                    />
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-semibold mb-3 text-gray-900">{attraction.title}</h3>
                    <p className="text-gray-600 mb-4 leading-relaxed">{attraction.description}</p>
                    <Link
                      href={attraction.link}
                      className="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold"
                    >
                      Czytaj więcej
                      <i className="fas fa-arrow-right ml-2"></i>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Museum Section */}
      <section className="py-16 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-gray-900">
                Muzeum Lniarstwa im. Filipa de Girarda
              </h2>
              <p className="text-lg text-gray-600">
                Jedyne w Polsce muzeum poświęcone historii przemysłu lniarskiego
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-2xl font-semibold mb-4 text-gray-900">O muzeum</h3>
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    Muzeum Lniarstwa im. Filipa de Girarda mieści się w dawnej Centrali Telefonicznej Zakładów Lniarskich.
                    To jedyne w Polsce muzeum poświęcone historii przemysłu lniarskiego, które prezentuje fascynującą
                    historię żyrardowskiej fabryki i proces produkcji tkanin lnianych.
                  </p>

                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <i className="fas fa-cog text-2xl text-blue-600 mb-2"></i>
                      <h4 className="font-semibold text-gray-900">Oryginalne maszyny</h4>
                      <p className="text-sm text-gray-600">Historyczne urządzenia w działaniu</p>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <i className="fas fa-palette text-2xl text-green-600 mb-2"></i>
                      <h4 className="font-semibold text-gray-900">Warsztaty</h4>
                      <p className="text-sm text-gray-600">Zajęcia dla dzieci i dorosłych</p>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <i className="fas fa-tshirt text-2xl text-purple-600 mb-2"></i>
                      <h4 className="font-semibold text-gray-900">Pokazy mody</h4>
                      <p className="text-sm text-gray-600">Prezentacje odzieży lnianej</p>
                    </div>
                  </div>

                  <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">Pokazy tkania na krośnie żakardowym</h4>
                    <p className="text-gray-700">
                      <strong>Każda środa, 10:00-15:00</strong> - Wyjątkowa okazja do zobaczenia,
                      jak działają historyczne maszyny na oryginalnym krośnie żakardowym z XIX wieku.
                    </p>
                  </div>
                </div>

                <div>
                  <div className="bg-gray-200 rounded-lg h-64 mb-6 overflow-hidden">
                    <img
                      src="/images/muzeum-lniarstwa.jpg"
                      alt="Muzeum Lniarstwa w Żyrardowie"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = '/images/placeholder-museum.jpg'
                      }}
                    />
                  </div>

                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-xl font-semibold mb-4 text-gray-900">Informacje praktyczne</h4>
                    <div className="space-y-3">
                      <div className="flex items-start">
                        <i className="fas fa-map-marker-alt text-blue-600 mt-1 mr-3"></i>
                        <div>
                          <strong>Adres:</strong><br />
                          ul. Centralna, Żyrardów
                        </div>
                      </div>
                      <div className="flex items-start">
                        <i className="fas fa-clock text-blue-600 mt-1 mr-3"></i>
                        <div>
                          <strong>Godziny otwarcia:</strong><br />
                          Wt-Nd: 10:00-16:00<br />
                          Poniedziałek: zamknięte
                        </div>
                      </div>
                      <div className="flex items-start">
                        <i className="fas fa-ticket-alt text-blue-600 mt-1 mr-3"></i>
                        <div>
                          <strong>Bilety:</strong><br />
                          Normalny: 8 zł<br />
                          Ulgowy: 5 zł
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Events Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-900">
              Wydarzenia kulturalne
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {upcomingEvents.map((event, index) => (
                <div key={index} className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
                  <div className="flex items-center mb-4">
                    <div className="bg-blue-600 text-white rounded-lg p-3 mr-4 text-center min-w-[60px]">
                      <div className="text-xl font-bold">{event.day}</div>
                      <div className="text-sm">{event.month}</div>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">{event.title}</h3>
                  </div>
                  <p className="text-gray-600 leading-relaxed">{event.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">
            Planujesz wizytę w Żyrardowie?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Śledź nas na Facebooku, aby być na bieżąco z wydarzeniami i atrakcjami w mieście!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="https://facebook.com/zyrardow.poleca.to"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center"
            >
              <i className="fab fa-facebook-f mr-2"></i>
              Polub nas na Facebooku
            </a>
            <Link
              href="/kontakt"
              className="bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-800 transition-colors border border-blue-400"
            >
              Skontaktuj się z nami
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="site-footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>Żyrardów.poleca.to</h3>
              <p>Portal lokalny miasta Żyrardów. Odkryj najlepsze firmy, usługi i atrakcje w naszym mieście.</p>
              <div className="social-links">
                <a href="https://facebook.com/zyrardow.poleca.to" target="_blank" rel="noopener noreferrer">
                  <i className="fab fa-facebook-f"></i>
                </a>
              </div>
            </div>

            <div className="footer-section">
              <h4>Nawigacja</h4>
              <ul>
                <li><Link href="/">Strona główna</Link></li>
                <li><Link href="/o-miescie">O mieście</Link></li>
                <li><Link href="/atrakcje">Atrakcje</Link></li>
                <li><Link href="/katalog-firm">Katalog firm</Link></li>
                <li><Link href="/kontakt">Kontakt</Link></li>
              </ul>
            </div>

            <div className="footer-section">
              <h4>Kontakt</h4>
              <div className="contact-info">
                <p><i className="fas fa-envelope"></i> <EMAIL></p>
                <p><i className="fas fa-map-marker-alt"></i> Żyrardów, Mazowieckie</p>
              </div>
            </div>

            <div className="footer-section">
              <h4>Informacje</h4>
              <ul>
                <li><Link href="/polityka-prywatnosci">Polityka prywatności</Link></li>
                <li><Link href="/regulamin">Regulamin</Link></li>
                <li><Link href="/dla-biznesu">Dla biznesu</Link></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 Żyrardów.poleca.to. Wszystkie prawa zastrzeżone.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
