/* Style dla strony Historia */

/* Hero section */
.page-hero {
    height: 500px;
    background-size: cover;
    background-position: center;
    position: relative;
    margin-top: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.5) 0%, rgba(0,0,0,0.7) 100%);
}

.page-hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.page-hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: var(--white);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.page-hero-content .lead {
    font-size: 1.5rem;
    margin-bottom: 0;
    color: var(--white);
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Breadcrumbs */
.breadcrumbs {
    background-color: var(--light-gray);
    padding: 15px 0;
    border-bottom: 1px solid var(--medium-gray);
}

.breadcrumbs-list {
    display: flex;
    list-style: none;
    flex-wrap: wrap;
}

.breadcrumbs-list li {
    display: flex;
    align-items: center;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.breadcrumbs-list li:not(:last-child)::after {
    content: '/';
    margin: 0 10px;
    color: var(--text-light);
}

.breadcrumbs-list a {
    color: var(--text-medium);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumbs-list a:hover {
    color: var(--primary-color);
}

/* Historia Section */
.history-section {
    padding: 80px 0;
}

.history-section .container {
    max-width: 1400px;
}

.history-intro {
    max-width: 1200px;
    margin: 0 auto 60px;
    text-align: center;
}

.history-intro .lead {
    font-size: 1.2rem;
    color: var(--text-dark);
    font-weight: 500;
    margin-bottom: 1.5rem;
}

/* Timeline */
.timeline,
.history-timeline {
    position: relative;
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px 0;
}

.timeline::before,
.history-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 4px;
    background-color: var(--primary-color);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: 60px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-content {
    position: relative;
    width: calc(50% - 50px);
    padding: 30px;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    transition: transform 0.3s, box-shadow 0.3s;
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.timeline-item:nth-child(odd) .timeline-content {
    margin-left: auto;
}

.timeline-item:nth-child(even) .timeline-content {
    margin-right: auto;
}

.timeline-content::before {
    content: '';
    position: absolute;
    top: 30px;
    width: 20px;
    height: 20px;
    background-color: var(--white);
    border: 4px solid var(--primary-color);
    border-radius: 50%;
}

.timeline-item:nth-child(odd) .timeline-content::before {
    left: -50px;
}

.timeline-item:nth-child(even) .timeline-content::before {
    right: -50px;
}

.timeline-date {
    display: inline-block;
    padding: 5px 15px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    margin-bottom: 15px;
}

.timeline-content h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.timeline-content p {
    color: var(--text-medium);
    margin-bottom: 0;
    line-height: 1.6;
}

.timeline-image {
    margin-top: 20px;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.timeline-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.5s ease;
}

.timeline-image:hover img {
    transform: scale(1.05);
}

/* Key Figures */
.key-figures {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.key-figures .container {
    max-width: 1400px;
}

.figures-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin-top: 40px;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.figure-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.figure-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.figure-image {
    height: 250px;
    overflow: hidden;
}

.figure-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.figure-card:hover .figure-image img {
    transform: scale(1.1);
}

.figure-content {
    padding: 25px;
}

.figure-content h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.figure-content .figure-role {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 15px;
    display: block;
}

.figure-content p {
    color: var(--text-medium);
    margin-bottom: 0;
    line-height: 1.6;
}

/* Historical Gallery */
.historical-gallery {
    padding: 80px 0;
}

.historical-gallery .container {
    max-width: 1400px;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 40px;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.gallery-item {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    position: relative;
    height: 250px;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 15px;
    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
    color: var(--white);
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s, transform 0.3s;
}

.gallery-item:hover .gallery-caption {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive */
@media (max-width: 1200px) {
    .figures-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .gallery-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .timeline::before {
        left: 30px;
    }

    .timeline-content {
        width: calc(100% - 80px);
        margin-left: 80px !important;
    }

    .timeline-item:nth-child(odd) .timeline-content::before,
    .timeline-item:nth-child(even) .timeline-content::before {
        left: -50px;
    }
}

@media (max-width: 768px) {
    .page-hero {
        height: 400px;
    }

    .page-hero-content h1 {
        font-size: 2.5rem;
    }

    .page-hero-content .lead {
        font-size: 1.2rem;
    }

    .figures-grid {
        grid-template-columns: 1fr;
    }

    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .gallery-grid {
        grid-template-columns: 1fr;
    }
}

/* History Cards */
.history-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin: 40px 0;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.history-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    position: relative;
    border: 2px solid transparent;
}

.history-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.card-year {
    position: absolute;
    top: -15px;
    left: 30px;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-weight: 700;
    font-size: 0.9rem;
    box-shadow: var(--shadow-sm);
}

.card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    margin: 20px auto 20px auto;
    color: var(--white);
    font-size: 1.5rem;
}

.history-card h3 {
    color: var(--text-dark);
    margin-bottom: 15px;
    font-size: 1.2rem;
    text-align: center;
}

.history-card p {
    color: var(--text-medium);
    line-height: 1.6;
    margin: 0;
    text-align: center;
}

@media (max-width: 768px) {
    .history-cards {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

/* History Facts */
.history-facts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-top: 40px;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.history-fact {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    text-align: center;
}

.history-fact:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.fact-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    margin: 0 auto 20px auto;
    color: var(--white) !important;
    font-size: 2rem;
}

.fact-icon i {
    color: var(--white) !important;
}

.fact-content h3 {
    color: var(--text-dark);
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.fact-content p {
    color: var(--text-medium);
    line-height: 1.6;
    margin: 0;
}
