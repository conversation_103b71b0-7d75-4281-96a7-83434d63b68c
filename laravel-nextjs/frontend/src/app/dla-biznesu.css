/* Style dla strony D<PERSON> Biznesu */

/* Hero section */
.page-hero {
    height: 500px;
    background-size: cover;
    background-position: center;
    position: relative;
    margin-top: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.5) 0%, rgba(0,0,0,0.7) 100%);
}

.page-hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.page-hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: var(--white);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.page-hero-content .lead {
    font-size: 1.5rem;
    margin-bottom: 0;
    color: var(--white);
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Breadcrumbs */
.breadcrumbs {
    background-color: var(--light-gray);
    padding: 15px 0;
    border-bottom: 1px solid var(--medium-gray);
}

.breadcrumbs-list {
    display: flex;
    list-style: none;
    flex-wrap: wrap;
}

.breadcrumbs-list li {
    display: flex;
    align-items: center;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.breadcrumbs-list li:not(:last-child)::after {
    content: '/';
    margin: 0 10px;
    color: var(--text-light);
}

.breadcrumbs-list a {
    color: var(--text-medium);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumbs-list a:hover {
    color: var(--primary-color);
}

/* Business Intro */
.business-intro {
    padding: 80px 0;
}

.intro-content {
    max-width: 900px;
    margin: 0 auto 60px;
    text-align: center;
}

.intro-content .lead {
    font-size: 1.2rem;
    color: var(--text-dark);
    font-weight: 500;
    margin-bottom: 1.5rem;
}

/* Business Advantages */
.business-advantages {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.advantages-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.advantage-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 30px;
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
}

.advantage-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.advantage-icon {
    width: 80px;
    height: 80px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.advantage-icon i {
    font-size: 32px;
    color: var(--primary-color);
}

.advantage-card h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.advantage-card p {
    color: var(--text-medium);
    margin-bottom: 0;
    line-height: 1.6;
}

/* Business Support */
.business-support {
    padding: 80px 0;
}

.support-tabs {
    margin-top: 40px;
}

.support-tabs-nav {
    display: flex;
    border-bottom: 2px solid var(--medium-gray);
    margin-bottom: 30px;
}

.support-tab-item {
    padding: 15px 30px;
    color: var(--text-medium);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.support-tab-item::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 3px;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.support-tab-item:hover,
.support-tab-item.active {
    color: var(--primary-color);
}

.support-tab-item:hover::after,
.support-tab-item.active::after {
    width: 100%;
}

.support-tab-content {
    display: none;
}

.support-tab-content.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.support-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
}

.support-image {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.support-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.5s ease;
}

.support-image:hover img {
    transform: scale(1.05);
}

.support-text h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.support-text p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.7;
}

.support-list {
    margin-bottom: 25px;
}

.support-list-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 15px;
}

.support-list-item:last-child {
    margin-bottom: 0;
}

.support-list-icon {
    width: 30px;
    height: 30px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.support-list-icon i {
    font-size: 14px;
    color: var(--primary-color);
}

.support-list-text h4 {
    font-size: 1.1rem;
    margin-bottom: 5px;
    color: var(--text-dark);
}

.support-list-text p {
    font-size: 0.95rem;
    margin-bottom: 0;
}

/* Investment Areas */
.investment-areas {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.areas-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.area-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.area-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.area-image {
    height: 250px;
    overflow: hidden;
}

.area-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.area-card:hover .area-image img {
    transform: scale(1.1);
}

.area-content {
    padding: 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.area-content h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.area-info {
    margin-bottom: 20px;
}

.area-info-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 10px;
    color: var(--text-medium);
}

.area-info-item:last-child {
    margin-bottom: 0;
}

.area-info-item i {
    color: var(--primary-color);
    margin-top: 4px;
}

.area-content p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.6;
    flex: 1;
}

/* Contact Form */
.business-contact {
    padding: 80px 0;
}

.contact-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    margin-top: 40px;
    align-items: center;
}

.contact-info h3 {
    font-size: 1.8rem;
    margin-bottom: 25px;
    color: var(--text-dark);
}

.contact-info p {
    color: var(--text-medium);
    margin-bottom: 30px;
    line-height: 1.7;
}

.contact-details {
    margin-bottom: 30px;
}

.contact-detail-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.contact-detail-item:last-child {
    margin-bottom: 0;
}

.contact-detail-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-detail-icon i {
    font-size: 18px;
    color: var(--primary-color);
}

.contact-detail-text h4 {
    font-size: 1.1rem;
    margin-bottom: 5px;
    color: var(--text-dark);
}

.contact-detail-text p,
.contact-detail-text a {
    color: var(--text-medium);
    margin-bottom: 0;
}

.contact-detail-text a:hover {
    color: var(--primary-color);
}

.contact-form {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 40px;
}

.contact-form h3 {
    font-size: 1.5rem;
    margin-bottom: 25px;
    color: var(--text-dark);
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-dark);
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-sm);
    font-family: var(--font-body);
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.1);
}

textarea.form-control {
    min-height: 150px;
    resize: vertical;
}

/* Responsive */
@media (max-width: 1200px) {
    .advantages-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .areas-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 992px) {
    .support-content,
    .contact-container {
        grid-template-columns: 1fr;
        gap: 40px;
    }
}

@media (max-width: 768px) {
    .page-hero {
        height: 400px;
    }
    
    .page-hero-content h1 {
        font-size: 2.5rem;
    }
    
    .page-hero-content .lead {
        font-size: 1.2rem;
    }
    
    .advantages-grid {
        grid-template-columns: 1fr;
    }
    
    .support-tabs-nav {
        flex-wrap: wrap;
    }
    
    .support-tab-item {
        flex: 0 0 50%;
        text-align: center;
        padding: 15px;
    }
}

@media (max-width: 576px) {
    .support-tab-item {
        flex: 0 0 100%;
    }
    
    .contact-form {
        padding: 25px;
    }
}
