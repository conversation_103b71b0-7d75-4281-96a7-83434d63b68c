'use client'

import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { useState } from 'react'

const contactInfo = [
  {
    icon: 'fas fa-envelope',
    title: 'Email',
    content: '<EMAIL>',
    link: 'mailto:<EMAIL>'
  },
  {
    icon: 'fas fa-phone-alt',
    title: 'Telefon',
    content: '570 888 999',
    link: 'tel:+48570888999'
  },
  {
    icon: 'fas fa-map-marker-alt',
    title: 'Adres',
    content: 'ul. Przykładowa 123\n96-300 Żyrardów',
    link: null
  },
  {
    icon: 'fas fa-clock',
    title: 'God<PERSON><PERSON> pracy',
    content: 'Poniedziałek - Piątek: 9:00 - 17:00\nSobota - Niedziela: Zamknięte',
    link: null
  }
]

export default function KontaktPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitStatus('idle')

    try {
      // Tutaj będzie integracja z API
      await new Promise(resolve => setTimeout(resolve, 1000)) // Symulacja wysyłania
      setSubmitStatus('success')
      setFormData({ name: '', email: '', subject: '', message: '' })
    } catch (error) {
      setSubmitStatus('error')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumbs */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-blue-600">Strona główna</Link>
            <span>/</span>
            <span className="text-gray-900">Kontakt</span>
          </nav>
        </div>
      </section>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Kontakt
            </h1>
            <p className="text-xl md:text-2xl opacity-90">
              Masz pytania? Chcesz dodać swoją firmę? Skontaktuj się z nami!
            </p>
          </div>
        </div>
      </section>

      {/* Contact Info Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-gray-900">Dane kontaktowe</h2>
              <p className="text-lg text-gray-600">Skontaktuj się z nami w dogodny dla Ciebie sposób</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
              {contactInfo.map((info, index) => (
                <div key={index} className="bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl transition-shadow">
                  <div className="bg-blue-100 text-blue-600 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <i className={info.icon + ' text-2xl'}></i>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-gray-900">{info.title}</h3>
                  {info.link ? (
                    <a 
                      href={info.link} 
                      className="text-blue-600 hover:text-blue-800 transition-colors"
                    >
                      {info.content.split('\n').map((line, i) => (
                        <span key={i}>
                          {line}
                          {i < info.content.split('\n').length - 1 && <br />}
                        </span>
                      ))}
                    </a>
                  ) : (
                    <p className="text-gray-600">
                      {info.content.split('\n').map((line, i) => (
                        <span key={i}>
                          {line}
                          {i < info.content.split('\n').length - 1 && <br />}
                        </span>
                      ))}
                    </p>
                  )}
                </div>
              ))}
            </div>

            {/* Social Media */}
            <div className="text-center">
              <h3 className="text-2xl font-semibold mb-6 text-gray-900">
                Znajdź nas w mediach społecznościowych
              </h3>
              <div className="flex justify-center space-x-6">
                <a 
                  href="https://facebook.com/zyrardow.poleca.to" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors"
                >
                  <i className="fab fa-facebook-f text-xl"></i>
                </a>
                <a 
                  href="https://instagram.com/zyrardow.poleca.to" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="bg-pink-600 text-white w-12 h-12 rounded-full flex items-center justify-center hover:bg-pink-700 transition-colors"
                >
                  <i className="fab fa-instagram text-xl"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-gray-900">Napisz do nas</h2>
              <p className="text-lg text-gray-600">Wypełnij formularz, a skontaktujemy się z Tobą</p>
            </div>

            <form onSubmit={handleSubmit} className="bg-gray-50 rounded-lg p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    Imię i nazwisko *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Wprowadź swoje imię i nazwisko"
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Wprowadź swój adres email"
                  />
                </div>
              </div>

              <div className="mb-6">
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                  Temat *
                </label>
                <select
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Wybierz temat</option>
                  <option value="dodanie-firmy">Dodanie firmy do portalu</option>
                  <option value="edycja-firmy">Edycja danych firmy</option>
                  <option value="wspolpraca">Współpraca</option>
                  <option value="reklama">Reklama</option>
                  <option value="problem-techniczny">Problem techniczny</option>
                  <option value="inne">Inne</option>
                </select>
              </div>

              <div className="mb-6">
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                  Wiadomość *
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  rows={6}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Opisz szczegółowo swoją sprawę..."
                ></textarea>
              </div>

              {submitStatus === 'success' && (
                <div className="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
                  Dziękujemy za wiadomość! Skontaktujemy się z Tobą wkrótce.
                </div>
              )}

              {submitStatus === 'error' && (
                <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                  Wystąpił błąd podczas wysyłania wiadomości. Spróbuj ponownie.
                </div>
              )}

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Wysyłanie...' : 'Wyślij wiadomość'}
              </button>
            </form>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-gray-900">Nasza lokalizacja</h2>
              <p className="text-lg text-gray-600">Znajdź nas na mapie</p>
            </div>

            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <iframe 
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d19638.65242552444!2d20.42916678659994!3d52.05000337972046!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x471942f7e05e8a01%3A0x5e9537d9e1152ab3!2s%C5%BByrard%C3%B3w!5e0!3m2!1spl!2spl!4v1623345678901!5m2!1spl!2spl" 
                width="100%" 
                height="450" 
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
              ></iframe>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
