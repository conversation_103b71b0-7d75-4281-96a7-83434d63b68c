/* Style dla stron<PERSON> */

/* Hero section */
.page-hero {
    height: 500px;
    background-size: cover;
    background-position: center;
    position: relative;
    margin-top: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.5) 0%, rgba(0,0,0,0.7) 100%);
}

.page-hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.page-hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: var(--white);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.page-hero-content .lead {
    font-size: 1.5rem;
    margin-bottom: 0;
    color: var(--white);
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Breadcrumbs */
.breadcrumbs {
    background-color: var(--light-gray);
    padding: 15px 0;
    border-bottom: 1px solid var(--medium-gray);
}

.breadcrumbs-list {
    display: flex;
    list-style: none;
    flex-wrap: wrap;
}

.breadcrumbs-list li {
    display: flex;
    align-items: center;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.breadcrumbs-list li:not(:last-child)::after {
    content: '/';
    margin: 0 10px;
    color: var(--text-light);
}

.breadcrumbs-list a {
    color: var(--text-medium);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumbs-list a:hover {
    color: var(--primary-color);
}

/* Monuments Intro */
.monuments-intro {
    padding: 80px 0;
}

.intro-content {
    max-width: 900px;
    margin: 0 auto 60px;
    text-align: center;
}

.intro-content .lead {
    font-size: 1.2rem;
    color: var(--text-dark);
    font-weight: 500;
    margin-bottom: 1.5rem;
}

/* Monuments Categories */
.monuments-categories {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 50px;
}

.category-filter {
    padding: 10px 20px;
    background-color: var(--white);
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    color: var(--text-medium);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.category-filter:hover,
.category-filter.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

/* Monuments List */
.monuments-list {
    margin-bottom: 80px;
}

.monument-item {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 40px;
    margin-bottom: 50px;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.monument-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.monument-item:last-child {
    margin-bottom: 0;
}

.monument-image {
    height: 100%;
    overflow: hidden;
}

.monument-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.monument-item:hover .monument-image img {
    transform: scale(1.1);
}

.monument-content {
    padding: 30px;
    display: flex;
    flex-direction: column;
}

.monument-category {
    display: inline-block;
    padding: 5px 15px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 15px;
    align-self: flex-start;
}

.monument-content h3 {
    font-size: 1.8rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.monument-info {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
}

.monument-info-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-medium);
    font-size: 0.95rem;
}

.monument-info-item i {
    color: var(--primary-color);
}

.monument-content p {
    color: var(--text-medium);
    margin-bottom: 25px;
    line-height: 1.7;
    flex: 1;
}

.monument-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.monument-status {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.monument-status.protected {
    color: #4CAF50;
}

.monument-status.in-renovation {
    color: #FFC107;
}

.monument-status.endangered {
    color: #F44336;
}

/* Monument Gallery */
.monument-gallery {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-top: 40px;
}

.gallery-item {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    position: relative;
    height: 250px;
    cursor: pointer;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 15px;
    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
    color: var(--white);
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s, transform 0.3s;
}

.gallery-item:hover .gallery-caption {
    opacity: 1;
    transform: translateY(0);
}

/* Monuments Map */
.monuments-map {
    padding: 80px 0;
}

.map-container {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    margin-top: 40px;
    height: 500px;
}

.map-legend {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 30px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-medium);
    font-size: 0.95rem;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
}

.legend-color.industrial {
    background-color: #F44336;
}

.legend-color.religious {
    background-color: #2196F3;
}

.legend-color.residential {
    background-color: #4CAF50;
}

.legend-color.public {
    background-color: #FFC107;
}

/* Responsive */
@media (max-width: 1200px) {
    .gallery-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .monument-item {
        grid-template-columns: 1fr;
    }
    
    .monument-image {
        height: 300px;
    }
}

@media (max-width: 768px) {
    .page-hero {
        height: 400px;
    }
    
    .page-hero-content h1 {
        font-size: 2.5rem;
    }
    
    .page-hero-content .lead {
        font-size: 1.2rem;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .map-container {
        height: 400px;
    }
}

@media (max-width: 576px) {
    .monuments-categories {
        flex-direction: column;
        align-items: center;
    }
    
    .category-filter {
        width: 100%;
        text-align: center;
    }
    
    .gallery-grid {
        grid-template-columns: 1fr;
    }
}
