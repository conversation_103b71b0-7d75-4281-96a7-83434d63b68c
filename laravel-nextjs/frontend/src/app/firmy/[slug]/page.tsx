'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';

interface Company {
  id: number;
  name: string;
  slug: string;
  description?: string;
  short_description?: string;
  rating: number;
  category?: string;
  is_open_now: boolean;
  logo?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  facebook?: string;
  instagram?: string;
  opening_hours?: {
    [key: string]: string;
  };
  gallery?: string[];
}

export default function CompanyDetailPage() {
  const params = useParams();
  const slug = params.slug as string;
  const [company, setCompany] = useState<Company | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCompany = async () => {
      try {
        const response = await fetch(`http://127.0.0.1:8000/api/companies/${slug}`);

        if (!response.ok) {
          throw new Error('Firma nie została znaleziona');
        }

        const data = await response.json();
        setCompany(data.data);
      } catch (err) {
        setError('Nie udało się pobrać danych firmy');
        console.error('Company fetch error:', err);

        // Fallback to mock data
        const mockCompany: Company = {
          id: 1,
          name: 'Restauracja Pod Lipami',
          slug: 'restauracja-pod-lipami',
          description: 'Restauracja Pod Lipami to miejsce, gdzie tradycyjna polska kuchnia spotyka się z nowoczesnym podejściem do gastronomii. Oferujemy dania przygotowywane z najwyższej jakości składników, w przytulnej atmosferze idealnej na rodzinne spotkania i romantyczne kolacje.',
          short_description: 'Tradycyjna kuchnia polska w sercu Żyrardowa',
          rating: 4.8,
          category: 'Gastronomia',
          is_open_now: true,
          address: 'ul. Główna 15, 96-300 Żyrardów',
          phone: '+48 ***********',
          email: '<EMAIL>',
          website: 'https://www.podlipami.pl',
          facebook: 'https://facebook.com/podlipami',
          instagram: 'https://instagram.com/podlipami',
          opening_hours: {
            'Poniedziałek': '12:00 - 22:00',
            'Wtorek': '12:00 - 22:00',
            'Środa': '12:00 - 22:00',
            'Czwartek': '12:00 - 22:00',
            'Piątek': '12:00 - 23:00',
            'Sobota': '12:00 - 23:00',
            'Niedziela': '12:00 - 21:00'
          },
          gallery: [
            '/images/restaurant1.jpg',
            '/images/restaurant2.jpg',
            '/images/restaurant3.jpg'
          ]
        };
        setCompany(mockCompany);
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchCompany();
    }
  }, [slug]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error && !company) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Firma nie została znaleziona</h1>
          <p className="text-gray-600 mb-8">Przepraszamy, ale nie mogliśmy znaleźć tej firmy.</p>
          <Link href="/firmy" className="btn btn-primary">
            Powrót do listy firm
          </Link>
        </div>
      </div>
    );
  }

  if (!company) return null;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="site-header">
        <div className="container">
          <nav className="main-nav">
            <div className="logo">
              <Link href="/">
                <Image
                  src="/images/logo-zyrardow-poleca.png"
                  alt="Żyrardów.poleca.to"
                  width={200}
                  height={60}
                />
              </Link>
            </div>
            <ul className="nav-links">
              <li><Link href="/">Strona główna</Link></li>
              <li><Link href="/kategorie">Kategorie</Link></li>
              <li><Link href="/firmy">Firmy</Link></li>
              <li><Link href="/kupony">Kupony</Link></li>
            </ul>
          </nav>
        </div>
      </header>

      {/* Breadcrumbs */}
      <section className="breadcrumbs">
        <div className="container">
          <nav>
            <Link href="/">Strona główna</Link>
            <span>/</span>
            <Link href="/firmy">Firmy</Link>
            <span>/</span>
            <span>{company.name}</span>
          </nav>
        </div>
      </section>

      {/* Company Header */}
      <section className="company-header">
        <div className="container">
          <div className="company-header-content">
            <div className="company-logo-large">
              {company.logo ? (
                <Image src={company.logo} alt={company.name} width={120} height={120} />
              ) : (
                <div className="company-logo-placeholder-large">
                  <i className="fas fa-building"></i>
                </div>
              )}
            </div>

            <div className="company-header-info">
              <h1>{company.name}</h1>
              <div className="company-meta">
                <span className="company-category">{company.category}</span>
                <div className="company-rating">
                  <div className="stars">
                    {[...Array(5)].map((_, i) => (
                      <i
                        key={i}
                        className={`fas fa-star ${i < Math.floor(company.rating) ? 'active' : ''}`}
                      ></i>
                    ))}
                  </div>
                  <span className="rating-value">{company.rating}/5</span>
                </div>
                <div className={`status ${company.is_open_now ? 'open' : 'closed'}`}>
                  <i className="fas fa-clock"></i>
                  {company.is_open_now ? 'Otwarte' : 'Zamknięte'}
                </div>
              </div>
              <p className="company-short-description">{company.short_description}</p>
            </div>

            <div className="company-actions">
              <button className="btn btn-primary">
                <i className="fas fa-heart"></i>
                Dodaj do ulubionych
              </button>
              <button className="btn btn-outline">
                <i className="fas fa-share"></i>
                Udostępnij
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Company Details */}
      <section className="company-details">
        <div className="container">
          <div className="company-details-grid">
            <div className="company-main-content">
              <div className="company-description">
                <h2>O firmie</h2>
                <p>{company.description || company.short_description}</p>
              </div>

              {company.gallery && company.gallery.length > 0 && (
                <div className="company-gallery">
                  <h2>Galeria</h2>
                  <div className="gallery-grid">
                    {company.gallery.map((image, index) => (
                      <div key={index} className="gallery-item">
                        <Image src={image} alt={`${company.name} - zdjęcie ${index + 1}`} width={300} height={200} />
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="company-sidebar">
              <div className="company-contact-card">
                <h3>Dane kontaktowe</h3>
                <div className="contact-info">
                  {company.address && (
                    <div className="contact-item">
                      <i className="fas fa-map-marker-alt"></i>
                      <span>{company.address}</span>
                    </div>
                  )}
                  {company.phone && (
                    <div className="contact-item">
                      <i className="fas fa-phone"></i>
                      <a href={`tel:${company.phone}`}>{company.phone}</a>
                    </div>
                  )}
                  {company.email && (
                    <div className="contact-item">
                      <i className="fas fa-envelope"></i>
                      <a href={`mailto:${company.email}`}>{company.email}</a>
                    </div>
                  )}
                  {company.website && (
                    <div className="contact-item">
                      <i className="fas fa-globe"></i>
                      <a href={company.website} target="_blank" rel="noopener noreferrer">
                        Strona internetowa
                      </a>
                    </div>
                  )}
                </div>

                {(company.facebook || company.instagram) && (
                  <div className="social-links">
                    <h4>Media społecznościowe</h4>
                    <div className="social-buttons">
                      {company.facebook && (
                        <a href={company.facebook} target="_blank" rel="noopener noreferrer" className="social-btn facebook">
                          <i className="fab fa-facebook-f"></i>
                          Facebook
                        </a>
                      )}
                      {company.instagram && (
                        <a href={company.instagram} target="_blank" rel="noopener noreferrer" className="social-btn instagram">
                          <i className="fab fa-instagram"></i>
                          Instagram
                        </a>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {company.opening_hours && (
                <div className="opening-hours-card">
                  <h3>Godziny otwarcia</h3>
                  <div className="opening-hours">
                    {Object.entries(company.opening_hours).map(([day, hours]) => (
                      <div key={day} className="opening-hours-item">
                        <span className="day">{day}</span>
                        <span className="hours">{hours}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="site-footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <div className="footer-logo">
                <Image
                  src="/images/logo-zyrardow-poleca.png"
                  alt="Żyrardów.poleca.to"
                  width={150}
                  height={45}
                />
              </div>
              <p>Portal lokalny Żyrardowa - odkryj najlepsze firmy, usługi i atrakcje w mieście Filip de Girard.</p>
            </div>
          </div>
          <div className="footer-bottom">
            <p>&copy; 2025 Żyrardów.poleca.to - Wszystkie prawa zastrzeżone</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
