'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';

interface Company {
  id: number;
  name: string;
  slug: string;
  short_description?: string;
  rating: number;
  category?: string;
  is_open_now: boolean;
  logo?: string;
  address?: string;
  phone?: string;
}

export default function FirmyPage() {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  useEffect(() => {
    // Mock data - replace with API call later
    const mockCompanies: Company[] = [
      { 
        id: 1, 
        name: 'Restauracja Pod Lipami', 
        slug: 'restauracja-pod-lipami', 
        short_description: 'Tradycyjna kuchnia polska w sercu Żyrardowa. Serwujemy domowe potrawy z najwyższej jakości składników.', 
        rating: 4.8, 
        category: 'Gastronomia', 
        is_open_now: true,
        address: 'ul. Główna 15, Żyrardów',
        phone: '+48 ***********'
      },
      { 
        id: 2, 
        name: 'Salon Piękności Venus', 
        slug: 'salon-pieknosci-venus', 
        short_description: 'Profesjonalne usługi kosmetyczne i fryzjerskie. Nowoczesny sprzęt i doświadczony personel.', 
        rating: 4.6, 
        category: 'Uroda', 
        is_open_now: false,
        address: 'ul. Piękna 8, Żyrardów',
        phone: '+48 ***********'
      },
      { 
        id: 3, 
        name: 'Warsztat Samochodowy Auto-Serwis', 
        slug: 'warsztat-auto-serwis', 
        short_description: 'Kompleksowe naprawy i serwis pojazdów. Specjalizujemy się w diagnostyce komputerowej.', 
        rating: 4.7, 
        category: 'Motoryzacja', 
        is_open_now: true,
        address: 'ul. Przemysłowa 22, Żyrardów',
        phone: '+48 555 123 456'
      },
      { 
        id: 4, 
        name: 'Apteka Centralna', 
        slug: 'apteka-centralna', 
        short_description: 'Apteka z pełnym asortymentem leków i suplementów. Profesjonalne doradztwo farmaceutyczne.', 
        rating: 4.5, 
        category: 'Zdrowie i uroda', 
        is_open_now: true,
        address: 'ul. Rynek 3, Żyrardów',
        phone: '+48 222 333 444'
      },
      { 
        id: 5, 
        name: 'Sklep Sportowy Champion', 
        slug: 'sklep-sportowy-champion', 
        short_description: 'Szeroki wybór sprzętu sportowego i odzieży. Marki światowej klasy w przystępnych cenach.', 
        rating: 4.4, 
        category: 'Sport i rekreacja', 
        is_open_now: false,
        address: 'ul. Sportowa 12, Żyrardów',
        phone: '+48 666 777 888'
      },
      { 
        id: 6, 
        name: 'Pizzeria Bella Italia', 
        slug: 'pizzeria-bella-italia', 
        short_description: 'Autentyczna pizza włoska z pieca opalanego drewnem. Dostawa do domu w całym Żyrardowie.', 
        rating: 4.9, 
        category: 'Gastronomia', 
        is_open_now: true,
        address: 'ul. Włoska 7, Żyrardów',
        phone: '+48 ***********'
      }
    ];
    
    setCompanies(mockCompanies);
    setLoading(false);
  }, []);

  const filteredCompanies = companies.filter(company => {
    const matchesSearch = company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         company.short_description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === '' || company.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const categories = [...new Set(companies.map(company => company.category))];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <Link href="/" className="text-2xl font-bold text-blue-600">
              Żyrardów.poleca.to
            </Link>
            <nav className="hidden md:flex space-x-8">
              <Link href="/" className="text-gray-600 hover:text-blue-600">Strona główna</Link>
              <Link href="/kategorie" className="text-gray-600 hover:text-blue-600">Kategorie</Link>
              <Link href="/firmy" className="text-blue-600 font-medium">Firmy</Link>
              <Link href="/kupony" className="text-gray-600 hover:text-blue-600">Kupony</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Firmy w Żyrardowie
          </h1>
          <p className="text-xl text-blue-100">
            Odkryj najlepsze lokalne firmy i usługi w naszym mieście
          </p>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="py-8 bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                <input
                  type="text"
                  placeholder="Szukaj firm..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="md:w-64">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Wszystkie kategorie</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </section>

      {/* Companies Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <p className="text-gray-600">
              Znaleziono {filteredCompanies.length} {filteredCompanies.length === 1 ? 'firmę' : 'firm'}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredCompanies.map((company) => (
              <div key={company.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {company.name}
                    </h3>
                    <p className="text-sm text-blue-600 mb-2">
                      {company.category}
                    </p>
                  </div>
                  <div className="flex items-center">
                    <div className="flex text-yellow-400">
                      {[...Array(5)].map((_, i) => (
                        <i 
                          key={i}
                          className={`fas fa-star ${i < Math.floor(company.rating) ? '' : 'text-gray-300'}`}
                        ></i>
                      ))}
                    </div>
                    <span className="ml-2 text-sm font-medium text-gray-900">
                      {company.rating}
                    </span>
                  </div>
                </div>

                <p className="text-gray-600 mb-4 line-clamp-3">
                  {company.short_description}
                </p>

                <div className="space-y-2 mb-4">
                  {company.address && (
                    <div className="flex items-center text-sm text-gray-500">
                      <i className="fas fa-map-marker-alt mr-2"></i>
                      {company.address}
                    </div>
                  )}
                  {company.phone && (
                    <div className="flex items-center text-sm text-gray-500">
                      <i className="fas fa-phone mr-2"></i>
                      {company.phone}
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center text-sm">
                    <div className={`w-2 h-2 rounded-full mr-2 ${company.is_open_now ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span className={company.is_open_now ? 'text-green-600' : 'text-red-600'}>
                      {company.is_open_now ? 'Otwarte' : 'Zamknięte'}
                    </span>
                  </div>
                  <Link
                    href={`/firmy/${company.slug}`}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Zobacz szczegóły
                  </Link>
                </div>
              </div>
            ))}
          </div>

          {filteredCompanies.length === 0 && (
            <div className="text-center py-12">
              <i className="fas fa-search text-6xl text-gray-300 mb-4"></i>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Nie znaleziono firm
              </h3>
              <p className="text-gray-600">
                Spróbuj zmienić kryteria wyszukiwania lub wybierz inną kategorię
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p>&copy; 2025 Żyrardów.poleca.to - Wszystkie prawa zastrzeżone</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
