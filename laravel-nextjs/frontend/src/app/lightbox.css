/* Lightbox Styles */
.lightbox {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lightbox.active {
    display: flex;
    opacity: 1;
}

.lightbox-content {
    background-color: #fff;
    border-radius: 8px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.lightbox.active .lightbox-content {
    transform: scale(1);
}

.lightbox-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    z-index: 10;
    transition: color 0.2s ease;
}

.lightbox-close:hover {
    color: #ff6600;
}

.lightbox-body {
    padding: 30px;
}

/* Coupon Details Styles */
.coupon-details-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.coupon-details-logo {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 20px;
    flex-shrink: 0;
}

.coupon-details-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.coupon-details-title {
    flex: 1;
}

.coupon-details-title h2 {
    margin: 0 0 5px;
    font-size: 22px;
    color: #333;
}

.coupon-details-title p {
    margin: 0;
    color: #ff6600;
    font-weight: 500;
}

.coupon-details-content {
    margin-bottom: 20px;
}

.coupon-details-content p {
    margin: 0 0 10px;
    line-height: 1.6;
    color: #555;
}

.coupon-details-code {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
    position: relative;
}

.coupon-details-code-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.coupon-details-code-value {
    font-size: 24px;
    font-weight: 700;
    color: #ff6600;
    letter-spacing: 2px;
    padding: 10px;
    background-color: #fff;
    border: 2px dashed #ff6600;
    border-radius: 4px;
    margin-bottom: 15px;
}

.coupon-copy-btn {
    background-color: #ff6600;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.coupon-copy-btn:hover {
    background-color: #e55c00;
}

.coupon-copy-btn:disabled {
    background-color: #4CAF50;
    cursor: default;
}

.coupon-details-info {
    display: grid;
    gap: 15px;
}

.coupon-details-info-item {
    display: flex;
    align-items: flex-start;
}

.coupon-details-info-label {
    font-weight: 500;
    color: #666;
    width: 100px;
    flex-shrink: 0;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .lightbox-content {
        width: 95%;
    }
    
    .coupon-details-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .coupon-details-logo {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .coupon-details-info-item {
        flex-direction: column;
    }
    
    .coupon-details-info-label {
        width: 100%;
        margin-bottom: 5px;
    }
}
