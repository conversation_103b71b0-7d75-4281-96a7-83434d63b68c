@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --primary-color: #FF6B00; /* Pomarańczowy */
    --primary-dark: #E05A00;
    --primary-light: #FF8533;
    --primary-color-dark: #E05A00; /* <PERSON><PERSON> d<PERSON> */
    --secondary-color: #666666; /* Szary */
    --secondary-dark: #4D4D4D;
    --secondary-light: #808080;
    --accent-color: #FFB74D; /* <PERSON><PERSON><PERSON>owy */
    --success-color: #27AE60; /* <PERSON><PERSON><PERSON> dla sukcesu */
    --danger: #e74c3c; /* Czerwony dla błędów */
    --warning: #f39c12; /* Pomarańczowy dla ostrzeżeń */
    --info: #3498db; /* Niebieski dla informacji */
    --text-dark: #333333;
    --text-medium: #666666;
    --text-light: #999999;
    --white: #FFFFFF;
    --off-white: #F9F9F9;
    --light-gray: #F5F5F5;
    --medium-gray: #E0E0E0;
    --dark-gray: #333333;
    --border-color: #E0E0E0;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 20px rgba(0,0,0,0.1);
    --shadow-xl: 0 20px 40px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 16px;
    --font-heading: 'Playfair Display', serif;
    --font-body: 'Montserrat', sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-body);
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--white);
    overflow-x: hidden;
    font-size: 16px;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 1rem;
}

h1 {
    font-size: 3.5rem;
}

h2 {
    font-size: 2.5rem;
}

h3 {
    font-size: 1.75rem;
}

h4 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1.5rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-subtitle {
    color: var(--text-medium);
    font-size: 1.2rem;
    margin-top: -0.5rem;
}

/* Header i nawigacja */
.site-header {
    background-color: var(--white);
    padding: 15px 0;
    box-shadow: var(--shadow-md);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95);
}

.main-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    text-align: center;
    transform: translateY(0);
    transition: var(--transition);
}

.logo:hover {
    transform: translateY(-2px);
}

.logo img {
    max-width: 200px;
    height: auto;
    filter: drop-shadow(var(--shadow-sm));
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 20px;
    margin-left: auto; /* Wyrównanie menu do prawej */
    margin-right: 20px;
}

.nav-links li {
    position: relative;
}

.nav-links a {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    padding: 10px;
    transition: var(--transition);
    position: relative;
    font-size: 0.95rem;
}

/* Submenu styles */
.has-submenu {
    position: relative;
}

.has-submenu > a::before {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-left: 5px;
    font-size: 0.8em;
    transition: transform 0.3s;
}

.has-submenu:hover > a::before {
    transform: rotate(180deg);
}

.submenu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #fff;
    min-width: 220px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    padding: 10px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s;
    z-index: 100;
}

.has-submenu:hover .submenu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.submenu li {
    margin: 0;
    padding: 0;
    display: block;
}

.submenu a {
    display: block;
    padding: 8px 20px;
    color: #333;
    font-weight: 400;
    font-size: 0.95em;
    transition: all 0.3s;
}

.submenu a:hover {
    background-color: #f8f8f8;
    color: var(--primary-color);
    padding-left: 25px;
}

.submenu a::after {
    display: none;
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.nav-links a:hover::after,
.nav-links a.active::after {
    width: 100%;
}

.nav-links a.active {
    color: var(--primary-color);
    font-weight: 600;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.search-toggle,
.mobile-menu-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 10px;
    color: var(--text-dark);
    transition: var(--transition);
}

.search-toggle:hover,
.mobile-menu-btn:hover {
    color: var(--primary-color);
}

.social-icon {
    color: var(--text-dark);
    font-size: 1.2rem;
    transition: var(--transition);
}

.social-icon:hover {
    color: var(--primary-color);
}

.mobile-menu-btn {
    display: none;
}

.mobile-menu-btn span {
    display: block;
    width: 25px;
    height: 2px;
    background: #000000; /* Czarny hamburger */
    margin: 5px 0;
    transition: var(--transition);
}

.search-container {
    display: none;
    padding: 15px 0;
    background-color: var(--light-gray);
    border-top: 1px solid var(--medium-gray);
}

.search-form {
    display: flex;
    max-width: 600px;
    margin: 0 auto;
}

.search-form input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
    font-family: var(--font-body);
    font-size: 1rem;
}

.search-form button {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 0 20px;
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
    cursor: pointer;
    transition: var(--transition);
}

.search-form button:hover {
    background: var(--primary-dark);
}

/* Hero Section */
.hero {
    padding-top: 100px;
    position: relative;
    overflow: hidden;
}

.hero-slider {
    position: relative;
    height: 600px;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    opacity: 0;
    transition: opacity 1s ease;
    z-index: 1;
    overflow: hidden;
}

.hero-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 100%);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    z-index: -1;
}

.hero-slide-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    filter: blur(12px);
    transform: scale(1.1);
    z-index: -1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 100%);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    z-index: -1;
}

.hero-slide.active {
    opacity: 1;
    z-index: 2;
}

.hero-content {
    max-width: 700px;
    color: var(--white);
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    animation: fadeInUp 1s ease;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: var(--white);
}

.hero-content .lead {
    font-size: 1.4rem;
    margin-bottom: 2rem;
    color: var(--white);
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-controls {
    position: absolute;
    bottom: 30px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    z-index: 10;
}

/* Ukrywamy przyciski prev/next w hero sliderze */
.hero-control.prev,
.hero-control.next {
    display: none;
}

.hero-dots {
    display: flex;
    gap: 10px;
}

.hero-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.3);
    cursor: pointer;
    transition: var(--transition);
}

.hero-dot.active {
    background: var(--white);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    border: none;
    font-family: var(--font-body);
    font-size: 1rem;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
    color: var(--white);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover {
    background: var(--secondary-dark);
    color: var(--white);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.btn-facebook {
    background: #4267B2;
    color: var(--white);
}

.btn-facebook:hover {
    background: #365899;
    color: var(--white);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 1.125rem;
}

/* Highlights Section */
.highlights {
    padding: 80px 0;
    background-color: var(--white);
}

.highlights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.highlight-card {
    background: var(--white);
    padding: 30px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border-top: 4px solid var(--primary-color);
}

.highlight-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.highlight-icon {
    width: 70px;
    height: 70px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 1.8rem;
}

.highlight-card h3 {
    color: var(--text-dark);
    margin-bottom: 15px;
}

.highlight-card p {
    color: var(--text-medium);
    margin-bottom: 20px;
}

.highlight-link {
    color: var(--primary-color);
    font-weight: 600;
    position: relative;
    display: inline-block;
}

.highlight-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.highlight-link:hover::after {
    width: 100%;
}

/* About City Section */
.about-city {
    padding: 80px 0;
    background-color: var(--off-white);
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.about-content {
    padding-right: 20px;
}

.about-content .lead {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.about-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.about-image {
    position: relative;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.about-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.5s ease;
}

.about-image:hover img {
    transform: scale(1.05);
}

.about-image-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.7);
    color: var(--white);
    padding: 10px 20px;
    font-size: 0.9rem;
    text-align: center;
}

/* History Timeline Section */
.history-timeline {
    padding: 80px 0;
    background-color: var(--white);
}

.timeline {
    position: relative;
    max-width: 1000px;
    margin: 50px auto;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 100%;
    background: var(--primary-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 60px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-date {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: var(--white);
    padding: 8px 16px;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    z-index: 2;
}

.timeline-content {
    position: relative;
    width: calc(50% - 40px);
    padding: 30px;
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.timeline-item:nth-child(odd) .timeline-content {
    margin-left: auto;
    margin-right: 0;
}

.timeline-item:nth-child(even) .timeline-content {
    margin-left: 0;
    margin-right: auto;
}

.timeline-content::before {
    content: '';
    position: absolute;
    top: 10px;
    width: 20px;
    height: 20px;
    background: var(--white);
    border: 4px solid var(--primary-color);
    border-radius: 50%;
}

.timeline-item:nth-child(odd) .timeline-content::before {
    left: -50px;
}

.timeline-item:nth-child(even) .timeline-content::before {
    right: -50px;
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.timeline-content h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.timeline-buttons {
    text-align: center;
    margin-top: 40px;
}

/* Attractions Section */
.attractions {
    padding: 80px 0;
    background-color: var(--off-white);
}

.attractions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.attraction-card {
    background: var(--white);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.attraction-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.attraction-image {
    height: 200px;
    overflow: hidden;
}

.attraction-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.attraction-card:hover .attraction-image img {
    transform: scale(1.1);
}

.attraction-content {
    padding: 20px;
}

.attraction-content h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.attraction-content p {
    color: var(--text-medium);
    margin-bottom: 15px;
}

.attraction-link {
    color: var(--primary-color);
    font-weight: 600;
    position: relative;
    display: inline-block;
}

.attraction-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.attraction-link:hover::after {
    width: 100%;
}

.attractions-buttons {
    text-align: center;
    margin-top: 40px;
}

/* Culture Sports Section */
.culture-sports {
    padding: 80px 0;
    background-color: var(--white);
}

.culture-sports-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: start;
}

.culture-sports-content h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    margin-top: 30px;
}

.culture-sports-content h3:first-child {
    margin-top: 0;
}

.feature-list {
    list-style: none;
    margin-bottom: 30px;
}

.feature-list li {
    margin-bottom: 15px;
    padding-left: 30px;
    position: relative;
}

.feature-list li i {
    position: absolute;
    left: 0;
    top: 3px;
    color: var(--primary-color);
}

.culture-sports-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 30px;
}

.culture-sports-gallery {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 200px);
    gap: 20px;
}

.gallery-item {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

/* District Info Section */
.district-info {
    padding: 80px 0;
    background-color: var(--off-white);
}

.district-map {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.map-container {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.map-container img {
    width: 100%;
    height: auto;
    display: block;
}

.district-description .lead {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.district-municipalities {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-top: 30px;
}

.municipality {
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.municipality:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.municipality h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.district-buttons {
    margin-top: 30px;
}

/* Business Section */
.business-section {
    padding: 80px 0;
    background-color: var(--white);
}

.business-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: start;
}

.business-content .lead {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.business-content h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    margin-top: 30px;
}

.business-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 30px;
}

.business-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.business-feature {
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    display: flex;
    gap: 15px;
    transition: var(--transition);
}

.business-feature:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.feature-content h4 {
    color: var(--text-dark);
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.feature-content p {
    color: var(--text-medium);
    font-size: 0.9rem;
    margin-bottom: 0;
}

/* Tourism Section */
.tourism {
    padding: 80px 0;
    background-color: var(--off-white);
}

.tourism-content {
    max-width: 800px;
    margin: 0 auto;
}

.tourism-content .lead {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.tourism-highlights {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin: 40px 0;
}

.tourism-highlight {
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    display: flex;
    gap: 20px;
    transition: var(--transition);
}

.tourism-highlight:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.highlight-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.highlight-content h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.highlight-content p {
    color: var(--text-medium);
    font-size: 0.95rem;
    margin-bottom: 0;
}

.tourism-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 40px;
}

/* Facebook Promo Section */
.facebook-promo {
    padding: 60px 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.facebook-promo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.1;
}

.facebook-promo-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.facebook-icon {
    width: 80px;
    height: 80px;
    background: var(--white);
    color: #4267B2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    margin: 0 auto 20px;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
}

.facebook-icon:hover {
    transform: scale(1.1) rotate(5deg);
}

.facebook-promo-content h2 {
    margin-bottom: 20px;
    color: var(--white);
    font-size: 2.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.facebook-promo-content p {
    margin-bottom: 30px;
    font-size: 1.2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.facebook-buttons {
    margin-top: 30px;
}

.btn-facebook {
    background: #4267B2;
    color: var(--white);
    padding: 15px 30px;
    font-size: 1.1rem;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
}

.btn-facebook:hover {
    background: #365899;
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.2);
}

/* SEO Content Section */
.seo-content {
    padding: 80px 0;
    background-color: var(--white);
}

.seo-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin-top: 40px;
}

.seo-column h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.seo-column p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.7;
}

/* Footer */
.site-footer {
    background: var(--secondary-color);
    color: var(--white);
    padding: 80px 0 20px;
    position: relative;
    overflow: hidden;
}

.site-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" fill="rgba(255,255,255,0.03)"/></svg>');
    opacity: 0.5;
}

.footer-top {
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: 50px;
    margin-bottom: 50px;
    position: relative;
    z-index: 2;
}

.footer-logo {
    margin-bottom: 20px;
}

.footer-logo img {
    margin-bottom: 15px;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.footer-logo p {
    color: var(--light-gray);
    font-size: 0.9rem;
}

.footer-nav {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
}

.footer-nav-column h3 {
    color: var(--white);
    margin-bottom: 20px;
    font-size: 1.2rem;
    position: relative;
    padding-bottom: 10px;
}

.footer-nav-column h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--primary-color);
}

.footer-nav-column ul {
    list-style: none;
}

.footer-nav-column ul li {
    margin-bottom: 10px;
}

.footer-nav-column a {
    color: var(--light-gray);
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.9rem;
    position: relative;
    padding-left: 15px;
}

.footer-nav-column a::before {
    content: '\f105';
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary-color);
}

.footer-nav-column a:hover {
    color: var(--white);
    transform: translateX(3px);
}

/* Footer social pod logo */
.footer-social {
    margin-top: 20px;
}

.footer-social h4,
.footer-social h5 {
    color: var(--white);
    margin-bottom: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.social-icons a:hover {
    background: var(--primary-color);
    transform: translateY(-3px);
}



.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.footer-bottom p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 0;
}

.footer-links {
    display: flex;
    list-style: none;
    gap: 20px;
}

.footer-links a {
    color: var(--text-light);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--white);
}

/* Cookie Notice */
.cookie-notice {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--text-dark);
    color: var(--white);
    padding: 15px 0;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    display: none;
}

.cookie-notice.active {
    display: block;
}

.cookie-notice .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.cookie-notice p {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.cookie-buttons {
    display: flex;
    gap: 10px;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-md);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 900;
}

.back-to-top.active {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--primary-dark);
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeInUp 0.6s ease forwards;
}

/* Page Hero Section */
.page-hero {
    height: 400px;
    background-size: cover;
    background-position: center;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin-top: 80px;
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 100%);
    z-index: 1;
}

.page-hero-content {
    position: relative;
    z-index: 2;
    color: var(--white);
    max-width: 800px;
}

.page-hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: var(--white);
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.page-hero-content .lead {
    font-size: 1.4rem;
    color: var(--white);
    opacity: 0.9;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Katalog firm */
.catalog-filters {
    padding: 30px 0;
    background-color: var(--white);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
    z-index: 10;
}

.filters-container {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.search-filter {
    flex-grow: 1;
    position: relative;
}

.search-filter input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
}

.search-filter button {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-medium);
    cursor: pointer;
}

.category-filter,
.sort-filter {
    min-width: 200px;
}

.category-filter select,
.sort-filter select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    background-color: var(--white);
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23666' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: calc(100% - 15px) center;
    padding-right: 40px;
}

.view-filter {
    display: flex;
    gap: 10px;
}

.view-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--light-gray);
    border: none;
    border-radius: var(--border-radius-md);
    color: var(--text-medium);
    cursor: pointer;
    transition: var(--transition);
}

.view-btn:hover {
    background-color: var(--medium-gray);
}

.view-btn.active {
    background-color: var(--primary-color);
    color: var(--white);
}

.catalog-companies {
    padding: 50px 0;
    background-color: var(--light-gray);
}

.companies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.companies-list .company-card {
    display: flex;
    flex-direction: row;
    max-width: 100%;
}

.companies-list .company-header {
    width: 300px;
    height: auto;
    flex-shrink: 0;
}

.companies-list .company-content {
    flex-grow: 1;
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 40px;
}

.pagination a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    color: var(--text-medium);
    text-decoration: none;
    transition: var(--transition);
}

.pagination a.next {
    width: auto;
    padding: 0 15px;
}

.pagination a.next i {
    margin-left: 5px;
}

.pagination a:hover {
    background-color: var(--medium-gray);
}

.pagination a.active {
    background-color: var(--primary-color);
    color: var(--white);
}

.catalog-actions {
    text-align: center;
}

/* Oferty */
.offers-filters {
    padding: 30px 0;
    background-color: var(--white);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
    z-index: 10;
}

.offers-list {
    padding: 50px 0;
    background-color: var(--light-gray);
}

.offers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.offer-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
    display: flex;
    flex-direction: column;
}

.offer-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.offer-company-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    font-size: 2rem;
}

.offer-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 5px 10px;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    font-size: 0.8rem;
    z-index: 2;
}

.offer-placeholder {
    height: 200px;
    background-color: var(--light-gray);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;
    color: var(--text-medium);
}

.offer-placeholder-icon {
    width: 60px;
    height: 60px;
    background-color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.offer-placeholder p {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.offer-content {
    padding: 20px;
}

.offer-buttons {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.offer-buttons .btn {
    flex: 1;
}

.offer-content h3 {
    margin-bottom: 5px;
    font-size: 1.3rem;
}

.offer-place {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.offer-place::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: var(--primary-color);
    border-radius: 50%;
    margin-right: 8px;
}

.offer-content p {
    color: var(--text-medium);
    margin-bottom: 15px;
    font-size: 0.95rem;
}

.offer-price {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.old-price {
    text-decoration: line-through;
    color: var(--text-light);
    margin-right: 10px;
    font-size: 0.9rem;
}

.new-price {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.2rem;
}

.offer-validity {
    color: var(--text-medium);
    font-size: 0.85rem;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.offer-validity i {
    margin-right: 5px;
}

.offers-actions {
    text-align: center;
}

/* Lightbox */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    animation: fadeInUp 0.3s ease;
}

.lightbox-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-medium);
    cursor: pointer;
    transition: var(--transition);
    z-index: 2;
}

.lightbox-close:hover {
    color: var(--primary-color);
}

.lightbox-body {
    padding: 30px;
}

.offer-details-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.offer-details-logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 20px;
    flex-shrink: 0;
}

.offer-details-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.offer-details-title h2 {
    margin-bottom: 5px;
    font-size: 1.8rem;
}

.offer-details-title p {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 0;
}

.offer-details-content {
    margin-bottom: 30px;
}

.offer-details-content p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.offer-details-info {
    background-color: var(--light-gray);
    padding: 20px;
    border-radius: var(--border-radius-md);
    margin-bottom: 30px;
}

.offer-details-info-item {
    display: flex;
    margin-bottom: 10px;
}

.offer-details-info-item:last-child {
    margin-bottom: 0;
}

.offer-details-info-label {
    font-weight: 600;
    width: 150px;
    flex-shrink: 0;
}

.offer-details-actions {
    display: flex;
    gap: 15px;
}

/* Kupony */
.coupons-filters {
    padding: 30px 0;
    background-color: var(--white);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
    z-index: 10;
}

.coupons-list {
    padding: 50px 0;
    background-color: var(--light-gray);
}

.coupons-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    margin-bottom: 40px;
}

@media (max-width: 1200px) {
    .coupons-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 900px) {
    .coupons-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 600px) {
    .coupons-grid {
        grid-template-columns: 1fr;
    }
}

.coupon-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition);
}

.coupon-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.coupon-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background-color: var(--light-gray);
    border-bottom: 1px dashed var(--medium-gray);
}

.coupon-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
}

.coupon-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.coupon-discount {
    text-align: center;
}

.coupon-discount span {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.coupon-discount small {
    font-size: 0.8rem;
    color: var(--text-medium);
}

.coupon-body {
    padding: 20px;
}

.coupon-body h3 {
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.coupon-body p {
    color: var(--text-medium);
    margin-bottom: 15px;
    font-size: 0.95rem;
}

.coupon-code-hidden {
    position: relative;
    margin-bottom: 15px;
    cursor: pointer;
}

.coupon-code-placeholder {
    background-color: var(--light-gray);
    padding: 12px;
    border-radius: var(--border-radius-md);
    text-align: center;
    color: var(--text-medium);
    font-size: 0.9rem;
    transition: var(--transition);
}

.coupon-code {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    font-size: 1.1rem;
    opacity: 0;
    transform: translateY(10px);
    transition: var(--transition);
}

.coupon-code-hidden.active .coupon-code {
    opacity: 1;
    transform: translateY(0);
}

.coupon-code-hidden.active .coupon-code-placeholder {
    opacity: 0;
}

.coupon-validity {
    color: var(--text-medium);
    font-size: 0.85rem;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.coupon-validity i {
    margin-right: 5px;
}

.coupon-footer {
    padding: 15px 20px;
    background: var(--light-gray);
    text-align: center;
}

.coupons-actions {
    text-align: center;
}

/* Coupon Details Lightbox */
.coupon-details-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.coupon-details-logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 20px;
    flex-shrink: 0;
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
}

.coupon-details-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.coupon-details-title h2 {
    margin-bottom: 5px;
    font-size: 1.8rem;
}

.coupon-details-title p {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 0;
}

.coupon-details-content {
    margin-bottom: 30px;
}

.coupon-details-content p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.coupon-details-code {
    background-color: var(--light-gray);
    padding: 20px;
    border-radius: var(--border-radius-md);
    margin-bottom: 30px;
    text-align: center;
}

.coupon-details-code-label {
    font-size: 0.9rem;
    color: var(--text-medium);
    margin-bottom: 10px;
}

.coupon-details-code-value {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 15px;
    border-radius: var(--border-radius-md);
    font-size: 1.5rem;
    font-weight: 600;
    letter-spacing: 2px;
    display: inline-block;
    min-width: 200px;
}

.coupon-details-info {
    background-color: var(--light-gray);
    padding: 20px;
    border-radius: var(--border-radius-md);
    margin-bottom: 30px;
}

.coupon-details-info-item {
    display: flex;
    margin-bottom: 10px;
}

.coupon-details-info-item:last-child {
    margin-bottom: 0;
}

.coupon-details-info-label {
    font-weight: 600;
    width: 150px;
    flex-shrink: 0;
}

.coupon-details-actions {
    display: flex;
    gap: 15px;
}

/* Strona kontaktowa */
.contact-section {
    padding: 80px 0;
    background-color: var(--white);
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
}

.contact-info-centered {
    max-width: 900px;
    margin: 0 auto;
    text-align: center;
}

.contact-info-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
    margin: 40px 0 50px 0;
    justify-items: center;
}

.contact-info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
}

.contact-info-icon {
    width: 50px;
    height: 50px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.contact-info-icon i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.contact-info-content h3 {
    margin-bottom: 5px;
    font-size: 1.2rem;
}

.contact-info-content p {
    color: var(--text-medium);
    margin-bottom: 0;
}

.contact-info-content a {
    color: var(--text-medium);
    text-decoration: none;
    transition: var(--transition);
}

.contact-info-content a:hover {
    color: var(--primary-color);
}

.contact-social {
    margin-top: 40px;
}

.contact-social h3 {
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.contact-social .social-links {
    display: flex;
    gap: 15px;
}

.contact-social .social-links a {
    width: 40px;
    height: 40px;
    background-color: var(--light-gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-medium);
    transition: var(--transition);
}

.contact-social .social-links a:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.contact-form-container {
    background-color: var(--light-gray);
    padding: 40px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
}

.contact-form {
    margin-top: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.2);
}

.form-checkbox {
    display: flex;
    align-items: flex-start;
}

.form-checkbox input {
    width: auto;
    margin-right: 10px;
    margin-top: 5px;
}

.form-checkbox label {
    margin-bottom: 0;
    font-weight: normal;
    font-size: 0.9rem;
}

.form-checkbox a {
    color: var(--primary-color);
}

.contact-map {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.map-container {
    margin-top: 40px;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background-color: var(--white);
}

.faq-container {
    margin-top: 40px;
}

.faq-item {
    margin-bottom: 20px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.faq-question {
    padding: 20px;
    background-color: var(--light-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: var(--transition);
}

.faq-question:hover {
    background-color: var(--medium-gray);
}

.faq-question h3 {
    margin: 0;
    font-size: 1.1rem;
}

.faq-toggle {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-medium);
    transition: var(--transition);
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
    color: var(--primary-color);
}

.faq-answer {
    padding: 0 20px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 20px;
    max-height: 1000px;
}

/* Strony dodawania treści */
.add-company-section,
.add-offer-section,
.add-coupon-section {
    padding: 80px 0;
    background-color: var(--white);
}

.add-company-grid,
.add-offer-grid,
.add-coupon-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 50px;
}

.add-company-form-container,
.add-offer-form-container,
.add-coupon-form-container {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 40px;
}

.form-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid var(--light-gray);
}

.form-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.form-section-title {
    margin-bottom: 20px;
    font-size: 1.3rem;
    color: var(--primary-color);
    position: relative;
    padding-left: 15px;
}

.form-section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.file-upload {
    position: relative;
    border: 2px dashed var(--medium-gray);
    border-radius: var(--border-radius-md);
    padding: 30px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.file-upload:hover {
    border-color: var(--primary-color);
}

.file-upload input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.file-upload-placeholder i {
    font-size: 2rem;
    color: var(--primary-color);
}

.file-upload-placeholder span {
    font-size: 1rem;
    color: var(--text-medium);
}

.file-upload-placeholder small {
    font-size: 0.8rem;
    color: var(--text-light);
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

.add-company-sidebar,
.add-offer-sidebar,
.add-coupon-sidebar {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.sidebar-widget {
    background-color: var(--light-gray);
    border-radius: var(--border-radius-md);
    padding: 30px;
    box-shadow: var(--shadow-sm);
}

.sidebar-widget h3 {
    margin-bottom: 20px;
    font-size: 1.3rem;
    color: var(--text-dark);
}

.sidebar-widget p {
    color: var(--text-medium);
    margin-bottom: 20px;
}

.benefits-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.benefits-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
}

.benefits-list li:last-child {
    margin-bottom: 0;
}

.benefits-list i {
    color: var(--primary-color);
    margin-right: 10px;
    margin-top: 3px;
}

.pricing-cards {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;
}

.pricing-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    position: relative;
}

.pricing-card.featured {
    border: 2px solid var(--primary-color);
    transform: scale(1.05);
}

.pricing-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 5px 10px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
}

.pricing-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid var(--light-gray);
}

.pricing-header h4 {
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.pricing {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.price {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
}

.period {
    font-size: 0.9rem;
    color: var(--text-medium);
}

.pricing-features {
    padding: 20px;
}

.pricing-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pricing-features li {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.pricing-features li:last-child {
    margin-bottom: 0;
}

.pricing-features i {
    width: 20px;
    margin-right: 10px;
    text-align: center;
}

.pricing-features i.fa-check {
    color: var(--success);
}

.pricing-features i.fa-times {
    color: var(--text-light);
}

.btn-outline {
    display: inline-block;
    padding: 10px 20px;
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    border-radius: var(--border-radius-md);
    text-decoration: none;
    text-align: center;
    font-weight: 600;
    transition: var(--transition);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* Strony informacyjne */
.breadcrumbs {
    padding: 15px 0;
    background-color: var(--light-gray);
    border-bottom: 1px solid var(--medium-gray);
}

.breadcrumbs-list {
    display: flex;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
}

.breadcrumbs-list li {
    font-size: 0.9rem;
    color: var(--text-medium);
}

.breadcrumbs-list li:not(:last-child)::after {
    content: '/';
    margin: 0 10px;
    color: var(--text-light);
}

.breadcrumbs-list a {
    color: var(--text-medium);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumbs-list a:hover {
    color: var(--primary-color);
}

/* Historia */
.history-intro {
    padding: 80px 0;
    background-color: var(--white);
}

.history-intro-content {
    max-width: 800px;
    margin: 0 auto;
}

.history-intro-text {
    margin-top: 40px;
}

.history-intro-text p {
    margin-bottom: 20px;
    line-height: 1.8;
    color: var(--text-medium);
}

.history-timeline {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.timeline {
    position: relative;
    max-width: 1000px;
    margin: 60px auto 0;
    padding: 20px 0;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 4px;
    background-color: var(--primary-color);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: 60px;
    display: flex;
    justify-content: center;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-date {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 40px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    z-index: 2;
}

.timeline-content {
    width: calc(50% - 50px);
    padding: 30px;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    position: relative;
}

.timeline-item:nth-child(odd) .timeline-content {
    margin-right: 50px;
}

.timeline-item:nth-child(even) .timeline-content {
    margin-left: 50px;
}

.timeline-item:nth-child(odd) .timeline-content::after {
    content: '';
    position: absolute;
    top: 20px;
    right: -15px;
    width: 0;
    height: 0;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-left: 15px solid var(--white);
}

.timeline-item:nth-child(even) .timeline-content::after {
    content: '';
    position: absolute;
    top: 20px;
    left: -15px;
    width: 0;
    height: 0;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-right: 15px solid var(--white);
}

.timeline-content h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.timeline-content p {
    color: var(--text-medium);
    line-height: 1.6;
}

.history-gallery {
    padding: 80px 0;
    background-color: var(--white);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.gallery-item {
    position: relative;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    height: 250px;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    color: var(--white);
    transform: translateY(100%);
    transition: var(--transition);
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-item:hover .gallery-caption {
    transform: translateY(0);
}

.gallery-caption h3 {
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.gallery-caption p {
    font-size: 0.9rem;
    opacity: 0.9;
}

.history-facts {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.facts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.fact-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    padding: 30px;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition);
}

.fact-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.fact-icon {
    width: 70px;
    height: 70px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.fact-icon i {
    font-size: 1.8rem;
    color: var(--primary-color);
}

.fact-card h3 {
    margin-bottom: 15px;
    color: var(--text-dark);
}

.fact-card p {
    color: var(--text-medium);
    line-height: 1.6;
}

/* Zabytki */
.monuments-intro {
    padding: 80px 0;
    background-color: var(--white);
}

.monuments-intro-content {
    max-width: 800px;
    margin: 0 auto;
}

.monuments-intro-text {
    margin-top: 40px;
}

.monuments-intro-text p {
    margin-bottom: 20px;
    line-height: 1.8;
    color: var(--text-medium);
}

.monuments-categories {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.categories-tabs {
    margin-top: 40px;
}

.tabs-nav {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 30px;
}

.tab-btn {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    background-color: var(--white);
    border: none;
    border-radius: var(--border-radius-md);
    color: var(--text-medium);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.tab-btn i {
    margin-right: 10px;
    font-size: 1.2rem;
}

.tab-btn:hover {
    background-color: var(--medium-gray);
}

.tab-btn.active {
    background-color: var(--primary-color);
    color: var(--white);
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.monuments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
}

.monument-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.monument-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.monument-image {
    height: 200px;
    overflow: hidden;
}

.monument-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.monument-card:hover .monument-image img {
    transform: scale(1.1);
}

.monument-content {
    padding: 20px;
}

.monument-content h3 {
    margin-bottom: 10px;
    font-size: 1.3rem;
    color: var(--text-dark);
}

.monument-date {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.monument-content p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.6;
}

.monuments-map {
    padding: 80px 0;
    background-color: var(--white);
}

.map-container {
    margin-top: 40px;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.map-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 30px;
}

.map-info-item {
    display: flex;
    align-items: flex-start;
}

.map-info-icon {
    width: 50px;
    height: 50px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.map-info-icon i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.map-info-content h3 {
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.map-info-content p {
    color: var(--text-medium);
    font-size: 0.9rem;
    line-height: 1.6;
}

/* Monument Details Lightbox */
.monument-details-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.monument-details-image {
    width: 100%;
    height: 300px;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    margin-bottom: 30px;
}

.monument-details-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.monument-details-title h2 {
    margin-bottom: 10px;
    font-size: 1.8rem;
}

.monument-details-title p {
    color: var(--primary-color);
    font-weight: 500;
}

.monument-details-content {
    margin-bottom: 30px;
}

.monument-details-content p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.monument-details-info {
    background-color: var(--light-gray);
    padding: 20px;
    border-radius: var(--border-radius-md);
    margin-bottom: 30px;
}

.monument-details-info-item {
    display: flex;
    margin-bottom: 10px;
}

.monument-details-info-item:last-child {
    margin-bottom: 0;
}

.monument-details-info-label {
    font-weight: 600;
    width: 150px;
    flex-shrink: 0;
}

.monument-details-gallery {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 30px;
}

.monument-details-gallery-item {
    height: 150px;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.monument-details-gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Turystyka */
.tourism-intro {
    padding: 80px 0;
    background-color: var(--white);
}

.tourism-intro-content {
    max-width: 800px;
    margin: 0 auto;
}

.tourism-intro-text {
    margin-top: 40px;
}

.tourism-intro-text p {
    margin-bottom: 20px;
    line-height: 1.8;
    color: var(--text-medium);
}

.tourism-attractions {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.attractions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.attraction-card {
    display: flex;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.attraction-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.attraction-image {
    width: 200px;
    flex-shrink: 0;
    overflow: hidden;
}

.attraction-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.attraction-card:hover .attraction-image img {
    transform: scale(1.1);
}

.attraction-content {
    padding: 20px;
    flex-grow: 1;
}

.attraction-content h3 {
    margin-bottom: 10px;
    font-size: 1.3rem;
    color: var(--text-dark);
}

.attraction-content p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.6;
}

.tourism-routes {
    padding: 80px 0;
    background-color: var(--white);
}

.routes-tabs {
    margin-top: 40px;
}

.route-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: start;
}

.route-image {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    height: 350px;
}

.route-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.route-info h3 {
    margin-bottom: 20px;
    font-size: 1.5rem;
    color: var(--text-dark);
}

.route-details {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.route-detail {
    display: flex;
    align-items: center;
    color: var(--text-medium);
}

.route-detail i {
    margin-right: 8px;
    color: var(--primary-color);
}

.route-info p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.6;
}

.tourism-events {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.event-card {
    display: flex;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.event-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.event-date {
    width: 80px;
    flex-shrink: 0;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px 0;
}

.event-day {
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1;
}

.event-month {
    font-size: 1rem;
    text-transform: uppercase;
}

.event-content {
    padding: 20px;
    flex-grow: 1;
}

.event-content h3 {
    margin-bottom: 10px;
    font-size: 1.3rem;
    color: var(--text-dark);
}

.event-location {
    color: var(--text-medium);
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.event-location i {
    margin-right: 5px;
    color: var(--primary-color);
}

.event-content p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.6;
}

.events-calendar {
    text-align: center;
    margin-top: 40px;
}

.tourism-info {
    padding: 80px 0;
    background-color: var(--white);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.info-card {
    background-color: var(--light-gray);
    border-radius: var(--border-radius-md);
    padding: 30px;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition);
}

.info-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.info-icon {
    width: 70px;
    height: 70px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.info-icon i {
    font-size: 1.8rem;
    color: var(--primary-color);
}

.info-card h3 {
    margin-bottom: 15px;
    color: var(--text-dark);
}

.info-card p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.6;
}

/* Nowa wyszukiwarka na stronie głównej */
.hero-search {
    margin-top: 30px;
    max-width: 800px;
}

.main-search-form {
    display: flex;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.search-input {
    flex-grow: 1;
    position: relative;
}

.search-input i {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    font-size: 1.2rem;
}

.search-input input {
    width: 100%;
    padding: 20px 20px 20px 50px;
    border: none;
    font-size: 1.1rem;
}

.search-input input:focus {
    outline: none;
}

.main-search-form .btn {
    border-radius: 0;
    padding: 0 30px;
    font-size: 1.1rem;
}

/* Kategorie na stronie głównej */
/* Catalog Layout */
.catalog-layout {
    display: flex;
    gap: 30px;
    margin-top: 30px;
}

.catalog-welcome {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 40px;
    margin-bottom: 30px;
    text-align: center;
}

.welcome-icon {
    width: 80px;
    height: 80px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.welcome-icon i {
    font-size: 32px;
    color: var(--primary-color);
}

.catalog-welcome h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.catalog-welcome p {
    color: var(--text-medium);
    margin-bottom: 25px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.seo-info-box {
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: var(--border-radius-md);
    padding: 15px 20px;
    margin-top: 40px;
    margin-bottom: 25px;
    text-align: center;
}

.seo-info-box p {
    color: var(--text-dark);
    font-weight: 500;
    margin: 0;
}

.seo-info-box i {
    color: var(--primary-color);
    margin-right: 8px;
}

.catalog-search {
    max-width: 600px;
    margin: 0 auto;
}

.catalog-search.main-search {
    margin-bottom: 30px;
}

.catalog-search-form {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.map-btn {
    display: flex;
    align-items: center;
    gap: 5px;
}

.catalog-search .search-input {
    flex: 1;
    position: relative;
    min-width: 250px;
}

.catalog-search .search-input i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.catalog-search .search-input input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    box-shadow: var(--shadow-sm);
    transition: box-shadow 0.3s;
}

.catalog-search .search-input input:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 102, 0, 0.3);
}

.categories-section {
    margin-bottom: 40px;
}

.categories-sidebar {
    width: 300px;
    flex-shrink: 0;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 25px;
    align-self: flex-start;
}

.categories-sidebar h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: var(--text-dark);
    padding-bottom: 15px;
    border-bottom: 1px solid var(--medium-gray);
}

.categories-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-item {
    margin-bottom: 5px;
    border-bottom: 1px solid var(--medium-gray);
}

.category-header {
    display: flex;
    align-items: center;
    padding: 12px 0;
    cursor: pointer;
    transition: color 0.3s;
}

.category-header:hover {
    color: var(--primary-color);
}

.category-header i:first-child {
    width: 20px;
    margin-right: 10px;
    color: var(--primary-color);
}

.category-header span {
    flex: 1;
    font-weight: 500;
}

.category-header i:last-child {
    font-size: 0.8rem;
    transition: transform 0.3s;
}

.category-item.active .category-header i:last-child {
    transform: rotate(180deg);
}

.subcategories-list {
    list-style: none;
    padding: 0 0 0 30px;
    margin: 0 0 10px 0;
    display: none;
}

.category-item.active .subcategories-list {
    display: block;
}

.subcategories-list li {
    margin-bottom: 10px;
}

.subcategories-list li:last-child {
    margin-bottom: 0;
}

.subcategories-list a {
    color: var(--text-medium);
    text-decoration: none;
    font-size: 0.95rem;
    transition: color 0.3s;
    display: block;
    padding: 5px 0;
}

.subcategories-list a:hover {
    color: var(--primary-color);
    padding-left: 5px;
}

.category-content {
    flex: 1;
}

.top-companies-section {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 25px;
}

.top-companies-section h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: var(--text-dark);
    padding-bottom: 15px;
    border-bottom: 1px solid var(--medium-gray);
}

.top-companies-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.top-company-card {
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    position: relative;
    margin-bottom: 20px;
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s, box-shadow 0.3s;
    padding: 20px;
    text-align: center;
}

.top-company-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.top-company-card:last-child {
    margin-bottom: 0;
}

.company-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--primary-color);
    color: white;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 5px 10px;
    border-radius: 20px;
    z-index: 1;
}

.top-company-card .company-logo {
    width: 120px;
    height: 120px;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f8f8;
    border-radius: 10px;
    border: 1px solid var(--medium-gray);
    box-shadow: var(--shadow-sm);
}

.company-logo img {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
    border-radius: 5px;
}

.company-info {
    padding: 0 10px;
}

.top-company-card .company-info h4 {
    font-size: 1.4rem;
    margin-bottom: 8px;
    color: var(--text-dark);
    font-weight: 700;
}

.top-company-card .company-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 10px;
    justify-content: center;
}

.company-tag {
    background-color: rgba(255, 102, 0, 0.1);
    color: var(--primary-color);
    font-size: 0.8rem;
    padding: 3px 8px;
    border-radius: 4px;
}

.company-description {
    font-size: 0.9rem;
    color: var(--text-medium);
    margin-bottom: 10px;
    line-height: 1.4;
}

.company-contact {
    font-size: 0.85rem;
    color: var(--text-medium);
    margin-bottom: 15px;
}

.company-contact p {
    margin-bottom: 5px;
}

.company-contact i {
    width: 15px;
    margin-right: 5px;
    color: var(--primary-color);
}

/* Style dla wyników wyszukiwania */
.search-subcategories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.search-subcategory-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    padding: 25px;
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
}

.search-subcategory-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.subcategory-icon {
    width: 60px;
    height: 60px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
}

.subcategory-icon i {
    font-size: 24px;
    color: var(--primary-color);
}

.search-subcategory-card h4 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.search-subcategory-card p {
    color: var(--text-medium);
    margin-bottom: 20px;
    font-size: 0.9rem;
    line-height: 1.5;
}

.search-help {
    background-color: var(--light-gray);
    border-radius: var(--border-radius-md);
    padding: 20px;
    text-align: center;
}

.search-help h4 {
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.search-help p {
    color: var(--text-medium);
    margin-bottom: 0;
}

.add-company-promo {
    display: flex;
    align-items: center;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: var(--border-radius-md);
    padding: 20px;
    margin-top: 30px;
}

.promo-icon {
    width: 60px;
    height: 60px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
}

.promo-icon i {
    font-size: 24px;
    color: var(--primary-color);
}

.promo-content {
    flex: 1;
}

.promo-content h4 {
    font-size: 1.1rem;
    margin-bottom: 5px;
    color: var(--text-dark);
}

.promo-content p {
    font-size: 0.9rem;
    color: var(--text-medium);
    margin-bottom: 15px;
}

/* Lightbox dla firm */
.company-lightbox-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.company-lightbox-overlay.active {
    opacity: 1;
    visibility: visible;
}

.company-lightbox-content {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    box-shadow: var(--shadow-lg);
}

.company-lightbox-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 2rem;
    color: var(--text-dark);
    cursor: pointer;
    z-index: 1;
}

.company-lightbox-body {
    padding: 30px;
}

.company-lightbox-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--medium-gray);
}

.company-lightbox-logo {
    width: 120px;
    height: 120px;
    flex-shrink: 0;
    margin-right: 20px;
    background-color: #f8f8f8;
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.company-lightbox-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.company-lightbox-title {
    flex: 1;
}

.company-lightbox-title h2 {
    font-size: 1.8rem;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.company-lightbox-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.company-lightbox-description {
    margin-bottom: 30px;
    line-height: 1.6;
}

.company-lightbox-details {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
}

.company-lightbox-contact,
.company-lightbox-social {
    flex: 1;
}

.company-lightbox-contact h3,
.company-lightbox-social h3,
.company-lightbox-gallery h3,
.company-lightbox-hours h3 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: var(--text-dark);
    padding-bottom: 10px;
    border-bottom: 1px solid var(--medium-gray);
}

.company-lightbox-contact p {
    margin-bottom: 10px;
    color: var(--text-medium);
}

.company-lightbox-contact i {
    width: 20px;
    margin-right: 10px;
    color: var(--primary-color);
}

.company-social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
}

.social-link:hover {
    background-color: var(--primary-color-dark);
}

.company-gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.gallery-item {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    height: 150px;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.company-hours-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.company-hours-list li {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid var(--medium-gray);
}

.company-hours-list li:last-child {
    border-bottom: none;
}

.company-hours-list .day {
    font-weight: 500;
    color: var(--text-dark);
}

.company-hours-list .hours {
    color: var(--text-medium);
}

body.lightbox-open {
    overflow: hidden;
}

@media (max-width: 992px) {
    .catalog-layout {
        flex-direction: column;
    }

    .categories-sidebar {
        width: 100%;
    }

    .company-logo {
        width: 100px;
        height: 100px;
    }

    .company-lightbox-details {
        flex-direction: column;
    }

    .company-lightbox-header {
        flex-direction: column;
        text-align: center;
    }

    .company-lightbox-logo {
        margin-right: 0;
        margin-bottom: 20px;
    }
}

/* ===== COMPANY CONTACT LIGHTBOX ===== */

.company-contact-header {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--light-gray);
}

.company-contact-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(255, 102, 0, 0.3);
}

.company-contact-logo i {
    font-size: 2rem;
    color: var(--white);
}

.company-contact-title h2 {
    font-size: 1.8rem;
    margin-bottom: 8px;
    color: var(--text-dark);
    font-weight: 700;
}

.company-contact-title p {
    color: var(--text-medium);
    margin: 0;
    font-size: 1rem;
}

.company-contact-content {
    margin-bottom: 30px;
}

.company-contact-content p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-medium);
    margin: 0;
}

.company-contact-info {
    margin-bottom: 30px;
}

.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.contact-info-item {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    background: var(--light-gray);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.contact-info-item:hover {
    background: rgba(255, 102, 0, 0.05);
    border-left-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.contact-info-icon {
    width: 50px;
    height: 50px;
    background: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.contact-info-icon i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.contact-info-content {
    flex-grow: 1;
}

.contact-info-label {
    font-size: 0.9rem;
    color: var(--text-medium);
    margin-bottom: 5px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.contact-info-value {
    font-size: 1.1rem;
    color: var(--text-dark);
    font-weight: 500;
}

.contact-info-value a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.contact-info-value a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.company-social-media {
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: var(--border-radius-md);
}

.company-social-media h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: var(--text-dark);
    text-align: center;
    font-weight: 600;
}

.social-media-links {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.social-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.social-link.facebook {
    background: linear-gradient(135deg, #1877f2 0%, #0d5aa7 100%);
    color: var(--white);
}

.social-link.facebook:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(24, 119, 242, 0.4);
}

.social-link.instagram {
    background: linear-gradient(135deg, #e4405f 0%, #833ab4 50%, #fcb045 100%);
    color: var(--white);
}

.social-link.instagram:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(228, 64, 95, 0.4);
}

.social-link i {
    margin-right: 8px;
    font-size: 1.1rem;
}

.company-contact-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.company-contact-actions .btn {
    min-width: 180px;
    justify-content: center;
}

@media (max-width: 768px) {
    .contact-info-grid {
        grid-template-columns: 1fr;
    }

    .company-contact-header {
        flex-direction: column;
        text-align: center;
    }

    .company-contact-logo {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .company-contact-actions {
        flex-direction: column;
    }

    .social-media-links {
        flex-direction: column;
        align-items: center;
    }
}

/* Wyniki wyszukiwania */
.search-results {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.search-filters {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 30px;
}

.filter-group {
    display: flex;
    align-items: center;
}

.filter-group label {
    margin-right: 10px;
    color: var(--text-medium);
}

.filter-group select {
    padding: 8px 15px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    background-color: var(--white);
    color: var(--text-dark);
}

.search-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
}

.no-results {
    text-align: center;
    padding: 50px 0;
}

.no-results-icon {
    font-size: 3rem;
    color: var(--text-light);
    margin-bottom: 20px;
}

.no-results h3 {
    margin-bottom: 15px;
    color: var(--text-dark);
    font-size: 1.5rem;
}

.no-results p {
    color: var(--text-medium);
    max-width: 600px;
    margin: 0 auto;
}

/* Sekcja kuponów rabatowych */
.featured-coupons {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.featured-coupons .coupons-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    margin-top: 40px;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsywność dla sekcji kuponów */
@media (max-width: 1200px) {
    .featured-coupons .coupons-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }
}

@media (max-width: 900px) {
    .featured-coupons .coupons-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .featured-coupons {
        padding: 60px 0;
    }
}

@media (max-width: 600px) {
    .featured-coupons .coupons-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .featured-coupons {
        padding: 40px 0;
    }

    .coupon-card {
        max-width: 100%;
    }

    .coupon-header {
        padding: 15px;
    }

    .coupon-logo {
        width: 60px;
        height: 60px;
    }

    .coupon-discount {
        font-size: 1.2rem;
        padding: 8px 12px;
    }

    .coupon-content {
        padding: 15px;
    }

    .coupon-content h3 {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .featured-coupons {
        padding: 30px 0;
    }

    .coupon-header {
        padding: 12px;
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .coupon-logo {
        width: 50px;
        height: 50px;
    }

    .coupon-discount {
        font-size: 1rem;
        padding: 6px 10px;
    }

    .coupon-content {
        padding: 12px;
    }

    .coupon-content h3 {
        font-size: 1rem;
        margin-bottom: 8px;
    }

    .coupon-title {
        font-size: 0.9rem;
        margin-bottom: 8px;
    }

    .coupon-description {
        font-size: 0.85rem;
        margin-bottom: 12px;
    }

    .coupon-validity {
        font-size: 0.8rem;
        margin-bottom: 15px;
    }

    .coupon-code-btn {
        padding: 8px;
        font-size: 0.9rem;
    }
}

.coupons-slider {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 40px;
}

.coupon-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    flex: 1;
    min-width: 300px;
    max-width: calc(33.333% - 20px);
    transition: var(--transition);
}

.coupon-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.coupon-header {
    position: relative;
    padding: 20px;
    background-color: rgba(255, 102, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.coupon-logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
}

.coupon-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.coupon-discount {
    background-color: var(--white);
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 700;
    padding: 10px 15px;
    border-radius: var(--border-radius-md);
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.coupon-content {
    padding: 20px;
}

.coupon-content h3 {
    margin-bottom: 10px;
    color: var(--text-dark);
    font-size: 1.2rem;
}

.coupon-title {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.coupon-description {
    color: var(--text-medium);
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.coupon-validity {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.coupon-validity i {
    margin-right: 5px;
    color: var(--primary-color);
}

.coupon-code-container {
    position: relative;
}

.coupon-code-btn {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.coupon-code-btn:hover {
    background-color: var(--primary-dark);
}

.coupon-code {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    padding: 12px;
    background-color: var(--white);
    border: 2px dashed var(--primary-color);
    border-radius: var(--border-radius-md);
    text-align: center;
    font-weight: 600;
    color: var(--primary-color);
}

.coupon-copy-btn {
    display: block;
    width: 100%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: 8px 15px;
    margin-top: 50px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
}

.coupon-copy-btn:hover {
    background-color: var(--primary-dark);
}

.coupon-copy-btn:disabled {
    background-color: #4CAF50;
    cursor: default;
}

/* Sekcja ofert */
.featured-offers {
    padding: 80px 0;
}

.offers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.offer-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition);
}

.offer-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.offer-image {
    position: relative;
    height: 200px;
}

.offer-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.offer-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 5px 10px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
}

.offer-content {
    padding: 20px;
}

.offer-content h3 {
    margin-bottom: 15px;
    color: var(--text-dark);
    font-size: 1.2rem;
}

.offer-company {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.offer-company img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

.offer-company span {
    color: var(--text-medium);
    font-size: 0.9rem;
}

.offer-description {
    color: var(--text-medium);
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.offer-price {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.price-old {
    color: var(--text-light);
    text-decoration: line-through;
    margin-right: 10px;
    font-size: 0.9rem;
}

.price-new {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.2rem;
}

/* Sekcja o mieście */
.about-city {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.about-city-content {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    margin-top: 40px;
}

.about-city-text {
    flex: 1;
    min-width: 300px;
}

.about-city-text p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.6;
}

.about-city-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 30px;
}

.about-city-image {
    flex: 1;
    min-width: 300px;
    max-width: 500px;
}

.about-city-image img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
}

/* Sekcja SEO */
.seo-content {
    padding: 80px 0;
    background-color: var(--white);
}

.seo-content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 40px;
}

.seo-content-item h2 {
    margin-bottom: 15px;
    color: var(--text-dark);
    font-size: 1.3rem;
}

.seo-content-item p {
    color: var(--text-medium);
    margin-bottom: 15px;
    line-height: 1.6;
}

/* Strona Historia */
.history-section {
    padding: 80px 0;
}

.history-content {
    max-width: 1000px;
    margin: 0 auto;
}

.history-intro {
    margin-bottom: 50px;
}

.history-intro h2 {
    margin-bottom: 20px;
    color: var(--text-dark);
    font-size: 1.8rem;
}

.history-intro p {
    color: var(--text-medium);
    margin-bottom: 15px;
    line-height: 1.6;
}

.history-timeline {
    position: relative;
    margin: 50px 0;
    padding-left: 30px;
}

.history-timeline:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--primary-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
    padding-left: 30px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-item:before {
    content: '';
    position: absolute;
    left: -10px;
    top: 5px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: var(--primary-color);
}

.timeline-date {
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
    font-size: 1.2rem;
}

.timeline-content h3 {
    margin-bottom: 10px;
    color: var(--text-dark);
    font-size: 1.2rem;
}

.timeline-content p {
    color: var(--text-medium);
    line-height: 1.6;
}

.history-section-content {
    margin-bottom: 50px;
}

.history-section-content h2 {
    margin-bottom: 20px;
    color: var(--text-dark);
    font-size: 1.8rem;
}

.history-section-content p {
    color: var(--text-medium);
    margin-bottom: 15px;
    line-height: 1.6;
}

.history-image {
    margin: 30px 0;
}

.history-image img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
}

.image-caption {
    margin-top: 10px;
    color: var(--text-light);
    font-style: italic;
    text-align: center;
    font-size: 0.9rem;
}

.history-cta {
    background-color: var(--light-gray);
    padding: 30px;
    border-radius: var(--border-radius-md);
    text-align: center;
    margin-top: 50px;
}

.history-cta h3 {
    margin-bottom: 20px;
    color: var(--text-dark);
    font-size: 1.5rem;
}

.history-cta-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
}

/* Sekcja ważnych postaci w historii */
.history-figures {
    margin-top: 30px;
}

.history-figure {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 40px;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.history-figure:last-child {
    margin-bottom: 0;
}

.figure-image {
    flex: 1;
    min-width: 250px;
    max-width: 300px;
}

.figure-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.figure-content {
    flex: 2;
    padding: 30px;
}

.figure-content h3 {
    margin-bottom: 15px;
    color: var(--text-dark);
    font-size: 1.3rem;
}

.figure-content p {
    color: var(--text-medium);
    margin-bottom: 15px;
    line-height: 1.6;
}

/* Sekcja ciekawostek historycznych */
.history-facts {
    margin-top: 30px;
}

.history-fact {
    display: flex;
    margin-bottom: 30px;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 20px;
}

.history-fact:last-child {
    margin-bottom: 0;
}

.fact-icon {
    width: 60px;
    height: 60px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.fact-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.fact-content {
    flex: 1;
}

.fact-content h3 {
    margin-bottom: 10px;
    color: var(--text-dark);
    font-size: 1.2rem;
}

.fact-content p {
    color: var(--text-medium);
    line-height: 1.6;
}

/* Sekcja promocji na Facebooku */
.social-promotion {
    background: linear-gradient(135deg, #3b5998, #4267B2);
    color: var(--white);
    padding: 60px 0;
    margin: 50px 0;
    border-radius: 50px 50px 0 0;
    position: relative;
    overflow: hidden;
}

.social-promotion::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 6px;
    background: #3b5998;
    border-radius: 3px;
}

.social-promotion-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 30px;
    position: relative;
    z-index: 2;
}

.social-promotion-text {
    flex: 2;
    min-width: 300px;
}

.social-promotion-text h2 {
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.social-promotion-text p {
    opacity: 0.9;
    font-size: 1.1rem;
}

.social-promotion-buttons {
    flex: 1;
    min-width: 200px;
    text-align: center;
}

.btn-facebook {
    background-color: var(--white);
    color: #3b5998;
    padding: 15px 30px;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn-facebook i {
    margin-right: 10px;
    font-size: 1.2rem;
}

.btn-facebook:hover {
    background-color: #f0f2f5;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

/* Sekcja SEO historii */
.history-seo {
    padding: 80px 0;
    background-color: var(--light-gray);
}

/* Strona Cennik */
.pricing-section {
    padding: 80px 0;
}

.pricing-plans {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 40px;
}

.pricing-plan {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    flex: 1;
    min-width: 250px;
    position: relative;
    transition: var(--transition);
}

.pricing-plan:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.pricing-plan.featured {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
    border: 2px solid var(--primary-color);
    z-index: 1;
}

.pricing-plan.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.plan-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 5px 10px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
}

.plan-header {
    padding: 30px;
    text-align: center;
    background-color: rgba(255, 102, 0, 0.1);
    border-bottom: 1px solid var(--medium-gray);
}

.plan-header h3 {
    margin-bottom: 15px;
    color: var(--text-dark);
    font-size: 1.5rem;
}

.plan-price {
    margin-bottom: 15px;
}

.price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.period {
    font-size: 1rem;
    color: var(--text-medium);
}

.plan-description {
    color: var(--text-medium);
    font-size: 0.9rem;
}

.plan-features {
    padding: 30px;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.plan-features li {
    margin-bottom: 15px;
    color: var(--text-medium);
    display: flex;
    align-items: center;
}

.plan-features li:last-child {
    margin-bottom: 0;
}

.plan-features i {
    margin-right: 10px;
    font-size: 1rem;
}

.plan-features i.fa-check {
    color: var(--success-color);
}

.plan-features i.fa-times {
    color: var(--danger-color);
}

.plan-footer {
    padding: 0 30px 30px;
    text-align: center;
}

.pricing-note {
    text-align: center;
    margin-top: 30px;
    color: var(--text-medium);
    font-style: italic;
}

.additional-services {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.service-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 30px;
    transition: var(--transition);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.service-icon {
    width: 60px;
    height: 60px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.service-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.service-card h3 {
    margin-bottom: 15px;
    color: var(--text-dark);
    font-size: 1.3rem;
}

.service-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.service-features li {
    margin-bottom: 10px;
    color: var(--text-medium);
    line-height: 1.6;
}

.service-features li:last-child {
    margin-bottom: 0;
}

.service-features strong {
    color: var(--text-dark);
}

.pricing-cta {
    padding: 80px 0;
    background-color: var(--primary-color);
    color: var(--white);
}

.pricing-cta-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.pricing-cta-content h2 {
    margin-bottom: 20px;
    font-size: 2rem;
}

.pricing-cta-content p {
    margin-bottom: 30px;
    font-size: 1.1rem;
    opacity: 0.9;
}

.pricing-cta-content .btn {
    background-color: var(--white);
    color: var(--primary-color);
}

.pricing-cta-content .btn:hover {
    background-color: var(--light-gray);
}

/* Strona Regulamin i Polityka Prywatności */
.terms-section,
.privacy-section {
    padding: 80px 0;
}

.terms-content,
.privacy-content {
    max-width: 1000px;
    margin: 0 auto;
}

.terms-intro,
.privacy-intro {
    margin-bottom: 50px;
}

.terms-intro p,
.privacy-intro p {
    color: var(--text-medium);
    margin-bottom: 15px;
    line-height: 1.6;
}

.terms-section-item,
.privacy-section-item {
    margin-bottom: 50px;
}

.terms-section-item h2,
.privacy-section-item h2 {
    margin-bottom: 20px;
    color: var(--text-dark);
    font-size: 1.5rem;
    border-bottom: 1px solid var(--medium-gray);
    padding-bottom: 10px;
}

.terms-section-item p,
.privacy-section-item p {
    color: var(--text-medium);
    margin-bottom: 15px;
    line-height: 1.6;
}

.terms-section-item ol,
.terms-section-item ul,
.privacy-section-item ol,
.privacy-section-item ul {
    padding-left: 20px;
    margin-bottom: 15px;
}

.terms-section-item li,
.privacy-section-item li {
    color: var(--text-medium);
    margin-bottom: 10px;
    line-height: 1.6;
}

.terms-section-item strong,
.privacy-section-item strong {
    color: var(--text-dark);
}

/* Breadcrumbs */
.breadcrumbs {
    background-color: var(--light-gray);
    padding: 15px 0;
    border-bottom: 1px solid var(--medium-gray);
}

.breadcrumbs-list {
    display: flex;
    list-style: none;
    flex-wrap: wrap;
}

.breadcrumbs-list li {
    position: relative;
    padding-right: 25px;
    margin-right: 15px;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.breadcrumbs-list li:not(:last-child)::after {
    content: '›';
    position: absolute;
    right: 0;
    top: 0;
    color: var(--text-light);
    font-size: 1.2rem;
}

.breadcrumbs-list a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumbs-list a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* History Timeline Section */
.history-timeline {
    padding: 80px 0;
    background-color: var(--light-gray);
    position: relative;
}

.timeline {
    position: relative;
    max-width: 1400px;
    margin: 60px auto;
}

.timeline::before {
    content: '';
    position: absolute;
    width: 4px;
    background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -2px;
    border-radius: 4px;
}

.timeline-item {
    padding: 10px 40px;
    position: relative;
    width: 50%;
    box-sizing: border-box;
    margin-bottom: 30px;
}

.timeline-item:nth-child(odd) {
    left: 0;
    text-align: right;
}

.timeline-item:nth-child(odd) .timeline-content {
    margin-left: auto;
}

.timeline-item:nth-child(even) {
    left: 50%;
}

.timeline-date {
    position: absolute;
    width: 120px;
    background-color: var(--primary-color);
    color: var(--white);
    text-align: center;
    padding: 8px 0;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: var(--shadow-md);
    z-index: 10;
}

.timeline-item:nth-child(odd) .timeline-date {
    right: -60px;
    transform: translateX(50%);
}

.timeline-item:nth-child(even) .timeline-date {
    left: -60px;
    transform: translateX(-50%);
}

.timeline-content {
    padding: 30px 35px;
    background-color: var(--white);
    position: relative;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    width: 90%;
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.timeline-content::before {
    content: '';
    position: absolute;
    width: 25px;
    height: 25px;
    background-color: var(--white);
    top: 20px;
    transform: rotate(45deg);
    z-index: -1;
    box-shadow: var(--shadow-sm);
}

.timeline-item:nth-child(odd) .timeline-content::before {
    right: -12px;
}

.timeline-item:nth-child(even) .timeline-content::before {
    left: -12px;
}

.timeline-content h3 {
    margin-top: 0;
    color: var(--primary-color);
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.timeline-content p {
    margin-bottom: 0;
    color: var(--text-medium);
    line-height: 1.6;
}

.timeline-buttons {
    text-align: center;
    margin-top: 40px;
}

/* History Intro Section */
.history-intro {
    padding: 80px 0;
    background-color: var(--white);
}

.history-intro-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.history-intro-content h2 {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.history-intro-content .lead {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.history-intro-image {
    position: relative;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.history-intro-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.5s ease;
}

.history-intro-image:hover img {
    transform: scale(1.05);
}

.image-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.7);
    color: var(--white);
    padding: 10px 20px;
    font-size: 0.9rem;
    text-align: center;
}

/* Industrial Heritage Section */
.industrial-heritage {
    padding: 80px 0;
    background-color: var(--off-white);
}

.industrial-heritage-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: start;
}

.industrial-heritage-content .lead {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.industrial-heritage-content h3 {
    color: var(--primary-color);
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.heritage-list {
    list-style: none;
    margin-bottom: 2rem;
}

.heritage-list li {
    margin-bottom: 15px;
    padding-left: 25px;
    position: relative;
}

.heritage-list li::before {
    content: '•';
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.industrial-heritage-gallery {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 200px);
    gap: 20px;
}

.gallery-item {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    position: relative;
    transition: var(--transition);
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.7);
    color: var(--white);
    padding: 8px 15px;
    font-size: 0.85rem;
    text-align: center;
}

/* Historical Figures Section */
.historical-figures {
    padding: 80px 0;
    background-color: var(--white);
}

.figures-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.figure-card {
    display: flex;
    background: var(--white);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.figure-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.figure-image {
    width: 150px;
    flex-shrink: 0;
    overflow: hidden;
}

.figure-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.figure-card:hover .figure-image img {
    transform: scale(1.1);
}

.figure-content {
    padding: 20px;
    flex-grow: 1;
}

.figure-content h3 {
    color: var(--primary-color);
    margin-bottom: 5px;
}

.figure-years {
    color: var(--text-medium);
    font-style: italic;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.figure-content p {
    color: var(--text-medium);
    margin-bottom: 10px;
    font-size: 0.95rem;
    line-height: 1.6;
}

/* History Facts Section */
.history-facts {
    padding: 80px 0;
    background-color: var(--off-white);
}

.facts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.fact-card {
    display: flex;
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    gap: 20px;
    align-items: flex-start;
}

.fact-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.fact-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.fact-content h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.fact-content p {
    color: var(--text-medium);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 0;
}

/* History SEO Section */
.history-seo {
    padding: 80px 0;
    background-color: var(--white);
}

.history-seo-content {
    max-width: 800px;
    margin: 0 auto;
}

.history-seo-content h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    text-align: center;
}

.history-seo-content p {
    color: var(--text-medium);
    margin-bottom: 1.5rem;
    line-height: 1.7;
    text-align: justify;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 0 30px;
    }
}

@media (max-width: 992px) {
    h1 {
        font-size: 3rem;
    }

    h2 {
        font-size: 2.2rem;
    }

    .about-grid,
    .culture-sports-grid,
    .district-map,
    .business-grid,
    .seo-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .about-content {
        padding-right: 0;
    }

    .footer-top {
        grid-template-columns: 1fr;
    }

    .footer-nav {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-middle {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--white);
        padding: 20px;
        flex-direction: column;
        gap: 15px;
        box-shadow: var(--shadow-md);
        z-index: 100;
    }

    .nav-links.active {
        display: flex;
    }

    .mobile-menu-btn {
        display: block;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content .lead {
        font-size: 1.2rem;
    }

    .hero-slider {
        height: 500px;
    }

    .highlights-grid {
        grid-template-columns: 1fr;
    }

    /* TOP 3 Companies Section - responsywność */
    .company-cards {
        grid-template-columns: 1fr;
    }

    .company-card {
        max-width: 450px;
        margin: 0 auto;
    }

    .categories-filter {
        flex-direction: column;
        align-items: center;
    }

    .category-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .subcategories {
        flex-direction: column;
        align-items: center;
    }

    .subcategory-btn {
        width: 100%;
        max-width: 250px;
        text-align: center;
    }

    .company-footer {
        flex-direction: column;
        gap: 15px;
    }

    .company-social {
        order: 2;
    }

    .company-website {
        order: 1;
    }

    /* Nowe sekcje na stronie głównej - responsywność */
    .businesses-grid,
    .offers-grid,
    .coupons-grid {
        grid-template-columns: 1fr;
    }

    .business-card,
    .offer-card,
    .coupon-card {
        max-width: 400px;
        margin: 0 auto;
    }

    .coupon-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .coupon-logo {
        margin: 0 auto;
    }

    /* Wspólne style responsywne dla podstron */
    .page-hero {
        height: 300px;
    }

    .page-hero-content h1 {
        font-size: 2.5rem;
    }

    .page-hero-content .lead {
        font-size: 1.2rem;
    }

    /* O-miescie.html responsywność */
    .about-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .city-stats {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .feature-card {
        flex-direction: column;
        text-align: center;
    }

    .feature-icon {
        margin: 0 auto 20px;
    }

    .map-info {
        grid-template-columns: 1fr;
    }

    .map-info-item {
        flex-direction: column;
        text-align: center;
    }

    .map-info-icon {
        margin: 0 auto 15px;
    }

    /* Historia.html responsywność */
    .history-intro-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .industrial-heritage-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .industrial-heritage-gallery {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(4, 200px);
    }

    .figures-grid {
        grid-template-columns: 1fr;
    }

    .figure-card {
        flex-direction: column;
    }

    .figure-image {
        width: 100%;
        height: 200px;
    }

    .facts-grid {
        grid-template-columns: 1fr;
    }

    .timeline::before {
        left: 30px;
    }

    .timeline-date {
        left: 30px;
        transform: none;
    }

    .timeline-content {
        width: calc(100% - 60px);
        margin-left: 60px !important;
        margin-top: 40px;
    }

    .timeline-item:nth-child(odd) .timeline-content::before,
    .timeline-item:nth-child(even) .timeline-content::before {
        left: -40px;
        right: auto;
    }

    /* Strony informacyjne - responsywność */
    .gallery-grid {
        grid-template-columns: 1fr;
    }

    .gallery-item {
        height: 200px;
    }

    .gallery-caption {
        transform: translateY(0);
    }

    .fact-card {
        padding: 20px;
    }

    .fact-icon {
        width: 60px;
        height: 60px;
    }

    .fact-icon i {
        font-size: 1.5rem;
    }

    .add-company-grid,
    .add-offer-grid,
    .add-coupon-grid {
        grid-template-columns: 1fr;
    }

    .add-company-form-container,
    .add-offer-form-container,
    .add-coupon-form-container {
        padding: 20px;
    }

    .form-actions {
        flex-direction: column;
    }

    .contact-grid {
        grid-template-columns: 1fr;
    }

    .tourism-highlights {
        grid-template-columns: 1fr;
    }

    .business-features {
        grid-template-columns: 1fr;
    }

    .district-municipalities {
        grid-template-columns: 1fr;
    }

    .footer-nav {
        grid-template-columns: 1fr;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .footer-links {
        justify-content: center;
        flex-wrap: wrap;
    }
}

@media (max-width: 576px) {
    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    .container {
        padding: 0 20px;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content .lead {
        font-size: 1.1rem;
    }

    .hero-slider {
        height: 400px;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    /* TOP 3 Companies Section - responsywność dla małych urządzeń */
    .top-companies {
        padding: 50px 0;
    }

    .company-header {
        height: 150px;
    }

    .company-logo {
        width: 60px;
        height: 60px;
        bottom: -20px;
    }

    .company-content {
        padding: 30px 15px 15px;
    }

    .company-title h3 {
        font-size: 1.2rem;
    }

    .company-tags {
        margin-bottom: 10px;
    }

    .company-tag {
        font-size: 0.7rem;
        padding: 2px 8px;
    }

    .company-description {
        font-size: 0.9rem;
        margin-bottom: 15px;
    }

    .contact-item {
        font-size: 0.85rem;
        margin-bottom: 6px;
    }

    .company-website {
        font-size: 0.85rem;
    }

    .social-link {
        width: 28px;
        height: 28px;
    }

    .company-badge {
        font-size: 0.75rem;
        padding: 3px 8px;
    }

    .company-seo-info {
        padding: 15px;
    }

    .company-seo-info p {
        font-size: 0.85rem;
    }

    /* Nowe sekcje na stronie głównej - responsywność dla małych urządzeń */
    .local-businesses,
    .special-offers,
    .discount-coupons {
        padding: 50px 0;
    }

    .business-logo,
    .offer-image {
        height: 150px;
    }

    .business-content,
    .offer-content,
    .coupon-body {
        padding: 15px;
    }

    .business-content h3,
    .offer-content h3,
    .coupon-body h3 {
        font-size: 1.2rem;
    }

    .business-content p,
    .offer-content p,
    .coupon-body p {
        font-size: 0.9rem;
        margin-bottom: 15px;
    }

    .business-category,
    .offer-place {
        font-size: 0.8rem;
        margin-bottom: 10px;
    }

    .business-rating {
        margin-bottom: 10px;
    }

    .business-rating i,
    .business-rating span {
        font-size: 0.8rem;
    }

    .business-link {
        font-size: 0.8rem;
    }

    .offer-badge {
        font-size: 0.8rem;
        padding: 3px 8px;
    }

    .offer-price {
        margin-bottom: 10px;
    }

    .old-price {
        font-size: 0.8rem;
    }

    .new-price {
        font-size: 1.1rem;
    }

    .offer-validity,
    .coupon-validity {
        font-size: 0.8rem;
        margin-bottom: 15px;
    }

    .coupon-header {
        padding: 15px;
    }

    .coupon-logo {
        width: 50px;
        height: 50px;
    }

    .coupon-discount span {
        font-size: 1.5rem;
    }

    .coupon-discount small {
        font-size: 0.7rem;
    }

    .coupon-code {
        padding: 8px;
        margin-bottom: 10px;
    }

    .coupon-code span {
        font-size: 1rem;
    }

    .coupon-footer {
        padding: 10px 15px;
    }

    .businesses-buttons,
    .offers-buttons,
    .coupons-buttons {
        margin-top: 30px;
    }

    /* Wspólne style responsywne dla małych urządzeń */
    .page-hero {
        height: 250px;
        margin-top: 60px;
    }

    .page-hero-content h1 {
        font-size: 2rem;
    }

    .page-hero-content .lead {
        font-size: 1rem;
    }

    .breadcrumbs-list {
        font-size: 0.8rem;
    }

    .section-header h2 {
        font-size: 1.8rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    /* O-miescie.html responsywność dla małych urządzeń */
    .about-city {
        padding: 50px 0;
    }

    .about-content .lead {
        font-size: 1rem;
    }

    .about-content p {
        font-size: 0.9rem;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-content h3 {
        font-size: 1rem;
    }

    .stat-content p {
        font-size: 1.5rem;
    }

    .city-features {
        padding: 50px 0;
    }

    .feature-card {
        padding: 15px;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .feature-content h3 {
        font-size: 1.1rem;
    }

    .feature-content p {
        font-size: 0.9rem;
    }

    .city-map {
        padding: 50px 0;
    }

    .map-info-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    .map-info-content h3 {
        font-size: 1.1rem;
    }

    .map-info-content p {
        font-size: 0.9rem;
    }

    .city-seo {
        padding: 50px 0;
    }

    .city-seo-content h2 {
        font-size: 1.8rem;
    }

    .city-seo-content p {
        font-size: 0.9rem;
    }

    /* Historia.html responsywność dla małych urządzeń */
    .history-intro {
        padding: 50px 0;
    }

    .history-intro-content h2 {
        font-size: 1.8rem;
    }

    .history-intro-content .lead {
        font-size: 1rem;
    }

    .industrial-heritage {
        padding: 50px 0;
    }

    .industrial-heritage-content h3 {
        font-size: 1.5rem;
    }

    .heritage-list li {
        font-size: 0.9rem;
    }

    .historical-figures {
        padding: 50px 0;
    }

    .figure-card {
        padding: 0;
    }

    .figure-image {
        height: 180px;
    }

    .figure-content {
        padding: 15px;
    }

    .figure-content h3 {
        font-size: 1.5rem;
    }

    .history-facts {
        padding: 50px 0;
    }

    .fact-card {
        padding: 15px;
        flex-direction: column;
        text-align: center;
    }

    .fact-icon {
        margin: 0 auto 15px;
    }

    .fact-content h3 {
        font-size: 1.3rem;
    }

    .history-seo {
        padding: 50px 0;
    }

    .history-seo-content h2 {
        font-size: 1.8rem;
    }

    .facebook-promo {
        padding: 40px 0;
    }

    .facebook-promo-content h2 {
        font-size: 1.8rem;
    }

    .facebook-promo-content p {
        font-size: 1rem;
    }

    .tourism-highlight {
        flex-direction: column;
        text-align: center;
    }

    .highlight-icon {
        margin: 0 auto 20px;
    }

    .business-feature {
        flex-direction: column;
        text-align: center;
    }

    .feature-icon {
        margin: 0 auto 20px;
    }

    .cookie-notice .container {
        flex-direction: column;
        text-align: center;
    }

    .cookie-buttons {
        justify-content: center;
    }

    .footer-top {
        grid-template-columns: 1fr;
    }

    .footer-middle {
        grid-template-columns: 1fr;
        gap: 30px;
    }
}

/* Attractions Categories Section */
.attractions-categories {
    padding: 80px 0;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.category-card {
    background: var(--white);
    padding: 30px;
    border-radius: 10px;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.category-card h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.category-card h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background: var(--primary-gradient);
}

.category-card ul {
    list-style: none;
}

.category-card ul li {
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
}

.category-card ul li::before {
    content: '•';
    color: var(--primary-color);
    position: absolute;
    left: 0;
}

/* Featured Attractions Section */
.featured-attractions {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.attractions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.attraction-card {
    background: var(--white);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.attraction-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.attraction-image {
    position: relative;
    padding-top: 60%;
    overflow: hidden;
}

.attraction-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.attraction-card:hover .attraction-image img {
    transform: scale(1.1);
}

.attraction-content {
    padding: 20px;
}

.attraction-content h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.attraction-content p {
    margin-bottom: 15px;
    color: var(--dark-gray);
}

.read-more {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: inline-block;
    position: relative;
}

.read-more::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: var(--transition);
}

.read-more:hover::after {
    width: 100%;
}

/* Events Calendar Section */
.events-calendar {
    padding: 80px 0;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.event-card {
    background: var(--white);
    padding: 20px;
    border-radius: 10px;
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.event-date {
    background: var(--primary-gradient);
    color: var(--white);
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    min-width: 80px;
}

.event-date .day {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    line-height: 1;
}

.event-date .month {
    display: block;
    font-size: 0.9rem;
    text-transform: uppercase;
}

.event-content h3 {
    color: var(--primary-color);
    margin-bottom: 5px;
}

.event-content p {
    color: var(--dark-gray);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .categories-grid,
    .attractions-grid,
    .calendar-grid {
        grid-template-columns: 1fr;
    }

    .event-card {
        flex-direction: column;
        text-align: center;
    }

    .event-date {
        margin: 0 auto;
    }
}

/* Cultural Institutions Section */
.cultural-institutions {
    padding: 80px 0;
}

.institutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.institution-card {
    background: var(--white);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.institution-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.institution-image {
    position: relative;
    padding-top: 60%;
    overflow: hidden;
}

.institution-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.institution-card:hover .institution-image img {
    transform: scale(1.1);
}

.institution-content {
    padding: 20px;
}

.institution-content h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.institution-content p {
    margin-bottom: 15px;
    color: var(--dark-gray);
}

/* Sports Facilities Section */
.sports-facilities {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.facilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.facility-card {
    background: var(--white);
    padding: 30px;
    border-radius: 10px;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.facility-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.facility-card h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.facility-card h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background: var(--primary-gradient);
}

.facility-card ul {
    list-style: none;
}

.facility-card ul li {
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
}

.facility-card ul li::before {
    content: '•';
    color: var(--primary-color);
    position: absolute;
    left: 0;
}

/* Sports Clubs Section */
.sports-clubs {
    padding: 80px 0;
}

.clubs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.club-card {
    background: var(--white);
    padding: 30px;
    border-radius: 10px;
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
}

.club-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.club-logo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.club-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.club-content h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.club-content p {
    color: var(--dark-gray);
    font-size: 0.9rem;
}

/* Upcoming Events Section */
.upcoming-events {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .institutions-grid,
    .facilities-grid,
    .clubs-grid,
    .events-grid {
        grid-template-columns: 1fr;
    }

    .club-card {
        flex-direction: column;
        text-align: center;
    }

    .club-logo {
        margin: 0 auto;
    }
}

/* Style dla podstrony "Powiat żyrardowski" */
.county-overview {
    padding: 4rem 0;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.overview-card {
    background: #fff;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.overview-card:hover {
    transform: translateY(-5px);
}

.overview-card h3 {
    color: #ff6b00;
    margin-bottom: 1rem;
}

.overview-card p {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
}

.municipalities {
    padding: 4rem 0;
    background: #f9f9f9;
}

.municipalities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.municipality-card {
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.municipality-card:hover {
    transform: translateY(-5px);
}

.municipality-image {
    height: 200px;
    overflow: hidden;
}

.municipality-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.municipality-card:hover .municipality-image img {
    transform: scale(1.1);
}

.municipality-content {
    padding: 1.5rem;
}

.municipality-content h3 {
    color: #ff6b00;
    margin-bottom: 1rem;
}

.municipality-content ul {
    list-style: none;
    padding: 0;
    margin-top: 1rem;
}

.municipality-content li {
    margin-bottom: 0.5rem;
    color: #666;
}

.county-attractions {
    padding: 4rem 0;
}

.attractions-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.attraction-card {
    background: #fff;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.attraction-card:hover {
    transform: translateY(-5px);
}

.attraction-card h3 {
    color: #ff6b00;
    margin-bottom: 1rem;
}

.attraction-card ul {
    list-style: none;
    padding: 0;
}

.attraction-card li {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.attraction-card li:before {
    content: "•";
    color: #ff6b00;
    position: absolute;
    left: 0;
}

.county-economy {
    padding: 4rem 0;
    background: #f9f9f9;
}

.economy-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.economy-card {
    background: #fff;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.economy-card:hover {
    transform: translateY(-5px);
}

.economy-card h3 {
    color: #ff6b00;
    margin-bottom: 1rem;
}

.economy-card p {
    color: #666;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .overview-grid,
    .attractions-grid,
    .economy-grid {
        grid-template-columns: 1fr;
    }

    .municipalities-grid {
        grid-template-columns: 1fr;
    }
}

/* About City Section */
.about-city {
    padding: 80px 0;
    background-color: var(--white);
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
    margin-bottom: 60px;
}

.about-content .lead {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 20px;
    font-weight: 500;
    line-height: 1.6;
}

.about-content p {
    margin-bottom: 20px;
    line-height: 1.7;
    color: var(--text-medium);
}

.about-image {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    position: relative;
}

.about-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.5s ease;
}

.about-image:hover img {
    transform: scale(1.05);
}

.city-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.stat-card {
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.stat-content {
    flex-grow: 1;
}

.stat-content h3 {
    color: var(--primary-color);
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.stat-content p {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: var(--text-dark);
}

.stat-info {
    font-size: 0.85rem;
    color: var(--text-light);
    display: block;
}

/* City Features Section */
.city-features {
    padding: 80px 0;
    background-color: var(--off-white);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-card {
    display: flex;
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    gap: 20px;
    align-items: flex-start;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.feature-content h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.feature-content p {
    color: var(--text-medium);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 0;
}

/* City Map Section */
.city-map {
    padding: 80px 0;
    background-color: var(--white);
}

.map-container {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    margin-bottom: 40px;
}

.map-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.map-info-item {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.map-info-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    flex-shrink: 0;
}

.map-info-content h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.map-info-content p {
    color: var(--text-medium);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 0;
}

/* City SEO Section */
.city-seo {
    padding: 80px 0;
    background-color: var(--off-white);
}

.city-seo-content {
    max-width: 800px;
    margin: 0 auto;
}

.city-seo-content h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    text-align: center;
}

.city-seo-content p {
    color: var(--text-medium);
    margin-bottom: 1.5rem;
    line-height: 1.7;
    text-align: justify;
}

/* Local Businesses Section */
.local-businesses {
    padding: 80px 0;
    background-color: var(--white);
}

.businesses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.business-card {
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
}

.business-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.business-logo {
    height: 180px;
    overflow: hidden;
}

.business-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.business-card:hover .business-logo img {
    transform: scale(1.1);
}

.business-content {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.business-content h3 {
    color: var(--primary-color);
    margin-bottom: 5px;
    font-size: 1.3rem;
}

.business-category {
    color: var(--text-medium);
    font-size: 0.9rem;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.business-category i {
    color: var(--primary-color);
}

.business-content p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.6;
    flex-grow: 1;
}

.business-rating {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 3px;
}

.business-rating i {
    color: #FFD700;
    font-size: 0.9rem;
}

.business-rating span {
    color: var(--text-medium);
    font-size: 0.9rem;
    margin-left: 5px;
}

.business-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: var(--transition);
    font-size: 0.9rem;
}

.business-link:hover {
    color: var(--primary-dark);
}

.businesses-buttons {
    text-align: center;
    margin-top: 40px;
}

/* Special Offers Section */
.special-offers {
    padding: 80px 0;
    background-color: var(--off-white);
    position: relative;
}

.special-offers::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" fill="rgba(0,0,0,0.03)"/></svg>');
    opacity: 0.5;
}

.offers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
    position: relative;
    z-index: 2;
}

.offer-card {
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
}

.offer-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.offer-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--primary-color);
    color: var(--white);
    padding: 5px 10px;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    font-size: 0.9rem;
    z-index: 10;
}

.offer-image {
    height: 180px;
    overflow: hidden;
}

.offer-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.offer-card:hover .offer-image img {
    transform: scale(1.1);
}

.offer-placeholder {
    height: 180px;
    background-color: var(--light-gray);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;
    transition: background-color 0.3s ease;
}

.offer-card:hover .offer-placeholder {
    background-color: var(--medium-gray);
}

.offer-placeholder-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.offer-placeholder p {
    color: var(--text-medium);
    font-size: 0.95rem;
    margin: 0;
    font-style: italic;
}

.offers-contact {
    text-align: center;
    margin: 30px 0;
    padding: 20px;
    background-color: var(--light-gray);
    border-radius: var(--border-radius-md);
}

.offers-contact p {
    margin-bottom: 15px;
    color: var(--text-medium);
    font-size: 1.1rem;
}

.offer-content {
    padding: 25px;
}

.offer-content h3 {
    color: var(--primary-color);
    margin-bottom: 5px;
    font-size: 1.3rem;
}

.offer-place {
    color: var(--text-medium);
    font-size: 0.9rem;
    margin-bottom: 15px;
    font-style: italic;
}

.offer-content p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.6;
}

.offer-price {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.old-price {
    color: var(--text-light);
    text-decoration: line-through;
    font-size: 0.9rem;
}

.new-price {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.2rem;
}

.offer-validity {
    color: var(--text-medium);
    font-size: 0.85rem;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.offers-buttons {
    text-align: center;
    margin-top: 40px;
    position: relative;
    z-index: 2;
}

/* Discount Coupons Section */
.discount-coupons {
    padding: 80px 0;
    background-color: var(--white);
}

.discount-coupons .coupons-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    margin-top: 40px;
}

.coupon-card {
    background: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition);
    border: 2px dashed var(--medium-gray);
}

.coupon-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.coupon-header {
    padding: 20px;
    background: var(--light-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.coupon-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--white);
}

.coupon-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.coupon-discount {
    text-align: center;
    background-color: var(--white);
    padding: 10px;
    border-radius: var(--border-radius-md);
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.coupon-discount span {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
    line-height: 1;
}

.coupon-discount small {
    font-size: 0.8rem;
    color: var(--text-medium);
}

.coupon-body {
    padding: 20px;
}

.coupon-body h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.coupon-body p {
    color: var(--text-medium);
    margin-bottom: 15px;
    line-height: 1.6;
    font-size: 0.95rem;
}

.coupon-code-hidden {
    position: relative;
    margin-bottom: 15px;
    cursor: pointer;
}

.coupon-code-placeholder {
    background: var(--light-gray);
    padding: 15px;
    text-align: center;
    border-radius: var(--border-radius-sm);
    color: var(--text-medium);
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.coupon-code-placeholder::after {
    content: '\f06e';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
}

.coupon-code-placeholder:hover {
    background: var(--medium-gray);
}

.coupon-code {
    background: var(--light-gray);
    padding: 10px;
    text-align: center;
    border-radius: var(--border-radius-sm);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.coupon-code-hidden.active .coupon-code {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

.coupon-code-hidden.active .coupon-code-placeholder {
    opacity: 0;
    pointer-events: none;
}

.coupon-code span {
    font-family: monospace;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
    letter-spacing: 2px;
}

.coupons-contact {
    text-align: center;
    margin: 30px 0;
    padding: 20px;
    background-color: var(--light-gray);
    border-radius: var(--border-radius-md);
}

.coupons-contact p {
    margin-bottom: 15px;
    color: var(--text-medium);
    font-size: 1.1rem;
}

.coupon-validity {
    color: var(--text-medium);
    font-size: 0.85rem;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 5px;
}

.coupon-footer {
    padding: 15px 20px;
    background: var(--light-gray);
    text-align: center;
}

/* TOP 3 Companies Section */
.top-companies {
    padding: 80px 0;
    background-color: var(--white);
    position: relative;
}

.top-companies::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, var(--light-gray), transparent);
}

.categories-filter {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    margin: 30px 0;
}

.category-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background-color: var(--white);
    border: 1px solid var(--light-gray);
    border-radius: var(--border-radius-md);
    color: var(--text-medium);
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.category-btn:hover {
    background-color: var(--light-gray);
    color: var(--text-dark);
}

.category-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.category-btn i {
    font-size: 1.1rem;
}

.subcategories {
    display: none;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin: 15px 0 30px;
    animation: fadeIn 0.3s ease;
}

.subcategories.active {
    display: flex;
}

.subcategory-btn {
    padding: 6px 15px;
    background-color: var(--light-gray);
    border-radius: var(--border-radius-sm);
    color: var(--text-medium);
    font-size: 0.85rem;
    cursor: pointer;
    transition: var(--transition);
}

.subcategory-btn:hover {
    background-color: var(--medium-gray);
    color: var(--text-dark);
}

.subcategory-btn.active {
    background-color: var(--primary-color);
    color: var(--white);
}

.company-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.company-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
}

.company-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.company-header {
    position: relative;
    height: 200px;
    overflow: hidden;
}

/* Style dla zwykłych kart firm (nie TOP 3) */
.company-card .company-logo {
    position: absolute;
    bottom: -30px;
    left: 20px;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: var(--white);
    border: 4px solid var(--white);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    z-index: 2;
}

/* Style dla obrazków w zwykłych kartach firm */
.company-card .company-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.company-cover {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.company-card:hover .company-cover {
    transform: scale(1.05);
}

.company-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 5px 10px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
}

.company-content {
    padding: 40px 20px 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.company-title {
    margin-bottom: 5px;
}

.company-title h3 {
    color: var(--text-dark);
    font-size: 1.3rem;
    margin: 0;
}

.company-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.company-tag {
    padding: 3px 10px;
    background-color: var(--light-gray);
    border-radius: var(--border-radius-sm);
    color: var(--text-medium);
    font-size: 0.75rem;
}

.company-description {
    color: var(--text-medium);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 20px;
    flex-grow: 1;
}

.company-contact {
    margin-bottom: 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.contact-item i {
    color: var(--primary-color);
    width: 16px;
    text-align: center;
}

.contact-item a {
    color: var(--text-medium);
    text-decoration: none;
    transition: var(--transition);
}

.contact-item a:hover {
    color: var(--primary-color);
}

.company-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid var(--light-gray);
}

.company-social {
    display: flex;
    gap: 10px;
}

.social-link {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--light-gray);
    color: var(--text-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.company-website {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition);
}

.company-website:hover {
    color: var(--primary-dark);
}

.company-seo-info {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    background-color: var(--light-gray);
    border-radius: var(--border-radius-md);
}

.company-seo-info p {
    color: var(--text-medium);
    font-size: 0.95rem;
    margin: 0;
}

.company-seo-info strong {
    color: var(--primary-color);
}

.top-companies-buttons {
    text-align: center;
    margin-top: 40px;
}

/* Lightbox */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: none;
}

.lightbox.active {
    display: block;
}

.lightbox-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.lightbox-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    animation: fadeInDown 0.3s ease;
}

.lightbox-close {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 30px;
    height: 30px;
    background-color: var(--light-gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    transition: var(--transition);
}

.lightbox-close:hover {
    background-color: var(--medium-gray);
}

.lightbox-content {
    padding: 0;
}

.lightbox-header {
    padding: 20px 30px;
    border-bottom: 1px solid var(--light-gray);
}

.lightbox-header h2 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.lightbox-body {
    padding: 30px;
    max-height: 60vh;
    overflow-y: auto;
}

.lightbox-footer {
    padding: 20px 30px;
    border-top: 1px solid var(--light-gray);
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.offer-details,
.coupon-details {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.offer-details-header,
.coupon-details-header {
    display: flex;
    gap: 20px;
    align-items: center;
}

.offer-details-logo,
.coupon-details-logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.offer-details-logo img,
.coupon-details-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.offer-details-title h3,
.coupon-details-title h3 {
    margin: 0 0 5px 0;
    color: var(--primary-color);
}

.offer-details-title p,
.coupon-details-title p {
    margin: 0;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.offer-details-content,
.coupon-details-content {
    background-color: var(--light-gray);
    padding: 20px;
    border-radius: var(--border-radius-md);
}

.offer-details-content h4,
.coupon-details-content h4 {
    margin: 0 0 15px 0;
    color: var(--text-dark);
}

.offer-details-content p,
.coupon-details-content p {
    margin: 0 0 10px 0;
    color: var(--text-medium);
    line-height: 1.6;
}

.offer-details-content ul,
.coupon-details-content ul {
    margin: 15px 0;
    padding-left: 20px;
}

.offer-details-content li,
.coupon-details-content li {
    margin-bottom: 8px;
    color: var(--text-medium);
}

.offer-details-price {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 20px 0;
}

.offer-details-price .old-price {
    font-size: 1.1rem;
}

.offer-details-price .new-price {
    font-size: 1.5rem;
}

.coupon-details-code {
    margin: 20px 0;
    background-color: var(--white);
    padding: 15px;
    border-radius: var(--border-radius-md);
    text-align: center;
    border: 2px dashed var(--primary-color);
}

.coupon-details-code span {
    font-family: monospace;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    letter-spacing: 3px;
}

.offer-details-validity,
.coupon-details-validity {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-medium);
    font-size: 0.9rem;
    margin-top: 15px;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}
/* Utility classes */
.text-center {
    text-align: center;
}

.mt-40 {
    margin-top: 40px;
}

.text-center .btn {
    margin-left: auto;
    margin-right: auto;
}

/* News Section */
.news-section {
    padding: 60px 0;
    background-color: var(--light-gray);
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.news-item {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    cursor: pointer;
}

.news-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.news-item-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    position: relative;
}

.news-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.news-item:hover .news-item-image img {
    transform: scale(1.05);
}

.news-item-image.no-image {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 3rem;
}

.news-category-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 5px 10px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.news-item-content {
    padding: 25px;
}

.news-item-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 15px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-item-excerpt {
    color: var(--text-medium);
    line-height: 1.6;
    margin: 0 0 20px 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-light);
}

.news-item-date {
    display: flex;
    align-items: center;
    gap: 5px;
}

.news-item-read-more {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
}

.news-item-read-more:hover {
    color: var(--secondary-color);
}

/* News Categories */
.news-category-local { background-color: #28a745; }
.news-category-events { background-color: #17a2b8; }
.news-category-business { background-color: #ffc107; color: var(--text-dark) !important; }
.news-category-culture { background-color: #6f42c1; }
.news-category-sport { background-color: #fd7e14; }

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 80px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    opacity: 0.1;
    z-index: 1;
}

.cta-section .container {
    position: relative;
    z-index: 2;
}

.cta-section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 20px 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.cta-section p {
    font-size: 1.2rem;
    margin: 0 0 40px 0;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-container {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    background-color: var(--white);
    color: var(--primary-color);
    text-decoration: none;
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    font-size: 1.1rem;
    transition: var(--transition);
    box-shadow: var(--shadow-md);
}

.cta-button:hover {
    background-color: var(--light-gray);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.cta-button i {
    font-size: 1.2rem;
}

/* Responsive CTA */
@media (max-width: 768px) {
    .cta-section h2 {
        font-size: 2rem;
    }

    .cta-section p {
        font-size: 1.1rem;
    }

    .cta-container {
        flex-direction: column;
        align-items: center;
    }

    .cta-button {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

/* ===== SEKCJA FILIP DE GIRARD ===== */
.filip-girard-year {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.filip-girard-year::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(45deg, transparent, transparent 2px, rgba(255,255,255,0.1) 2px, rgba(255,255,255,0.1) 4px);
    opacity: 0.05;
    z-index: 1;
}

.filip-girard-year .container {
    position: relative;
    z-index: 2;
}

.girard-header {
    text-align: center;
    margin-bottom: 60px;
}

.girard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: center;
    margin-bottom: 40px;
}

.girard-info p {
    font-size: 1.2rem;
    line-height: 1.7;
    color: var(--text-medium);
    margin-bottom: 40px;
}

.girard-highlights {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.highlight-item {
    background: var(--white);
    padding: 30px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: var(--transition);
}

.highlight-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.highlight-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px auto;
    color: var(--white);
    font-size: 2rem;
}

.highlight-item h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 15px 0;
}

.highlight-item p {
    color: var(--text-medium);
    font-size: 0.95rem;
    line-height: 1.5;
    margin: 0;
}

.girard-image {
    text-align: center;
}

.girard-image img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.girard-cta {
    text-align: center;
}

/* ===== SEKCJA POMNIK HISTORII ===== */
.heritage-monument {
    background: var(--white);
    padding: 80px 0;
}

.heritage-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.heritage-text p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-medium);
    margin-bottom: 40px;
}

.heritage-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 40px;
}

.stat {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: var(--border-radius-md);
    border-left: 4px solid var(--primary-color);
}

.stat .number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stat .label {
    font-size: 0.9rem;
    color: var(--text-medium);
    font-weight: 500;
}

.heritage-image {
    text-align: center;
}

.heritage-image img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

/* Responsywność dla nowych sekcji */
@media (max-width: 768px) {
    .girard-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .girard-highlights {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .heritage-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .heritage-stats {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

/* ===== SEKCJE MUZEUM I ZABYTKÓW ===== */
.museum-section {
    background: var(--white);
    padding: 80px 0;
}

.museum-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: start;
    margin-bottom: 60px;
}

.museum-info {
    display: grid;
    gap: 40px;
}

.museum-description h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 15px 0;
}

.museum-description p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-medium);
    margin-bottom: 20px;
}

.museum-highlights {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.highlight-box {
    background: var(--light-gray);
    padding: 25px;
    border-radius: var(--border-radius-md);
    text-align: center;
    transition: var(--transition);
}

.highlight-box:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-sm);
}

.highlight-box .highlight-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px auto;
    color: var(--white);
    font-size: 1.5rem;
}

.highlight-box h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 10px 0;
}

.highlight-box p {
    font-size: 0.9rem;
    color: var(--text-medium);
    margin: 0;
}

.museum-image {
    text-align: center;
}

.museum-image img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.image-caption {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-top: 10px;
    font-style: italic;
}

.museum-practical {
    background: var(--light-gray);
    padding: 40px;
    border-radius: var(--border-radius-lg);
}

.practical-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 30px 0;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.info-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-top: 3px;
}

.info-item div {
    font-size: 0.95rem;
    line-height: 1.5;
}

.info-item strong {
    color: var(--text-dark);
    font-weight: 600;
}

/* ===== SEKCJA SZCZEGÓŁÓW POMNIKA HISTORII ===== */
.heritage-monument-details {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 80px 0;
}

.heritage-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: start;
    margin-bottom: 60px;
}

.heritage-description h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 30px 0 15px 0;
}

.heritage-description h3:first-child {
    margin-top: 0;
}

.heritage-description p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-medium);
    margin-bottom: 20px;
}

.heritage-features {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.heritage-features li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 1rem;
    color: var(--text-medium);
}

.heritage-features i {
    color: var(--primary-color);
    font-size: 0.9rem;
}

.heritage-stats-detailed {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.stat-card {
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
}

.stat-content h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0 0 5px 0;
}

.stat-content p {
    font-size: 0.9rem;
    color: var(--text-medium);
    margin: 0;
}

.heritage-gallery {
    display: grid;
    gap: 20px;
}

.gallery-main img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.gallery-thumbs {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.gallery-thumbs img {
    width: 100%;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: var(--transition);
}

.gallery-thumbs img:hover {
    transform: scale(1.05);
}

.heritage-importance {
    background: var(--white);
    padding: 40px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.importance-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 20px 0;
}

.importance-content > p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-medium);
    margin-bottom: 30px;
}

.importance-points {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.point {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.point i {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-top: 5px;
}

.point h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 8px 0;
}

.point p {
    font-size: 0.95rem;
    color: var(--text-medium);
    margin: 0;
    line-height: 1.5;
}

/* ===== SEKCJA ROK FILIPA DE GIRARDA ===== */
.filip-girard-year {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: var(--white);
    padding: 80px 0;
    position: relative;
}

.filip-girard-year.girard-light-theme {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
    color: var(--text-dark) !important;
    border-top: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
    overflow: hidden;
}

.filip-girard-year::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="70" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    pointer-events: none;
}

.girard-header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 1;
}

.girard-header .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(45deg, #f39c12, #e67e22);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.girard-header .section-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin: 0;
}

.girard-light-theme .girard-header .section-subtitle {
    color: var(--text-medium);
    opacity: 1;
}

.girard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: center;
    margin-bottom: 40px;
    position: relative;
    z-index: 1;
}

.girard-info p {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 30px;
    opacity: 0.95;
}

.girard-light-theme .girard-info p {
    color: var(--text-medium);
    opacity: 1;
}

.girard-highlights {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
}

.highlight-item {
    text-align: center;
    padding: 25px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.highlight-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
}

.girard-light-theme .highlight-item {
    background: var(--white);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--light-gray);
    backdrop-filter: none;
}

.girard-light-theme .highlight-item:hover {
    box-shadow: var(--shadow-md);
    background: var(--white);
}

.highlight-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #f39c12, #e67e22);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px auto;
    color: var(--white);
    font-size: 1.5rem;
}

.highlight-item h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 10px 0;
    color: var(--white);
}

.highlight-item p {
    font-size: 0.9rem;
    opacity: 0.9;
    margin: 0;
    line-height: 1.5;
}

.girard-light-theme .highlight-item h3 {
    color: var(--text-dark);
}

.girard-light-theme .highlight-item p {
    color: var(--text-medium);
    opacity: 1;
}

.girard-image {
    text-align: center;
}

.girard-image img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.girard-cta {
    text-align: center;
    position: relative;
    z-index: 1;
}

/* ===== SEKCJA POMNIK HISTORII ===== */
.heritage-monument {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 80px 0;
    position: relative;
}

.heritage-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.heritage-text .section-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.heritage-text .section-subtitle {
    font-size: 1.1rem;
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 25px;
}

.heritage-text p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-medium);
    margin-bottom: 30px;
}

.heritage-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 30px;
}

.heritage-stats .stat {
    text-align: center;
    padding: 20px;
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.heritage-stats .stat:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.heritage-stats .stat .number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.heritage-stats .stat .label {
    font-size: 0.9rem;
    color: var(--text-medium);
    font-weight: 500;
}

.heritage-image {
    text-align: center;
}

.heritage-image img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

/* Responsywność dla nowych sekcji */
@media (max-width: 768px) {
    .museum-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .museum-highlights {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .info-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .heritage-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .heritage-stats-detailed {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .gallery-thumbs {
        grid-template-columns: 1fr;
    }

    .importance-points {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    /* Responsywność dla sekcji Girarda */
    .girard-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .girard-highlights {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .girard-header .section-title {
        font-size: 2rem;
    }

    /* Responsywność dla sekcji Pomnika Historii */
    .heritage-stats {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .heritage-text .section-title {
        font-size: 1.8rem;
    }
}

/* ===== NOWY DESIGN OFERT ===== */
.offers-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-bottom: 50px;
}

.offer-card-new {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    border: 2px solid transparent;
}

.offer-card-new:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.offer-card-new.featured {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #fff, #f8f9fa);
}

.offer-badge-new {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.offer-badge-new.hot {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: var(--white);
    animation: pulse 2s infinite;
}

.offer-badge-new.new {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
    color: var(--white);
}

.offer-badge-new.popular {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: var(--white);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.offer-image-new {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.offer-image-new img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.offer-card-new:hover .offer-image-new img {
    transform: scale(1.1);
}

.offer-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.3), rgba(0,0,0,0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.offer-card-new:hover .offer-overlay {
    opacity: 1;
}

.offer-discount {
    background: var(--white);
    color: var(--primary-color);
    padding: 15px 20px;
    border-radius: 50%;
    font-size: 1.2rem;
    font-weight: 700;
    box-shadow: var(--shadow-md);
    transform: scale(0.8);
    transition: var(--transition);
}

.offer-card-new:hover .offer-discount {
    transform: scale(1);
}

.offer-content-new {
    padding: 25px;
}

.offer-company-new {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--light-gray);
}

.company-logo-small {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--light-gray);
}

.company-info h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 3px 0;
}

.company-category {
    font-size: 0.8rem;
    color: var(--text-light);
    background: var(--light-gray);
    padding: 2px 8px;
    border-radius: 10px;
}

.offer-title-new {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 12px 0;
    line-height: 1.3;
}

.offer-description-new {
    font-size: 0.95rem;
    color: var(--text-medium);
    line-height: 1.5;
    margin-bottom: 20px;
}

.offer-details {
    margin-bottom: 20px;
}

.offer-price-new {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.offer-price-new .price-old {
    font-size: 0.9rem;
    color: var(--text-light);
    text-decoration: line-through;
}

.offer-price-new .price-new {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--primary-color);
}

.offer-price-new .price-save {
    font-size: 0.8rem;
    color: var(--success-color);
    background: rgba(39, 174, 96, 0.1);
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 600;
}

.offer-validity {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    color: var(--text-light);
}

.offer-validity i {
    color: var(--primary-color);
}

.offer-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.btn-full {
    width: 100%;
    padding: 12px;
    font-size: 0.9rem;
    font-weight: 600;
    border-radius: var(--border-radius-md);
    transition: var(--transition);
}

.btn-full i {
    margin-right: 5px;
}

.offers-cta {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 40px;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;
    position: relative;
    overflow: hidden;
}

.offers-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="0.5" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    pointer-events: none;
}

.cta-content {
    flex: 1;
    position: relative;
    z-index: 1;
}

.cta-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 10px 0;
}

.cta-content p {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0 0 20px 0;
    line-height: 1.5;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 12px 25px;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.btn-lg {
    padding: 15px 30px;
    font-size: 1.1rem;
    position: relative;
    z-index: 1;
}

/* Responsywność dla nowych ofert */
@media (max-width: 768px) {
    .offers-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .offer-card-new {
        margin: 0 10px;
    }

    .offers-cta {
        flex-direction: column;
        text-align: center;
        gap: 20px;
        padding: 30px 20px;
    }

    .offer-actions {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .cta-content h3 {
        font-size: 1.3rem;
    }
}

/* ===== STYLE DLA STRONY KULTURA-SPORT ===== */
.facility-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    border: 2px solid transparent;
}

.facility-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.facility-card.featured {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #fff, #f8f9fa);
}

.facility-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: var(--white);
    font-size: 1.5rem;
}

.facility-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 15px 0;
}

.facility-description {
    font-size: 1rem;
    color: var(--text-medium);
    line-height: 1.6;
    margin-bottom: 15px;
}

.facility-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.facility-card li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    font-size: 0.95rem;
    color: var(--text-medium);
}

.facility-card li i {
    color: var(--success-color);
    font-size: 0.9rem;
}

.club-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    border: 2px solid transparent;
}

.club-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.club-card.featured {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #fff, #f8f9fa);
}

.club-logo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
}

.club-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    border: 3px solid var(--light-gray);
}

.club-content h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 12px 0;
}

.club-content p {
    font-size: 0.95rem;
    color: var(--text-medium);
    line-height: 1.6;
    margin-bottom: 15px;
}

.club-details {
    display: flex;
    gap: 10px;
}

.club-sport, .club-level {
    font-size: 0.8rem;
    padding: 4px 10px;
    border-radius: 12px;
    font-weight: 600;
}

.club-sport {
    background: var(--primary-color);
    color: var(--white);
}

.club-level {
    background: var(--light-gray);
    color: var(--text-medium);
}

.event-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    border: 2px solid transparent;
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.event-card.featured {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #fff, #f8f9fa);
}

.event-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: var(--white);
    font-size: 1.5rem;
}

.event-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 12px 0;
}

.event-content p {
    font-size: 0.95rem;
    color: var(--text-medium);
    line-height: 1.6;
    margin-bottom: 15px;
}

.event-details {
    display: flex;
    gap: 10px;
}

.event-type, .event-frequency {
    font-size: 0.8rem;
    padding: 4px 10px;
    border-radius: 12px;
    font-weight: 600;
}

.event-type {
    background: var(--primary-color);
    color: var(--white);
}

.event-frequency {
    background: var(--light-gray);
    color: var(--text-medium);
}

.tourism-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    text-align: center;
}

.tourism-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.tourism-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px auto;
    color: var(--white);
    font-size: 1.5rem;
}

.tourism-content h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 12px 0;
}

.tourism-content p {
    font-size: 0.95rem;
    color: var(--text-medium);
    line-height: 1.6;
    margin-bottom: 20px;
}

.facilities-grid, .clubs-grid, .events-grid, .tourism-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

/* Responsywność dla strony kultura-sport */
@media (max-width: 768px) {
    .facilities-grid, .clubs-grid, .events-grid, .tourism-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .facility-card, .club-card, .event-card, .tourism-card {
        padding: 20px;
    }

    .facility-icon, .event-icon, .tourism-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .club-logo {
        width: 60px;
        height: 60px;
    }
}

/* ===== STYLE DLA ZARZĄDZANIA KOLEJNOŚCIĄ SEKCJI ===== */
.section-order-container {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    border: 1px solid var(--light-gray);
}

.section-description {
    color: var(--text-medium);
    margin-bottom: 20px;
    font-size: 0.95rem;
}

.sortable-sections {
    min-height: 400px;
    border: 2px dashed var(--light-gray);
    border-radius: var(--border-radius-lg);
    padding: 15px;
    background: #f8f9fa;
}

.section-item {
    display: flex;
    align-items: center;
    background: var(--white);
    border: 1px solid var(--light-gray);
    border-radius: var(--border-radius-lg);
    padding: 15px;
    margin-bottom: 10px;
    cursor: move;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.section-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.section-item.sortable-ghost {
    opacity: 0.5;
    background: var(--primary-color);
    color: var(--white);
}

.section-item.sortable-chosen {
    background: var(--primary-color);
    color: var(--white);
}

.section-handle {
    margin-right: 15px;
    color: var(--text-light);
    font-size: 1.2rem;
    cursor: grab;
}

.section-handle:active {
    cursor: grabbing;
}

.section-info {
    flex: 1;
}

.section-info h4 {
    margin: 0 0 5px 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
}

.section-info p {
    margin: 0;
    font-size: 0.85rem;
    color: var(--text-medium);
}

.section-status {
    margin-left: 15px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.active {
    background: #e8f5e8;
    color: #27ae60;
}

.status-badge.hidden {
    background: #fef2e8;
    color: #f39c12;
}

.status-badge.fixed {
    background: #e8f2ff;
    color: #3498db;
}

.section-order-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--light-gray);
}

/* Responsywność dla zarządzania kolejnością sekcji */
@media (max-width: 768px) {
    .section-order-actions {
        flex-direction: column;
    }

    .section-item {
        padding: 12px;
    }

    .section-handle {
        margin-right: 10px;
    }
}

/* ===== MAPA STRONY ===== */
.sitemap-section {
    padding: 80px 0;
    background: var(--white);
}

.sitemap-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-bottom: 60px;
}

.sitemap-column h2 {
    color: var(--primary-color);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--light-gray);
    display: flex;
    align-items: center;
    gap: 10px;
}

.sitemap-column h2 i {
    color: var(--secondary-color);
}

.sitemap-list {
    list-style: none;
    padding: 0;
    margin: 0 0 30px 0;
}

.sitemap-list li {
    margin-bottom: 8px;
}

.sitemap-list a {
    color: var(--text-medium);
    text-decoration: none;
    padding: 5px 0;
    display: block;
    transition: var(--transition);
    border-left: 3px solid transparent;
    padding-left: 15px;
}

.sitemap-list a:hover {
    color: var(--primary-color);
    border-left-color: var(--secondary-color);
    padding-left: 20px;
}

.sitemap-footer {
    background: var(--light-gray);
    padding: 40px;
    border-radius: var(--border-radius-lg);
    margin-top: 40px;
}

.sitemap-info h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.sitemap-info p {
    color: var(--text-medium);
    line-height: 1.6;
    margin-bottom: 20px;
}

/* ===== DODAJ KUPON ===== */
.add-coupon-section {
    padding: 80px 0;
    background: var(--white);
}

.add-coupon-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    align-items: start;
}

.add-coupon-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.info-box, .benefits-box, .process-box, .seo-content {
    background: var(--white);
    padding: 30px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--light-gray);
}

.info-box {
    text-align: center;
    background: linear-gradient(135deg, #fff5f0 0%, #ffffff 100%);
    border-color: var(--secondary-color);
}

.info-icon {
    width: 80px;
    height: 80px;
    background: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--white);
    font-size: 2rem;
}

.benefits-list {
    list-style: none;
    padding: 0;
    margin: 20px 0 0 0;
}

.benefits-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 10px;
    background: var(--light-gray);
    border-radius: var(--border-radius-sm);
}

.benefits-list li i {
    color: var(--secondary-color);
    margin-right: 10px;
    margin-top: 2px;
    flex-shrink: 0;
}

.process-steps {
    margin-top: 20px;
}

.process-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
    padding: 20px;
    background: var(--light-gray);
    border-radius: var(--border-radius-md);
}

.step-number {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 20px;
    flex-shrink: 0;
}

.step-content h4 {
    margin: 0 0 8px 0;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.step-content p {
    margin: 0;
    color: var(--text-medium);
    line-height: 1.5;
}

.contact-sidebar {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-box, .pricing-box {
    background: var(--white);
    padding: 30px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--light-gray);
}

.contact-box h3, .pricing-box h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.contact-info {
    margin-bottom: 25px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    padding: 15px;
    background: var(--light-gray);
    border-radius: var(--border-radius-sm);
}

.contact-item i {
    color: var(--secondary-color);
    margin-right: 15px;
    margin-top: 2px;
    flex-shrink: 0;
    width: 20px;
}

.contact-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.contact-item a:hover {
    text-decoration: underline;
}

.pricing-info {
    margin-bottom: 20px;
}

.pricing-item {
    position: relative;
    padding: 20px;
    border: 2px solid var(--light-gray);
    border-radius: var(--border-radius-md);
    margin-bottom: 15px;
    transition: var(--transition);
}

.pricing-item.featured {
    border-color: var(--secondary-color);
    background: linear-gradient(135deg, #fff5f0 0%, #ffffff 100%);
}

.pricing-badge {
    position: absolute;
    top: -10px;
    right: 20px;
    background: var(--secondary-color);
    color: var(--white);
    padding: 5px 15px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
}

.pricing-name {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.pricing-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 15px;
}

.pricing-features {
    color: var(--text-medium);
    line-height: 1.6;
    font-size: 0.9rem;
}

.pricing-features i {
    color: var(--secondary-color);
    margin-right: 8px;
}

.pricing-note {
    background: var(--light-gray);
    padding: 15px;
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    color: var(--text-medium);
    margin: 0;
}

.pricing-note i {
    color: var(--primary-color);
    margin-right: 8px;
}

@media (max-width: 992px) {
    .add-coupon-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .contact-sidebar {
        order: -1;
    }
}

/* ===== LOADING SPINNER ===== */

.companies-loading {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-color);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-gray);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== DYNAMIC CATEGORIES STYLES ===== */

.categories-list .category-item {
    transition: all 0.3s ease;
}

.categories-list .category-item.active .subcategories-list {
    max-height: 500px;
    opacity: 1;
}

.categories-list .subcategories-list {
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transition: all 0.3s ease;
}

.categories-list .subcategories-list a.active {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

.category-header .fa-chevron-down {
    transition: transform 0.3s ease;
}

/* Style dla dynamicznego ładowania */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #666;
    text-align: center;
}

.loading-placeholder i {
    font-size: 2rem;
    margin-bottom: 15px;
    color: var(--primary-color);
    animation: spin 1s linear infinite;
}

.loading-placeholder p {
    margin: 0;
    font-size: 16px;
    color: #666;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #666;
    text-align: center;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #adb5bd;
}

.empty-state h4 {
    margin: 0 0 10px 0;
    font-size: 18px;
    color: #495057;
}

.empty-state p {
    margin: 0;
    font-size: 14px;
    color: #6c757d;
}

.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #dc3545;
    text-align: center;
    background: #f8d7da;
    border-radius: 12px;
    border: 2px solid #f5c6cb;
}

.error-state i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #dc3545;
}

.error-state h4 {
    margin: 0 0 10px 0;
    font-size: 18px;
    color: #721c24;
}

.error-state p {
    margin: 0 0 20px 0;
    font-size: 14px;
    color: #721c24;
}
