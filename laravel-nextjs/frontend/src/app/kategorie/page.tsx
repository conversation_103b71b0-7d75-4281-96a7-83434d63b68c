'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';

interface Category {
  id: number;
  name: string;
  slug: string;
  icon: string;
  color: string;
  companies_count: number;
}

export default function KategoriePage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data - replace with API call later
    const mockCategories: Category[] = [
      { id: 1, name: 'Gastronomia', slug: 'gastronomia', icon: 'fas fa-utensils', color: '#e74c3c', companies_count: 12 },
      { id: 2, name: 'Zdrowie i uroda', slug: 'zdrowie-uroda', icon: 'fas fa-heartbeat', color: '#27ae60', companies_count: 8 },
      { id: 3, name: 'Uroda', slug: 'uroda', icon: 'fas fa-cut', color: '#9b59b6', companies_count: 6 },
      { id: 4, name: 'Sport i rekreacja', slug: 'sport-rekreacja', icon: 'fas fa-dumbbell', color: '#3498db', companies_count: 4 },
      { id: 5, name: 'Motoryzacja', slug: 'motoryzacja', icon: 'fas fa-car', color: '#f39c12', companies_count: 7 },
      { id: 6, name: 'Usługi', slug: 'uslugi', icon: 'fas fa-tools', color: '#34495e', companies_count: 15 },
      { id: 7, name: 'Zakupy', slug: 'zakupy', icon: 'fas fa-shopping-bag', color: '#e67e22', companies_count: 9 },
      { id: 8, name: 'Edukacja', slug: 'edukacja', icon: 'fas fa-graduation-cap', color: '#2c3e50', companies_count: 3 },
    ];
    
    setCategories(mockCategories);
    setLoading(false);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <Link href="/" className="text-2xl font-bold text-blue-600">
              Żyrardów.poleca.to
            </Link>
            <nav className="hidden md:flex space-x-8">
              <Link href="/" className="text-gray-600 hover:text-blue-600">Strona główna</Link>
              <Link href="/kategorie" className="text-blue-600 font-medium">Kategorie</Link>
              <Link href="/firmy" className="text-gray-600 hover:text-blue-600">Firmy</Link>
              <Link href="/kupony" className="text-gray-600 hover:text-blue-600">Kupony</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Kategorie Firm w Żyrardowie
          </h1>
          <p className="text-xl text-blue-100">
            Znajdź firmy według kategorii - od gastronomii po usługi
          </p>
        </div>
      </section>

      {/* Categories Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/kategorie/${category.slug}`}
                className="group bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 p-6 text-center"
              >
                <div 
                  className="w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center text-3xl text-white group-hover:scale-110 transition-transform duration-300"
                  style={{ backgroundColor: category.color }}
                >
                  <i className={category.icon}></i>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                  {category.name}
                </h3>
                <p className="text-gray-600 mb-4">
                  {category.companies_count} {category.companies_count === 1 ? 'firma' : 'firm'}
                </p>
                <div className="inline-flex items-center text-blue-600 font-medium group-hover:text-blue-700">
                  Zobacz firmy
                  <i className="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Nie znalazłeś swojej kategorii?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Skontaktuj się z nami, a pomożemy Ci znaleźć odpowiednią firmę
          </p>
          <Link
            href="/kontakt"
            className="inline-flex items-center px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors"
          >
            <i className="fas fa-envelope mr-2"></i>
            Skontaktuj się z nami
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">Żyrardów.poleca.to</h3>
              <p className="text-gray-400">
                Portal lokalny Żyrardowa - odkryj najlepsze firmy w mieście
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Dla firm</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/dla-biznesu" className="hover:text-white">Dodaj firmę</Link></li>
                <li><Link href="/cennik" className="hover:text-white">Cennik</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">O Żyrardowie</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/o-miescie" className="hover:text-white">O mieście</Link></li>
                <li><Link href="/historia" className="hover:text-white">Historia</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Kontakt</h4>
              <ul className="space-y-2 text-gray-400">
                <li><i className="fas fa-envelope mr-2"></i> <EMAIL></li>
                <li><i className="fas fa-phone mr-2"></i> +48 123 456 789</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2025 Żyrardów.poleca.to - Wszystkie prawa zastrzeżone</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
