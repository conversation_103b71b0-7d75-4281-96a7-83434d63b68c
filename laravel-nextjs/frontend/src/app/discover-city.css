/* Style dla sek<PERSON>ji "<PERSON><PERSON><PERSON><PERSON><PERSON>" */

.discover-city {
    padding: 60px 0;
    background-color: var(--light-gray);
}

.discover-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.discover-item {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.discover-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.discover-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.discover-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.discover-item:hover .discover-image img {
    transform: scale(1.1);
}

.discover-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 60%, rgba(0, 0, 0, 0) 100%);
    color: var(--white);
    transition: transform 0.3s ease;
}

.discover-item:hover .discover-overlay {
    transform: translateY(-10px);
}

.discover-overlay h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: var(--white);
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.discover-overlay p {
    font-size: 0.9rem;
    margin-bottom: 15px;
    color: var(--white);
    opacity: 0.9;
}

.discover-overlay .btn {
    background-color: transparent;
    border: 2px solid var(--white);
    color: var(--white);
    padding: 5px 15px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.discover-overlay .btn:hover {
    background-color: var(--white);
    color: var(--primary-color);
}

/* Animacja */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.discover-item {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.discover-item:nth-child(1) { animation-delay: 0.1s; }
.discover-item:nth-child(2) { animation-delay: 0.2s; }
.discover-item:nth-child(3) { animation-delay: 0.3s; }
.discover-item:nth-child(4) { animation-delay: 0.4s; }
.discover-item:nth-child(5) { animation-delay: 0.5s; }
.discover-item:nth-child(6) { animation-delay: 0.6s; }

/* Responsywność */
@media (max-width: 1200px) {
    .discover-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .discover-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .discover-image {
        height: 250px;
    }
}

@media (max-width: 576px) {
    .discover-image {
        height: 200px;
    }
    
    .discover-overlay h3 {
        font-size: 1.1rem;
    }
    
    .discover-overlay p {
        font-size: 0.8rem;
        margin-bottom: 10px;
    }
}
