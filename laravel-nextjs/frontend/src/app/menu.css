/* Style dla menu z rozwijanymi listami */

/* Główne menu */
.main-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    position: relative;
}

.logo {
    flex-shrink: 0;
    margin-right: 30px;
}

.logo img {
    display: block;
    max-width: 100%;
    height: auto;
}

.nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    flex-grow: 1;
}

.nav-links > li {
    position: relative;
    margin-right: 20px;
}

.nav-links > li:last-child {
    margin-right: 0;
}

.nav-links > li > a {
    display: block;
    padding: 10px 15px;
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    position: relative;
}

.nav-links > li > a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background-color: var(--primary-color);
    transition: width 0.3s ease, left 0.3s ease;
}

.nav-links > li > a:hover::after,
.nav-links > li > a.active::after {
    width: 100%;
    left: 0;
}

.nav-links > li > a:hover,
.nav-links > li > a.active {
    color: var(--primary-color);
}

/* Rozwijane menu */
.has-submenu {
    position: relative;
}

.has-submenu > a {
    padding-right: 25px !important;
}

.has-submenu > a::before {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
}

.has-submenu:hover > a::before {
    transform: translateY(-50%) rotate(180deg);
}

.submenu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 250px;
    background-color: var(--white);
    box-shadow: var(--shadow-md);
    border-radius: var(--border-radius-sm);
    padding: 10px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
    z-index: 100;
}

.has-submenu:hover .submenu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.submenu li {
    list-style: none;
}

.submenu a {
    display: block;
    padding: 10px 20px;
    color: var(--text-medium);
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.95rem;
}

.submenu a:hover,
.submenu a.active {
    color: var(--primary-color);
    background-color: rgba(255, 102, 0, 0.05);
}

/* Akcje w menu */
.nav-actions {
    display: flex;
    align-items: center;
    margin-left: 30px;
}

.search-toggle,
.mobile-menu-btn {
    background: none;
    border: none;
    color: var(--text-dark);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 10px;
    transition: var(--transition);
}

.search-toggle:hover,
.mobile-menu-btn:hover {
    color: var(--primary-color);
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #3b5998;
    color: var(--white);
    border-radius: 50%;
    margin: 0 10px;
    transition: var(--transition);
}

.social-icon:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.install-app-button {
    display: flex;
    align-items: center;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-sm);
    padding: 8px 15px;
    margin-left: 10px;
    cursor: pointer;
    transition: var(--transition);
}

.install-app-button i {
    margin-right: 8px;
}

.install-app-button:hover {
    background-color: var(--primary-color-dark);
}

.mobile-menu-btn {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 20px;
}

.mobile-menu-btn span {
    display: block;
    width: 100%;
    height: 2px;
    background-color: var(--text-dark);
    transition: var(--transition);
}

/* Wyszukiwarka */
.search-container {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: var(--white);
    box-shadow: var(--shadow-md);
    padding: 20px;
    z-index: 99;
    display: none;
}

.search-container.active {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-form {
    display: flex;
    max-width: 800px;
    margin: 0 auto;
}

.search-form input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid var(--medium-gray);
    border-right: none;
    border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
    font-size: 1rem;
}

.search-form input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.search-form button {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 0 20px;
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
    cursor: pointer;
    transition: var(--transition);
}

.search-form button:hover {
    background-color: var(--primary-color-dark);
}

/* Responsywność */
@media (max-width: 1200px) {
    .nav-links > li {
        margin-right: 10px;
    }
    
    .nav-links > li > a {
        padding: 10px;
    }
    
    .install-app-button span {
        display: none;
    }
    
    .install-app-button i {
        margin-right: 0;
    }
}

@media (max-width: 992px) {
    .main-nav {
        padding: 10px 0;
    }
    
    .nav-links {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 300px;
        height: calc(100vh - 80px);
        background-color: var(--white);
        box-shadow: var(--shadow-lg);
        flex-direction: column;
        padding: 20px 0;
        overflow-y: auto;
        transition: left 0.3s ease;
        z-index: 100;
    }
    
    .nav-links.active {
        left: 0;
    }
    
    .nav-links > li {
        margin-right: 0;
        margin-bottom: 5px;
    }
    
    .nav-links > li > a {
        padding: 15px 20px;
    }
    
    .submenu {
        position: static;
        width: 100%;
        box-shadow: none;
        opacity: 1;
        visibility: visible;
        transform: none;
        display: none;
        padding: 0;
        background-color: var(--light-gray);
    }
    
    .has-submenu.active .submenu {
        display: block;
    }
    
    .has-submenu > a::before {
        content: '\f105';
    }
    
    .has-submenu.active > a::before {
        transform: translateY(-50%) rotate(90deg);
    }
    
    .submenu a {
        padding: 12px 30px;
    }
    
    .mobile-menu-btn {
        display: flex;
    }
    
    .nav-actions {
        margin-left: auto;
    }
    
    .social-icon,
    .install-app-button {
        display: none;
    }
}

@media (max-width: 576px) {
    .logo {
        margin-right: 15px;
    }
    
    .logo img {
        width: 150px;
    }
    
    .nav-links {
        width: 100%;
    }
}
