'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';

interface Coupon {
  id: number;
  title: string;
  description: string;
  code: string;
  discount: string;
  valid_to: string;
  company?: {
    name: string;
    slug: string;
  };
  category?: string;
}

export default function KuponyPage() {
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  useEffect(() => {
    // Mock data - replace with API call later
    const mockCoupons: Coupon[] = [
      { 
        id: 1, 
        title: '20% zniżki na obiad', 
        description: 'Rabat na wszystkie dania główne w godzinach 12:00-16:00. Nie łączy się z innymi promocjami.', 
        code: 'OBIAD20', 
        discount: '20%', 
        valid_to: '2025-12-31', 
        company: { name: 'Restauracja Pod Lipami', slug: 'restauracja-pod-lipami' },
        category: 'Gastronomia'
      },
      { 
        id: 2, 
        title: 'Darmowa konsultacja kosmetyczna', 
        description: 'Bezpłatna konsultacja z doświadczonym kosmetologiem. Analiza skóry i dobór odpowiedniej pielęgnacji.', 
        code: 'KONSULT', 
        discount: '100%', 
        valid_to: '2025-06-30', 
        company: { name: 'Salon Piękności Venus', slug: 'salon-pieknosci-venus' },
        category: 'Uroda'
      },
      { 
        id: 3, 
        title: '15% rabatu na naprawę', 
        description: 'Zniżka na wszystkie usługi serwisowe. Obejmuje robociznę i części zamienne.', 
        code: 'NAPRAWA15', 
        discount: '15%', 
        valid_to: '2025-08-31', 
        company: { name: 'Warsztat Auto-Serwis', slug: 'warsztat-auto-serwis' },
        category: 'Motoryzacja'
      },
      { 
        id: 4, 
        title: 'Druga pizza gratis', 
        description: 'Przy zakupie pizzy w cenie regularnej, druga pizza o tej samej lub niższej wartości gratis.', 
        code: 'PIZZA2', 
        discount: '50%', 
        valid_to: '2025-07-15', 
        company: { name: 'Pizzeria Bella Italia', slug: 'pizzeria-bella-italia' },
        category: 'Gastronomia'
      },
      { 
        id: 5, 
        title: '10% zniżki na sprzęt sportowy', 
        description: 'Rabat na cały asortyment sprzętu sportowego i odzieży. Minimum zakupów 200 zł.', 
        code: 'SPORT10', 
        discount: '10%', 
        valid_to: '2025-09-30', 
        company: { name: 'Sklep Sportowy Champion', slug: 'sklep-sportowy-champion' },
        category: 'Sport i rekreacja'
      },
      { 
        id: 6, 
        title: 'Darmowa dostawa leków', 
        description: 'Bezpłatna dostawa zamówionych leków w obrębie Żyrardowa. Minimum zamówienia 50 zł.', 
        code: 'DOSTAWA', 
        discount: '100%', 
        valid_to: '2025-12-31', 
        company: { name: 'Apteka Centralna', slug: 'apteka-centralna' },
        category: 'Zdrowie i uroda'
      }
    ];
    
    setCoupons(mockCoupons);
    setLoading(false);
  }, []);

  const filteredCoupons = coupons.filter(coupon => {
    const matchesSearch = coupon.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         coupon.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         coupon.company?.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === '' || coupon.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const categories = [...new Set(coupons.map(coupon => coupon.category))];

  const copyToClipboard = (code: string) => {
    navigator.clipboard.writeText(code);
    // You could add a toast notification here
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <Link href="/" className="text-2xl font-bold text-blue-600">
              Żyrardów.poleca.to
            </Link>
            <nav className="hidden md:flex space-x-8">
              <Link href="/" className="text-gray-600 hover:text-blue-600">Strona główna</Link>
              <Link href="/kategorie" className="text-gray-600 hover:text-blue-600">Kategorie</Link>
              <Link href="/firmy" className="text-gray-600 hover:text-blue-600">Firmy</Link>
              <Link href="/kupony" className="text-blue-600 font-medium">Kupony</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-green-600 to-green-800 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Kupony Rabatowe Żyrardów
          </h1>
          <p className="text-xl text-green-100">
            Oszczędzaj w lokalnych firmach - ekskluzywne rabaty tylko dla mieszkańców
          </p>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="py-8 bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                <input
                  type="text"
                  placeholder="Szukaj kuponów..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="md:w-64">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="">Wszystkie kategorie</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </section>

      {/* Coupons Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <p className="text-gray-600">
              Znaleziono {filteredCoupons.length} {filteredCoupons.length === 1 ? 'kupon' : 'kuponów'}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredCoupons.map((coupon) => (
              <div key={coupon.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {coupon.title}
                      </h3>
                      <p className="text-sm text-blue-600 mb-2">
                        {coupon.company?.name}
                      </p>
                    </div>
                    <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                      {coupon.discount}
                    </div>
                  </div>

                  <p className="text-gray-600 mb-4 text-sm">
                    {coupon.description}
                  </p>

                  <div className="bg-gray-50 rounded-lg p-4 mb-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-xs text-gray-500 mb-1">Kod kuponu:</p>
                        <p className="font-mono text-lg font-bold text-gray-900">{coupon.code}</p>
                      </div>
                      <button
                        onClick={() => copyToClipboard(coupon.code)}
                        className="px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <i className="fas fa-copy mr-1"></i>
                        Kopiuj
                      </button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="text-gray-500">
                      <i className="fas fa-calendar mr-1"></i>
                      Ważny do: {new Date(coupon.valid_to).toLocaleDateString('pl-PL')}
                    </div>
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                      {coupon.category}
                    </span>
                  </div>
                </div>

                <div className="bg-gray-50 px-6 py-3">
                  <Link
                    href={`/firmy/${coupon.company?.slug}`}
                    className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    Zobacz firmę <i className="fas fa-arrow-right ml-1"></i>
                  </Link>
                </div>
              </div>
            ))}
          </div>

          {filteredCoupons.length === 0 && (
            <div className="text-center py-12">
              <i className="fas fa-ticket-alt text-6xl text-gray-300 mb-4"></i>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Nie znaleziono kuponów
              </h3>
              <p className="text-gray-600">
                Spróbuj zmienić kryteria wyszukiwania lub wybierz inną kategorię
              </p>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-green-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Chcesz dodać swój kupon?
          </h2>
          <p className="text-xl text-green-100 mb-8">
            Skontaktuj się z nami i zacznij przyciągać nowych klientów
          </p>
          <Link
            href="/dla-biznesu"
            className="inline-flex items-center px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-green-600 transition-colors"
          >
            <i className="fas fa-plus mr-2"></i>
            Dodaj kupon
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p>&copy; 2025 Żyrardów.poleca.to - Wszystkie prawa zastrzeżone</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
