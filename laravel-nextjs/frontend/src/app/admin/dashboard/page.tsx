'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import AdminLayout from '@/components/admin/AdminLayout';

interface Stats {
  companies: number;
  coupons: number;
  categories: number;
  users: number;
}

interface Activity {
  id: number;
  type: string;
  message: string;
  time: string;
  icon: string;
  color: string;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<Stats>({ companies: 0, coupons: 0, categories: 0, users: 0 });
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Fetch dashboard data
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Try to fetch real data from API
      const [statsResponse] = await Promise.all([
        fetch('http://127.0.0.1:8000/api/admin/stats', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          },
        }),
      ]);

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Fallback to mock data
      setStats({
        companies: 124,
        coupons: 38,
        categories: 12,
        users: 1254,
      });
    }

    // Mock activities data
    setActivities([
      {
        id: 1,
        type: 'company',
        message: 'Nowa firma dodana: Restauracja Pod Akacjami',
        time: '2 godziny temu',
        icon: 'fas fa-building',
        color: 'rgba(255, 102, 0, 0.1)',
      },
      {
        id: 2,
        type: 'coupon',
        message: 'Nowy kupon: BELLA20',
        time: '5 godzin temu',
        icon: 'fas fa-ticket-alt',
        color: 'rgba(255, 193, 7, 0.1)',
      },
      {
        id: 3,
        type: 'category',
        message: 'Nowa kategoria: Elektronika',
        time: '1 dzień temu',
        icon: 'fas fa-list',
        color: 'rgba(40, 167, 69, 0.1)',
      },
      {
        id: 4,
        type: 'delete',
        message: 'Usunięta firma: Sklep ABC',
        time: '2 dni temu',
        icon: 'fas fa-trash-alt',
        color: 'rgba(220, 53, 69, 0.1)',
      },
    ]);

    setLoading(false);
  };



  if (loading) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="p-8">
          <header className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600">Witaj w panelu administracyjnym Żyrardów Poleca</p>
          </header>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-blue-100">
                  <i className="fas fa-building text-blue-600 text-xl"></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Firmy</h3>
                  <p className="text-3xl font-bold text-blue-600">{stats.companies}</p>
                  <p className="text-sm text-green-600">
                    <i className="fas fa-arrow-up mr-1"></i>
                    12% w tym miesiącu
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-yellow-100">
                  <i className="fas fa-ticket-alt text-yellow-600 text-xl"></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Kupony</h3>
                  <p className="text-3xl font-bold text-yellow-600">{stats.coupons}</p>
                  <p className="text-sm text-green-600">
                    <i className="fas fa-arrow-up mr-1"></i>
                    15% w tym miesiącu
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-green-100">
                  <i className="fas fa-list text-green-600 text-xl"></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Kategorie</h3>
                  <p className="text-3xl font-bold text-green-600">{stats.categories}</p>
                  <p className="text-sm text-green-600">
                    <i className="fas fa-arrow-up mr-1"></i>
                    5% w tym miesiącu
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-purple-100">
                  <i className="fas fa-users text-purple-600 text-xl"></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Użytkownicy</h3>
                  <p className="text-3xl font-bold text-purple-600">{stats.users}</p>
                  <p className="text-sm text-green-600">
                    <i className="fas fa-arrow-up mr-1"></i>
                    8% w tym miesiącu
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Dashboard Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Activity */}
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">Ostatnia aktywność</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {activities.map((activity) => (
                    <div key={activity.id} className="flex items-center">
                      <div className="p-2 rounded-full" style={{ backgroundColor: activity.color }}>
                        <i className={`${activity.icon} text-gray-600`}></i>
                      </div>
                      <div className="ml-4">
                        <p className="text-sm text-gray-900">{activity.message}</p>
                        <p className="text-xs text-gray-500">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">Szybkie akcje</h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-2 gap-4">
                  <Link href="/admin/companies/add" className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <i className="fas fa-plus-circle text-2xl text-blue-600 mb-2"></i>
                    <span className="text-sm font-medium text-gray-900">Dodaj firmę</span>
                  </Link>
                  <Link href="/admin/coupons/add" className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <i className="fas fa-ticket-alt text-2xl text-yellow-600 mb-2"></i>
                    <span className="text-sm font-medium text-gray-900">Dodaj kupon</span>
                  </Link>
                  <Link href="/admin/categories/add" className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <i className="fas fa-list text-2xl text-green-600 mb-2"></i>
                    <span className="text-sm font-medium text-gray-900">Dodaj kategorię</span>
                  </Link>
                  <Link href="/admin/settings" className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <i className="fas fa-cog text-2xl text-purple-600 mb-2"></i>
                    <span className="text-sm font-medium text-gray-900">Ustawienia</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
      </div>
    </AdminLayout>
  );
}
