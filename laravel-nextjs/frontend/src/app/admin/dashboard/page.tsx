'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import AdminLayout from '@/components/admin/AdminLayout';

interface Stats {
  companies: {
    total: number;
    active: number;
    pending: number;
    top: number;
  };
  coupons: {
    total: number;
    active: number;
    expired: number;
  };
  categories: {
    total: number;
    active: number;
  };
  users: {
    total: number;
    admins: number;
  };
}

interface Activity {
  id: number;
  type: string;
  message: string;
  time: string;
  icon: string;
  color: string;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<Stats>({
    companies: { total: 0, active: 0, pending: 0, top: 0 },
    coupons: { total: 0, active: 0, expired: 0 },
    categories: { total: 0, active: 0 },
    users: { total: 0, admins: 0 }
  });
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Fetch dashboard data
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Try to fetch real data from API
      const [statsResponse] = await Promise.all([
        fetch('http://127.0.0.1:8000/api/admin/stats', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          },
        }),
      ]);

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Fallback to mock data
      setStats({
        companies: { total: 124, active: 118, pending: 6, top: 8 },
        coupons: { total: 38, active: 32, expired: 6 },
        categories: { total: 12, active: 12 },
        users: { total: 1254, admins: 3 },
      });
    }

    // Mock activities data
    setActivities([
      {
        id: 1,
        type: 'company',
        message: 'Nowa firma dodana: Restauracja Pod Akacjami',
        time: '2 godziny temu',
        icon: 'fas fa-building',
        color: 'rgba(255, 102, 0, 0.1)',
      },
      {
        id: 2,
        type: 'coupon',
        message: 'Nowy kupon: BELLA20',
        time: '5 godzin temu',
        icon: 'fas fa-ticket-alt',
        color: 'rgba(255, 193, 7, 0.1)',
      },
      {
        id: 3,
        type: 'category',
        message: 'Nowa kategoria: Elektronika',
        time: '1 dzień temu',
        icon: 'fas fa-list',
        color: 'rgba(40, 167, 69, 0.1)',
      },
      {
        id: 4,
        type: 'delete',
        message: 'Usunięta firma: Sklep ABC',
        time: '2 dni temu',
        icon: 'fas fa-trash-alt',
        color: 'rgba(220, 53, 69, 0.1)',
      },
    ]);

    setLoading(false);
  };



  if (loading) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="p-8 bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
          <header className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Dashboard
                </h1>
                <p className="text-gray-600 mt-2">Witaj w panelu administracyjnym Żyrardów Poleca</p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="bg-white/70 backdrop-blur-sm rounded-xl px-4 py-2 border border-white/20 shadow-lg">
                  <span className="text-sm text-gray-600">Ostatnia aktualizacja:</span>
                  <span className="text-sm font-semibold text-gray-900 ml-2">
                    {new Date().toLocaleTimeString('pl-PL')}
                  </span>
                </div>
              </div>
            </div>
          </header>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl p-6 border border-white/20 hover:shadow-2xl transition-all duration-300 hover:scale-105">
              <div className="flex items-center">
                <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg">
                  <i className="fas fa-building text-white text-xl"></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Firmy</h3>
                  <p className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent">
                    {stats.companies.total}
                  </p>
                  <p className="text-sm text-green-600 flex items-center">
                    <i className="fas fa-check-circle mr-1"></i>
                    {stats.companies.active} aktywnych
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl p-6 border border-white/20 hover:shadow-2xl transition-all duration-300 hover:scale-105">
              <div className="flex items-center">
                <div className="p-4 rounded-2xl bg-gradient-to-br from-yellow-500 to-orange-500 shadow-lg">
                  <i className="fas fa-ticket-alt text-white text-xl"></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Kupony</h3>
                  <p className="text-3xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">
                    {stats.coupons.total}
                  </p>
                  <p className="text-sm text-green-600 flex items-center">
                    <i className="fas fa-clock mr-1"></i>
                    {stats.coupons.active} aktywnych
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl p-6 border border-white/20 hover:shadow-2xl transition-all duration-300 hover:scale-105">
              <div className="flex items-center">
                <div className="p-4 rounded-2xl bg-gradient-to-br from-green-500 to-emerald-500 shadow-lg">
                  <i className="fas fa-list text-white text-xl"></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Kategorie</h3>
                  <p className="text-3xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                    {stats.categories.total}
                  </p>
                  <p className="text-sm text-green-600 flex items-center">
                    <i className="fas fa-list mr-1"></i>
                    {stats.categories.active} aktywnych
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl p-6 border border-white/20 hover:shadow-2xl transition-all duration-300 hover:scale-105">
              <div className="flex items-center">
                <div className="p-4 rounded-2xl bg-gradient-to-br from-purple-500 to-indigo-500 shadow-lg">
                  <i className="fas fa-users text-white text-xl"></i>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Użytkownicy</h3>
                  <p className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">
                    {stats.users.total}
                  </p>
                  <p className="text-sm text-green-600 flex items-center">
                    <i className="fas fa-user-shield mr-1"></i>
                    {stats.users.admins} adminów
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Dashboard Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Activity */}
            <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
              <div className="p-6 border-b border-gray-200/50">
                <h2 className="text-xl font-semibold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                  Ostatnia aktywność
                </h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {activities.map((activity, index) => (
                    <div
                      key={activity.id}
                      className="flex items-center p-3 rounded-xl hover:bg-white/50 transition-all duration-200"
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className="p-3 rounded-xl shadow-lg" style={{ backgroundColor: activity.color }}>
                        <i className={`${activity.icon} text-gray-700`}></i>
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                        <p className="text-xs text-gray-500">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
              <div className="p-6 border-b border-gray-200/50">
                <h2 className="text-xl font-semibold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                  Szybkie akcje
                </h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-2 gap-4">
                  <Link
                    href="/admin/companies/add"
                    className="group flex flex-col items-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200/50 rounded-2xl hover:shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                      <i className="fas fa-plus-circle text-xl text-white"></i>
                    </div>
                    <span className="text-sm font-semibold text-gray-900 mt-3">Dodaj firmę</span>
                  </Link>
                  <Link
                    href="/admin/coupons/add"
                    className="group flex flex-col items-center p-6 bg-gradient-to-br from-yellow-50 to-orange-100 border border-yellow-200/50 rounded-2xl hover:shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    <div className="p-3 rounded-xl bg-gradient-to-br from-yellow-500 to-orange-500 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                      <i className="fas fa-ticket-alt text-xl text-white"></i>
                    </div>
                    <span className="text-sm font-semibold text-gray-900 mt-3">Dodaj kupon</span>
                  </Link>
                  <Link
                    href="/admin/categories/add"
                    className="group flex flex-col items-center p-6 bg-gradient-to-br from-green-50 to-emerald-100 border border-green-200/50 rounded-2xl hover:shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    <div className="p-3 rounded-xl bg-gradient-to-br from-green-500 to-emerald-500 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                      <i className="fas fa-list text-xl text-white"></i>
                    </div>
                    <span className="text-sm font-semibold text-gray-900 mt-3">Dodaj kategorię</span>
                  </Link>
                  <Link
                    href="/admin/settings"
                    className="group flex flex-col items-center p-6 bg-gradient-to-br from-purple-50 to-indigo-100 border border-purple-200/50 rounded-2xl hover:shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-indigo-500 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                      <i className="fas fa-cog text-xl text-white"></i>
                    </div>
                    <span className="text-sm font-semibold text-gray-900 mt-3">Ustawienia</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
      </div>
    </AdminLayout>
  );
}
