'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';

interface Company {
  id: number;
  name: string;
  slug: string;
  description: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  logo: string;
  category_id: number;
  category_name: string;
  is_top: boolean;
  top_position: number | null;
  status: 'active' | 'pending' | 'inactive';
  created_at: string;
  updated_at: string;
}

interface Category {
  id: number;
  name: string;
}

export default function AdminCompaniesPage() {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [selectedCompanies, setSelectedCompanies] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in
    const token = localStorage.getItem('admin_token');
    if (!token) {
      router.push('/admin/login');
      return;
    }

    fetchData();
  }, [router, currentPage, searchTerm, categoryFilter, statusFilter, sortBy]);

  const fetchData = async () => {
    try {
      const [companiesResponse, categoriesResponse] = await Promise.all([
        fetch(`http://localhost:8000/api/v1/admin/companies?page=${currentPage}&search=${searchTerm}&category=${categoryFilter}&status=${statusFilter}&sort=${sortBy}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          },
        }),
        fetch('http://localhost:8000/api/v1/categories', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          },
        }),
      ]);

      if (companiesResponse.ok) {
        const companiesData = await companiesResponse.json();
        setCompanies(companiesData.data || []);
        setTotalPages(companiesData.last_page || 1);
      }

      if (categoriesResponse.ok) {
        const categoriesData = await categoriesResponse.json();
        setCategories(categoriesData.data || []);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      // Fallback to mock data
      setCompanies([
        {
          id: 1,
          name: 'Restauracja Pod Akacjami',
          slug: 'restauracja-pod-akacjami',
          description: 'Najlepsza restauracja w Żyrardowie',
          address: 'ul. Przykładowa 1, Żyrardów',
          phone: '+**************',
          email: '<EMAIL>',
          website: 'https://podakacjami.pl',
          logo: '/images/business-logo1.jpg',
          category_id: 1,
          category_name: 'Restauracje i kawiarnie',
          is_top: true,
          top_position: 1,
          status: 'active',
          created_at: '2023-05-12T10:00:00Z',
          updated_at: '2023-05-12T10:00:00Z',
        },
        {
          id: 2,
          name: 'Salon Fryzjerski Bella',
          slug: 'salon-fryzjerski-bella',
          description: 'Profesjonalne usługi fryzjerskie',
          address: 'ul. Przykładowa 2, Żyrardów',
          phone: '+**************',
          email: '<EMAIL>',
          website: 'https://bella.pl',
          logo: '/images/business-logo2.jpg',
          category_id: 4,
          category_name: 'Zdrowie i uroda',
          is_top: false,
          top_position: null,
          status: 'active',
          created_at: '2023-05-10T10:00:00Z',
          updated_at: '2023-05-10T10:00:00Z',
        },
        {
          id: 3,
          name: 'Sklep Sportowy Active',
          slug: 'sklep-sportowy-active',
          description: 'Sprzęt sportowy i odzież',
          address: 'ul. Przykładowa 3, Żyrardów',
          phone: '+**************',
          email: '<EMAIL>',
          website: 'https://active.pl',
          logo: '/images/business-logo3.jpg',
          category_id: 2,
          category_name: 'Sklepy',
          is_top: false,
          top_position: null,
          status: 'pending',
          created_at: '2023-05-08T10:00:00Z',
          updated_at: '2023-05-08T10:00:00Z',
        },
      ]);
      setCategories([
        { id: 1, name: 'Restauracje i kawiarnie' },
        { id: 2, name: 'Sklepy' },
        { id: 3, name: 'Usługi' },
        { id: 4, name: 'Zdrowie i uroda' },
        { id: 5, name: 'Rozrywka i rekreacja' },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('admin_token');
    router.push('/admin/login');
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCompanies(companies.map(c => c.id));
    } else {
      setSelectedCompanies([]);
    }
  };

  const handleSelectCompany = (companyId: number, checked: boolean) => {
    if (checked) {
      setSelectedCompanies([...selectedCompanies, companyId]);
    } else {
      setSelectedCompanies(selectedCompanies.filter(id => id !== companyId));
    }
  };

  const handleDeleteCompany = async (companyId: number) => {
    if (confirm('Czy na pewno chcesz usunąć tę firmę?')) {
      try {
        const response = await fetch(`http://localhost:8000/api/v1/admin/companies/${companyId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          },
        });

        if (response.ok) {
          setCompanies(companies.filter(c => c.id !== companyId));
          alert('Firma została usunięta');
        } else {
          alert('Błąd podczas usuwania firmy');
        }
      } catch (error) {
        console.error('Error deleting company:', error);
        // Fallback - remove from local state
        setCompanies(companies.filter(c => c.id !== companyId));
        alert('Firma została usunięta (demo)');
      }
    }
  };

  const handleToggleStatus = async (companyId: number) => {
    try {
      const company = companies.find(c => c.id === companyId);
      if (!company) return;

      const newStatus = company.status === 'active' ? 'inactive' : 'active';
      
      const response = await fetch(`http://localhost:8000/api/v1/admin/companies/${companyId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        setCompanies(companies.map(c => 
          c.id === companyId ? { ...c, status: newStatus } : c
        ));
        alert(`Status firmy został zmieniony na ${newStatus === 'active' ? 'aktywny' : 'nieaktywny'}`);
      } else {
        alert('Błąd podczas zmiany statusu');
      }
    } catch (error) {
      console.error('Error toggling status:', error);
      // Fallback - update local state
      const company = companies.find(c => c.id === companyId);
      if (company) {
        const newStatus = company.status === 'active' ? 'inactive' : 'active';
        setCompanies(companies.map(c => 
          c.id === companyId ? { ...c, status: newStatus } : c
        ));
        alert(`Status firmy został zmieniony (demo)`);
      }
    }
  };

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      active: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      inactive: 'bg-red-100 text-red-800',
    };

    const statusLabels = {
      active: 'Aktywna',
      pending: 'Oczekująca',
      inactive: 'Nieaktywna',
    };

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusClasses[status as keyof typeof statusClasses]}`}>
        {statusLabels[status as keyof typeof statusLabels]}
      </span>
    );
  };

  const getTopBadge = (company: Company) => {
    if (!company.is_top || !company.top_position) return null;

    const badgeClasses = {
      1: 'bg-gradient-to-r from-yellow-400 to-orange-500',
      2: 'bg-gradient-to-r from-gray-300 to-gray-500',
      3: 'bg-gradient-to-r from-yellow-600 to-yellow-800',
    };

    return (
      <span className={`ml-2 px-2 py-1 text-xs font-bold text-white rounded-full ${badgeClasses[company.top_position as keyof typeof badgeClasses] || 'bg-blue-500'}`}>
        TOP {company.top_position}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white shadow-lg">
          <div className="p-6">
            <Image
              src="/images/logo-zyrardow-poleca.png"
              alt="Żyrardów.poleca.to"
              width={150}
              height={45}
              className="mb-6"
            />
            <div className="mb-6">
              <h3 className="font-semibold text-gray-900">Administrator</h3>
              <p className="text-sm text-gray-600"><EMAIL></p>
            </div>
          </div>

          <nav className="px-6">
            <ul className="space-y-2">
              <li>
                <Link href="/admin/dashboard" className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                  <i className="fas fa-tachometer-alt mr-3"></i>
                  Dashboard
                </Link>
              </li>
              <li>
                <Link href="/admin/companies" className="flex items-center px-4 py-2 text-blue-600 bg-blue-50 rounded-lg">
                  <i className="fas fa-building mr-3"></i>
                  Firmy
                </Link>
              </li>
              <li>
                <Link href="/admin/coupons" className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                  <i className="fas fa-ticket-alt mr-3"></i>
                  Kupony
                </Link>
              </li>
              <li>
                <Link href="/admin/categories" className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                  <i className="fas fa-list mr-3"></i>
                  Kategorie
                </Link>
              </li>
              <li>
                <Link href="/admin/users" className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                  <i className="fas fa-users mr-3"></i>
                  Użytkownicy
                </Link>
              </li>
              <li>
                <Link href="/admin/settings" className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                  <i className="fas fa-cog mr-3"></i>
                  Ustawienia
                </Link>
              </li>
            </ul>
          </nav>

          <div className="absolute bottom-6 left-6 right-6">
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-4 py-2 text-red-600 hover:bg-red-50 rounded-lg"
            >
              <i className="fas fa-sign-out-alt mr-3"></i>
              Wyloguj
            </button>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-8">
          <header className="mb-8">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Zarządzanie firmami</h1>
                <p className="text-gray-600">Dodawaj, edytuj i usuwaj firmy w katalogu</p>
              </div>
              <div className="flex items-center gap-4">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Szukaj firmy..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <i className="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                </div>
                <Link
                  href="/admin/companies/add"
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <i className="fas fa-plus mr-2"></i>
                  Dodaj firmę
                </Link>
              </div>
            </div>
          </header>

          {/* Filters */}
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Kategoria</label>
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Wszystkie kategorie</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Wszystkie statusy</option>
                  <option value="active">Aktywne</option>
                  <option value="pending">Oczekujące</option>
                  <option value="inactive">Nieaktywne</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Sortuj według</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="name">Nazwa (A-Z)</option>
                  <option value="name-desc">Nazwa (Z-A)</option>
                  <option value="date-newest">Data dodania (najnowsze)</option>
                  <option value="date-oldest">Data dodania (najstarsze)</option>
                </select>
              </div>
              <div className="flex items-end">
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setCategoryFilter('');
                    setStatusFilter('');
                    setSortBy('name');
                  }}
                  className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <i className="fas fa-redo mr-2"></i>
                  Resetuj filtry
                </button>
              </div>
            </div>
          </div>

          {/* Companies Table */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <input
                        type="checkbox"
                        checked={selectedCompanies.length === companies.length && companies.length > 0}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Logo</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nazwa firmy</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kategoria</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Adres</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data dodania</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Akcje</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {companies.map((company) => (
                    <tr key={company.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedCompanies.includes(company.id)}
                          onChange={(e) => handleSelectCompany(company.id, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Image
                          src={company.logo || '/images/default-company-logo.svg'}
                          alt={`Logo ${company.name}`}
                          width={40}
                          height={40}
                          className="rounded object-cover"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-gray-900">{company.name}</span>
                          {getTopBadge(company)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {company.category_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {company.address}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(company.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(company.created_at).toLocaleDateString('pl-PL')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <Link
                            href={`/admin/companies/edit/${company.id}`}
                            className="text-blue-600 hover:text-blue-900"
                            title="Edytuj"
                          >
                            <i className="fas fa-edit"></i>
                          </Link>
                          <Link
                            href={`/firmy/${company.slug}`}
                            target="_blank"
                            className="text-green-600 hover:text-green-900"
                            title="Podgląd"
                          >
                            <i className="fas fa-eye"></i>
                          </Link>
                          <button
                            onClick={() => handleToggleStatus(company.id)}
                            className={`${company.status === 'active' ? 'text-gray-600 hover:text-gray-900' : 'text-green-600 hover:text-green-900'}`}
                            title="Zmień status"
                          >
                            <i className={`fas ${company.status === 'active' ? 'fa-toggle-on' : 'fa-toggle-off'}`}></i>
                          </button>
                          <button
                            onClick={() => handleDeleteCompany(company.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Usuń"
                          >
                            <i className="fas fa-trash-alt"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                  >
                    Poprzednia
                  </button>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                  >
                    Następna
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Strona <span className="font-medium">{currentPage}</span> z{' '}
                      <span className="font-medium">{totalPages}</span>
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                      <button
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                      >
                        <i className="fas fa-chevron-left"></i>
                      </button>
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const page = i + 1;
                        return (
                          <button
                            key={page}
                            onClick={() => setCurrentPage(page)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              currentPage === page
                                ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                            }`}
                          >
                            {page}
                          </button>
                        );
                      })}
                      <button
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                      >
                        <i className="fas fa-chevron-right"></i>
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}
