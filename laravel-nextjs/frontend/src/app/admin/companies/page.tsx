'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import AdminLayout from '@/components/admin/AdminLayout';

interface Company {
  id: number;
  name: string;
  slug: string;
  description: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  logo: string;
  category_id: number;
  category_name: string;
  is_top: boolean;
  top_position: number | null;
  status: 'active' | 'pending' | 'inactive';
  created_at: string;
  updated_at: string;
}

interface Category {
  id: number;
  name: string;
}

export default function AdminCompaniesPage() {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [selectedCompanies, setSelectedCompanies] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const router = useRouter();

  useEffect(() => {
    fetchData();
  }, [currentPage, searchTerm, categoryFilter, statusFilter, sortBy]);

  const fetchData = async () => {
    try {
      const [companiesResponse, categoriesResponse] = await Promise.all([
        fetch(`http://127.0.0.1:8000/api/admin/companies?page=${currentPage}&search=${searchTerm}&category=${categoryFilter}&status=${statusFilter}&sort=${sortBy}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          },
        }),
        fetch('http://127.0.0.1:8000/api/admin/categories', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          },
        }),
      ]);

      if (companiesResponse.ok) {
        const companiesData = await companiesResponse.json();
        setCompanies(companiesData.data || []);
        setTotalPages(companiesData.last_page || 1);
      }

      if (categoriesResponse.ok) {
        const categoriesData = await categoriesResponse.json();
        setCategories(categoriesData.data || []);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      // Fallback to mock data
      setCompanies([
        {
          id: 1,
          name: 'Restauracja Pod Akacjami',
          slug: 'restauracja-pod-akacjami',
          description: 'Najlepsza restauracja w Żyrardowie',
          address: 'ul. Przykładowa 1, Żyrardów',
          phone: '+**************',
          email: '<EMAIL>',
          website: 'https://podakacjami.pl',
          logo: '/images/business-logo1.jpg',
          category_id: 1,
          category_name: 'Restauracje i kawiarnie',
          is_top: true,
          top_position: 1,
          status: 'active',
          created_at: '2023-05-12T10:00:00Z',
          updated_at: '2023-05-12T10:00:00Z',
        },
        {
          id: 2,
          name: 'Salon Fryzjerski Bella',
          slug: 'salon-fryzjerski-bella',
          description: 'Profesjonalne usługi fryzjerskie',
          address: 'ul. Przykładowa 2, Żyrardów',
          phone: '+**************',
          email: '<EMAIL>',
          website: 'https://bella.pl',
          logo: '/images/business-logo2.jpg',
          category_id: 4,
          category_name: 'Zdrowie i uroda',
          is_top: false,
          top_position: null,
          status: 'active',
          created_at: '2023-05-10T10:00:00Z',
          updated_at: '2023-05-10T10:00:00Z',
        },
        {
          id: 3,
          name: 'Sklep Sportowy Active',
          slug: 'sklep-sportowy-active',
          description: 'Sprzęt sportowy i odzież',
          address: 'ul. Przykładowa 3, Żyrardów',
          phone: '+**************',
          email: '<EMAIL>',
          website: 'https://active.pl',
          logo: '/images/business-logo3.jpg',
          category_id: 2,
          category_name: 'Sklepy',
          is_top: false,
          top_position: null,
          status: 'pending',
          created_at: '2023-05-08T10:00:00Z',
          updated_at: '2023-05-08T10:00:00Z',
        },
      ]);
      setCategories([
        { id: 1, name: 'Restauracje i kawiarnie' },
        { id: 2, name: 'Sklepy' },
        { id: 3, name: 'Usługi' },
        { id: 4, name: 'Zdrowie i uroda' },
        { id: 5, name: 'Rozrywka i rekreacja' },
      ]);
    } finally {
      setLoading(false);
    }
  };



  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCompanies(companies.map(c => c.id));
    } else {
      setSelectedCompanies([]);
    }
  };

  const handleSelectCompany = (companyId: number, checked: boolean) => {
    if (checked) {
      setSelectedCompanies([...selectedCompanies, companyId]);
    } else {
      setSelectedCompanies(selectedCompanies.filter(id => id !== companyId));
    }
  };

  const handleDeleteCompany = async (companyId: number) => {
    if (confirm('Czy na pewno chcesz usunąć tę firmę?')) {
      try {
        const response = await fetch(`http://127.0.0.1:8000/api/admin/companies/${companyId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          },
        });

        if (response.ok) {
          setCompanies(companies.filter(c => c.id !== companyId));
          alert('Firma została usunięta');
        } else {
          alert('Błąd podczas usuwania firmy');
        }
      } catch (error) {
        console.error('Error deleting company:', error);
        // Fallback - remove from local state
        setCompanies(companies.filter(c => c.id !== companyId));
        alert('Firma została usunięta (demo)');
      }
    }
  };

  const handleToggleStatus = async (companyId: number) => {
    try {
      const company = companies.find(c => c.id === companyId);
      if (!company) return;

      const newStatus = company.status === 'active' ? 'inactive' : 'active';

      const response = await fetch(`http://127.0.0.1:8000/api/admin/companies/${companyId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        setCompanies(companies.map(c =>
          c.id === companyId ? { ...c, status: newStatus } : c
        ));
        alert(`Status firmy został zmieniony na ${newStatus === 'active' ? 'aktywny' : 'nieaktywny'}`);
      } else {
        alert('Błąd podczas zmiany statusu');
      }
    } catch (error) {
      console.error('Error toggling status:', error);
      // Fallback - update local state
      const company = companies.find(c => c.id === companyId);
      if (company) {
        const newStatus = company.status === 'active' ? 'inactive' : 'active';
        setCompanies(companies.map(c =>
          c.id === companyId ? { ...c, status: newStatus } : c
        ));
        alert(`Status firmy został zmieniony (demo)`);
      }
    }
  };

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      active: 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200',
      pending: 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border border-yellow-200',
      inactive: 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border border-red-200',
    };

    const statusLabels = {
      active: 'Aktywna',
      pending: 'Oczekująca',
      inactive: 'Nieaktywna',
    };

    const statusIcons = {
      active: 'fas fa-check-circle',
      pending: 'fas fa-clock',
      inactive: 'fas fa-times-circle',
    };

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-xs font-semibold rounded-xl shadow-sm ${statusClasses[status as keyof typeof statusClasses]}`}>
        <i className={`${statusIcons[status as keyof typeof statusIcons]} mr-1.5`}></i>
        {statusLabels[status as keyof typeof statusLabels]}
      </span>
    );
  };

  const getTopBadge = (company: Company) => {
    if (!company.is_top || !company.top_position) return null;

    const badgeClasses = {
      1: 'bg-gradient-to-r from-yellow-400 to-orange-500 shadow-lg',
      2: 'bg-gradient-to-r from-gray-400 to-gray-600 shadow-lg',
      3: 'bg-gradient-to-r from-amber-600 to-yellow-700 shadow-lg',
    };

    const badgeIcons = {
      1: 'fas fa-crown',
      2: 'fas fa-medal',
      3: 'fas fa-award',
    };

    return (
      <span className={`ml-3 inline-flex items-center px-3 py-1.5 text-xs font-bold text-white rounded-xl ${badgeClasses[company.top_position as keyof typeof badgeClasses] || 'bg-gradient-to-r from-blue-500 to-purple-500 shadow-lg'}`}>
        <i className={`${badgeIcons[company.top_position as keyof typeof badgeIcons] || 'fas fa-star'} mr-1.5`}></i>
        TOP {company.top_position}
      </span>
    );
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="p-8 bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
          <header className="mb-8">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Zarządzanie firmami
                </h1>
                <p className="text-gray-600 mt-2">Dodawaj, edytuj i usuwaj firmy w katalogu</p>
              </div>
              <div className="flex items-center gap-4">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Szukaj firmy..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-12 pr-4 py-3 bg-white/70 backdrop-blur-sm border border-white/20 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-lg transition-all duration-300"
                  />
                  <i className="fas fa-search absolute left-4 top-4 text-gray-400"></i>
                </div>
                <Link
                  href="/admin/companies/add"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl hover:shadow-lg transition-all duration-300 hover:scale-105 font-semibold"
                >
                  <i className="fas fa-plus mr-2"></i>
                  Dodaj firmę
                </Link>
              </div>
            </div>
          </header>

          {/* Filters */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl p-6 mb-6 border border-white/20">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">Kategoria</label>
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                >
                  <option value="">Wszystkie kategorie</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">Status</label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                >
                  <option value="">Wszystkie statusy</option>
                  <option value="active">Aktywne</option>
                  <option value="pending">Oczekujące</option>
                  <option value="inactive">Nieaktywne</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">Sortuj według</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                >
                  <option value="name">Nazwa (A-Z)</option>
                  <option value="name-desc">Nazwa (Z-A)</option>
                  <option value="date-newest">Data dodania (najnowsze)</option>
                  <option value="date-oldest">Data dodania (najstarsze)</option>
                </select>
              </div>
              <div className="flex items-end">
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setCategoryFilter('');
                    setStatusFilter('');
                    setSortBy('name');
                  }}
                  className="w-full px-4 py-3 bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-xl hover:from-gray-200 hover:to-gray-300 transition-all duration-300 hover:scale-105 font-semibold"
                >
                  <i className="fas fa-redo mr-2"></i>
                  Resetuj filtry
                </button>
              </div>
            </div>
          </div>

          {/* Companies Table */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden border border-white/20">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200/50">
                <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">
                      <input
                        type="checkbox"
                        checked={selectedCompanies.length === companies.length && companies.length > 0}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        className="rounded-lg border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                      />
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Logo</th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Nazwa firmy</th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Kategoria</th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Adres</th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Data dodania</th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Akcje</th>
                  </tr>
                </thead>
                <tbody className="bg-white/50 divide-y divide-gray-200/50">
                  {companies.map((company, index) => (
                    <tr
                      key={company.id}
                      className="hover:bg-white/80 transition-all duration-200 hover:shadow-lg"
                      style={{ animationDelay: `${index * 50}ms` }}
                    >
                      <td className="px-6 py-5 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedCompanies.includes(company.id)}
                          onChange={(e) => handleSelectCompany(company.id, e.target.checked)}
                          className="rounded-lg border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                        />
                      </td>
                      <td className="px-6 py-5 whitespace-nowrap">
                        <div className="w-12 h-12 rounded-xl overflow-hidden shadow-lg">
                          <Image
                            src={company.logo || '/images/default-company-logo.svg'}
                            alt={`Logo ${company.name}`}
                            width={48}
                            height={48}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </td>
                      <td className="px-6 py-5 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="text-sm font-semibold text-gray-900">{company.name}</span>
                          {getTopBadge(company)}
                        </div>
                      </td>
                      <td className="px-6 py-5 whitespace-nowrap text-sm font-medium text-gray-600">
                        {company.category_name}
                      </td>
                      <td className="px-6 py-5 whitespace-nowrap text-sm text-gray-600">
                        {company.address}
                      </td>
                      <td className="px-6 py-5 whitespace-nowrap">
                        {getStatusBadge(company.status)}
                      </td>
                      <td className="px-6 py-5 whitespace-nowrap text-sm text-gray-600">
                        {new Date(company.created_at).toLocaleDateString('pl-PL')}
                      </td>
                      <td className="px-6 py-5 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-3">
                          <Link
                            href={`/admin/companies/edit/${company.id}`}
                            className="p-2 rounded-lg bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 transition-all duration-200 hover:scale-110"
                            title="Edytuj"
                          >
                            <i className="fas fa-edit text-sm"></i>
                          </Link>
                          <Link
                            href={`/firmy/${company.slug}`}
                            target="_blank"
                            className="p-2 rounded-lg bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-700 transition-all duration-200 hover:scale-110"
                            title="Podgląd"
                          >
                            <i className="fas fa-eye text-sm"></i>
                          </Link>
                          <button
                            onClick={() => handleToggleStatus(company.id)}
                            className={`p-2 rounded-lg transition-all duration-200 hover:scale-110 ${
                              company.status === 'active'
                                ? 'bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-gray-700'
                                : 'bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-700'
                            }`}
                            title="Zmień status"
                          >
                            <i className={`fas text-sm ${company.status === 'active' ? 'fa-toggle-on' : 'fa-toggle-off'}`}></i>
                          </button>
                          <button
                            onClick={() => handleDeleteCompany(company.id)}
                            className="p-2 rounded-lg bg-red-100 text-red-600 hover:bg-red-200 hover:text-red-700 transition-all duration-200 hover:scale-110"
                            title="Usuń"
                          >
                            <i className="fas fa-trash-alt text-sm"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                  >
                    Poprzednia
                  </button>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                  >
                    Następna
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Strona <span className="font-medium">{currentPage}</span> z{' '}
                      <span className="font-medium">{totalPages}</span>
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                      <button
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                      >
                        <i className="fas fa-chevron-left"></i>
                      </button>
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const page = i + 1;
                        return (
                          <button
                            key={page}
                            onClick={() => setCurrentPage(page)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              currentPage === page
                                ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                            }`}
                          >
                            {page}
                          </button>
                        );
                      })}
                      <button
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                      >
                        <i className="fas fa-chevron-right"></i>
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </div>
      </div>
    </AdminLayout>
  );
}
