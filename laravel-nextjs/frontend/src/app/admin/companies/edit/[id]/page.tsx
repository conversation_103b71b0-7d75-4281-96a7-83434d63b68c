'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import AdminLayout from '@/components/admin/AdminLayout'
import Image from 'next/image'

interface Company {
  id: number;
  name: string;
  slug: string;
  category_id: number;
  short_description: string;
  description: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  logo: string;
  images: string[];
  is_top: boolean;
  top_position: number | null;
  status: string;
  opening_hours: any;
  social_media: any;
  seo_title: string;
  seo_description: string;
  seo_keywords: string;
}

interface Category {
  id: number;
  name: string;
}

export default function EditCompany() {
  const router = useRouter()
  const params = useParams()
  const companyId = params.id

  const [company, setCompany] = useState<Company | null>(null)
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')

  const [formData, setFormData] = useState({
    name: '',
    category_id: '',
    short_description: '',
    description: '',
    address: '',
    phone: '',
    email: '',
    website: '',
    is_top: false,
    top_position: '',
    status: 'active',
    seo_title: '',
    seo_description: '',
    seo_keywords: ''
  })

  useEffect(() => {
    fetchCompany()
    fetchCategories()
  }, [companyId])

  const fetchCompany = async () => {
    try {
      const response = await fetch(`http://127.0.0.1:8000/api/companies/${companyId}`)
      if (response.ok) {
        const data = await response.json()
        setCompany(data)
        setFormData({
          name: data.name || '',
          category_id: data.category_id?.toString() || '',
          short_description: data.short_description || '',
          description: data.description || '',
          address: data.address || '',
          phone: data.phone || '',
          email: data.email || '',
          website: data.website || '',
          is_top: data.is_top || false,
          top_position: data.top_position?.toString() || '',
          status: data.status || 'active',
          seo_title: data.seo_title || '',
          seo_description: data.seo_description || '',
          seo_keywords: data.seo_keywords || ''
        })
      }
    } catch (error) {
      console.error('Error fetching company:', error)
      setError('Błąd podczas ładowania danych firmy')
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await fetch('http://127.0.0.1:8000/api/categories')
      if (response.ok) {
        const data = await response.json()
        setCategories(data)
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)
    setError('')

    try {
      const token = localStorage.getItem('admin_token')
      const response = await fetch(`http://127.0.0.1:8000/api/admin/companies/${companyId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        router.push('/admin/companies')
      } else {
        const errorData = await response.json()
        setError(errorData.message || 'Błąd podczas zapisywania')
      }
    } catch (error) {
      console.error('Error updating company:', error)
      setError('Błąd podczas zapisywania')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="p-8 bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <i className="fas fa-spinner fa-spin text-4xl text-blue-600 mb-4"></i>
              <p className="text-gray-600">Ładowanie danych firmy...</p>
            </div>
          </div>
        </div>
      </AdminLayout>
    )
  }

  if (!company) {
    return (
      <AdminLayout>
        <div className="p-8 bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
          <div className="text-center">
            <i className="fas fa-exclamation-triangle text-4xl text-red-600 mb-4"></i>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Firma nie została znaleziona</h1>
            <p className="text-gray-600 mb-4">Firma o podanym ID nie istnieje</p>
            <button
              onClick={() => router.push('/admin/companies')}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700"
            >
              Powrót do listy firm
            </button>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="p-8 bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
                Edytuj firmę
              </h1>
              <p className="text-gray-600">Edytuj dane firmy: {company.name}</p>
            </div>
            <button
              onClick={() => router.push('/admin/companies')}
              className="bg-white/70 backdrop-blur-sm border border-white/20 rounded-xl px-6 py-3 text-gray-700 hover:bg-white/90 transition-all duration-300 hover:scale-105 shadow-lg font-semibold"
            >
              <i className="fas fa-arrow-left mr-2"></i>
              Powrót
            </button>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit}>
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl p-8 mb-8 border border-white/20">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl mb-6">
                {error}
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Nazwa firmy *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-4 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 shadow-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Kategoria *
                </label>
                <select
                  name="category_id"
                  value={formData.category_id}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-4 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 shadow-sm"
                >
                  <option value="">Wybierz kategorię</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Krótki opis
                </label>
                <textarea
                  name="short_description"
                  value={formData.short_description}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-4 py-4 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 shadow-sm resize-none"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Szczegółowy opis
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={6}
                  className="w-full px-4 py-4 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 shadow-sm resize-none"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Adres
                </label>
                <input
                  type="text"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  className="w-full px-4 py-4 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 shadow-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Telefon
                </label>
                <input
                  type="text"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full px-4 py-4 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 shadow-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Email
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-4 py-4 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 shadow-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Strona internetowa
                </label>
                <input
                  type="url"
                  name="website"
                  value={formData.website}
                  onChange={handleInputChange}
                  className="w-full px-4 py-4 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 shadow-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Status
                </label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full px-4 py-4 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 shadow-sm"
                >
                  <option value="active">Aktywna</option>
                  <option value="pending">Oczekująca</option>
                  <option value="inactive">Nieaktywna</option>
                </select>
              </div>

              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_top"
                    checked={formData.is_top}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3"
                  />
                  <span className="text-sm font-semibold text-gray-700">Firma TOP</span>
                </label>
              </div>

              {formData.is_top && (
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Pozycja TOP
                  </label>
                  <select
                    name="top_position"
                    value={formData.top_position}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 shadow-sm"
                  >
                    <option value="">Wybierz pozycję</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                  </select>
                </div>
              )}
            </div>

            {/* Navigation */}
            <div className="flex justify-between mt-8">
              <button
                type="button"
                onClick={() => router.push('/admin/companies')}
                className="px-8 py-4 bg-white/70 backdrop-blur-sm border border-white/20 rounded-xl text-gray-700 hover:bg-white/90 transition-all duration-300 hover:scale-105 shadow-lg font-semibold"
              >
                <i className="fas fa-times mr-2"></i>
                Anuluj
              </button>

              <button
                type="submit"
                disabled={saving}
                className="px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:shadow-lg disabled:opacity-50 transition-all duration-300 hover:scale-105 font-semibold"
              >
                {saving ? (
                  <>
                    <i className="fas fa-spinner fa-spin mr-2"></i>
                    Zapisywanie...
                  </>
                ) : (
                  <>
                    Zapisz zmiany
                    <i className="fas fa-save ml-2"></i>
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
      </div>
    </AdminLayout>
  )
}
