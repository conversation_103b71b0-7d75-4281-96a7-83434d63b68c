/* Style dla ekranu powitalnego */

.welcome-screen {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 40px;
    text-align: center;
}

.welcome-icon {
    width: 80px;
    height: 80px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.welcome-icon i {
    font-size: 32px;
    color: var(--primary-color);
}

.welcome-screen h3 {
    font-size: 1.8rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.welcome-screen p {
    font-size: 1.1rem;
    color: var(--text-medium);
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.welcome-categories {
    margin-top: 40px;
}

.welcome-category-row {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
}

.welcome-category-row:last-child {
    margin-bottom: 0;
}

.welcome-category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    width: 150px;
    transition: transform 0.3s;
}

.welcome-category-item:hover {
    transform: translateY(-10px);
}

.welcome-category-item i {
    width: 60px;
    height: 60px;
    background-color: rgba(255, 102, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 15px;
    transition: background-color 0.3s, color 0.3s;
}

.welcome-category-item:hover i {
    background-color: var(--primary-color);
    color: var(--white);
}

.welcome-category-item span {
    font-size: 1rem;
    color: var(--text-dark);
    font-weight: 500;
    text-align: center;
    display: block;
}

.welcome-category-item small {
    font-size: 0.8rem;
    color: var(--text-medium);
    font-weight: 400;
    margin-top: 3px;
    display: block;
    text-align: center;
}

@media (max-width: 992px) {
    .welcome-category-row {
        flex-wrap: wrap;
        gap: 20px;
    }

    .welcome-category-item {
        width: 120px;
    }
}

@media (max-width: 576px) {
    .welcome-screen {
        padding: 30px 20px;
    }

    .welcome-screen h3 {
        font-size: 1.5rem;
    }

    .welcome-screen p {
        font-size: 1rem;
    }

    .welcome-category-row {
        flex-direction: column;
        align-items: center;
        gap: 30px;
    }

    .welcome-category-item {
        width: 100%;
        max-width: 200px;
    }
}
