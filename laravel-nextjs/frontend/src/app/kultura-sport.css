/* Style dla strony <PERSON> i Sport */

/* Hero section */
.page-hero {
    height: 500px;
    background-size: cover;
    background-position: center;
    position: relative;
    margin-top: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.5) 0%, rgba(0,0,0,0.7) 100%);
}

.page-hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.page-hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: var(--white);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.page-hero-content .lead {
    font-size: 1.5rem;
    margin-bottom: 0;
    color: var(--white);
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Breadcrumbs */
.breadcrumbs {
    background-color: var(--light-gray);
    padding: 15px 0;
    border-bottom: 1px solid var(--medium-gray);
}

.breadcrumbs-list {
    display: flex;
    list-style: none;
    flex-wrap: wrap;
}

.breadcrumbs-list li {
    display: flex;
    align-items: center;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.breadcrumbs-list li:not(:last-child)::after {
    content: '/';
    margin: 0 10px;
    color: var(--text-light);
}

.breadcrumbs-list a {
    color: var(--text-medium);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumbs-list a:hover {
    color: var(--primary-color);
}

/* Tabs Navigation */
.tabs-navigation {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    margin-top: -30px;
    position: relative;
    z-index: 10;
}

.tabs-nav {
    display: flex;
    list-style: none;
    border-bottom: 1px solid var(--medium-gray);
}

.tabs-nav-item {
    flex: 1;
    text-align: center;
}

.tabs-nav-link {
    display: block;
    padding: 20px;
    color: var(--text-medium);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    position: relative;
}

.tabs-nav-link::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 0;
    height: 3px;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.tabs-nav-link:hover,
.tabs-nav-link.active {
    color: var(--primary-color);
}

.tabs-nav-link:hover::after,
.tabs-nav-link.active::after {
    width: 100%;
}

.tabs-nav-link i {
    margin-right: 10px;
}

/* Culture Section */
.culture-section {
    padding: 80px 0;
}

.culture-intro {
    max-width: 900px;
    margin: 0 auto 60px;
    text-align: center;
}

.culture-intro .lead {
    font-size: 1.2rem;
    color: var(--text-dark);
    font-weight: 500;
    margin-bottom: 1.5rem;
}

/* Culture Institutions */
.institutions-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 60px;
}

.institution-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.institution-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.institution-image {
    height: 200px;
    overflow: hidden;
}

.institution-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.institution-card:hover .institution-image img {
    transform: scale(1.1);
}

.institution-content {
    padding: 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.institution-content h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.institution-info {
    margin-bottom: 15px;
}

.institution-info-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 10px;
    color: var(--text-medium);
    font-size: 0.95rem;
}

.institution-info-item:last-child {
    margin-bottom: 0;
}

.institution-info-item i {
    color: var(--primary-color);
    margin-top: 4px;
}

.institution-content p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.6;
    flex: 1;
}

/* Cultural Events */
.cultural-events {
    padding: 80px 0;
    background-color: var(--light-gray);
}

.events-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.event-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.event-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.event-image {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.event-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.event-card:hover .event-image img {
    transform: scale(1.1);
}

.event-date {
    position: absolute;
    top: 15px;
    left: 15px;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 10px 15px;
    border-radius: var(--border-radius-sm);
    text-align: center;
    line-height: 1.2;
}

.event-date-day {
    font-size: 1.5rem;
    font-weight: 700;
    display: block;
}

.event-date-month {
    font-size: 0.9rem;
    text-transform: uppercase;
}

.event-content {
    padding: 25px;
}

.event-content h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.event-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
}

.event-info-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.event-info-item i {
    color: var(--primary-color);
}

.event-content p {
    color: var(--text-medium);
    margin-bottom: 20px;
    line-height: 1.6;
}

/* Sports Section */
.sports-section {
    padding: 80px 0;
}

.sports-intro {
    max-width: 900px;
    margin: 0 auto 60px;
    text-align: center;
}

.sports-intro .lead {
    font-size: 1.2rem;
    color: var(--text-dark);
    font-weight: 500;
    margin-bottom: 1.5rem;
}

/* Sports Facilities */
.facilities-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-bottom: 60px;
}

.facility-card {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 20px;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.facility-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.facility-image {
    height: 100%;
    overflow: hidden;
}

.facility-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.facility-card:hover .facility-image img {
    transform: scale(1.1);
}

.facility-content {
    padding: 20px;
}

.facility-content h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.facility-info {
    margin-bottom: 15px;
}

.facility-info-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 8px;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.facility-info-item:last-child {
    margin-bottom: 0;
}

.facility-info-item i {
    color: var(--primary-color);
    margin-top: 4px;
}

/* Responsive */
@media (max-width: 1200px) {
    .institutions-grid,
    .events-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .facilities-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 992px) {
    .tabs-nav {
        flex-wrap: wrap;
    }
    
    .tabs-nav-item {
        flex: 0 0 50%;
    }
}

@media (max-width: 768px) {
    .page-hero {
        height: 400px;
    }
    
    .page-hero-content h1 {
        font-size: 2.5rem;
    }
    
    .page-hero-content .lead {
        font-size: 1.2rem;
    }
    
    .institutions-grid,
    .events-grid {
        grid-template-columns: 1fr;
    }
    
    .facility-card {
        grid-template-columns: 1fr;
    }
    
    .facility-image {
        height: 200px;
    }
}

@media (max-width: 576px) {
    .tabs-nav-item {
        flex: 0 0 100%;
    }
}
