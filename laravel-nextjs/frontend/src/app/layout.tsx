import type { Metadata } from "next";
import "./globals.css";
import "./menu.css";
import "./city-info.css";
import "./seo-content.css";
import "./map.css";
import "./install-app.css";
import "./welcome-screen.css";
import "./offers-slider.css";
import "./coupons-slider.css";
import "./panorama.css";
import "./discover-city.css";
import "./events-stats.css";
import "./lightbox.css";
import "./weather-widget.css";
import "./o-miescie.css";
import "./atrakcje.css";
import "./historia.css";
import "./zabytki.css";
import "./kultura-sport.css";
import "./attraction-details.css";
import "./news-article.css";
import "./wiadomosci.css";
import "./dla-biznesu.css";
import "./error-pages.css";
import "./style.css";

export const metadata: Metadata = {
  title: "Żyrardów.poleca.to - Portal lokalny Żyrardowa",
  description: "Odkryj najlepsze firmy, usługi i atrakcje w Żyrardowie. Portal lokalny z katalogiem firm, kuponami rabatowymi i informacjami o mieście Filip de Girard.",
  other: {
    'font-awesome': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pl">
      <head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
      </head>
      <body className="antialiased">
        {children}
      </body>
    </html>
  );
}
