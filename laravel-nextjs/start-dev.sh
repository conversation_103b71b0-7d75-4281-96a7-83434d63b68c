#!/bin/bash

# 🚀 Skrypt uruchamiający wszystkie serwisy - Laravel + Next.js
# Żyrardów Poleca

# Kolory
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Sprawdź czy jesteśmy w odpowiednim katalogu
if [[ ! -d "backend" || ! -d "frontend" ]]; then
    echo "❌ Uruchom skrypt z katalogu laravel-nextjs/"
    exit 1
fi

print_success "🚀 Uruchamianie Żyrardów Poleca - Laravel + Next.js"
echo ""

# Funkcja do zabijania procesów przy wyjściu
cleanup() {
    print_warning "🛑 Zatrzymywanie wszystkich serwisów..."
    kill $(jobs -p) 2>/dev/null
    exit
}

# Przechwytuj Ctrl+C
trap cleanup SIGINT

# Sprawdź czy porty są wolne
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        print_warning "Port $1 jest zajęty. Zatrzymaj proces używający tego portu."
        return 1
    fi
    return 0
}

print_status "Sprawdzanie dostępności portów..."
check_port 8000 || exit 1
check_port 3000 || exit 1
check_port 3001 || exit 1

print_success "Wszystkie porty są dostępne!"
echo ""

# Uruchom backend Laravel
print_status "🔧 Uruchamianie backendu Laravel (port 8000)..."
cd backend
php artisan serve --host=0.0.0.0 --port=8000 &
LARAVEL_PID=$!
cd ..

# Poczekaj chwilę na uruchomienie Laravel
sleep 3

# Sprawdź czy Laravel działa
if curl -s http://localhost:8000/api/v1/stats >/dev/null 2>&1; then
    print_success "✅ Backend Laravel uruchomiony!"
else
    print_warning "⚠️  Backend Laravel może nie być gotowy (to normalne przy pierwszym uruchomieniu)"
fi

# Uruchom frontend Next.js
print_status "🎨 Uruchamianie frontendu Next.js (port 3000)..."
cd frontend
pnpm dev &
FRONTEND_PID=$!
cd ..

# Uruchom admin panel
print_status "⚙️  Uruchamianie panelu admin (port 3001)..."
cd admin
pnpm dev --port 3001 &
ADMIN_PID=$!
cd ..

echo ""
print_success "🎉 Wszystkie serwisy zostały uruchomione!"
echo ""
echo "🌐 Dostępne URLs:"
echo "   📊 Backend API:  http://localhost:8000/api/v1"
echo "   🏠 Frontend:     http://localhost:3000"
echo "   ⚙️  Admin Panel:  http://localhost:3001"
echo ""
echo "📋 Przydatne endpointy API:"
echo "   📈 Statystyki:   http://localhost:8000/api/v1/stats"
echo "   🏢 Firmy:        http://localhost:8000/api/v1/companies"
echo "   🏆 TOP Firmy:    http://localhost:8000/api/v1/companies/top"
echo "   📂 Kategorie:    http://localhost:8000/api/v1/categories"
echo ""
print_warning "💡 Naciśnij Ctrl+C aby zatrzymać wszystkie serwisy"
echo ""

# Czekaj na sygnał zakończenia
wait
