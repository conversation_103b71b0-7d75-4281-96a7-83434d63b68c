<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\CompanyController;
use App\Http\Controllers\Api\CouponController;
use App\Http\Controllers\Api\StatsController;
use App\Http\Controllers\Admin\AdminCompanyController;
use App\Http\Controllers\Admin\AdminCouponController;
use App\Http\Controllers\Admin\AdminCategoryController;
use App\Http\Controllers\Admin\AdminUserController;
use App\Http\Controllers\Admin\AdminSettingsController;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Api\CategoryController as ApiCategoryController;
use App\Http\Controllers\Api\CompanyController as ApiCompanyController;
use App\Http\Controllers\Api\CouponController as ApiCouponController;
use App\Http\Controllers\Api\StatsController as ApiStatsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public API routes (without prefix for frontend)
// Stats & Health
Route::get('/stats', [ApiStatsController::class, 'index']);
Route::get('/health', [ApiStatsController::class, 'health']);

// Categories
Route::get('/categories', [ApiCategoryController::class, 'index']);
Route::get('/categories/{category}', [ApiCategoryController::class, 'show']);
Route::get('/categories/{category}/companies', [ApiCategoryController::class, 'companies']);

// Companies
Route::get('/companies', [ApiCompanyController::class, 'index']);
Route::get('/companies/top', [ApiCompanyController::class, 'top']);
Route::get('/companies/search', [ApiCompanyController::class, 'search']);
Route::get('/companies/{company}', [ApiCompanyController::class, 'show']);
Route::post('/companies/submit', [ApiCompanyController::class, 'submit']); // For public submissions

// Coupons
Route::get('/coupons', [ApiCouponController::class, 'index']);
Route::get('/coupons/active', [ApiCouponController::class, 'active']);
Route::get('/coupons/{coupon}', [ApiCouponController::class, 'show']);
Route::post('/coupons/validate', [ApiCouponController::class, 'validateCoupon']);
Route::post('/coupons/{coupon}/use', [ApiCouponController::class, 'use']);

// Public API routes (v1) - for backward compatibility
Route::prefix('v1')->group(function () {
    // Stats & Health
    Route::get('/stats', [ApiStatsController::class, 'index']);
    Route::get('/health', [ApiStatsController::class, 'health']);

    // Categories
    Route::get('/categories', [ApiCategoryController::class, 'index']);
    Route::get('/categories/{category}', [ApiCategoryController::class, 'show']);
    Route::get('/categories/{category}/companies', [ApiCategoryController::class, 'companies']);

    // Companies
    Route::get('/companies', [ApiCompanyController::class, 'index']);
    Route::get('/companies/top', [ApiCompanyController::class, 'top']);
    Route::get('/companies/search', [ApiCompanyController::class, 'search']);
    Route::get('/companies/{company}', [ApiCompanyController::class, 'show']);

    // Coupons
    Route::get('/coupons', [ApiCouponController::class, 'index']);
    Route::get('/coupons/active', [ApiCouponController::class, 'active']);
    Route::get('/coupons/{coupon}', [ApiCouponController::class, 'show']);
    Route::post('/coupons/validate', [ApiCouponController::class, 'validateCoupon']);
    Route::post('/coupons/{coupon}/use', [ApiCouponController::class, 'use']);
});

// Admin Authentication routes
Route::prefix('admin')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');
    Route::get('/user', [AuthController::class, 'user'])->middleware('auth:sanctum');
});

// Admin API routes (requires authentication)
Route::prefix('admin')->middleware('auth:sanctum')->group(function () {
    // Dashboard stats
    Route::get('/stats', [DashboardController::class, 'stats']);

    // Companies management
    Route::apiResource('companies', AdminCompanyController::class);
    Route::patch('/companies/{company}/status', [AdminCompanyController::class, 'updateStatus']);
    Route::patch('/companies/{company}/top-position', [AdminCompanyController::class, 'updateTopPosition']);
    Route::post('/companies/{company}/upload-logo', [AdminCompanyController::class, 'uploadLogo']);
    Route::post('/companies/{company}/upload-images', [AdminCompanyController::class, 'uploadImages']);

    // Categories management
    Route::apiResource('categories', AdminCategoryController::class);
    Route::post('/categories/reorder', [AdminCategoryController::class, 'reorder']);

    // Coupons management
    Route::apiResource('coupons', AdminCouponController::class);
    Route::patch('/coupons/{coupon}/status', [AdminCouponController::class, 'updateStatus']);

    // Users management
    Route::apiResource('users', AdminUserController::class);
    Route::patch('/users/{user}/status', [AdminUserController::class, 'updateStatus']);

    // Settings management
    Route::get('/settings', [AdminSettingsController::class, 'index']);
    Route::put('/settings', [AdminSettingsController::class, 'update']);
});

// Auth routes
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
