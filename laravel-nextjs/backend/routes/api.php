<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\CompanyController;
use App\Http\Controllers\Api\CouponController;
use App\Http\Controllers\Api\StatsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public API routes (v1)
Route::prefix('v1')->group(function () {
    // Stats & Health
    Route::get('/stats', [StatsController::class, 'index']);
    Route::get('/health', [StatsController::class, 'health']);

    // Categories
    Route::get('/categories', [CategoryController::class, 'index']);
    Route::get('/categories/{category}', [CategoryController::class, 'show']);
    Route::get('/categories/{category}/companies', [CategoryController::class, 'companies']);

    // Companies
    Route::get('/companies', [CompanyController::class, 'index']);
    Route::get('/companies/top', [CompanyController::class, 'top']);
    Route::get('/companies/search', [CompanyController::class, 'search']);
    Route::get('/companies/{company}', [CompanyController::class, 'show']);

    // Coupons
    Route::get('/coupons', [CouponController::class, 'index']);
    Route::get('/coupons/active', [CouponController::class, 'active']);
    Route::get('/coupons/{coupon}', [CouponController::class, 'show']);
    Route::post('/coupons/validate', [CouponController::class, 'validate']);
    Route::post('/coupons/{coupon}/use', [CouponController::class, 'use']);
});

// Admin API routes (requires authentication)
Route::prefix('admin')->middleware('auth:sanctum')->group(function () {
    // Dashboard stats
    Route::get('/stats', [StatsController::class, 'dashboard']);

    // Categories management
    Route::apiResource('categories', CategoryController::class);

    // Companies management
    Route::apiResource('companies', CompanyController::class);

    // Coupons management
    Route::apiResource('coupons', CouponController::class);
});

// Auth routes
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
