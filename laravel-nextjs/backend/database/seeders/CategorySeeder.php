<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Dane z oryginalnej strony Żyrardów.poleca.to
        $categories = [
            [
                'name' => 'Gastronomia',
                'description' => 'Restauracje, kawiarnie, bary, catering',
                'icon' => 'fas fa-utensils',
                'color' => '#e74c3c',
                'sort_order' => 1
            ],
            [
                'name' => 'Zdrowie i uroda',
                'description' => 'Przychodnie, gabinety, salony kosmetyczne',
                'icon' => 'fas fa-heartbeat',
                'color' => '#27ae60',
                'sort_order' => 2
            ],
            [
                'name' => 'Uroda',
                'description' => 'Salony fryzjerskie, kosmetyczne, SPA',
                'icon' => 'fas fa-cut',
                'color' => '#9b59b6',
                'sort_order' => 3
            ],
            [
                'name' => 'Sport i rekreacja',
                'description' => 'Siłownie, kluby fitness, sklepy sportowe',
                'icon' => 'fas fa-dumbbell',
                'color' => '#3498db',
                'sort_order' => 4
            ],
            [
                'name' => 'Motoryzacja',
                'description' => 'Warsztaty, komisy, stacje paliw',
                'icon' => 'fas fa-car',
                'color' => '#f39c12',
                'sort_order' => 5
            ],
            [
                'name' => 'Usługi',
                'description' => 'Różne usługi dla mieszkańców',
                'icon' => 'fas fa-tools',
                'color' => '#34495e',
                'sort_order' => 6
            ],
            [
                'name' => 'Zakupy',
                'description' => 'Sklepy, centra handlowe, butiki',
                'icon' => 'fas fa-shopping-bag',
                'color' => '#e67e22',
                'sort_order' => 7
            ],
            [
                'name' => 'Edukacja',
                'description' => 'Szkoły, kursy, korepetycje',
                'icon' => 'fas fa-graduation-cap',
                'color' => '#2c3e50',
                'sort_order' => 8
            ],
            [
                'name' => 'Motoryzacja',
                'description' => 'Warsztaty, stacje paliw, sklepy motoryzacyjne',
                'icon' => '🚗',
                'color' => '#84CC16',
                'sort_order' => 7
            ],
            [
                'name' => 'Dom i Ogród',
                'description' => 'Sklepy budowlane, meble, artykuły ogrodnicze',
                'icon' => '🏠',
                'color' => '#F97316',
                'sort_order' => 8
            ]
        ];

        foreach ($categories as $categoryData) {
            Category::create($categoryData);
        }

        // Dodaj podkategorie dla Gastronomii
        $gastronomiaId = Category::where('name', 'Gastronomia')->first()->id;

        $subcategories = [
            [
                'name' => 'Pizzerie',
                'parent_id' => $gastronomiaId,
                'icon' => '🍕',
                'color' => '#e74c3c',
                'sort_order' => 1
            ],
            [
                'name' => 'Kawiarnie',
                'parent_id' => $gastronomiaId,
                'icon' => '☕',
                'color' => '#e74c3c',
                'sort_order' => 2
            ],
            [
                'name' => 'Fast Food',
                'parent_id' => $gastronomiaId,
                'icon' => '🍔',
                'color' => '#e74c3c',
                'sort_order' => 3
            ]
        ];

        foreach ($subcategories as $subcategoryData) {
            Category::create($subcategoryData);
        }
    }
}
