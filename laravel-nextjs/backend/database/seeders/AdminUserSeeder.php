<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Sprawdź czy admin już istnieje
        $adminExists = User::where('email', '<EMAIL>')->first();

        if (!$adminExists) {
            User::create([
                'name' => 'Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123456'),
                'is_admin' => true,
                'email_verified_at' => now(),
            ]);

            $this->command->info('Administrator został utworzony: <EMAIL> / admin123456');
        } else {
            $this->command->info('Administrator już istnieje.');
        }
    }
}
