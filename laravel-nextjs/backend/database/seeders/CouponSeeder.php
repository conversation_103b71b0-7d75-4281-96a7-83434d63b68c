<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Coupon;
use App\Models\Company;

class CouponSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $bellaVista = Company::where('name', 'Pizzeria Bella Vista')->first();
        $aromat = Company::where('name', 'Kawiarnia Aromat')->first();
        $metamorfoza = Company::where('name', 'Salon Fryzjerski "Metamorfoza"')->first();

        $coupons = [
            [
                'company_id' => $bellaVista->id,
                'title' => '20% zniżki na wszystkie pizze',
                'description' => 'Skorzystaj z 20% zniżki na wszystkie pizze w naszej restauracji. Oferta ważna przy zamówieniu na miejscu lub na wynos.',
                'code' => 'PIZZA20',
                'discount_type' => 'percentage',
                'discount_value' => 20.00,
                'min_order_value' => 30.00,
                'max_uses' => 100,
                'used_count' => 15,
                'valid_from' => now(),
                'valid_to' => now()->addDays(30),
                'terms_conditions' => 'Oferta nie łączy się z innymi promocjami. Ważna tylko przy okazaniu kuponu.'
            ],
            [
                'company_id' => $aromat->id,
                'title' => 'Darmowa kawa przy zakupie ciasta',
                'description' => 'Zamów dowolne ciasto i otrzymaj kawę gratis! Idealna okazja na słodką przerwę.',
                'code' => 'KAWA2024',
                'discount_type' => 'free_shipping',
                'discount_value' => 0.00,
                'max_uses' => 50,
                'used_count' => 8,
                'valid_from' => now(),
                'valid_to' => now()->addDays(14),
                'terms_conditions' => 'Oferta dotyczy kawy espresso, americano lub cappuccino. Nie dotyczy kaw specjalnych.'
            ],
            [
                'company_id' => $metamorfoza->id,
                'title' => '50 zł zniżki na usługi koloryzacji',
                'description' => 'Profesjonalna koloryzacja włosów z rabatem 50 zł. Skorzystaj z usług naszych doświadczonych stylistów.',
                'code' => 'KOLOR50',
                'discount_type' => 'fixed',
                'discount_value' => 50.00,
                'min_order_value' => 150.00,
                'max_uses' => 25,
                'used_count' => 3,
                'valid_from' => now(),
                'valid_to' => now()->addDays(45),
                'terms_conditions' => 'Oferta dotyczy pełnej koloryzacji włosów. Wymagana rezerwacja terminu.'
            ],
            [
                'company_id' => $bellaVista->id,
                'title' => 'Darmowa dostawa przy zamówieniu powyżej 40 zł',
                'description' => 'Zamów jedzenie na wynos za minimum 40 zł i otrzymaj darmową dostawę do domu.',
                'code' => 'DOSTAWA0',
                'discount_type' => 'free_shipping',
                'discount_value' => 0.00,
                'min_order_value' => 40.00,
                'max_uses' => null, // Bez limitu
                'used_count' => 42,
                'valid_from' => now(),
                'valid_to' => now()->addDays(60),
                'terms_conditions' => 'Dostawa w promieniu 5 km od restauracji. Czas dostawy 30-45 minut.'
            ],
            [
                'company_id' => $aromat->id,
                'title' => '15% zniżki na wszystkie napoje',
                'description' => 'Rabat na wszystkie napoje w naszej kawiarni - kawy, herbaty, soki i napoje gazowane.',
                'code' => 'NAPOJE15',
                'discount_type' => 'percentage',
                'discount_value' => 15.00,
                'min_order_value' => 20.00,
                'max_uses' => 75,
                'used_count' => 22,
                'valid_from' => now(),
                'valid_to' => now()->addDays(21),
                'terms_conditions' => 'Oferta ważna od poniedziałku do piątku w godzinach 14:00-17:00.'
            ]
        ];

        foreach ($coupons as $couponData) {
            Coupon::create($couponData);
        }
    }
}
