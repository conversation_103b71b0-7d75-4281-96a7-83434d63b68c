{"name": "laravel/prompts", "type": "library", "description": "Add beautiful and user-friendly forms to your command-line applications.", "license": "MIT", "autoload": {"psr-4": {"Laravel\\Prompts\\": "src/"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "require": {"php": "^8.1", "ext-mbstring": "*", "illuminate/collections": "^10.0|^11.0", "symfony/console": "^6.2|^7.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "pestphp/pest": "^2.3", "mockery/mockery": "^1.5", "phpstan/phpstan-mockery": "^1.1"}, "conflict": {"illuminate/console": ">=10.17.0 <10.25.0", "laravel/framework": ">=10.17.0 <10.25.0"}, "suggest": {"ext-pcntl": "Required for the spinner to be animated."}, "config": {"allow-plugins": {"pestphp/pest-plugin": true}}, "extra": {"branch-alias": {"dev-main": "0.1.x-dev"}}, "prefer-stable": true, "minimum-stability": "dev"}