name: run-tests

on:
  - push
  - pull_request

jobs:
  test:
    runs-on: ${{ matrix.os }}

    strategy:
      fail-fast: true
      matrix:
        os: [ubuntu-latest]
        php: [8.3, 8.2, 8.1, 8.0]
        laravel: ['8.*', '9.*', '10.*', '11.*', '12.*']
        stability: [prefer-stable]
        include:
          - laravel: 11.*
            testbench: 9.*
          - laravel: 10.*
            testbench: 8.*
          - laravel: 9.*
            testbench: 7.*
          - laravel: 8.*
            testbench: 6.23
          - laravel: 12.*
            testbench: 10.*
        exclude:
          - laravel: 11.*
            php: 8.1
          - laravel: 11.*
            php: 8.0
          - laravel: 10.*
            php: 8.0
          - laravel: 12.*
            php: 8.1
          - laravel: 12.*
            php: 8.0

    name: P${{ matrix.php }} - L${{ matrix.laravel }} - ${{ matrix.stability }} - ${{ matrix.os }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv, imagick
          coverage: none

      - name: Setup problem matchers
        run: |
          echo "::add-matcher::${{ runner.tool_cache }}/php.json"
          echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

      - name: Install dependencies
        run: |
          composer require "laravel/framework:${{ matrix.laravel }}" "orchestra/testbench:${{ matrix.testbench }}" --no-interaction --no-update
          composer update --${{ matrix.stability }} --prefer-dist --no-interaction

      - name: Execute tests
        run: vendor/bin/pest
