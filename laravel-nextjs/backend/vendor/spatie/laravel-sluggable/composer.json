{"name": "spatie/laravel-sluggable", "description": "Generate slugs when saving Eloquent models", "license": "MIT", "keywords": ["spatie", "laravel-sluggable"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "homepage": "https://github.com/spatie/laravel-sluggable", "require": {"php": "^8.0", "illuminate/database": "^8.0|^9.0|^10.0|^11.0|^12.0", "illuminate/support": "^8.0|^9.0|^10.0|^11.0|^12.0"}, "require-dev": {"orchestra/testbench": "^6.23|^7.0|^8.0|^9.0|^10.0", "pestphp/pest": "^1.20|^2.0|^3.7", "spatie/laravel-translatable": "^5.0|^6.0"}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Spatie\\Sluggable\\": "src"}}, "autoload-dev": {"psr-4": {"Spatie\\Sluggable\\Tests\\": "tests"}}, "config": {"allow-plugins": {"pestphp/pest-plugin": true}}, "scripts": {"format": "vendor/bin/php-cs-fixer fix --allow-risky=yes", "test": "vendor/bin/pest"}}