<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Category::with(['children', 'companies'])
            ->active()
            ->ordered();

        // Filtruj tylko kategorie główne jeśli nie ma parametru 'all'
        if (!$request->has('all')) {
            $query->main();
        }

        $categories = $query->get();

        return response()->json([
            'success' => true,
            'data' => $categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description,
                    'icon' => $category->icon,
                    'color' => $category->color,
                    'companies_count' => $category->companies->count(),
                    'children' => $category->children->map(function ($child) {
                        return [
                            'id' => $child->id,
                            'name' => $child->name,
                            'slug' => $child->slug,
                            'companies_count' => $child->companies->count()
                        ];
                    })
                ];
            })
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Category $category): JsonResponse
    {
        $category->load(['children', 'companies.category', 'parent']);

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
                'description' => $category->description,
                'icon' => $category->icon,
                'color' => $category->color,
                'breadcrumb' => $category->getBreadcrumb(),
                'companies_count' => $category->companies->count(),
                'children' => $category->children->map(function ($child) {
                    return [
                        'id' => $child->id,
                        'name' => $child->name,
                        'slug' => $child->slug,
                        'companies_count' => $child->companies->count()
                    ];
                }),
                'companies' => $category->companies->map(function ($company) {
                    return [
                        'id' => $company->id,
                        'name' => $company->name,
                        'slug' => $company->slug,
                        'short_description' => $company->short_description,
                        'logo' => $company->getLogoUrl(),
                        'rating' => $company->rating,
                        'is_top' => $company->is_top,
                        'is_open_now' => $company->isOpenNow()
                    ];
                })
            ]
        ]);
    }

    /**
     * Get companies for a specific category
     */
    public function companies(Category $category, Request $request): JsonResponse
    {
        $query = $category->getAllCompanies()
            ->with('category')
            ->active();

        // Sortowanie
        $sort = $request->get('sort', 'name');
        switch ($sort) {
            case 'popular':
                $query->popular();
                break;
            case 'rating':
                $query->byRating();
                break;
            case 'top':
                $query->orderBy('is_top', 'desc')->orderBy('top_position');
                break;
            default:
                $query->orderBy('name');
        }

        // Wyszukiwanie
        if ($search = $request->get('search')) {
            $query->search($search);
        }

        $companies = $query->paginate(12);

        return response()->json([
            'success' => true,
            'data' => $companies->items(),
            'pagination' => [
                'current_page' => $companies->currentPage(),
                'last_page' => $companies->lastPage(),
                'per_page' => $companies->perPage(),
                'total' => $companies->total()
            ]
        ]);
    }
}
