<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Coupon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CouponController extends Controller
{
    /**
     * Display a listing of active coupons.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Coupon::with('company.category')
            ->active()
            ->valid()
            ->available();

        // Filtrowanie po firmie
        if ($companyId = $request->get('company_id')) {
            $query->where('company_id', $companyId);
        }

        // Filtrowanie po typie rabatu
        if ($discountType = $request->get('discount_type')) {
            $query->byDiscountType($discountType);
        }

        // Sortowanie
        $sort = $request->get('sort', 'valid_to');
        switch ($sort) {
            case 'discount':
                $query->orderBy('discount_value', 'desc');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            default:
                $query->orderBy('valid_to', 'asc');
        }

        $coupons = $query->paginate(12);

        return response()->json([
            'success' => true,
            'data' => $coupons->items(),
            'pagination' => [
                'current_page' => $coupons->currentPage(),
                'last_page' => $coupons->lastPage(),
                'per_page' => $coupons->perPage(),
                'total' => $coupons->total()
            ]
        ]);
    }

    /**
     * Display the specified coupon.
     */
    public function show(Coupon $coupon): JsonResponse
    {
        if (!$coupon->isValid()) {
            return response()->json([
                'success' => false,
                'message' => 'Kupon nie jest dostępny'
            ], 404);
        }

        $coupon->load('company.category');

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $coupon->id,
                'title' => $coupon->title,
                'description' => $coupon->description,
                'code' => $coupon->code,
                'discount_type' => $coupon->discount_type,
                'discount_value' => $coupon->discount_value,
                'discount_formatted' => $coupon->getFormattedDiscount(),
                'min_order_value' => $coupon->min_order_value,
                'valid_from' => $coupon->valid_from->format('Y-m-d'),
                'valid_to' => $coupon->valid_to->format('Y-m-d'),
                'remaining_uses' => $coupon->getRemainingUses(),
                'usage_percentage' => $coupon->getUsagePercentage(),
                'terms_conditions' => $coupon->terms_conditions,
                'company' => [
                    'id' => $coupon->company->id,
                    'name' => $coupon->company->name,
                    'slug' => $coupon->company->slug,
                    'logo' => $coupon->company->getLogoUrl(),
                    'category' => $coupon->company->category->name
                ]
            ]
        ]);
    }

    /**
     * Use a coupon (increment usage counter).
     */
    public function use(Request $request, Coupon $coupon): JsonResponse
    {
        if (!$coupon->isValid()) {
            return response()->json([
                'success' => false,
                'message' => 'Kupon nie jest dostępny lub wygasł'
            ], 400);
        }

        if ($coupon->use()) {
            return response()->json([
                'success' => true,
                'message' => 'Kupon został wykorzystany',
                'remaining_uses' => $coupon->getRemainingUses()
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Nie udało się wykorzystać kuponu'
        ], 400);
    }

    /**
     * Get active coupons (public endpoint).
     */
    public function active(): JsonResponse
    {
        $coupons = Coupon::with('company')
            ->active()
            ->valid()
            ->available()
            ->orderBy('valid_to', 'asc')
            ->limit(6)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $coupons->map(function ($coupon) {
                return [
                    'id' => $coupon->id,
                    'title' => $coupon->title,
                    'description' => $coupon->description,
                    'code' => $coupon->code,
                    'discount' => $coupon->getFormattedDiscount(),
                    'valid_to' => $coupon->valid_to->format('Y-m-d'),
                    'company' => [
                        'id' => $coupon->company->id,
                        'name' => $coupon->company->name,
                        'slug' => $coupon->company->slug,
                        'logo' => $coupon->company->getLogoUrl()
                    ]
                ];
            })
        ]);
    }

    /**
     * Validate coupon code.
     */
    public function validate(Request $request): JsonResponse
    {
        $code = $request->get('code');

        if (empty($code)) {
            return response()->json([
                'success' => false,
                'message' => 'Brak kodu kuponu'
            ], 400);
        }

        $coupon = Coupon::where('code', $code)->first();

        if (!$coupon) {
            return response()->json([
                'success' => false,
                'message' => 'Nieprawidłowy kod kuponu'
            ], 404);
        }

        if (!$coupon->isValid()) {
            return response()->json([
                'success' => false,
                'message' => 'Kupon wygasł lub nie jest dostępny',
                'status' => $coupon->getStatus()
            ], 400);
        }

        return response()->json([
            'success' => true,
            'message' => 'Kupon jest ważny',
            'data' => [
                'id' => $coupon->id,
                'title' => $coupon->title,
                'discount' => $coupon->getFormattedDiscount(),
                'min_order_value' => $coupon->min_order_value,
                'valid_to' => $coupon->valid_to->format('Y-m-d')
            ]
        ]);
    }
}
