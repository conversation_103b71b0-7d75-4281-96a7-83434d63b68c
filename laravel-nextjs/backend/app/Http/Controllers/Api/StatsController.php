<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Company;
use App\Models\Coupon;
use Illuminate\Http\JsonResponse;

class StatsController extends Controller
{
    /**
     * Get general statistics
     */
    public function index(): JsonResponse
    {
        $stats = [
            'companies' => [
                'total' => Company::count(),
                'active' => Company::active()->count(),
                'top' => Company::top()->count()
            ],
            'categories' => [
                'total' => Category::count(),
                'active' => Category::active()->count(),
                'main' => Category::main()->count()
            ],
            'coupons' => [
                'total' => Coupon::count(),
                'active' => Coupon::active()->valid()->available()->count(),
                'expired' => Coupon::where('valid_to', '<', now())->count()
            ],
            'popular_categories' => Category::withCount('companies')
                ->active()
                ->orderBy('companies_count', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($category) {
                    return [
                        'name' => $category->name,
                        'companies_count' => $category->companies_count
                    ];
                }),
            'top_companies' => Company::active()
                ->top()
                ->orderBy('top_position')
                ->limit(3)
                ->get()
                ->map(function ($company) {
                    return [
                        'name' => $company->name,
                        'views' => $company->views_count,
                        'rating' => $company->rating
                    ];
                })
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'generated_at' => now()->toISOString()
        ]);
    }

    /**
     * Get dashboard statistics for admin
     */
    public function dashboard(): JsonResponse
    {
        $today = now()->startOfDay();
        $thisWeek = now()->startOfWeek();
        $thisMonth = now()->startOfMonth();

        $stats = [
            'overview' => [
                'total_companies' => Company::count(),
                'active_companies' => Company::active()->count(),
                'total_categories' => Category::count(),
                'active_coupons' => Coupon::active()->valid()->available()->count()
            ],
            'recent_activity' => [
                'companies_today' => Company::where('created_at', '>=', $today)->count(),
                'companies_this_week' => Company::where('created_at', '>=', $thisWeek)->count(),
                'companies_this_month' => Company::where('created_at', '>=', $thisMonth)->count(),
                'total_views_today' => Company::where('updated_at', '>=', $today)->sum('views_count')
            ],
            'top_viewed_companies' => Company::active()
                ->orderBy('views_count', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($company) {
                    return [
                        'id' => $company->id,
                        'name' => $company->name,
                        'views' => $company->views_count,
                        'rating' => $company->rating
                    ];
                }),
            'categories_distribution' => Category::withCount('companies')
                ->active()
                ->orderBy('companies_count', 'desc')
                ->get()
                ->map(function ($category) {
                    return [
                        'name' => $category->name,
                        'count' => $category->companies_count,
                        'color' => $category->color
                    ];
                }),
            'coupons_status' => [
                'active' => Coupon::active()->valid()->available()->count(),
                'expired' => Coupon::where('valid_to', '<', now())->count(),
                'used_up' => Coupon::whereRaw('used_count >= max_uses')->whereNotNull('max_uses')->count(),
                'inactive' => Coupon::where('is_active', false)->count()
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'generated_at' => now()->toISOString()
        ]);
    }

    /**
     * Get health check status
     */
    public function health(): JsonResponse
    {
        $health = [
            'status' => 'healthy',
            'database' => 'connected',
            'version' => '1.0.0',
            'environment' => app()->environment(),
            'timestamp' => now()->toISOString()
        ];

        try {
            // Test database connection
            Company::count();
            $health['database'] = 'connected';
        } catch (\Exception $e) {
            $health['status'] = 'unhealthy';
            $health['database'] = 'disconnected';
            $health['error'] = $e->getMessage();
        }

        return response()->json($health);
    }
}
