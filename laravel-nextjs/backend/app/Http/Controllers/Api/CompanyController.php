<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CompanyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Company::with(['category', 'coupons'])
            ->active();

        // Filtrowanie po kategorii
        if ($categoryId = $request->get('category_id')) {
            $query->where('category_id', $categoryId);
        }

        // Wyszukiwanie
        if ($search = $request->get('search')) {
            $query->search($search);
        }

        // Sortowanie
        $sort = $request->get('sort', 'name');
        switch ($sort) {
            case 'popular':
                $query->popular();
                break;
            case 'rating':
                $query->byRating();
                break;
            case 'top':
                $query->orderBy('is_top', 'desc')->orderBy('top_position');
                break;
            default:
                $query->orderBy('name');
        }

        $companies = $query->paginate(12);

        return response()->json([
            'success' => true,
            'data' => $companies->items(),
            'pagination' => [
                'current_page' => $companies->currentPage(),
                'last_page' => $companies->lastPage(),
                'per_page' => $companies->perPage(),
                'total' => $companies->total()
            ]
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Company $company): JsonResponse
    {
        // Zwiększ licznik wyświetleń
        $company->incrementViews();

        $company->load(['category', 'coupons' => function ($query) {
            $query->active()->valid()->available();
        }]);

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $company->id,
                'name' => $company->name,
                'slug' => $company->slug,
                'description' => $company->description,
                'short_description' => $company->short_description,
                'address' => $company->address,
                'phone' => $company->phone,
                'email' => $company->email,
                'website' => $company->website,
                'facebook' => $company->facebook,
                'instagram' => $company->instagram,
                'opening_hours' => $company->getFormattedOpeningHours(),
                'logo' => $company->getLogoUrl(),
                'images' => $company->getImageUrls(),
                'rating' => $company->rating,
                'reviews_count' => $company->reviews_count,
                'views_count' => $company->views_count,
                'is_top' => $company->is_top,
                'is_open_now' => $company->isOpenNow(),
                'category' => [
                    'id' => $company->category->id,
                    'name' => $company->category->name,
                    'slug' => $company->category->slug
                ],
                'active_coupons' => $company->coupons->map(function ($coupon) {
                    return [
                        'id' => $coupon->id,
                        'title' => $coupon->title,
                        'description' => $coupon->description,
                        'code' => $coupon->code,
                        'discount' => $coupon->getFormattedDiscount(),
                        'valid_to' => $coupon->valid_to->format('Y-m-d')
                    ];
                })
            ]
        ]);
    }

    /**
     * Get TOP companies
     */
    public function top(): JsonResponse
    {
        $companies = Company::with('category')
            ->active()
            ->top()
            ->limit(6)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $companies->map(function ($company) {
                return [
                    'id' => $company->id,
                    'name' => $company->name,
                    'slug' => $company->slug,
                    'short_description' => $company->short_description,
                    'logo' => $company->getLogoUrl(),
                    'rating' => $company->rating,
                    'category' => $company->category->name,
                    'is_open_now' => $company->isOpenNow()
                ];
            })
        ]);
    }

    /**
     * Search companies
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('q');

        if (empty($query)) {
            return response()->json([
                'success' => false,
                'message' => 'Brak frazy wyszukiwania'
            ], 400);
        }

        $companies = Company::with('category')
            ->active()
            ->search($query)
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $companies->map(function ($company) {
                return [
                    'id' => $company->id,
                    'name' => $company->name,
                    'slug' => $company->slug,
                    'short_description' => $company->short_description,
                    'logo' => $company->getLogoUrl(),
                    'category' => $company->category->name
                ];
            })
        ]);
    }
}
