<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;

class AdminSettingsController extends Controller
{
    /**
     * Get all settings
     */
    public function index(): JsonResponse
    {
        $settings = $this->getSettings();

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }

    /**
     * Update settings
     */
    public function update(Request $request): JsonResponse
    {
        $request->validate([
            'site_name' => 'nullable|string|max:255',
            'site_description' => 'nullable|string|max:1000',
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:20',
            'contact_address' => 'nullable|string|max:500',
            'facebook_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'youtube_url' => 'nullable|url|max:255',
            'google_analytics_id' => 'nullable|string|max:50',
            'google_maps_api_key' => 'nullable|string|max:100',
            'weather_api_key' => 'nullable|string|max:100',
            'weather_city' => 'nullable|string|max:100',
            'seo_title' => 'nullable|string|max:255',
            'seo_description' => 'nullable|string|max:500',
            'seo_keywords' => 'nullable|string|max:1000',
            'maintenance_mode' => 'boolean',
            'allow_registration' => 'boolean',
            'require_email_verification' => 'boolean',
            'max_companies_per_category' => 'nullable|integer|min:1',
            'max_images_per_company' => 'nullable|integer|min:1|max:20',
            'max_file_size_mb' => 'nullable|integer|min:1|max:50',
        ]);

        $settings = $this->getSettings();
        
        // Aktualizuj tylko przesłane wartości
        foreach ($request->all() as $key => $value) {
            $settings[$key] = $value;
        }

        // Zapisz ustawienia
        $this->saveSettings($settings);

        return response()->json([
            'success' => true,
            'message' => 'Ustawienia zostały zaktualizowane pomyślnie',
            'data' => $settings
        ]);
    }

    /**
     * Get settings from storage
     */
    private function getSettings(): array
    {
        $defaultSettings = [
            'site_name' => 'Żyrardów.poleca.to',
            'site_description' => 'Portal lokalnych firm w Żyrardowie',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '',
            'contact_address' => 'Żyrardów, Polska',
            'facebook_url' => '',
            'instagram_url' => '',
            'twitter_url' => '',
            'youtube_url' => '',
            'google_analytics_id' => '',
            'google_maps_api_key' => '',
            'weather_api_key' => '',
            'weather_city' => 'Żyrardów',
            'seo_title' => 'Żyrardów.poleca.to - Portal lokalnych firm',
            'seo_description' => 'Odkryj najlepsze firmy, usługi i atrakcje w Żyrardowie. Portal lokalnych przedsiębiorców z kuponami rabatowymi.',
            'seo_keywords' => 'Żyrardów, firmy, usługi, kupony, rabaty, lokalne, przedsiębiorcy',
            'maintenance_mode' => false,
            'allow_registration' => false,
            'require_email_verification' => true,
            'max_companies_per_category' => 100,
            'max_images_per_company' => 10,
            'max_file_size_mb' => 5,
        ];

        // Sprawdź czy plik ustawień istnieje
        if (Storage::disk('local')->exists('settings.json')) {
            $savedSettings = json_decode(Storage::disk('local')->get('settings.json'), true);
            return array_merge($defaultSettings, $savedSettings ?? []);
        }

        return $defaultSettings;
    }

    /**
     * Save settings to storage
     */
    private function saveSettings(array $settings): void
    {
        Storage::disk('local')->put('settings.json', json_encode($settings, JSON_PRETTY_PRINT));
        
        // Wyczyść cache ustawień
        Cache::forget('app_settings');
    }

    /**
     * Get cached settings (for use in other parts of the app)
     */
    public static function getCachedSettings(): array
    {
        return Cache::remember('app_settings', 3600, function () {
            $controller = new self();
            return $controller->getSettings();
        });
    }

    /**
     * Reset settings to default
     */
    public function reset(): JsonResponse
    {
        // Usuń plik ustawień
        if (Storage::disk('local')->exists('settings.json')) {
            Storage::disk('local')->delete('settings.json');
        }

        // Wyczyść cache
        Cache::forget('app_settings');

        $defaultSettings = $this->getSettings();

        return response()->json([
            'success' => true,
            'message' => 'Ustawienia zostały przywrócone do wartości domyślnych',
            'data' => $defaultSettings
        ]);
    }

    /**
     * Export settings
     */
    public function export(): JsonResponse
    {
        $settings = $this->getSettings();

        return response()->json([
            'success' => true,
            'data' => $settings,
            'filename' => 'zyrardow_poleca_settings_' . date('Y-m-d_H-i-s') . '.json'
        ]);
    }

    /**
     * Import settings
     */
    public function import(Request $request): JsonResponse
    {
        $request->validate([
            'settings_file' => 'required|file|mimes:json|max:1024' // 1MB max
        ]);

        try {
            $fileContent = file_get_contents($request->file('settings_file')->getRealPath());
            $importedSettings = json_decode($fileContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return response()->json([
                    'success' => false,
                    'message' => 'Nieprawidłowy format pliku JSON'
                ], 422);
            }

            // Waliduj importowane ustawienia
            $currentSettings = $this->getSettings();
            $validSettings = [];

            foreach ($importedSettings as $key => $value) {
                if (array_key_exists($key, $currentSettings)) {
                    $validSettings[$key] = $value;
                }
            }

            // Połącz z obecnymi ustawieniami
            $finalSettings = array_merge($currentSettings, $validSettings);
            
            $this->saveSettings($finalSettings);

            return response()->json([
                'success' => true,
                'message' => 'Ustawienia zostały zaimportowane pomyślnie',
                'data' => $finalSettings
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Błąd podczas importowania ustawień: ' . $e->getMessage()
            ], 500);
        }
    }
}
