<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Coupon;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class AdminCouponController extends Controller
{
    /**
     * Display a listing of coupons for admin
     */
    public function index(Request $request): JsonResponse
    {
        $query = Coupon::with(['company']);

        // Filtrowanie
        if ($companyId = $request->get('company_id')) {
            $query->where('company_id', $companyId);
        }

        if ($status = $request->get('status')) {
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            } elseif ($status === 'expired') {
                $query->where('valid_to', '<', now());
            } elseif ($status === 'valid') {
                $query->where('valid_to', '>=', now());
            }
        }

        if ($discountType = $request->get('discount_type')) {
            $query->where('discount_type', $discountType);
        }

        // Wyszukiwanie
        if ($search = $request->get('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Sortowanie
        $sort = $request->get('sort', 'created_at');
        $direction = $request->get('direction', 'desc');
        
        switch ($sort) {
            case 'valid_to':
                $query->orderBy('valid_to', $direction);
                break;
            case 'discount_value':
                $query->orderBy('discount_value', $direction);
                break;
            case 'used_count':
                $query->orderBy('used_count', $direction);
                break;
            case 'company':
                $query->join('companies', 'coupons.company_id', '=', 'companies.id')
                      ->orderBy('companies.name', $direction)
                      ->select('coupons.*');
                break;
            default:
                $query->orderBy($sort, $direction);
        }

        $coupons = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $coupons->items(),
            'pagination' => [
                'current_page' => $coupons->currentPage(),
                'last_page' => $coupons->lastPage(),
                'per_page' => $coupons->perPage(),
                'total' => $coupons->total(),
                'from' => $coupons->firstItem(),
                'to' => $coupons->lastItem(),
            ]
        ]);
    }

    /**
     * Store a newly created coupon
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'company_id' => 'required|exists:companies,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'code' => 'required|string|max:50|unique:coupons,code',
            'discount_type' => ['required', Rule::in(array_keys(Coupon::DISCOUNT_TYPES))],
            'discount_value' => 'required|numeric|min:0',
            'min_order_value' => 'nullable|numeric|min:0',
            'max_uses' => 'nullable|integer|min:1',
            'valid_from' => 'required|date|after_or_equal:today',
            'valid_to' => 'required|date|after:valid_from',
            'is_active' => 'boolean',
            'terms_conditions' => 'nullable|string|max:2000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Błędy walidacji',
                'errors' => $validator->errors()
            ], 422);
        }

        // Walidacja wartości rabatu w zależności od typu
        if ($request->discount_type === 'percentage' && $request->discount_value > 100) {
            return response()->json([
                'success' => false,
                'message' => 'Rabat procentowy nie może być większy niż 100%'
            ], 422);
        }

        $coupon = Coupon::create(array_merge($request->all(), [
            'used_count' => 0
        ]));

        return response()->json([
            'success' => true,
            'message' => 'Kupon został dodany pomyślnie',
            'data' => $coupon->load('company')
        ], 201);
    }

    /**
     * Display the specified coupon
     */
    public function show(Coupon $coupon): JsonResponse
    {
        $coupon->load('company');

        return response()->json([
            'success' => true,
            'data' => $coupon
        ]);
    }

    /**
     * Update the specified coupon
     */
    public function update(Request $request, Coupon $coupon): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'company_id' => 'required|exists:companies,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'code' => 'required|string|max:50|unique:coupons,code,' . $coupon->id,
            'discount_type' => ['required', Rule::in(array_keys(Coupon::DISCOUNT_TYPES))],
            'discount_value' => 'required|numeric|min:0',
            'min_order_value' => 'nullable|numeric|min:0',
            'max_uses' => 'nullable|integer|min:1',
            'valid_from' => 'required|date',
            'valid_to' => 'required|date|after:valid_from',
            'is_active' => 'boolean',
            'terms_conditions' => 'nullable|string|max:2000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Błędy walidacji',
                'errors' => $validator->errors()
            ], 422);
        }

        // Walidacja wartości rabatu w zależności od typu
        if ($request->discount_type === 'percentage' && $request->discount_value > 100) {
            return response()->json([
                'success' => false,
                'message' => 'Rabat procentowy nie może być większy niż 100%'
            ], 422);
        }

        $coupon->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Kupon został zaktualizowany pomyślnie',
            'data' => $coupon->load('company')
        ]);
    }

    /**
     * Remove the specified coupon
     */
    public function destroy(Coupon $coupon): JsonResponse
    {
        $coupon->delete();

        return response()->json([
            'success' => true,
            'message' => 'Kupon został usunięty pomyślnie'
        ]);
    }

    /**
     * Update coupon status
     */
    public function updateStatus(Request $request, Coupon $coupon): JsonResponse
    {
        $request->validate([
            'is_active' => 'required|boolean'
        ]);

        $coupon->update(['is_active' => $request->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'Status kuponu został zaktualizowany',
            'data' => $coupon
        ]);
    }
}
