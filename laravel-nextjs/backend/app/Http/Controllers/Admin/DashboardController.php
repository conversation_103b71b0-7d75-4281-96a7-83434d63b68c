<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Category;
use App\Models\Coupon;
use App\Models\User;
use Illuminate\Http\JsonResponse;

class DashboardController extends Controller
{
    /**
     * Get dashboard statistics
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'companies' => [
                'total' => Company::count(),
                'active' => Company::where('is_active', true)->count(),
                'pending' => Company::where('is_active', false)->count(),
                'top' => Company::where('is_top', true)->count(),
            ],
            'categories' => [
                'total' => Category::count(),
                'active' => Category::where('is_active', true)->count(),
            ],
            'coupons' => [
                'total' => Coupon::count(),
                'active' => Coupon::where('is_active', true)
                    ->where('valid_to', '>=', now())
                    ->count(),
                'expired' => Coupon::where('valid_to', '<', now())->count(),
            ],
            'users' => [
                'total' => User::count(),
                'admins' => User::where('is_admin', true)->count(),
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
