<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class AdminUserController extends Controller
{
    /**
     * Display a listing of users for admin
     */
    public function index(Request $request): JsonResponse
    {
        $query = User::query();

        // Filtrowanie
        if ($status = $request->get('status')) {
            if ($status === 'admin') {
                $query->where('is_admin', true);
            } elseif ($status === 'user') {
                $query->where('is_admin', false);
            }
        }

        if ($request->has('email_verified')) {
            if ($request->email_verified === 'true') {
                $query->whereNotNull('email_verified_at');
            } elseif ($request->email_verified === 'false') {
                $query->whereNull('email_verified_at');
            }
        }

        // Wyszukiwanie
        if ($search = $request->get('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Sortowanie
        $sort = $request->get('sort', 'created_at');
        $direction = $request->get('direction', 'desc');
        
        $query->orderBy($sort, $direction);

        $users = $query->paginate($request->get('per_page', 15));

        // Ukryj hasła w odpowiedzi
        $users->getCollection()->transform(function ($user) {
            unset($user->password);
            return $user;
        });

        return response()->json([
            'success' => true,
            'data' => $users->items(),
            'pagination' => [
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'per_page' => $users->perPage(),
                'total' => $users->total(),
                'from' => $users->firstItem(),
                'to' => $users->lastItem(),
            ]
        ]);
    }

    /**
     * Store a newly created user
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'is_admin' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Błędy walidacji',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'is_admin' => $request->is_admin ?? false,
            'email_verified_at' => now(), // Auto-verify admin created users
        ]);

        // Ukryj hasło w odpowiedzi
        unset($user->password);

        return response()->json([
            'success' => true,
            'message' => 'Użytkownik został dodany pomyślnie',
            'data' => $user
        ], 201);
    }

    /**
     * Display the specified user
     */
    public function show(User $user): JsonResponse
    {
        // Ukryj hasło
        $userData = $user->toArray();
        unset($userData['password']);

        return response()->json([
            'success' => true,
            'data' => $userData
        ]);
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8|confirmed',
            'is_admin' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Błędy walidacji',
                'errors' => $validator->errors()
            ], 422);
        }

        // Sprawdź czy nie próbujemy usunąć uprawnień admin ostatniemu adminowi
        if (!$request->is_admin && $user->is_admin) {
            $adminCount = User::where('is_admin', true)->count();
            if ($adminCount <= 1) {
                return response()->json([
                    'success' => false,
                    'message' => 'Nie można usunąć uprawnień administratora ostatniemu adminowi'
                ], 422);
            }
        }

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'is_admin' => $request->is_admin ?? false,
        ];

        // Aktualizuj hasło tylko jeśli zostało podane
        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        // Ukryj hasło w odpowiedzi
        $userData = $user->fresh()->toArray();
        unset($userData['password']);

        return response()->json([
            'success' => true,
            'message' => 'Użytkownik został zaktualizowany pomyślnie',
            'data' => $userData
        ]);
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user): JsonResponse
    {
        // Sprawdź czy nie próbujemy usunąć ostatniego admina
        if ($user->is_admin) {
            $adminCount = User::where('is_admin', true)->count();
            if ($adminCount <= 1) {
                return response()->json([
                    'success' => false,
                    'message' => 'Nie można usunąć ostatniego administratora'
                ], 422);
            }
        }

        // Sprawdź czy użytkownik nie próbuje usunąć samego siebie
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Nie można usunąć własnego konta'
            ], 422);
        }

        $user->delete();

        return response()->json([
            'success' => true,
            'message' => 'Użytkownik został usunięty pomyślnie'
        ]);
    }

    /**
     * Update user status (admin/regular)
     */
    public function updateStatus(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'is_admin' => 'required|boolean'
        ]);

        // Sprawdź czy nie próbujemy usunąć uprawnień admin ostatniemu adminowi
        if (!$request->is_admin && $user->is_admin) {
            $adminCount = User::where('is_admin', true)->count();
            if ($adminCount <= 1) {
                return response()->json([
                    'success' => false,
                    'message' => 'Nie można usunąć uprawnień administratora ostatniemu adminowi'
                ], 422);
            }
        }

        $user->update(['is_admin' => $request->is_admin]);

        // Ukryj hasło w odpowiedzi
        $userData = $user->fresh()->toArray();
        unset($userData['password']);

        return response()->json([
            'success' => true,
            'message' => 'Status użytkownika został zaktualizowany',
            'data' => $userData
        ]);
    }
}
