<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class AdminCompanyController extends Controller
{
    /**
     * Display a listing of companies for admin
     */
    public function index(Request $request): JsonResponse
    {
        $query = Company::with(['category']);

        // Filtrowanie
        if ($categoryId = $request->get('category_id')) {
            $query->where('category_id', $categoryId);
        }

        if ($status = $request->get('status')) {
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        if ($isTop = $request->get('is_top')) {
            $query->where('is_top', $isTop === 'true');
        }

        // Wyszukiwanie
        if ($search = $request->get('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Sortowanie
        $sort = $request->get('sort', 'name');
        $direction = $request->get('direction', 'asc');

        switch ($sort) {
            case 'created_at':
                $query->orderBy('created_at', $direction);
                break;
            case 'views_count':
                $query->orderBy('views_count', $direction);
                break;
            case 'rating':
                $query->orderBy('rating', $direction);
                break;
            case 'top_position':
                $query->orderBy('is_top', 'desc')->orderBy('top_position', 'asc');
                break;
            default:
                $query->orderBy('name', $direction);
        }

        $companies = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $companies->items(),
            'pagination' => [
                'current_page' => $companies->currentPage(),
                'last_page' => $companies->lastPage(),
                'per_page' => $companies->perPage(),
                'total' => $companies->total(),
                'from' => $companies->firstItem(),
                'to' => $companies->lastItem(),
            ]
        ]);
    }

    /**
     * Store a newly created company
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'description' => 'nullable|string|max:5000',
            'short_description' => 'nullable|string|max:500',
            'address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|url|max:255',
            'facebook' => 'nullable|url|max:255',
            'instagram' => 'nullable|url|max:255',
            'opening_hours' => 'nullable|array',
            'is_top' => 'boolean',
            'top_position' => 'nullable|integer|min:1|max:3',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Błędy walidacji',
                'errors' => $validator->errors()
            ], 422);
        }

        // Sprawdź czy pozycja TOP jest dostępna
        if ($request->is_top && $request->top_position) {
            $existingTop = Company::where('top_position', $request->top_position)
                                 ->where('is_top', true)
                                 ->first();
            if ($existingTop) {
                return response()->json([
                    'success' => false,
                    'message' => "Pozycja TOP {$request->top_position} jest już zajęta"
                ], 422);
            }
        }

        $company = Company::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Firma została dodana pomyślnie',
            'data' => $company->load('category')
        ], 201);
    }

    /**
     * Display the specified company
     */
    public function show(Company $company): JsonResponse
    {
        $company->load(['category', 'coupons']);

        return response()->json([
            'success' => true,
            'data' => $company
        ]);
    }

    /**
     * Update the specified company
     */
    public function update(Request $request, Company $company): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'description' => 'nullable|string|max:5000',
            'short_description' => 'nullable|string|max:500',
            'address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|url|max:255',
            'facebook' => 'nullable|url|max:255',
            'instagram' => 'nullable|url|max:255',
            'opening_hours' => 'nullable|array',
            'is_top' => 'boolean',
            'top_position' => 'nullable|integer|min:1|max:3',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Błędy walidacji',
                'errors' => $validator->errors()
            ], 422);
        }

        // Sprawdź czy pozycja TOP jest dostępna (jeśli się zmieniła)
        if ($request->is_top && $request->top_position &&
            $request->top_position !== $company->top_position) {
            $existingTop = Company::where('top_position', $request->top_position)
                                 ->where('is_top', true)
                                 ->where('id', '!=', $company->id)
                                 ->first();
            if ($existingTop) {
                return response()->json([
                    'success' => false,
                    'message' => "Pozycja TOP {$request->top_position} jest już zajęta"
                ], 422);
            }
        }

        $company->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Firma została zaktualizowana pomyślnie',
            'data' => $company->load('category')
        ]);
    }

    /**
     * Remove the specified company
     */
    public function destroy(Company $company): JsonResponse
    {
        // Usuń pliki związane z firmą
        if ($company->logo) {
            Storage::disk('public')->delete($company->logo);
        }

        if ($company->images) {
            foreach ($company->images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $company->delete();

        return response()->json([
            'success' => true,
            'message' => 'Firma została usunięta pomyślnie'
        ]);
    }

    /**
     * Update company status
     */
    public function updateStatus(Request $request, Company $company): JsonResponse
    {
        $request->validate([
            'is_active' => 'required|boolean'
        ]);

        $company->update(['is_active' => $request->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'Status firmy został zaktualizowany',
            'data' => $company
        ]);
    }

    /**
     * Update company TOP position
     */
    public function updateTopPosition(Request $request, Company $company): JsonResponse
    {
        $request->validate([
            'is_top' => 'required|boolean',
            'top_position' => 'nullable|integer|min:1|max:3'
        ]);

        // Sprawdź czy pozycja TOP jest dostępna
        if ($request->is_top && $request->top_position) {
            $existingTop = Company::where('top_position', $request->top_position)
                                 ->where('is_top', true)
                                 ->where('id', '!=', $company->id)
                                 ->first();
            if ($existingTop) {
                return response()->json([
                    'success' => false,
                    'message' => "Pozycja TOP {$request->top_position} jest już zajęta"
                ], 422);
            }
        }

        $company->update([
            'is_top' => $request->is_top,
            'top_position' => $request->is_top ? $request->top_position : null
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Pozycja TOP została zaktualizowana',
            'data' => $company
        ]);
    }

    /**
     * Upload company logo
     */
    public function uploadLogo(Request $request, Company $company): JsonResponse
    {
        $request->validate([
            'logo' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120' // 5MB
        ]);

        // Usuń stare logo
        if ($company->logo) {
            Storage::disk('public')->delete($company->logo);
        }

        // Zapisz nowe logo
        $logoPath = $request->file('logo')->store('companies/logos', 'public');

        $company->update(['logo' => $logoPath]);

        return response()->json([
            'success' => true,
            'message' => 'Logo zostało przesłane pomyślnie',
            'data' => [
                'logo_url' => Storage::url($logoPath)
            ]
        ]);
    }

    /**
     * Upload company images
     */
    public function uploadImages(Request $request, Company $company): JsonResponse
    {
        $request->validate([
            'images' => 'required|array|max:10',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120' // 5MB każdy
        ]);

        $uploadedImages = [];
        $currentImages = $company->images ?? [];

        foreach ($request->file('images') as $image) {
            $imagePath = $image->store('companies/images', 'public');
            $uploadedImages[] = $imagePath;
        }

        // Połącz z istniejącymi obrazami
        $allImages = array_merge($currentImages, $uploadedImages);

        $company->update(['images' => $allImages]);

        return response()->json([
            'success' => true,
            'message' => 'Zdjęcia zostały przesłane pomyślnie',
            'data' => [
                'images' => array_map(function($path) {
                    return Storage::url($path);
                }, $allImages)
            ]
        ]);
    }
}
