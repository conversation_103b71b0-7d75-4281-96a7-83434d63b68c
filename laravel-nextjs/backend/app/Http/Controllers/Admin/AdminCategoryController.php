<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class AdminCategoryController extends Controller
{
    /**
     * Display a listing of categories for admin
     */
    public function index(Request $request): JsonResponse
    {
        $query = Category::with(['parent', 'children', 'companies']);

        // Filtrowanie
        if ($request->has('parent_id')) {
            if ($request->parent_id === 'null' || $request->parent_id === null) {
                $query->whereNull('parent_id');
            } else {
                $query->where('parent_id', $request->parent_id);
            }
        }

        if ($status = $request->get('status')) {
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Wyszukiwanie
        if ($search = $request->get('search')) {
            $query->where('name', 'like', "%{$search}%");
        }

        // Sortowanie
        $sort = $request->get('sort', 'sort_order');
        $direction = $request->get('direction', 'asc');
        
        if ($sort === 'companies_count') {
            $query->withCount('companies')->orderBy('companies_count', $direction);
        } else {
            $query->orderBy($sort, $direction);
        }

        $categories = $query->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $categories->items(),
            'pagination' => [
                'current_page' => $categories->currentPage(),
                'last_page' => $categories->lastPage(),
                'per_page' => $categories->perPage(),
                'total' => $categories->total(),
                'from' => $categories->firstItem(),
                'to' => $categories->lastItem(),
            ]
        ]);
    }

    /**
     * Store a newly created category
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:categories,name',
            'description' => 'nullable|string|max:1000',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|regex:/^#[0-9A-F]{6}$/i',
            'parent_id' => 'nullable|exists:categories,id',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Błędy walidacji',
                'errors' => $validator->errors()
            ], 422);
        }

        // Ustaw domyślną kolejność sortowania
        if (!$request->has('sort_order')) {
            $maxOrder = Category::where('parent_id', $request->parent_id)->max('sort_order') ?? 0;
            $request->merge(['sort_order' => $maxOrder + 1]);
        }

        $category = Category::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Kategoria została dodana pomyślnie',
            'data' => $category->load(['parent', 'children'])
        ], 201);
    }

    /**
     * Display the specified category
     */
    public function show(Category $category): JsonResponse
    {
        $category->load(['parent', 'children', 'companies']);

        return response()->json([
            'success' => true,
            'data' => $category
        ]);
    }

    /**
     * Update the specified category
     */
    public function update(Request $request, Category $category): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:categories,name,' . $category->id,
            'description' => 'nullable|string|max:1000',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|regex:/^#[0-9A-F]{6}$/i',
            'parent_id' => 'nullable|exists:categories,id',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Błędy walidacji',
                'errors' => $validator->errors()
            ], 422);
        }

        // Sprawdź czy kategoria nie jest ustawiana jako własny rodzic
        if ($request->parent_id == $category->id) {
            return response()->json([
                'success' => false,
                'message' => 'Kategoria nie może być własnym rodzicem'
            ], 422);
        }

        // Sprawdź czy kategoria nie jest ustawiana jako dziecko swojego dziecka
        if ($request->parent_id && $category->isAncestorOf($request->parent_id)) {
            return response()->json([
                'success' => false,
                'message' => 'Wykryto cykliczną zależność kategorii'
            ], 422);
        }

        $category->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Kategoria została zaktualizowana pomyślnie',
            'data' => $category->load(['parent', 'children'])
        ]);
    }

    /**
     * Remove the specified category
     */
    public function destroy(Category $category): JsonResponse
    {
        // Sprawdź czy kategoria ma firmy
        if ($category->companies()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Nie można usunąć kategorii, która zawiera firmy'
            ], 422);
        }

        // Sprawdź czy kategoria ma podkategorie
        if ($category->children()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Nie można usunąć kategorii, która ma podkategorie'
            ], 422);
        }

        $category->delete();

        return response()->json([
            'success' => true,
            'message' => 'Kategoria została usunięta pomyślnie'
        ]);
    }

    /**
     * Reorder categories
     */
    public function reorder(Request $request): JsonResponse
    {
        $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
            'parent_id' => 'nullable|exists:categories,id'
        ]);

        foreach ($request->categories as $categoryData) {
            Category::where('id', $categoryData['id'])
                   ->update([
                       'sort_order' => $categoryData['sort_order'],
                       'parent_id' => $request->parent_id
                   ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Kolejność kategorii została zaktualizowana'
        ]);
    }
}
