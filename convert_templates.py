#!/usr/bin/env python3
"""
Skrypt do konwersji plików HTML na system szablonów
"""

import os
import re
import glob

def convert_html_file(file_path):
    """Konwertuje pojedynczy plik HTML na system szablonów"""
    print(f"Przetwarzanie: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Usuń istniejący nagłówek (od <header do </header>)
    content = re.sub(r'<header[^>]*>.*?</header>', '', content, flags=re.DOTALL)
    
    # Usuń istniejącą stopkę (od <footer do </footer>)
    content = re.sub(r'<footer[^>]*>.*?</footer>', '', content, flags=re.DOTALL)
    
    # Usuń cookie notice
    content = re.sub(r'<div class="cookie-notice"[^>]*>.*?</div>', '', content, flags=re.DOTALL)
    
    # Usuń back-to-top
    content = re.sub(r'<a[^>]*class="back-to-top"[^>]*>.*?</a>', '', content, flags=re.DOTALL)
    
    # Dodaj kontener nagłówka po <body>
    content = re.sub(r'(<body[^>]*>)', r'\1\n    <!-- Kontener na nagłówek - zostanie wypełniony przez templates.js -->\n    <div id="header"></div>\n', content)
    
    # Dodaj kontener stopki przed </body>
    content = re.sub(r'(</body>)', r'    <!-- Kontener na stopkę - zostanie wypełniony przez templates.js -->\n    <div id="footer"></div>\n\n\1', content)
    
    # Dodaj templates.js jeśli nie istnieje
    if 'templates.js' not in content:
        # Znajdź pierwsze wystąpienie <script src="js/ i dodaj templates.js przed nim
        content = re.sub(r'(<script src="js/)', r'    <script src="js/templates.js"></script>\n    \1', content, count=1)
        
        # Jeśli nie ma żadnych skryptów js/, dodaj przed </body>
        if 'templates.js' not in content:
            content = re.sub(r'(</body>)', r'    <script src="js/templates.js"></script>\n\1', content)
    
    # Zapisz zmodyfikowany plik
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Zakończono: {file_path}")

def main():
    """Główna funkcja skryptu"""
    # Znajdź wszystkie pliki HTML (z wyjątkiem admin i templates)
    html_files = glob.glob('*.html')
    html_files = [f for f in html_files if not f.startswith('admin/') and not f.startswith('templates/')]
    
    print(f"Znaleziono {len(html_files)} plików HTML do przetworzenia")
    
    for file_path in html_files:
        try:
            convert_html_file(file_path)
        except Exception as e:
            print(f"Błąd podczas przetwarzania {file_path}: {e}")
    
    print("Konwersja zakończona!")

if __name__ == "__main__":
    main()
